// rn-user-defaults.d.ts
declare module 'rn-user-defaults' {
  export default class RNUserDefaults {
    static get(key: string): Promise<string | null>;
    static set(key: string, value: string): Promise<void>;
    static clear(key: string): Promise<void>;
    static clearAll(): Promise<void>;
    static setObjectForKey(key: string, value: object): Promise<void>;
    static objectForKey(key: string): Promise<object | null>;
  }
}
