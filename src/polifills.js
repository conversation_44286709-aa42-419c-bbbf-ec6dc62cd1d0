import 'intl-pluralrules';
import 'react-native-url-polyfill/auto';

// Add any other locales you support

// hermes engine doesn't support Intl yet
//https://github.com/moment/luxon/issues/956
import '@formatjs/intl-locale/polyfill';
import '@formatjs/intl-datetimeformat/polyfill';
import '@formatjs/intl-datetimeformat/add-all-tz';
import '@formatjs/intl-datetimeformat/locale-data/en';
import '@formatjs/intl-datetimeformat/locale-data/en-GB';
import '@formatjs/intl-datetimeformat/locale-data/pt';
import '@formatjs/intl-datetimeformat/locale-data/fr-CA';
import '@formatjs/intl-datetimeformat/locale-data/nl';
import '@formatjs/intl-datetimeformat/locale-data/fr';
import '@formatjs/intl-datetimeformat/locale-data/de';
import '@formatjs/intl-datetimeformat/locale-data/it';
import '@formatjs/intl-datetimeformat/locale-data/ja';
import '@formatjs/intl-datetimeformat/locale-data/pt';
import '@formatjs/intl-datetimeformat/locale-data/ro';
import '@formatjs/intl-datetimeformat/locale-data/es';
import '@formatjs/intl-datetimeformat/locale-data/et';
import '@formatjs/intl-datetimeformat/locale-data/pl';
import '@formatjs/intl-datetimeformat/locale-data/bg';
import '@formatjs/intl-datetimeformat/locale-data/sv';
import '@formatjs/intl-datetimeformat/locale-data/da';
import '@formatjs/intl-datetimeformat/locale-data/fi';
import '@formatjs/intl-datetimeformat/locale-data/lv';
import '@formatjs/intl-datetimeformat/locale-data/hu';
import '@formatjs/intl-datetimeformat/locale-data/cs';
import '@formatjs/intl-datetimeformat/locale-data/ko';
import '@formatjs/intl-datetimeformat/locale-data/fil';
import '@formatjs/intl-datetimeformat/locale-data/hi';
import '@formatjs/intl-datetimeformat/locale-data/th';
import '@formatjs/intl-datetimeformat/locale-data/vi';
import '@formatjs/intl-datetimeformat/locale-data/id';

import '@formatjs/intl-listformat/polyfill';
import '@formatjs/intl-listformat/locale-data/en';
import '@formatjs/intl-listformat/locale-data/en-GB';
import '@formatjs/intl-listformat/locale-data/pt';
import '@formatjs/intl-listformat/locale-data/fr-CA';
import '@formatjs/intl-listformat/locale-data/nl';
import '@formatjs/intl-listformat/locale-data/fr';
import '@formatjs/intl-listformat/locale-data/de';
import '@formatjs/intl-listformat/locale-data/it';
import '@formatjs/intl-listformat/locale-data/ja';
import '@formatjs/intl-listformat/locale-data/pt';
import '@formatjs/intl-listformat/locale-data/ro';
import '@formatjs/intl-listformat/locale-data/es';
import '@formatjs/intl-listformat/locale-data/et';
import '@formatjs/intl-listformat/locale-data/pl';
import '@formatjs/intl-listformat/locale-data/bg';
import '@formatjs/intl-listformat/locale-data/sv';
import '@formatjs/intl-listformat/locale-data/da';
import '@formatjs/intl-listformat/locale-data/fi';
import '@formatjs/intl-listformat/locale-data/lv';
import '@formatjs/intl-listformat/locale-data/hu';
import '@formatjs/intl-listformat/locale-data/cs';
import '@formatjs/intl-listformat/locale-data/zh';
import '@formatjs/intl-listformat/locale-data/ko';
import '@formatjs/intl-listformat/locale-data/fil';
import '@formatjs/intl-listformat/locale-data/hi';
import '@formatjs/intl-listformat/locale-data/th';
import '@formatjs/intl-listformat/locale-data/vi';
import '@formatjs/intl-listformat/locale-data/id';

import '@formatjs/intl-relativetimeformat/polyfill';
import '@formatjs/intl-relativetimeformat/locale-data/en';
import '@formatjs/intl-relativetimeformat/locale-data/en-GB';
import '@formatjs/intl-relativetimeformat/locale-data/fr-CA';
import '@formatjs/intl-relativetimeformat/locale-data/nl';
import '@formatjs/intl-relativetimeformat/locale-data/fr';
import '@formatjs/intl-relativetimeformat/locale-data/de';
import '@formatjs/intl-relativetimeformat/locale-data/it';
import '@formatjs/intl-relativetimeformat/locale-data/ja';
import '@formatjs/intl-relativetimeformat/locale-data/pt';
import '@formatjs/intl-relativetimeformat/locale-data/ro';
import '@formatjs/intl-relativetimeformat/locale-data/es';
import '@formatjs/intl-relativetimeformat/locale-data/et';
import '@formatjs/intl-relativetimeformat/locale-data/pl';
import '@formatjs/intl-relativetimeformat/locale-data/bg';
import '@formatjs/intl-relativetimeformat/locale-data/sv';
import '@formatjs/intl-relativetimeformat/locale-data/da';
import '@formatjs/intl-relativetimeformat/locale-data/fi';
import '@formatjs/intl-relativetimeformat/locale-data/lv';
import '@formatjs/intl-relativetimeformat/locale-data/hu';
import '@formatjs/intl-relativetimeformat/locale-data/cs';
import '@formatjs/intl-relativetimeformat/locale-data/zh';
import '@formatjs/intl-relativetimeformat/locale-data/ko';
import '@formatjs/intl-relativetimeformat/locale-data/fil';
import '@formatjs/intl-relativetimeformat/locale-data/hi';
import '@formatjs/intl-relativetimeformat/locale-data/th';
import '@formatjs/intl-relativetimeformat/locale-data/vi';
import '@formatjs/intl-relativetimeformat/locale-data/id';

import '@formatjs/intl-pluralrules/polyfill';
import '@formatjs/intl-pluralrules/locale-data/en';
import '@formatjs/intl-pluralrules/locale-data/nl';
import '@formatjs/intl-pluralrules/locale-data/fr';
import '@formatjs/intl-pluralrules/locale-data/de';
import '@formatjs/intl-pluralrules/locale-data/it';
import '@formatjs/intl-pluralrules/locale-data/ja';
import '@formatjs/intl-pluralrules/locale-data/pt';
import '@formatjs/intl-pluralrules/locale-data/ro';
import '@formatjs/intl-pluralrules/locale-data/es';
import '@formatjs/intl-pluralrules/locale-data/et';
import '@formatjs/intl-pluralrules/locale-data/pl';
import '@formatjs/intl-pluralrules/locale-data/bg';
import '@formatjs/intl-pluralrules/locale-data/sv';
import '@formatjs/intl-pluralrules/locale-data/da';
import '@formatjs/intl-pluralrules/locale-data/fi';
import '@formatjs/intl-pluralrules/locale-data/lv';
import '@formatjs/intl-pluralrules/locale-data/hu';
import '@formatjs/intl-pluralrules/locale-data/cs';
import '@formatjs/intl-pluralrules/locale-data/zh';
import '@formatjs/intl-pluralrules/locale-data/ko';
import '@formatjs/intl-pluralrules/locale-data/fil';
import '@formatjs/intl-pluralrules/locale-data/hi';
import '@formatjs/intl-pluralrules/locale-data/th';
import '@formatjs/intl-pluralrules/locale-data/vi';
import '@formatjs/intl-pluralrules/locale-data/id';

import '@formatjs/intl-numberformat/polyfill';
import '@formatjs/intl-numberformat/locale-data/en';
import '@formatjs/intl-numberformat/locale-data/en-GB';
import '@formatjs/intl-numberformat/locale-data/nl';
import '@formatjs/intl-numberformat/locale-data/fr';
import '@formatjs/intl-numberformat/locale-data/fr-CA';
import '@formatjs/intl-numberformat/locale-data/de';
import '@formatjs/intl-numberformat/locale-data/it';
import '@formatjs/intl-numberformat/locale-data/ja';
import '@formatjs/intl-numberformat/locale-data/pt';
import '@formatjs/intl-numberformat/locale-data/ro';
import '@formatjs/intl-numberformat/locale-data/es';
import '@formatjs/intl-numberformat/locale-data/et';
import '@formatjs/intl-numberformat/locale-data/pl';
import '@formatjs/intl-numberformat/locale-data/bg';
import '@formatjs/intl-numberformat/locale-data/sv';
import '@formatjs/intl-numberformat/locale-data/da';
import '@formatjs/intl-numberformat/locale-data/fi';
import '@formatjs/intl-numberformat/locale-data/lv';
import '@formatjs/intl-numberformat/locale-data/hu';
import '@formatjs/intl-numberformat/locale-data/cs';
import '@formatjs/intl-numberformat/locale-data/zh';
import '@formatjs/intl-numberformat/locale-data/ko';
import '@formatjs/intl-numberformat/locale-data/fil';
import '@formatjs/intl-numberformat/locale-data/hi';
import '@formatjs/intl-numberformat/locale-data/th';
import '@formatjs/intl-numberformat/locale-data/vi';
import '@formatjs/intl-numberformat/locale-data/id';
