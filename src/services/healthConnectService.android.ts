import {
  getGrantedPermissions,
  getSdkStatus,
  readRecords,
  aggregateRecord,
  initialize,
  Permission,
  SdkAvailabilityStatus,
  requestPermission,
} from 'react-native-health-connect';
import {Platform} from 'react-native';
import {storage} from '@/src/utils/localStorage';
import {api} from '@/src/services/api';
import {DateTime} from 'luxon';
import logger from '@/src/utils/logger';

export type HealthConnectStatus = 'connected' | 'partial' | 'disconnected';

export const permissionsToRequest = [
  {accessType: 'read', recordType: 'Steps'},
  {accessType: 'read', recordType: 'HeartRate'},
  {accessType: 'read', recordType: 'ActiveCaloriesBurned'},
  {accessType: 'read', recordType: 'TotalCaloriesBurned'},
  {accessType: 'read', recordType: 'Distance'},
  {accessType: 'read', recordType: 'Speed'},
  {accessType: 'read', recordType: 'ExerciseSession'},
  {accessType: 'read', recordType: 'SleepSession'},
  {accessType: 'read', recordType: 'BackgroundAccessPermission'},
] as const;

class HealthConnectService {
  private static instance: HealthConnectService;
  private initialized = false;
 
  private constructor() {}

  public static getInstance(): HealthConnectService {
    if (!HealthConnectService.instance) {
      HealthConnectService.instance = new HealthConnectService();
    }
    return HealthConnectService.instance;
  }

  public async init(initialConnected?: boolean): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      logger.info('🔧 Initializing Health Connect client...');
      this.initialized = await initialize();
      if (!this.initialized) {
        logger.warn('❌ Failed to initialize Health Connect client');
        return false;
      }
      logger.info('✅ Health Connect initialized');
      if (initialConnected) {
        storage.set('healthConnect.status', 'connected');
      }
      if (!api.isLoggedIn()) {
        logger.info('⚠️ Not logged in, skipping checkWorkouts');
        return true;
      }
      // Get user status and update connection status and last sync date
      await api
        .getApiClient()
        .healthConnectControllerGetUserStatus()
        .then((r) => {
          logger.info('User status data: ', r.data);

          // Save connection status based on healthConnectConnected
          const healthConnectConnected = r.data?.data?.healthConnectConnected;
          const lastSyncDate = r.data?.data?.lastSyncDate;

          if (healthConnectConnected === true) {
            storage.set('healthConnect.status', 'connected');
            logger.debug('✅ Health Connect status saved as connected');
          } else if (healthConnectConnected === false) {
            storage.set('healthConnect.status', 'disconnected');
            logger.debug('⚠️ Health Connect status saved as disconnected');
          }

          // Save last sync date if available
          if (lastSyncDate) {
            this.setLastSyncDate(lastSyncDate);
            logger.info(
              `✅ Last sync date saved: ${new Date(lastSyncDate).toISOString()}`,
            );
          }
        })
        .catch((error) => logger.error('Error getting user status:', error));
      return true;
    } catch (error) {
      logger.error('❌ Health Connect initialization failed:', error);
      return false;
    }
  }

  public async checkPermissions(): Promise<Permission[]> {
    if (!this.initialized) {
      await this.init();
    }

    try {
      const sdkStatus = await getSdkStatus();
      if (sdkStatus !== SdkAvailabilityStatus.SDK_AVAILABLE) {
        logger.warn('⚠️ Health Connect SDK not available:', sdkStatus);
        return [];
      }

      const granted = await getGrantedPermissions();
      if (granted.some((p) => p.recordType === 'StepsCadence')) {
        granted.splice(
          granted.findIndex((p) => p.recordType === 'StepsCadence'),
          1,
        );
      }
      if (granted.some((p) => p.recordType === 'CyclingPedalingCadence')) {
        granted.splice(
          granted.findIndex((p) => p.recordType === 'CyclingPedalingCadence'),
          1,
        );
      }
      logger.info('✅ Granted permissions:', granted);
      return granted;
    } catch (error) {
      logger.error('❌ Check permissions failed:', error);
      return [];
    }
  }

  public async grantPermissions(): Promise<Permission[]> {
    if (!this.initialized) {
      await this.init();
    }

    try {
      // Convert the readonly array to a mutable array and pass it to requestPermission
      await requestPermission([...permissionsToRequest]);

      const granted = await this.checkPermissions();
      return granted;
    } catch (error) {
      logger.error('❌ Grant permissions failed:', error);
      return [];
    }
  }

  public async connect(): Promise<boolean> {
    try {
      const granted = await this.grantPermissions();
      logger.debug('Granted permissions:', granted);
      if (granted.length > 0) {
        const status = await this.deriveStatus(granted);
        storage.set('healthConnect.status', status);
        logger.info('Derived status:', status);
        await api
          .getApiClient()
          .healthConnectControllerConnectWithHealthConnect();
        logger.debug('✅ Connected to Health Connect backend');
        return true;
      }
      return false;
    } catch (error) {
      logger.error('❌ Connect failed:', error);
      return false;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      storage.set('healthConnect.status', 'disconnected');
      await api.getApiClient().healthConnectControllerDisconnectHealthConnect();
      logger.debug('✅ Disconnected from Health Connect backend');
    } catch (error) {
      logger.error('❌ Disconnect failed:', error);
    }
  }
  public async setStatus(status: HealthConnectStatus): Promise<void> {
    storage.set('healthConnect.status', status);
  }

  public async getStatus(): Promise<HealthConnectStatus> {
    const storageStatus = storage.getString('healthConnect.status');
    if (storageStatus) {
      return storageStatus as HealthConnectStatus;
    }
    return 'disconnected';
  }

  public async deriveStatus(
    permissions?: Permission[],
  ): Promise<HealthConnectStatus> {
    if (!permissions) {
      permissions = await this.checkPermissions();
    }
    if (permissions.length === 0) {
      return 'disconnected';
    }
    return permissions.length >= permissionsToRequest.length
      ? 'connected'
      : 'partial';
  }

  public getLastSyncDate(): Date | undefined {
    const date = storage.getNumber('healthConnect.lastSyncDate');
    return date ? new Date(date) : undefined;
  }

  private setLastSyncDate(lastSyncDate?: number) {
    if (lastSyncDate) {
      storage.set('healthConnect.lastSyncDate', lastSyncDate);
    }
  }

  public async checkWorkouts() {
    logger.debug('📍 HEALTH CONNECT: Starting checkWorkouts function');
    if (Platform.OS !== 'android') {
      logger.debug('⚠️ Not Android platform, skipping checkWorkouts');
      return;
    }
    logger.debug('📍 HEALTH CONNECT: Platform check passed');

    if (!api.isLoggedIn()) {
      logger.warn('⚠️ Not logged in, skipping checkWorkouts');
      return;
    }
    logger.debug('📍 HEALTH CONNECT: Login check passed');

    const status = await this.getStatus();
    logger.info('📍 HEALTH CONNECT: Current status:', status);

    if (status !== 'connected') {
      logger.info('⚠️ Health Connect is not connected, status:', status);
      return;
    }
    logger.debug(
      '📍 HEALTH CONNECT: Status check passed, proceeding with workout check...',
    );
    try {
      await initialize();

      let startDate = this.getLastSyncDate();
      logger.debug(
        '✅ Health Connect: HealthConnectService.getLastSyncDate():',
        startDate,
      );
      if (!startDate) {
        startDate = DateTime.now().minus({hours: 1}).toJSDate();
      }
      const endDate = DateTime.now().toJSDate();
      logger.debug('✅ Health Connect: startDate:', startDate);
      logger.debug('✅ Health Connect: endDate:', endDate);

      // Check if we have all the necessary permissions
      const granted = await getGrantedPermissions();
      logger.debug('✅ Health Connect: Granted permissions:', granted);

      // Try to read exercise records
      let result;
      try {
        logger.info(
          '✅ Health Connect: Attempting to read exercise records...',
        );
        // First attempt: Try to read only user-input data to avoid permission issues
        result = await readRecords('ExerciseSession', {
          timeRangeFilter: {
            operator: 'between',
            startTime: startDate.toISOString(),
            endTime: endDate.toISOString(),
          },
        });
        logger.info(
          '✅ Health Connect: Successfully read exercise records with dataOriginFilter',
        );
      } catch (readError: any) {
        logger.error(
          '❌ Health Connect: Error reading exercise records with data origin filter:',
          readError,
        );
        try {
          // Second attempt: Try without the filter, but this might cause the SecurityException
          logger.info(
            '✅ Health Connect: Attempting to read exercise records without data origin filter...',
          );
          result = await readRecords('ExerciseSession', {
            timeRangeFilter: {
              operator: 'between',
              startTime: startDate.toISOString(),
              endTime: endDate.toISOString(),
            },
          });
          logger.info(
            '✅ Health Connect: Successfully read exercise records without dataOriginFilter',
          );
        } catch (fallbackError: any) {
          logger.error(
            '❌ Health Connect: Error reading exercise records without filter:',
            fallbackError,
          );
          // If both attempts fail, create an empty result to avoid further errors
          result = {records: []};
        }
      }

      // The result object has a 'records' property that contains the array of exercise records
      const exerciseRecords = result.records || [];
      logger.info(
        '✅ Health Connect: Found exercise records:',
        exerciseRecords.length,
      );

      const sendData = [];
      for (let i = 0; i < exerciseRecords.length; i++) {
        const rec: any = {...exerciseRecords[i]};
        const totalCaloriesBurned = await aggregateRecord({
          recordType: 'TotalCaloriesBurned',
          timeRangeFilter: {
            operator: 'between',
            startTime: exerciseRecords[i].startTime,
            endTime: exerciseRecords[i].endTime,
          },
        });
        const heartRate = await aggregateRecord({
          recordType: 'HeartRate',
          timeRangeFilter: {
            operator: 'between',
            startTime: exerciseRecords[i].startTime,
            endTime: exerciseRecords[i].endTime,
          },
        });
        const distance = await aggregateRecord({
          recordType: 'Distance',
          timeRangeFilter: {
            operator: 'between',
            startTime: exerciseRecords[i].startTime,
            endTime: exerciseRecords[i].endTime,
          },
        });
        rec.type = 'workout';
        rec.details = {
          totalCalories: Math.round(
            totalCaloriesBurned?.ENERGY_TOTAL?.inKilocalories,
          ),
          totalDistance: Math.round(distance?.DISTANCE?.inMeters),
          uid: exerciseRecords[i].metadata?.id,
          totalActiveTime: Math.round(
            Math.abs(
              new Date(exerciseRecords[i].endTime).getTime() -
                new Date(exerciseRecords[i].startTime).getTime(),
            ) / 1000,
          ),
          avgHeartRate: Math.round(heartRate?.BPM_AVG),
        };
        sendData.push(rec);
      }
      if (sendData.length === 0) {
        return;
      }
      logger.info(
        '✅ Health Connect: Sending data to backend:',
        JSON.stringify(sendData),
      );
      try {
        const response = await api
          .getApiClient()
          .healthConnectControllerPushActivities({
            pushActivitiesHCDto: {data: sendData, manualSync: false},
          });

        logger.info('✅ Health Connect: Response from backend:', response.data);

        // Safely access nested properties
        const responseData = response?.data?.data;
        logger.debug(
          '📍 HEALTH CONNECT: Processed response data:',
          responseData,
        );

        if (
          responseData &&
          responseData.success &&
          responseData.data &&
          responseData.data.lastSyncDate
        ) {
          logger.info(
            '📍 HEALTH CONNECT: Setting last sync date:',
            responseData.data.lastSyncDate,
          );
          this.setLastSyncDate(responseData.data.lastSyncDate);
        } else {
          logger.warn(
            '⚠️ HEALTH CONNECT: Could not update last sync date - missing or invalid data in response',
          );
        }

        logger.info('✅✅✅ HEALTH CONNECT: Data processing completed');
      } catch (apiError) {
        logger.error(
          '❌ HEALTH CONNECT: API error when pushing activities:',
          apiError,
        );
        throw apiError; // Re-throw to be caught by the outer catch block
      }
    } catch (error: any) {
      logger.error('❌ Check workouts failed:', error);

      // Check if this is a security exception related to reading from other applications
      if (
        error.message &&
        error.message.includes('SecurityException') &&
        error.message.includes('ExerciseSessionRecord') &&
        error.message.includes('from other applications')
      ) {
        logger.warn(
          '⚠️ Security exception detected: Cannot read workout data from other applications',
        );
        logger.warn(
          'This is a known limitation with Health Connect. The app can only read workout data created by this app.',
        );
        logger.warn('To fix this issue, try one of the following:');
        logger.warn(
          '1. Use dataOriginFilter to only read data created by your app',
        );
        logger.warn(
          '2. Request the user to grant additional permissions through the Health Connect app settings',
        );

        // We could show a message to the user suggesting they open Health Connect settings
        // For now, we'll just log the error and continue
      }
    }
  }
}

export default HealthConnectService.getInstance();
