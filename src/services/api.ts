import axios, {
  AxiosInstance,
  AxiosRequestHeaders,
  AxiosRequestConfig,
  AxiosHeaders,
  AxiosError,
} from 'axios';
import Constants from 'expo-constants';
import {
  Configuration,
  DefaultApi,
  DefaultApiAxiosParamCreator,
} from '@gojoe/typescript-sdk';
import {
  getUserAccessToken,
  getUserRefreshAccessToken,
  logLogout,
  setUserAccessToken,
  setUserRefreshAccessToken,
} from '@/src/utils/auth';
import logger from '@/src/utils/logger';
import appleHealthService from './appleHealthService';
import {networkStatus, retryWithBackoff} from '@/src/utils/network';

// Extend AxiosRequestConfig to include _retry
interface AxiosRequestConfigWithRetry extends AxiosRequestConfig {
  _retry?: boolean;
}

interface CustomErrorResponse {
  errorType?: string;
  // Add other properties that might exist in the response if needed
}

class API {
  private static instance: API;
  private userAccessToken?: string;
  private readonly axiosInstance: AxiosInstance;
  private readonly axiosInstanceWithoutInterceptor: AxiosInstance;
  public defaultHeaders: Record<string, string | null> = {};
  private apiClient?: DefaultApi;

  private refreshTokenPromise: Promise<string> | null = null;

  private isRefreshingToken = false; // Flag to prevent multiple refresh requests
  private requestQueue: ((token: string) => void)[] = []; // Queue for pending requests

  private constructor() {
    this.axiosInstance = axios.create();
    this.axiosInstanceWithoutInterceptor = axios.create();
    this.setAxiosInterceptors();
  }

  setDefaultHeaders(defaultHeaders: Record<string, string | null>) {
    logger.debug('setting default headers', defaultHeaders);
    this.defaultHeaders = defaultHeaders;
  }

  private getDefaultHeaders(): AxiosRequestHeaders {
    return AxiosHeaders.from(this.defaultHeaders);
  }
  private getBaseUrl() {
    return Constants.expoConfig?.extra?.api.url;
  }

  public static getInstance(): API {
    if (!API.instance) {
      API.instance = new API();
    }
    return API.instance;
  }

  public set accessToken(token: string | undefined) {
    this.userAccessToken = token;
    this.apiClient = new DefaultApi(
      this.apiConfiguration(),
      this.getBaseUrl(),
      this.axiosInstance,
    );
  }

  public getApiClient(): DefaultApi {
    if (!this.apiClient) {
      this.apiClient = new DefaultApi(
        this.apiConfiguration(),
        this.getBaseUrl(),
        this.axiosInstance,
      );
    }
    return this.apiClient;
  }
  public isLoggedIn() {
    return !!this.userAccessToken;
  }

  public async getConnectUrl(
    scheme: string,
    methodName: string,
  ): Promise<string> {
    // Ensure that apiClient is defined before calling the method
    if (!this.apiClient) {
      throw new Error('API client is not initialized');
    }

    // Get the configuration from DefaultApi
    // const configuration = this.apiConfiguration();

    // Create the paramCreator with the configuration
    const paramCreator: any = DefaultApiAxiosParamCreator();

    // Dynamically check if the method exists in DefaultApiAxiosParamCreator
    if (typeof paramCreator[methodName] !== 'function') {
      throw new Error(
        `Method ${methodName} does not exist in DefaultApiAxiosParamCreator`,
      );
    }

    try {
      // Call the method from DefaultApiAxiosParamCreator to generate URL and options
      const {url} = await paramCreator[methodName](scheme);
      return `${this.getBaseUrl()}${url}`;
    } catch (error: any) {
      throw new Error(`Error calling ${methodName}: ${error.message}`);
    }
  }

  private async refreshAccessToken(): Promise<string> {
    logger.debug('refreshing access token');
    if (this.isRefreshingToken) {
      // If a refresh is already in progress, return a promise that resolves when it's done
      return new Promise((resolve, reject) => {
        this.requestQueue.push((token) => {
          if (token) {
            resolve(token);
          } else {
            reject(new Error('Refresh failed'));
          }
        });
      });
    }

    this.isRefreshingToken = true;

    try {
      const refreshToken = await getUserRefreshAccessToken();
      const accessToken = await getUserAccessToken();

      if (!refreshToken || !accessToken) {
        throw new Error('No refresh token available');
      }

      const apiClient = new DefaultApi(
        new Configuration(),
        this.getBaseUrl(),
        this.axiosInstanceWithoutInterceptor,
      );

      // Perform the token refresh
      const newTokens = await apiClient
        .userAuthControllerRefreshToken({
          refreshTokenRequestDto: {
            refreshToken,
            accessToken,
          },
        })
        .then((res) => res.data.data);
      logger.debug('new tokens', newTokens);
      // Save the new tokens
      await setUserAccessToken(newTokens.accessToken);
      await setUserRefreshAccessToken(newTokens.refreshToken);
      this.accessToken = newTokens.accessToken;
      // Update Access token and Refresh token
      await appleHealthService.setUserTokens();
      // Notify all queued requests with the new token
      this.requestQueue.forEach((callback) => callback(newTokens.accessToken));
      this.requestQueue = []; // Clear the queue

      return newTokens.accessToken;
    } catch (error) {
      // If refresh fails, notify all queued requests with an error
      logger.error('Failed to refresh token:', error);
      this.requestQueue.forEach((callback) => callback(''));
      this.requestQueue = []; // Clear the queue
      await logLogout({
        reason: {
          error,
          message: 'Failed to refresh token',
        },
        filePath: 'src/services/api.ts',
      });
      // TODO: Handle logout logic here
      // await logout();
      throw error;
    } finally {
      this.isRefreshingToken = false;
    }
  }

  private setAxiosInterceptors() {
    // Request Interceptor
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        const commonHeaders = this.getDefaultHeaders();
        config.headers = AxiosHeaders.from({
          ...commonHeaders,
          ...(config.headers || ({} as AxiosRequestHeaders)),
        });
        // Wait for token refresh if it's in progress
        if (this.refreshTokenPromise) {
          try {
            await this.refreshTokenPromise;
          } catch (error) {
            return Promise.reject(error);
          }
        }

        // Add Authorization header
        if (this.userAccessToken) {
          config.headers.Authorization = `Bearer ${this.userAccessToken}`;
        }

        return config;
      },
      (error) => Promise.reject(error),
    );

    // Response Interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error: AxiosError<CustomErrorResponse>) => {
        const originalRequest = error.config as
          | AxiosRequestConfigWithRetry
          | undefined;

        // Check if this is a network error (no response from server)
        const isNetworkError =
          !error.response && error.message === 'Network Error';

        // Handle network errors with retry logic
        if (isNetworkError && originalRequest && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // Log the network error
            logger.warn(
              'Network error detected, will attempt to retry request',
            );

            // Check if we're online before retrying
            if (!networkStatus.getIsConnected()) {
              logger.warn(
                'Device is offline, waiting for network connection...',
              );

              // Wait for network to be restored before retrying
              await new Promise<void>((resolve) => {
                const removeListener = networkStatus.addListener(
                  (isConnected) => {
                    if (isConnected) {
                      removeListener();
                      resolve();
                    }
                  },
                );

                // Set a timeout to prevent waiting indefinitely
                setTimeout(() => {
                  removeListener();
                  resolve();
                }, 30000); // 30 seconds timeout
              });
            }

            // If we're back online, retry the request with exponential backoff
            return await retryWithBackoff(
              () => this.axiosInstance(originalRequest),
              3, // Max 3 retries
              1000, // Start with 1 second delay
            );
          } catch (retryError) {
            logger.error('All retry attempts failed:', retryError);
            return Promise.reject(retryError);
          }
        }

        // Check for expired token
        const isTokenExpired =
          error.response?.status === 401 &&
          error.response?.data?.errorType === 'EXPIRED_ACCESS_TOKEN';

        if (isTokenExpired && originalRequest && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const newAccessToken = await this.refreshAccessToken();

            // Update the token in the original request's headers
            if (originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
            } else {
              originalRequest.headers = {
                Authorization: `Bearer ${newAccessToken}`,
              };
            }

            // Retry the original request
            return this.axiosInstance(originalRequest);
          } catch (refreshError) {
            return Promise.reject(refreshError);
          }
        }

        // Log all other errors
        if (error.response) {
          logger.error(
            `API Error: ${error.response.status} - ${JSON.stringify(error.response.data)}`,
            error.config?.url,
          );
        } else {
          logger.error(`API Error: ${error.message}`, error.config?.url);
        }

        return Promise.reject(error);
      },
    );
  }

  private apiConfiguration() {
    return new Configuration({
      accessToken: this.userAccessToken,
    });
  }
}

export const api = API.getInstance();
