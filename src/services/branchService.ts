import branch from 'react-native-branch';
import logger from '@/src/utils/logger';
import {router} from 'expo-router';
import {waitForAppToBeActive} from '../utils/waitForAppToBeActive';

export interface BranchLinkData {
  [key: string]: any;
  $canonical_url?: string;
  $desktop_url?: string;
  $ios_url?: string;
  $android_url?: string;
}

export interface BranchLinkProperties {
  feature?: string;
  channel?: string;
  campaign?: string;
  stage?: string;
  tags?: string[];
  alias?: string;
}

/**
 * Generates a Branch.io deep link
 */
export const generateBranchLink = async (
  linkData: BranchLinkData,
  linkProps: BranchLinkProperties = {},
): Promise<string> => {
  try {
    logger.info('Generating Branch link with data:', linkData);
    logger.info('Link properties:', linkProps);

    // Create Branch Universal Object with canonical URL to show path in link
    const branchUniversalObject = await branch.createBranchUniversalObject(
      `challenge_${linkData.challengeId || Date.now()}`,
      {
        locallyIndex: true,
        title: linkData.title || 'GoJoe Challenge Invitation',
        contentDescription:
          linkData.description || 'Join this challenge on GoJoe!',
        contentImageUrl: linkData.imageUrl,
        canonicalUrl: linkData.$canonical_url, // This makes Branch use the full path in URL
        contentMetadata: {
          customMetadata: linkData,
        },
      },
    );

    // Separate control parameters (for routing) and link properties (for analytics)
    const controlParams: any = {
      $fallback_url: 'https://gojoe.com',
      $desktop_url: linkData.$desktop_url || 'https://gojoe.com',
      // Add custom data that will be passed to the app when link is opened
      challengeId: linkData.challengeId,
      type: linkData.type || 'challenge_invite',
      $deeplink_path: linkData.$canonical_url,
      route: linkData.$canonical_url,
    };

    // Only add passcode if it's defined and valid to avoid "undefined" strings
    if (
      linkData.passcode &&
      linkData.passcode !== 'undefined' &&
      typeof linkData.passcode === 'string' &&
      linkData.passcode.trim()
    ) {
      controlParams.passcode = linkData.passcode;
    }

    // Add branch identifier if present
    if (linkData.branch && typeof linkData.branch === 'string') {
      controlParams.branch = linkData.branch;
    }

    // Link properties are for Branch analytics and campaign tracking
    const linkProperties = {
      feature: linkProps.feature || 'sharing',
      channel: linkProps.channel || 'app',
      campaign: linkProps.campaign || 'challenge_invite',
      stage: linkProps.stage,
      tags: linkProps.tags || ['challenge'],
      // Set alias to the full path structure to show in URL
      alias: linkData.$canonical_url,
    };

    const {url} = await branchUniversalObject.generateShortUrl(
      linkProperties,
      controlParams,
    );

    logger.info('Generated Branch URL:', url);
    return url;
  } catch (error) {
    logger.error('Failed to generate Branch link:', error);
    throw error;
  }
};

/**
 * Initializes Branch SDK
 */
export const initializeBranch = async (): Promise<void> => {
  try {
    logger.info('🔗 Subscribing to Branch links...');

    branch.subscribe({
      onOpenStart: ({uri, cachedInitialEvent}) => {
        logger.info('Branch link opening:', {uri, cachedInitialEvent});
      },
      onOpenComplete: async ({error, params, uri}) => {
        if (error) {
          logger.error('Branch link open error:', error);
          return;
        }

        if (params && params['+clicked_branch_link']) {
          await waitForAppToBeActive();
          handleBranchLink(params);
        }
      },
    });

    logger.info('Branch SDK initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize Branch SDK:', error);
  }
};

/**
 * Handles incoming Branch deep links
 */
export const handleBranchLink = (params: Record<string, any>) => {
  logger.debug('🔗 Handling Branch link with params:', params);

  const encodedPath = params?.$deeplink_path;
  if (typeof encodedPath !== 'string') {
    logger.warn('⚠️ Invalid or missing $deeplink_path:', params);
    return;
  }

  try {
    // 1. Decode full path (e.g. "challenge/abc123?passcode=xyz&branch=abc")
    const decoded = decodeURIComponent(encodedPath);

    // 2. Split into path and query string
    const [pathWithId, queryString] = decoded.split('?');

    // 3. Extract path parts
    const pathParts = pathWithId.split('/').filter(Boolean); // removes empty strings
    logger.debug('🔗 Path parts:', pathParts);
    // ❌ Skip navigation if no valid route
    if (pathParts.length < 2) {
      logger.info(
        '⚠️ No valid path parts in Branch link — ignoring navigation',
      );
      return;
    }

    const baseRoute = `/${pathParts[0]}`;
    const dynamicId = pathParts[1];
    const dynamicParamName = `${pathParts[0]}Id`;
    const pathname = `${baseRoute}/[${dynamicParamName}]`;

    const searchParams = new URLSearchParams(queryString || '');
    const query: Record<string, string> = {
      [dynamicParamName]: dynamicId,
    };
    searchParams.forEach((value, key) => {
      query[key] = value;
    });

    logger.info('🚀 Navigating via router.push:', {pathname, query});
    router.push({pathname, params: query});
  } catch (err) {
    logger.error('❌ Failed to parse and navigate Branch link:', {
      encodedPath,
      err,
    });
  }
};

/**
 * Gets the latest Branch parameters
 */
export const getLatestBranchParams = async (): Promise<any> => {
  try {
    const latestParams = await branch.getLatestReferringParams();
    logger.info('Latest Branch params:', latestParams);
    return latestParams;
  } catch (error) {
    logger.error('Failed to get latest Branch params:', error);
    return null;
  }
};

export default {
  generateBranchLink,
  initializeBranch,
  getLatestBranchParams,
};
