import Intercom, {Space} from '@intercom/intercom-react-native';
import {UserProfileDto} from '@gojoe/typescript-sdk';
import {storage} from '../utils/localStorage';
import logger from '@/src/utils/logger';

type STATUS = 'disconnected' | 'guest' | 'connected';

class GoJoeIntercom {
  status: STATUS = 'disconnected';
  constructor() {
    const intercomUser = storage.getString('intercom.user');
    if (intercomUser) {
      this.status = intercomUser === 'guest' ? 'guest' : 'connected';
    }
  }

  async login(user: UserProfileDto) {
    try {
      const isLoggedIn = await Intercom.isUserLoggedIn();
      if (isLoggedIn) {
        this.logout();
      }
      await Intercom.loginUserWithUserAttributes({
        userId: user.id,
      });
      storage.set('intercom.user', user.id);
      this.status = 'connected';

      await Intercom.updateUser({
        userId: user.id,
        email: user.email,
        name: user.name ?? `${user.firstName} ${user.lastName}`,
        companies:
          user && user.businesses
            ? user.businesses.map((business) => {
                return {
                  id: business.id,
                  name: business.name,
                };
              })
            : [],
      });
    } catch (e) {
      await logger.error(e);
    }
  }

  async logout() {
    try {
      await Intercom.logout();
      this.status = 'disconnected';
      storage.remove('intercom.user');
    } catch (e) {
      await logger.error(e);
    }
  }

  async openChat(user: UserProfileDto, retry = true) {
    try {
      if (this.status === 'disconnected') {
        await Intercom.loginUnidentifiedUser();
        this.status = 'guest';
      }
      await Intercom.presentSpace(Space.messages);
    } catch (e) {
      if (retry) {
        await this.logout();
        await this.login(user);
        await this.openChat(user, false);
        return;
      }
      await logger.error(e);
    }
  }
}
const intercom = new GoJoeIntercom();
export default intercom;
