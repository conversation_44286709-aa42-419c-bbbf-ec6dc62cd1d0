import logger from '@/src/utils/logger';
import {api} from '@/src/services/api';
import {Platform, NativeModules} from 'react-native';
import {getDeviceUUID} from '@/src/utils/device';
import {UserAppleHealthStatusDto} from '@gojoe/typescript-sdk';
import {
  getUserAccessToken,
  getUserRefreshAccessToken,
  setUserAccessToken,
  setUserRefreshAccessToken,
} from '../utils/auth';
import {storage} from '../utils/localStorage';
export const appleHealthStatusStorageKey = 'appleHealth.status';
const {GJRNWorkouts, GJRNUserDefaults, GJRNHealthKit} = NativeModules;
export type AppleHealthStatus = 'connected' | 'disconnected';

class AppleHealthService {
  private static instance: AppleHealthService;
  private initialized = false;

  private constructor() {
    const apiBaseUrl = process.env.EXPO_PUBLIC_API_URL;
    logger.debug('🍎 AppleHealth: API Base URL:', apiBaseUrl);
    if (apiBaseUrl) {
      logger.debug('🍎 AppleHealth: Setting apiBaseUrl:', apiBaseUrl);
      GJRNUserDefaults.set(apiBaseUrl, 'apiBaseUrl');
    }
  }

  public static getInstance(): AppleHealthService {
    if (!AppleHealthService.instance) {
      AppleHealthService.instance = new AppleHealthService();
    }
    return AppleHealthService.instance;
  }

  private async handleConnected(result: UserAppleHealthStatusDto) {
    logger.info('🍏 AppleHealth: Handling connection status...');
    await GJRNUserDefaults.set(
      result.healthKitConnected ? 'true' : 'false',
      'appleHealthConnected',
    );
    this.setStatus(result.healthKitConnected ? 'connected' : 'disconnected');
    await GJRNUserDefaults.set(result?.userId, 'userId');
    await GJRNUserDefaults.set(await getDeviceUUID(), 'deviceId');
    await this.setUserTokens();

    if (result.healthKitConnected) {
      logger.info(
        '✅ AppleHealth: HealthKit already connected in backend, authorizing...',
      );
      await this.authorize();
      logger.info('🏃 AppleHealth: Starting workout tracking...');
      GJRNWorkouts.start();
    } else {
      logger.warn('⚠️ AppleHealth: HealthKit is not connected');
    }
  }

  // Save new tokens from native code if refresh happens there
  public async saveNewUserTokens(accessToken: string, refreshToken: string) {
    logger.info('🔁 AppleHealth: Saving new user tokens');
    await setUserAccessToken(accessToken);
    await setUserRefreshAccessToken(refreshToken);
  }

  public async setUserTokens() {
    logger.debug('🔐 AppleHealth: Setting current access and refresh tokens');
    const accessToken = await getUserAccessToken();
    const refreshToken = await getUserRefreshAccessToken();
    await GJRNUserDefaults.set(accessToken, 'userAccessToken');
    await GJRNUserDefaults.set(refreshToken, 'userRefreshToken');
  }

  private async getLastSyncDate() {
    const date = await GJRNUserDefaults.get('healthKitLastSyncDate');
    logger.debug('📅 AppleHealth: Retrieved last sync date:', date);
    return isNaN(date) ? null : date;
  }

  private async setLastSyncDate(result: UserAppleHealthStatusDto) {
    if (result.lastSyncDate) {
      logger.debug(
        '📅 AppleHealth: Storing last sync date:',
        result.lastSyncDate,
      );
      await GJRNUserDefaults.set(
        `${result.lastSyncDate}`,
        'healthKitLastSyncDate',
      );
      storage.set('appleHealth.lastSyncDate', `${result.lastSyncDate}`);
    }
  }

  private async authorize() {
    return GJRNHealthKit.authorize((err: Error, result: any) => {
      if (err) {
        logger.error('❌ AppleHealth: Authorization failed:', err);
      } else {
        logger.info('✅ AppleHealth: Authorized successfully:', result);
      }
    });
  }

  public async checkStatus() {
    if (Platform.OS !== 'ios') {
      logger.warn('🚫 AppleHealth: Not on iOS, skipping status check');
      return;
    }

    logger.info('🔍 AppleHealth: Checking HealthKit status...');
    try {
      const result = (await api
        .getApiClient()
        .appleHealthControllerStatus()
        .then((res) => res.data.data)) as UserAppleHealthStatusDto;

      if (result) {
        logger.debug('📦 AppleHealth: Status received from backend:', result);
        const healthKitConnected = result.healthKitConnected;

        const lastSyncDate = await this.getLastSyncDate();
        logger.info('📅 AppleHealth: Last sync date:', lastSyncDate);
        if (!lastSyncDate) {
          logger.info('🕐 AppleHealth: No sync date found, setting new one');
          await this.setLastSyncDate(result);
        }
        logger.info(
          '✅ AppleHealth: Status check completed',
          healthKitConnected,
        );
        if (healthKitConnected === true) {
          this.setStatus('connected');
          logger.debug('✅ Apple Health status saved as connected');
        } else if (healthKitConnected === false) {
          this.setStatus('disconnected');
          logger.debug('⚠️ Apple Health status saved as disconnected');
        }
        await this.handleConnected(result);
      } else {
        logger.warn('⚠️ AppleHealth: Received empty status data');
      }
    } catch (e: any) {
      logger.error(
        '❌ AppleHealth: Error fetching HealthKit status:',
        e,
        e.stack,
      );
    }
    return this.getStatus();
  }
  public setStatus(status: AppleHealthStatus) {
    storage.set(appleHealthStatusStorageKey, status);
  }

  public getStatus(): AppleHealthStatus | undefined {
    const storageStatus = storage.getString(appleHealthStatusStorageKey);
    logger.debug('🍎 AppleHealth: Status from storage:', storageStatus);
    if (storageStatus) {
      return storageStatus as AppleHealthStatus;
    }
    return 'disconnected';
  }
}

export default AppleHealthService.getInstance();
