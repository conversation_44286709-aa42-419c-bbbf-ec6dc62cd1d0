import * as BackgroundFetch from 'expo-background-fetch';
import * as TaskManager from 'expo-task-manager';
import healthConnectService from './healthConnectService';
import logger from '@/src/utils/logger';

const HEALTH_CONNECT_SYNC_TASK = 'HEALTH_CONNECT_SYNC_TASK';

// Define the task
TaskManager.defineTask(HEALTH_CONNECT_SYNC_TASK, async () => {
  try {
    logger.info('🔴🔴🔴 BACKGROUND SYNC TASK TRIGGERED 🔴🔴🔴');
    logger.debug('🔄 Starting Health Connect checkWorkouts...');

    // Wrap in another try/catch to ensure we always log completion
    try {
      await healthConnectService.checkWorkouts();
      logger.debug('✅✅✅ BACKGROUND SYNC TASK COMPLETED SUCCESSFULLY ✅✅✅');
    } catch (innerError) {
      logger.error('❌❌❌ BACKGROUND SYNC TASK INNER ERROR ❌❌❌');
      logger.error('Health Connect checkWorkouts failed:', innerError);
      // Don't rethrow, so we can still log completion
    }
    return BackgroundFetch.BackgroundFetchResult.NewData;
  } catch (error) {
    logger.error('❌❌❌ BACKGROUND SYNC TASK FAILED ❌❌❌');
    logger.error('Background sync failed:', error);
    return BackgroundFetch.BackgroundFetchResult.Failed;
  }
});

export const registerBackgroundTasks = async () => {
  try {
    // Initialize Health Connect service
    const initialized = await healthConnectService.init();
    logger.info('Health Connect initialized in background task:', initialized);

    // Check if task is already registered
    const isRegistered = await TaskManager.isTaskRegisteredAsync(
      HEALTH_CONNECT_SYNC_TASK,
    );
    if (isRegistered) {
      logger.info('Task already registered, skipping registration');
      // Even if the task is already registered, we can still trigger a manual sync
      // to ensure data is up to date
      logger.debug('Triggering initial sync...');
      await healthConnectService.checkWorkouts();
      return;
    }

    // Register the task
    await BackgroundFetch.registerTaskAsync(HEALTH_CONNECT_SYNC_TASK, {
      minimumInterval: 60, // 1 minute
      stopOnTerminate: false,
      startOnBoot: true,
    });

    logger.info('Task registered successfully');
    // Trigger initial sync after registration
    logger.debug('Triggering initial sync...');
    await healthConnectService.checkWorkouts();
  } catch (err) {
    logger.error('Task registration failed:', err);
  }
};

export const unregisterBackgroundTasks = async () => {
  try {
    await BackgroundFetch.unregisterTaskAsync(HEALTH_CONNECT_SYNC_TASK);
  } catch (err) {
    logger.error('Task unregistration failed:', err);
  }
};

export const triggerManualSync = async () => {
  logger.info('🔄 Manually triggering background sync...');
  try {
    // Check Health Connect status before triggering sync
    const status = await healthConnectService.getStatus();
    logger.debug('Health Connect status before sync:', status);

    // Since executeTaskAsync is not available, directly call the task function
    logger.debug('Executing Health Connect checkWorkouts...');
    await healthConnectService.checkWorkouts();
    logger.debug('✅ Manual sync triggered successfully');

    // Log a clear message that will be visible in the console
    logger.debug('🚀🚀🚀 BACKGROUND SYNC TRIGGERED SUCCESSFULLY 🚀🚀🚀');
  } catch (error) {
    logger.error('❌ Manual sync trigger failed:', error);
  }
};

// Function to force execute the background task for testing
export const forceExecuteBackgroundTask = async () => {
  logger.debug('🔥🔥🔥 FORCING BACKGROUND TASK EXECUTION 🔥🔥🔥');
  try {
    // Directly execute the task function as defined in TaskManager.defineTask
    logger.info('🔄 Starting forced background task execution...');

    // Execute the same code that would run in the background task
    await healthConnectService.checkWorkouts();

    logger.debug('✅✅✅ FORCED BACKGROUND TASK COMPLETED SUCCESSFULLY ✅✅✅');
    return true;
  } catch (error) {
    logger.error('❌❌❌ FORCED BACKGROUND TASK FAILED ❌❌❌');
    logger.error('Error details:', error);
    return false;
  }
};
