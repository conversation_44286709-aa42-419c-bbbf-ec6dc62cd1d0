import TrackPlayer, {Event} from 'react-native-track-player';
import logger from '@/src/utils/logger';

export async function PlaybackService() {
  TrackPlayer.addEventListener(Event.RemotePlay, async () => {
    await TrackPlayer.play();
  });

  TrackPlayer.addEventListener(Event.RemotePause, async () => {
    await TrackPlayer.pause();
  });

  TrackPlayer.addEventListener(Event.RemoteStop, async () => {
    await TrackPlayer.stop();
  });

  TrackPlayer.addEventListener(Event.RemoteNext, async () => {
    await TrackPlayer.skipToNext();
  });

  TrackPlayer.addEventListener(Event.RemotePrevious, async () => {
    await TrackPlayer.skipToPrevious();
  });

  TrackPlayer.addEventListener(Event.PlaybackState, async (state) => {
    logger.debug('Playback state changed:', state.state);
  });

  TrackPlayer.addEventListener(Event.PlaybackError, async (error) => {
    logger.error('Playback Error:', error);
  });
}
