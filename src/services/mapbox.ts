import Constants from 'expo-constants';
import Mapbox from '@rnmapbox/maps';
import polyline from '@mapbox/polyline';
import logger from '@/src/utils/logger';

const accessToken = Constants.expoConfig?.extra?.mapbox.accessToken;

Mapbox.setAccessToken(accessToken)
  .then(() => {
    logger.debug('success');
  })
  .catch((reason) => {
    logger.error('error', reason);
  });

export const mapBoxImageUrl = (routeCoords: [number, number][]) => {
  // const latLonCoords: [number, number][] = routeCoords.map(([lon, lat]) => [
  //   lat,
  //   lon,
  // ]); // ⬅️ this is key

  const encodedPolyline = polyline.encode(
    routeCoords.map(([lon, lat]) => [lat, lon]), // [lon, lat] ➜ [lat, lon]
  );
  const encodedPath = encodeURIComponent(`path-5+f44-0.5(${encodedPolyline})`);
  return `https://api.mapbox.com/styles/v1/mapbox/streets-v12/static/${encodedPath}/auto/500x300?access_token=${accessToken}`;
};

export default Mapbox;
