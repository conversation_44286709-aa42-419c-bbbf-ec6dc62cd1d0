import {
  BusinessListDto,
  UserProfileDto,
  UserProfileDtoAccountTypeEnum,
} from '@gojoe/typescript-sdk';
import {QueryClient} from '@tanstack/react-query';

const queryClient = new QueryClient();

export const invalidateCache = async (keys: string[][], timeout = 3000) => {
  setTimeout(async () => {
    for (let i = 0; i < keys.length; i += 1) {
      await queryClient.invalidateQueries({
        queryKey: keys[i],
        refetchType: 'all',
        exact: true,
      });
    }
  }, timeout);
};

export const handleDeletePost = async (postId: string) => {
  const allQueries = queryClient.getQueryCache().findAll();
  const keys = [['feed'], ['post', postId]];
  for (let i = 0; i < allQueries.length; i += 1) {
    if (
      allQueries[i].queryKey.some(
        (key) => key === 'challenge' || key === 'user' || key === 'posts',
      )
    ) {
      keys.push(allQueries[i].queryKey as any);
    }
  }
  await invalidateCache(keys);
};

export const handleCacheJoinBusiness = async (
  business: BusinessListDto,
  authUser: UserProfileDto | undefined,
) => {
  if (!authUser) {
    return;
  }
  if (parseInt(business.contractLength, 10) > 0) {
    authUser.accountType = UserProfileDtoAccountTypeEnum.Premium;
  }
  return invalidateCache([
    ['business', 'users', business.id],
    ['business', business.id],
    ['challenges', 'featured', 'me'],
    ['challenges', 'finished', 'me'],
    ['challenges', 'active', 'me'],
    ['feed'],
    ['user', 'me'],
    ['user', authUser.id],
    ['journeys'],
    ['user', 'businesses'],
  ]);
};

export const handleCacheLeaveBusiness = async (
  businessId: string,
  authUser: UserProfileDto | undefined,
) => {
  await invalidateCache([
    ['challenges', 'featured', 'me'],
    ['challenges', 'finished', 'me'],
    ['challenges', 'active', 'me'],
    ['business', 'users', businessId],
    ['business', businessId],
    ['feed'],
    ['journeys'],
    ['user', 'businesses'],
  ]);

  if (!authUser) {
    return;
  }

  queryClient.setQueryData(['user', 'me'], (oldData: any) => {
    if (!oldData) {
      return oldData;
    }
    const newData = {...oldData};
    let accountType: UserProfileDtoAccountTypeEnum =
      UserProfileDtoAccountTypeEnum.Free;
    newData.businesses = newData.businesses.filter(
      (business: BusinessListDto) => business.id !== businessId,
    );
    newData.businesses.forEach((business: BusinessListDto) => {
      if (parseInt(business.contractLength, 10) > 0) {
        accountType = UserProfileDtoAccountTypeEnum.Premium;
      }
    });
    authUser.accountType = accountType;
    return newData;
  });
  queryClient.setQueryData(['user', authUser.id], (oldData: any) => {
    if (!oldData) {
      return oldData;
    }
    const newData = {...oldData};
    newData.businesses = newData.businesses.filter(
      (business: BusinessListDto) => business.id !== businessId,
    );
    return newData;
  });
};
