import {z} from 'zod';

export type CreateTeamUserDto = {
  userId: string;
  status?: boolean;
  isLeader?: boolean;
  isAdmin?: boolean;
  position: number;
};

export type CreateTeamFormValues = {
  name: string;
  logo?: string;
  users: CreateTeamUserDto[];
  passcode?: string;
  challengeCode?: string;
  isChallenging?: boolean;
};

export type EditTeamFormValues = {
  name: string;
  logo?: string;
  users?: CreateTeamUserDto[];
  passcode?: string;
  challengeCode?: string;
  isChallenging?: boolean;
};

// Schema for name and logo validation
const nameLogoSchema = {
  name: z
    .string()
    .min(1, {message: 'Team name is required'})
    .max(30, {message: 'Team name must be 30 characters or less'}),
  logo: z.string().optional(),
};

// Schema for creating a new team (requires users)
export const createTeamSchema = z.object({
  ...nameLogoSchema,
  users: z
    .array(
      z.object({
        userId: z.string(),
        status: z.boolean().optional(),
        isLeader: z.boolean().optional(),
        isAdmin: z.boolean().optional(),
        position: z.number(),
      }),
    )
    .min(1, {message: 'At least one team member is required'}),
  passcode: z.string().optional(),
  challengeCode: z.string().optional(),
  isChallenging: z.boolean().optional(),
});

// Schema for editing a team (doesn't require users)
export const editTeamSchema = z.object({
  ...nameLogoSchema,
  users: z
    .array(
      z.object({
        userId: z.string(),
        status: z.boolean().optional(),
        isLeader: z.boolean().optional(),
        isAdmin: z.boolean().optional(),
        position: z.number(),
      }),
    )
    .optional(),
  passcode: z.string().optional(),
  challengeCode: z.string().optional(),
  isChallenging: z.boolean().optional(),
});

export const defaultValues: CreateTeamFormValues = {
  name: '',
  logo: undefined,
  users: [],
  passcode: undefined,
  challengeCode: undefined,
  isChallenging: false,
};
