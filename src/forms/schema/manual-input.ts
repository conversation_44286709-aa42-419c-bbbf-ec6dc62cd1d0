import {z} from 'zod';

export const manualInputSchema = z.object({
  activityType: z.object({
    id: z.string().min(1, {message: 'Activity Type is required'}),
    name: z.string(),
    hasDistance: z.boolean(),
    indexPoints: z.number(),
  }),
  distance: z.number().min(0),
  time: z.number().min(0),
  date: z.date(),
  evidences: z.array(
    z.object({
      id: z.string(),
      url: z.string(),
    }),
  ),
  media: z.array(
    z.object({
      id: z.string(),
      url: z.string(),
    }),
  ),
  title: z.string().optional(),
});

export type ManualInputFormInterface = z.infer<typeof manualInputSchema>;

export const defaultValues: ManualInputFormInterface = {
  activityType: {
    id: '',
    name: '',
    hasDistance: false,
    indexPoints: 1000,
  },
  distance: 0,
  time: 0,
  date: new Date(),
  evidences: [],
  media: [],
  title: '',
};
