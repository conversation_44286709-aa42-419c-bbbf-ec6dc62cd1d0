import {z} from 'zod';

export const addClubEventSchema = z.object({
  type: z.string(),
  date: z.date(),
  title: z.string().min(1, {message: 'Event title is required'}),
  duration: z.string().optional(),
  description: z.string().min(1, {
    message: 'Description is required',
  }),
  inviteUrl: z
    .string()
    .url({message: 'Invalid URL'})
    .or(z.literal(''))
    .optional(),
});

export type AddClubEventFormInterface = z.infer<typeof addClubEventSchema>;

export const defaultValues: AddClubEventFormInterface = {
  type: 'virtual',
  date: new Date(),
  title: '',
  duration: '60',
  description: '',
  inviteUrl: '',
};
