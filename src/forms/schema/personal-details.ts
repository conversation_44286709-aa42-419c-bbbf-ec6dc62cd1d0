import {z} from 'zod';

export type FormInterface = {
  firstName: string;
  lastName: string;
  profilePicture?: string | null;
  dateOfBirth?: Date;
  gender?: string;
  weight?: number;
  height?: number;
  levelId?: string;
  countryId?: string;
};
export const formSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  dateOfBirth: z.date().optional(),
  gender: z.string().optional(),
  profilePicture: z.string().optional().nullable(),
  weight: z.number().optional(),
  height: z.number().optional(),
  levelId: z.string().optional(),
  countryId: z.string().optional(),
});

export const defaultValues = {
  firstName: '',
  lastName: '',
  dateOfBirth: undefined,
  gender: undefined,
  profilePicture: undefined,
  weight: undefined,
  height: undefined,
  levelId: undefined,
  countryId: undefined,
};
