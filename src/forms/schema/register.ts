import {z} from 'zod';

export const registerSchema = z.object({
  firstName: z
    .string()
    .min(2, {message: 'First name must be at least 2 characters.'}),
  lastName: z
    .string()
    .min(2, {message: 'Last name must be at least 2 characters.'}),
  email: z.string().email({message: 'Please enter a valid email address'}),
  password: z.string().min(1, {message: 'Verification code is required'}),
});

export type RegisterFormInterface = z.infer<typeof registerSchema>;

export const defaultValues: RegisterFormInterface = {
  firstName: '',
  lastName: '',
  email: '',
  password: '',
};
