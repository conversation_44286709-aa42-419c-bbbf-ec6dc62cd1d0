import React from 'react';
import ActionSheet, {SheetProps} from 'react-native-actions-sheet';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme, YStack} from 'tamagui';

import type {ActionSheetProps} from 'react-native-actions-sheet/dist/src/types';

interface Props extends ActionSheetProps, SheetProps {
  children: React.ReactNode;
}

const ThemedActionSheet: React.FC<Props> = ({children, ...props}) => {
  const safeAreaInsets = useSafeAreaInsets();
  const theme = useTheme();
  const backgroundColor = theme.windowBackground
    ? theme.windowBackground.val
    : 'white';
  const overlayColor = theme.color ? theme.color.val : 'black';
  return (
    <YStack>
      <ActionSheet
        gestureEnabled
        safeAreaInsets={safeAreaInsets}
        useBottomSafeAreaPadding={true}
        defaultOverlayOpacity={0.5}
        overlayColor={overlayColor}
        containerStyle={{
          backgroundColor,
          borderTopLeftRadius: 24,
          borderTopRightRadius: 24,
        }}
        indicatorStyle={{
          backgroundColor: overlayColor,
          position: 'absolute',
          zIndex: 100,
          width: 100,
          top: 4,
          height: 4,
          borderRadius: 2,
        }}
        {...props}>
        {children}
      </ActionSheet>
    </YStack>
  );
};

export default ThemedActionSheet;
