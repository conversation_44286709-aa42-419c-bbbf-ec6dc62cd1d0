import React from 'react';
import {<PERSON><PERSON><PERSON>ana<PERSON>, SheetP<PERSON>, ScrollView} from 'react-native-actions-sheet';
import {XStack, YStack} from 'tamagui';
import {X as CloseIcon} from '@tamagui/lucide-icons';

import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import StyledText from '../components/UI/StyledText';

const capitalizeFirstLetter = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

const SelectSheet: React.FC<SheetProps<'select'>> = ({sheetId, payload}) => {
  const options = payload?.options || [];
  const selectedValue = payload?.selectedValue || '';
  const onSelect = payload?.onSelect || (() => {});

  const handleOnClose = async () => {
    await SheetManager.hide(sheetId);
  };

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack width='100%' height='100%'>
        <YStack paddingTop={24} paddingEnd={12} alignSelf='flex-end'>
          <CloseIcon color='$grey1' size={24} onPress={handleOnClose} />
        </YStack>

        <ScrollView showsVerticalScrollIndicator={false}>
          {options.map((option) => (
            <XStack
              key={option.value}
              backgroundColor={
                option.value === selectedValue
                  ? '$primary'
                  : '$windowBackground'
              }
              alignItems='center'
              height={44}
              paddingHorizontal={12}
              borderRadius={8}
              onPress={() => {
                onSelect(option.value);
                handleOnClose();
              }}>
              <StyledText
                color={option.value === selectedValue ? '$white1' : '$color'}>
                {capitalizeFirstLetter(option.label)}
              </StyledText>
            </XStack>
          ))}
        </ScrollView>
      </YStack>
    </ThemedActionSheet>
  );
};

export default SelectSheet;
