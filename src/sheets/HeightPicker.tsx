import React, {useState} from 'react';
import {She<PERSON><PERSON>ana<PERSON>, SheetProps} from 'react-native-actions-sheet';
import {XStack, YStack, useTheme} from 'tamagui';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {useLocales} from '@/src/contexts/LocaleContext';
import {Platform, StyleSheet} from 'react-native';
import I18nText from '@/src/components/I18nText';
import Button from '@/src/components/UI/Button';
import {Picker} from '@react-native-picker/picker';
import convert from 'convert'; // Conversion library
import UnitSettings from '../components/UnitSettings';

export enum HeightUnit {
  Centimeters = 'cm',
  FeetInches = 'ft-in',
}

const HeightPicker: React.FC<SheetProps<'heightPicker'>> = ({
  sheetId,
  payload,
}) => {
  const {locales, setLocales} = useLocales();
  const unit = locales?.heightUnit || HeightUnit.Centimeters;
  const theme = useTheme();
  const color = theme.color?.get() ?? '#000000';

  // Height in cm (default: 170 cm)
  const [height, setHeight] = useState<number>(payload?.value || 170);

  // Convert to feet and inches
  const heightInInches = Math.round(convert(height, 'cm').to('in'));
  const feet = Math.floor(heightInInches / 12);
  const inches = heightInInches % 12;

  // Update height when selecting cm
  const setHeightFromCm = (val: number) => setHeight(val);

  // Update height when selecting feet
  const setFromFeet = (val: number) => {
    setHeight(Math.round(convert(val * 12 + inches, 'in').to('cm')));
  };

  // Update height when selecting inches
  const setFromInches = (val: number) => {
    setHeight(Math.round(convert(feet * 12 + val, 'in').to('cm')));
  };

  const handleClose = () => {
    if (payload?.onSelect) {
      payload.onSelect(height);
    }
  };

  const handlePressCancel = () => SheetManager.hide(sheetId);

  const handleUnitChange = (selectedUnit: HeightUnit) => {
    setLocales({
      ...locales,
      heightUnit: selectedUnit,
    });
  };

  const handlePressCentimeters = () => {
    return handleUnitChange(HeightUnit.Centimeters);
  };
  const handlePressFeetInches = () => {
    return handleUnitChange(HeightUnit.FeetInches);
  };

  return (
    <ThemedActionSheet
      sheetId={sheetId}
      gestureEnabled={false}
      onClose={handleClose}>
      <YStack paddingVertical='$4'>
        <XStack
          height='$6'
          paddingHorizontal='$5'
          borderBottomWidth={StyleSheet.hairlineWidth}
          borderColor='$windowBackground'
          justifyContent='space-between'
          alignItems='center'>
          <I18nText fontSize={20} fontWeight='600'>
            {payload?.title}
          </I18nText>

          <XStack
            backgroundColor='$grey3'
            height='$2.5'
            paddingHorizontal='$1.5'
            borderRadius='$3'
            alignItems='center'>
            <UnitSettings
              value={HeightUnit.Centimeters}
              isActive={locales.heightUnit === HeightUnit.Centimeters}
              onPress={handlePressCentimeters}
            />
            <UnitSettings
              value={HeightUnit.FeetInches}
              isActive={locales.heightUnit === HeightUnit.FeetInches}
              onPress={handlePressFeetInches}
            />
          </XStack>

          <Button
            chromeless
            onPress={handlePressCancel}
            backgroundColor='transparent'>
            <I18nText
              fontSize={12}
              fontWeight='600'
              borderBottomWidth={1}
              borderBottomColor={color}>
              Done
            </I18nText>
          </Button>
        </XStack>

        {/* Height Picker */}
        <YStack>
          {unit === HeightUnit.Centimeters ? (
            // Single picker for cm
            <Picker
              itemStyle={{
                color: color,
              }}
              selectedValue={height}
              dropdownIconColor={color}
              onValueChange={setHeightFromCm}
              style={Platform.OS === 'android' ? {color: color} : undefined}>
              {[...Array(121)].map((_, i) => (
                <Picker.Item key={i} label={`${i + 100} cm`} value={i + 100} />
              ))}
            </Picker>
          ) : (
            // Two pickers for feet & inches
            <XStack>
              <Picker
                itemStyle={{
                  color: color,
                }}
                selectedValue={feet}
                dropdownIconColor={color}
                onValueChange={setFromFeet}
                style={{
                  flex: 1,
                  color: Platform.OS === 'android' ? color : undefined,
                }}>
                {[...Array(4)].map((_, i) => (
                  <Picker.Item key={i} label={`${i + 4} ft`} value={i + 4} />
                ))}
              </Picker>
              <Picker
                itemStyle={{
                  color: color,
                }}
                selectedValue={inches}
                dropdownIconColor={color}
                onValueChange={setFromInches}
                style={{
                  flex: 1,
                  color: Platform.OS === 'android' ? color : undefined,
                }}>
                {[...Array(12)].map((_, i) => (
                  <Picker.Item key={i} label={`${i} in`} value={i} />
                ))}
              </Picker>
            </XStack>
          )}
        </YStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default HeightPicker;
