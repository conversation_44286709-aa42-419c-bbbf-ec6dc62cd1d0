import React, {useEffect, useState} from 'react';
import {useMutation, useQueryClient} from '@tanstack/react-query';
import {SheetManager, SheetProps} from 'react-native-actions-sheet';
import {YStack} from 'tamagui';

import {api} from '../services/api';
import {triggerHaptics} from '../utils/haptics';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import useBusinessRegions from '../hooks/api/useBusinessRegions';
import useBusinessDepartments from '../hooks/api/useBusinessDepartments';
import CustomSelect from '../components/CustomSelect';
import I18nText from '../components/I18nText';
import StyledButton from '../components/UI/Button';

const BusinessOnboarding: React.FC<SheetProps<'business_onboarding'>> = ({
  sheetId,
  payload,
}) => {
  const businessId = payload?.businessId ?? '';
  const settings = payload?.settings;
  const businessUser = payload?.businessUser;
  const businessName = payload?.businessName;
  const isRegionRequired = payload?.isRegionRequired ?? true;
  const isDepartmentRequired = payload?.isDepartmentRequired ?? true;

  const {regions} = useBusinessRegions(businessId);
  const {departments} = useBusinessDepartments(businessId);

  const [region, setRegion] = useState<
    {id: string; name: string} | undefined
  >();
  const [department, setDepartment] = useState<
    {id: string; name: string} | undefined
  >();

  useEffect(() => {
    if (businessUser) {
      if (businessUser.businessRegion) {
        setRegion({
          id: businessUser.businessRegion.id,
          name: businessUser.businessRegion.name,
        });
      }
      if (businessUser.businessDepartment) {
        setDepartment({
          id: businessUser.businessDepartment.id,
          name: businessUser.businessDepartment.name,
        });
      }
    }
  }, [businessUser]);

  const handleOnPressRegions = async () => {
    await triggerHaptics();

    SheetManager.show('select', {
      payload: {
        options: regions.map((region) => ({
          label: region.name,
          value: region.id,
        })),
        selectedValue: region?.id,
        onSelect: (value: string) => {
          const selectedRegion = regions.find((region) => region.id === value);
          if (selectedRegion) {
            setRegion({
              id: selectedRegion.id,
              name: selectedRegion.name,
            });
          }
        },
      },
    });
  };

  const handleOnPressDepartments = async () => {
    await triggerHaptics();

    SheetManager.show('select', {
      payload: {
        options: departments.map((deparment) => ({
          label: deparment.name,
          value: deparment.id,
        })),
        selectedValue: department?.id,
        onSelect: (value: string) => {
          const selectedDepartment = departments.find(
            (department) => department.id === value,
          );
          if (selectedDepartment) {
            setDepartment({
              id: selectedDepartment.id,
              name: selectedDepartment.name,
            });
          }
        },
      },
    });
  };

  const queryClient = useQueryClient();

  const {mutate, isPending} = useMutation({
    mutationFn: () =>
      api.getApiClient().businessUsersControllerUpdate({
        updateBusinessUserSettingsDto: {
          businessId: businessId,
          regionId: region?.id,
          departmentId: department?.id,
        },
      }),
    onSuccess: () => {
      // Invalidate the businessUser query to refresh the data
      queryClient.invalidateQueries({
        queryKey: ['businessUser', {businessId, userId: businessUser?.userId}],
      });
    },
  });

  const onSave = () => {
    mutate();
    SheetManager.hide(sheetId);
  };

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack width='100%' height='100%' padding='$5'>
        <I18nText fontSize={24} fontWeight={700} i18nParams={{businessName}}>
          {`Where do you belong in {{businessName}}`}
        </I18nText>

        {settings?.onboarding &&
          settings.onboarding.map((item) => {
            switch (item.name.toLowerCase()) {
              case 'region':
                return (
                  <CustomSelect
                    key={item.name}
                    label={item.displayName}
                    value={region?.name ?? ''}
                    onPress={handleOnPressRegions}
                    isRequired={isRegionRequired}
                  />
                );
              case 'department':
                return (
                  <CustomSelect
                    key={item.name}
                    label={item.displayName}
                    value={department?.name ?? ''}
                    onPress={handleOnPressDepartments}
                    isRequired={isDepartmentRequired}
                  />
                );
            }
          })}

        <StyledButton
          marginTop='$5'
          onPress={onSave}
          loading={isPending}
          disabled={
            (isRegionRequired && !region?.id) ||
            (isDepartmentRequired && !department?.id)
          }>
          <I18nText fontWeight='bold' color='$white1'>
            Save
          </I18nText>
        </StyledButton>
      </YStack>
    </ThemedActionSheet>
  );
};

export default BusinessOnboarding;
