import React, {useEffect, useState} from 'react';
import {<PERSON>etManager, SheetProps} from 'react-native-actions-sheet';
import {XStack, YStack, Group, useTheme} from 'tamagui'; // Import Group from Tamagui
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {useLocales} from '@/src/contexts/LocaleContext';
import {Platform, StyleSheet} from 'react-native';
import I18nText from '@/src/components/I18nText';
import Button from '@/src/components/UI/Button';
import {Picker} from '@react-native-picker/picker';
import convert from 'convert'; // Use the conversion library
import UnitSettings from '../components/UnitSettings';

export enum WeightUnit {
  Kilograms = 'kg',
  Pounds = 'lbs',
  Stones = 'st',
}

const WeightPicker: React.FC<SheetProps<'weightPicker'>> = ({
  sheetId,
  payload,
}) => {
  const {locales, setLocales} = useLocales();
  const unit = locales?.weightUnit || WeightUnit.Kilograms;
  const theme = useTheme();
  const color = theme.color?.get() ?? '#000000';

  // Weight in grams
  const defaultWeight = payload?.value || 70000;
  const defaultKg = Math.floor(defaultWeight / 1000);
  const defaultGrams = Math.round((defaultWeight % 1000) / 100) * 100;

  const [kg, setKg] = useState(defaultKg);
  const [grams, setGrams] = useState(defaultGrams);
  const [weight, setWeight] = useState(defaultWeight);

  useEffect(() => {
    setWeight(parseInt(`${kg}`) * 1000 + parseInt(`${grams}`));
  }, [kg, grams]);

  const weightInPounds = Math.round(convert(weight, 'g').to('lb'));
  const stones = Math.floor(weightInPounds / 14);
  const pounds = Math.round(weightInPounds % 14);

  const setFromLbs = (val: number) => {
    setWeight(Math.floor(convert(val, 'lb').to('g')));
  };
  const setFromStones = (val: number) => {
    setWeight(Math.floor(convert(val * 14 + pounds, 'lb').to('g')));
  };

  const setFromPounds = (val: number) => {
    setWeight(Math.floor(convert(stones * 14 + val, 'lb').to('g')));
  };

  const handleClose = () => {
    if (payload?.onSelect) {
      payload.onSelect(weight);
    }
  };

  const handlePressCancel = () => SheetManager.hide(sheetId);

  const handleUnitChange = (selectedUnit: WeightUnit) => {
    setLocales({
      ...locales,
      weightUnit: selectedUnit,
    });
  };

  const handlePressKilograms = () => {
    return handleUnitChange(WeightUnit.Kilograms);
  };
  const handlePressStones = () => {
    return handleUnitChange(WeightUnit.Stones);
  };
  const handlePressPounds = () => {
    return handleUnitChange(WeightUnit.Pounds);
  };

  return (
    <ThemedActionSheet
      sheetId={sheetId}
      gestureEnabled={false}
      onClose={handleClose}>
      <YStack paddingVertical='$4'>
        <XStack
          height='$6'
          paddingHorizontal='$5'
          borderBottomWidth={StyleSheet.hairlineWidth}
          borderColor='$windowBackground'
          justifyContent='space-between'
          alignItems='center'>
          <I18nText fontSize={20} fontWeight='600'>
            {payload?.title}
          </I18nText>

          {/* Unit Switcher */}
          <XStack
            backgroundColor='$grey3'
            height='$2.5'
            paddingHorizontal='$1.5'
            borderRadius='$3'
            alignItems='center'>
            <UnitSettings
              value={WeightUnit.Kilograms}
              isActive={locales.weightUnit === WeightUnit.Kilograms}
              onPress={handlePressKilograms}
            />
            <UnitSettings
              value={WeightUnit.Stones}
              isActive={locales.weightUnit === WeightUnit.Stones}
              onPress={handlePressStones}
            />
            <UnitSettings
              value={WeightUnit.Pounds}
              isActive={locales.weightUnit === WeightUnit.Pounds}
              onPress={handlePressPounds}
            />
          </XStack>
          <Button
            chromeless
            onPress={handlePressCancel}
            backgroundColor='transparent'>
            <I18nText
              fontSize={12}
              fontWeight='600'
              borderBottomWidth={1}
              borderBottomColor={color}>
              Done
            </I18nText>
          </Button>
        </XStack>

        {/* Weight Picker */}
        <YStack>
          {unit === WeightUnit.Kilograms ? (
            // Two columns for kg and grams
            <XStack>
              <Picker
                itemStyle={{
                  color: color,
                }}
                dropdownIconColor={color}
                selectedValue={kg}
                onValueChange={setKg}
                style={{
                  flex: 1,
                  color: Platform.OS === 'android' ? color : undefined,
                }}>
                {[...Array(241)].map((_, i) => (
                  <Picker.Item key={i} label={`${i + 10} kg`} value={i + 10} />
                ))}
              </Picker>
              <Picker
                itemStyle={{
                  color: color,
                }}
                dropdownIconColor={color}
                selectedValue={grams}
                onValueChange={setGrams}
                style={{
                  flex: 1,
                  color: Platform.OS === 'android' ? color : undefined,
                }}>
                {[...Array(10)].map((_, i) => (
                  <Picker.Item key={i} label={`${i * 100} g`} value={i * 100} />
                ))}
              </Picker>
            </XStack>
          ) : unit === WeightUnit.Pounds ? (
            <Picker
              itemStyle={{
                color: color,
              }}
              dropdownIconColor={color}
              selectedValue={weightInPounds}
              onValueChange={setFromLbs}
              style={Platform.OS === 'android' ? {color: color} : undefined}>
              {[...Array(551)].map((_, i) => (
                <Picker.Item key={i} label={`${i + 22} lbs`} value={i + 22} />
              ))}
            </Picker>
          ) : (
            <XStack>
              <Picker
                itemStyle={{
                  color: color,
                }}
                dropdownIconColor={color}
                selectedValue={stones}
                onValueChange={setFromStones}
                style={{
                  flex: 1,
                  color: Platform.OS === 'android' ? color : undefined,
                }}>
                {[...Array(40)].map((_, i) => (
                  <Picker.Item key={i} label={`${i} st`} value={i} />
                ))}
              </Picker>
              <Picker
                itemStyle={{
                  color: color,
                }}
                dropdownIconColor={color}
                selectedValue={pounds}
                onValueChange={setFromPounds}
                style={{
                  flex: 1,
                  color: Platform.OS === 'android' ? color : undefined,
                }}>
                {[...Array(14)].map((_, i) => (
                  <Picker.Item key={i} label={`${i} lbs`} value={i} />
                ))}
              </Picker>
            </XStack>
          )}
        </YStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default WeightPicker;
