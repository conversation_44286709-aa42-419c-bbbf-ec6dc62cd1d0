import React, {useState} from 'react';
import {She<PERSON><PERSON><PERSON><PERSON>, SheetProps} from 'react-native-actions-sheet';
import {useTheme, XStack, YStack} from 'tamagui';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {Platform, StyleSheet} from 'react-native';
import I18nText from '@/src/components/I18nText';
import Button from '@/src/components/UI/Button';
import {Picker} from '@react-native-picker/picker';

const ActivityTimePicker: React.FC<SheetProps<'activity_time_picker'>> = ({
  sheetId,
  payload,
}) => {
  const theme = useTheme();
  const color = theme.color?.get() ?? '#000000';

  // Convert seconds to hours, minutes, seconds
  const total = payload?.value ?? 0;

  const hours = Math.floor(total / 3600);
  const minutes = Math.floor((total % 3600) / 60);
  const seconds = total % 60;

  // Always use string values for consistency across platforms
  const [hoursInput, setHoursInput] = useState(hours.toString());
  const [minutesInput, setMinutesInput] = useState(minutes.toString());
  const [secondsInput, setSecondsInput] = useState(seconds.toString());

  const handleClose = () => {
    if (payload?.onSelect) {
      // Parse string values to integers, defaulting to 0 if parsing fails
      const hoursVal = parseInt(hoursInput) || 0;
      const minutesVal = parseInt(minutesInput) || 0;
      const secondsVal = parseInt(secondsInput) || 0;

      const totalSeconds = hoursVal * 3600 + minutesVal * 60 + secondsVal;

      // Always call onSelect, even if totalSeconds is 0
      payload.onSelect(totalSeconds);
    }
  };

  const handlePressDone = () => SheetManager.hide(sheetId);

  return (
    <ThemedActionSheet
      sheetId={sheetId}
      gestureEnabled={false}
      onClose={handleClose}>
      <YStack paddingVertical='$4'>
        <XStack
          height='$6'
          borderBottomWidth={StyleSheet.hairlineWidth}
          borderColor='$windowBackground'
          justifyContent='flex-end'
          alignItems='center'>
          <Button
            chromeless
            onPress={handlePressDone}
            backgroundColor='transparent'>
            <I18nText
              fontSize={12}
              fontWeight='600'
              borderBottomWidth={1}
              borderBottomColor={color}>
              Done
            </I18nText>
          </Button>
        </XStack>

        <XStack>
          <YStack flex={1}>
            <Picker
              style={{color: color}}
              itemStyle={{
                color: color,
              }}
              dropdownIconColor={color}
              selectedValue={hoursInput}
              onValueChange={(value) => {
                // Always convert to string for consistency
                const stringValue = value.toString();
                setHoursInput(stringValue);
              }}>
              {Array.from({length: 100}, (_, i) => {
                // Always use string values for consistency
                const stringValue = i.toString();
                return (
                  <Picker.Item
                    key={`value-${i}`}
                    label={stringValue.padStart(2, '0')}
                    value={stringValue}
                  />
                );
              })}
            </Picker>
            <I18nText
              position='absolute'
              right={Platform.OS === 'ios' ? 20 : 35}
              top={Platform.OS === 'ios' ? 90 : 5}
              fontSize={26}>
              h
            </I18nText>
          </YStack>
          <YStack flex={1}>
            <Picker
              selectedValue={minutesInput}
              onValueChange={(value) => {
                // Always convert to string for consistency
                const stringValue = value.toString();
                setMinutesInput(stringValue);
              }}
              style={{color: color}}
              itemStyle={{
                color: color,
              }}
              dropdownIconColor={color}>
              {Array.from({length: 60}, (_, i) => {
                // Always use string values for consistency
                const stringValue = i.toString();
                return (
                  <Picker.Item
                    key={`value-${i}`}
                    label={stringValue.padStart(2, '0')}
                    value={stringValue}
                  />
                );
              })}
            </Picker>
            <I18nText
              position='absolute'
              right={Platform.OS === 'ios' ? 20 : 35}
              top={Platform.OS === 'ios' ? 90 : 5}
              fontSize={26}
              zIndex={100}>
              m
            </I18nText>
          </YStack>
          <YStack flex={1}>
            <Picker
              selectedValue={secondsInput}
              onValueChange={(value) => {
                // Always convert to string for consistency
                const stringValue = value.toString();
                setSecondsInput(stringValue);
              }}
              style={{color: color}}
              itemStyle={{
                color: color,
              }}
              dropdownIconColor={color}>
              {Array.from({length: 60}, (_, i) => {
                // Always use string values for consistency
                const stringValue = i.toString();
                return (
                  <Picker.Item
                    key={`value-${i}`}
                    label={stringValue.padStart(2, '0')}
                    value={stringValue}
                  />
                );
              })}
            </Picker>
            <I18nText
              position='absolute'
              right={Platform.OS === 'ios' ? 20 : 35}
              top={Platform.OS === 'ios' ? 90 : 5}
              fontSize={26}>
              s
            </I18nText>
          </YStack>
        </XStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default ActivityTimePicker;
