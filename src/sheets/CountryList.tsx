import React, {useEffect, useState} from 'react';
import {KeyboardAvoidingView, Platform} from 'react-native';
import {SheetManager, SheetProps} from 'react-native-actions-sheet';
import {XStack, YStack} from 'tamagui';
import {Check} from '@tamagui/lucide-icons';
import {FlashList} from 'react-native-actions-sheet/dist/src/views/FlashList';
import {CountryDto} from '@gojoe/typescript-sdk';

import useCountries from '@/src/hooks/api/useCountries';
import I18nText from '@/src/components/I18nText';
import {StyledInput} from '@/src/components/UI/StyledInput';
import ThemedActionSheet from './ThemedActionSheet';
import {useTranslation} from 'react-i18next';

const CountryListSheet: React.FC<SheetProps<'country_list'>> = ({
  sheetId,
  payload,
}) => {
  const {t} = useTranslation();
  const [q, setQ] = useState('');
  const {data} = useCountries();
  const [selectedCountryId, setSelectedCountryId] = React.useState<
    string | undefined
  >();

  useEffect(() => {
    if (payload && payload.countryId) {
      setSelectedCountryId(payload.countryId);
    }
  }, [payload]);

  const handleClose = () => {
    return SheetManager.hide(sheetId);
  };
  const renderItem = ({item}: {item: CountryDto}) => {
    if (!payload) {
      return null;
    }
    const isSelected = selectedCountryId === item.id;
    const handleSelect = async () => {
      payload.onSelect(item);
      await handleClose();
    };
    return (
      <XStack
        paddingHorizontal='$5'
        height={56}
        alignItems='center'
        justifyContent='space-between'
        onPress={handleSelect}>
        <I18nText
          fontWeight={isSelected ? '700' : '500'}
          color={isSelected ? '$primary' : '$color'}>
          {item.name}
        </I18nText>
        {selectedCountryId === item.id && <Check color={'$primary'} />}
      </XStack>
    );
  };
  return (
    <ThemedActionSheet sheetId={sheetId}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'android' ? 'height' : undefined}>
        <YStack width='100%' height='100%'>
          <XStack
            justifyContent='space-between'
            alignItems='center'
            paddingHorizontal='$5'
            borderBottomWidth={1}
            borderBottomColor='$accentGrey'
            height={56}>
            <I18nText fontWeight='700' fontSize={24}>
              Select country
            </I18nText>
            <I18nText fontWeight='500' color='$primary' onPress={handleClose}>
              Dismiss
            </I18nText>
          </XStack>
          <StyledInput
            placeholder={t('Search...')}
            marginTop={24}
            marginBottom={12}
            marginHorizontal={24}
            autoFocus
            value={q}
            onChangeText={setQ}
          />
          <FlashList
            data={
              q
                ? data.filter((c) =>
                    c.name.toLowerCase().includes(q.toLowerCase()),
                  )
                : data
            }
            renderItem={renderItem}
            estimatedItemSize={56}
            showsVerticalScrollIndicator={false}
          />
        </YStack>
      </KeyboardAvoidingView>
    </ThemedActionSheet>
  );
};
export default CountryListSheet;
