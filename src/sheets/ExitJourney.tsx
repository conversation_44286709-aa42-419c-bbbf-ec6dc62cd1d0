import React from 'react';
import {useTranslation} from 'react-i18next';
import ThemedActionSheet from './ThemedActionSheet';
import {SheetManager, SheetProps} from 'react-native-actions-sheet';
import {FontAwesome} from '@expo/vector-icons';
import {XStack, YStack} from 'tamagui';
import I18nText from '../components/I18nText';
import useMutationLeaveJourney from '../hooks/api/useMutationLeaveJourney';
import {ActivityIndicator, Alert} from 'react-native';
import {triggerHaptics} from '../utils/haptics';

const ExitJourney: React.FC<SheetProps<'exitJourney'>> = ({
  sheetId,
  payload,
}) => {
  const {journeyId} = payload as {journeyId: string};
  const {t} = useTranslation();
  const {mutate, isPending} = useMutationLeaveJourney(journeyId);

  const handlePressLeaveJourney = async () => {
    if (isPending) {
      return;
    }

    await triggerHaptics();
    mutate();
    await SheetManager.hide(sheetId);
  };

  const handlePressLeave = () => {
    Alert.alert(
      t('Exit Journey'),
      t(
        'Are you sure you want to end this Journey? You will loose all progress. You can restart it anytime.',
      ),
      [
        {
          text: t('No, Let me Continue'),
          style: 'cancel',
        },
        {
          text: t('Yes, End Journey'),
          style: 'destructive',
          onPress: () => handlePressLeaveJourney(),
        },
      ],
    );
  };

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack padding='$5'>
        <XStack alignItems='center' onPress={handlePressLeave}>
          {isPending ? (
            <ActivityIndicator color='#FFF' />
          ) : (
            <FontAwesome name='remove' size={18} color='#888888' />
          )}
          <I18nText fontSize='$4' fontWeight='$5' marginLeft='$2'>
            Exit Journey
          </I18nText>
        </XStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default ExitJourney;
