import React from 'react';
import {Sheet<PERSON>anager, SheetProps} from 'react-native-actions-sheet';
import {Image, useTheme, XStack, YStack} from 'tamagui';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import I18nText from '@/src/components/I18nText';
import PassCode from '@/src/components/PassCode';
import {triggerHaptics} from '@/src/utils/haptics';
import {useRouter} from 'expo-router';
import {ActivitiesIcon, RecordIcon} from '@/src/components/GoJoeIcon';

const Languages: React.FC<SheetProps<'add'>> = ({sheetId}) => {
  const theme = useTheme();
  const iconColor = theme.color ? theme.color.get() : '#000000';
  const router = useRouter();

  const handlePressPassCode = async () => {
    await triggerHaptics();
    await SheetManager.hide(sheetId);
    router.push('/passcode');
  };

  const handlePressManualInput = async () => {
    await triggerHaptics();
    await SheetManager.hide(sheetId);
    router.push('/manual-input');
  };

  const handlePressTracker = async () => {
    await triggerHaptics();
    await SheetManager.hide(sheetId);
    router.push('/tracker');
  };

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack padding='$5'>
        <PassCode onPress={handlePressPassCode} />
        <XStack gap={8} marginTop='$4'>
          <YStack
            onPress={handlePressTracker}
            flex={1}
            borderWidth={1}
            borderColor='$grey3'
            backgroundColor='$background'
            height={120}
            borderRadius={16}
            justifyContent='center'
            alignItems='center'>
            <RecordIcon color='$primary' size={36} />
            <I18nText fontWeight='600' marginTop={8} color='$primary'>
              Record Activity
            </I18nText>
          </YStack>
          <YStack
            onPress={handlePressManualInput}
            flex={1}
            borderWidth={1}
            borderColor='$grey3'
            backgroundColor='$background'
            height={120}
            borderRadius={16}
            justifyContent='center'
            alignItems='center'>
            <ActivitiesIcon size={36} color='$primary' />
            <I18nText fontWeight='600' marginTop={8} color='$primary'>
              Manual Activity
            </I18nText>
          </YStack>
        </XStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default Languages;
