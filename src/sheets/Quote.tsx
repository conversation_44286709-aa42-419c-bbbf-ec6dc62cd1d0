import React from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, SheetProps} from 'react-native-actions-sheet';
import {Spinner, YStack, Image} from 'tamagui';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';
import StyledText from '@/src/components/UI/StyledText';
import useQuoteOfTheDay from '@/src/hooks/api/useQuoteOfTheDay';
import StyledButton from '../components/UI/Button';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const QuoteSheet: React.FC<SheetProps<'quote'>> = ({sheetId}) => {
  const {quote, isLoading} = useQuoteOfTheDay();
  const {top, bottom} = useSafeAreaInsets();

  const handleClose = async () => {
    await triggerHaptics();
    await SheetManager.hide(sheetId);
  };

  return (
    <ThemedActionSheet
      sheetId={sheetId}
      useBottomSafeAreaPadding={false}
      safeAreaInsets={{top: top, left: 0, right: 0, bottom: 0}}>
      <YStack width='100%' position='relative' height='100%'>
        <Image
          position='absolute'
          top={0}
          left={0}
          right={0}
          bottom={0}
          width='100%'
          height='100%'
          objectFit='cover'
          source={require('@/assets/images/quote_of_the_day_bg.jpg')}
          zIndex={-1}
          borderTopLeftRadius={24}
          borderTopRightRadius={24}
        />
        <YStack flex={1} />
        <YStack padding='$5' gap='$4' paddingBottom={bottom}>
          <I18nText fontSize={20} fontWeight='600' color='white'>
            Quote of the Day
          </I18nText>

          <YStack
            gap='$3'
            minHeight={100}
            justifyContent='center'
            marginBottom='$12'>
            {isLoading ? (
              <YStack
                alignItems='center'
                justifyContent='center'
                paddingVertical='$4'>
                <Spinner size='large' color='white' />
              </YStack>
            ) : quote ? (
              <YStack>
                <StyledText
                  marginTop='$3.5'
                  fontWeight='600'
                  fontSize={40}
                  lineHeight={44}
                  fontStyle='italic'
                  color='white'>
                  {quote.quote}
                </StyledText>

                {quote.author && (
                  <StyledText fontSize={14} color='white' opacity={0.9}>
                    — {quote.author}
                    {quote.authorTitle && (
                      <StyledText fontSize={12} color='white' opacity={0.7}>
                        {', '}
                        {quote.authorTitle}
                      </StyledText>
                    )}
                  </StyledText>
                )}
              </YStack>
            ) : (
              <StyledText
                fontWeight='600'
                fontSize={18}
                fontStyle='italic'
                color='white'>
                The only bad workout is the one that didn't happen.
              </StyledText>
            )}
          </YStack>

          <StyledButton
            onPress={handleClose}
            backgroundColor='$background'
            color='$primary'
            marginTop='$4'
            marginBottom='$4'>
            <I18nText color='$color' fontWeight='600'>
              Close
            </I18nText>
          </StyledButton>
        </YStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default QuoteSheet;
