import {registerSheet, SheetDefinition} from 'react-native-actions-sheet';
import {
  ActivityTypeDto,
  ActivityTypeListDto,
  BusinessListDto,
  CountryDto,
  TeamsAwardsDto,
  UsersAwardsDto,
  ActivityTypeMetricsDto,
  SearchUserListDto,
  PostListDto,
  GiftCardDto,
  GiftCardOptionsDto,
  ChallengeMapPointsDto,
  BusinessSettingsDto,
  BusinessUserDto,
  FeaturedChallengeListItemDto,
} from '@gojoe/typescript-sdk';
import Languages from './Languages';
import SignIn from './SignIn';
import Add from './Add';
import DateTimePicker from './DateTimePicker';
import GenderPicker from './GenderPicker';
import BurnedCaloriesPicker from './BurnedCaloriesPicker';
import WeightPicker from './WeightPicker';
import HeightPicker from './HeightPicker';
import CountryList from './CountryList';
import BusinessSettings from './BusinessSettings';
import ActivityLevelPicker from './ActivityLevelPicker';
import ActivityTypes from './ActivityTypes';
import Club from './Club';
import ExitJourney from './ExitJourney';
import JourneyEventOptions from './JourneyEventOptions';
import ChallengeActivityTypeList from './challenge-activity-type-list';
import ChallengeDescription from './challenge-description';
import Survey from './Survey';
import TeamAwards from './team-awards';
import SoloAwards from './solo-awards';
import InfoUnfiltered from './info_unfiltered';
import InfoEvidence from './info_evidence';
import ActivitySummaryAll from './ActivitySummaryAll';
import UserSearch from './UserSearch';
import TermsOfUse from './TermsOfUse';
import PrivacyPolicy from './PrivacyPolicy';
import PasscodeSheet from './PasscodeSheet';
import DistancePicker from './DistancePicker';
import ActivityTimePicker from './ActivityTimePicker';
import FindFriendsSheet from './FindFriends';
import BusinessDescription from './business-description';
import PostReactionUserListSheet from './PostReactionUserList';
import CommentMenu from './CommentMenu';
import PostMenu from './PostMenu';
import JoeLevel from './JoeLevel';
import StreakSheet from './Streak';
import QuoteSheet from './Quote';
import RewardsMultiplier from './RewardsMultiplier';
import RewardsMultiplierInfoNoBust from './RewardsMultiplierInfoNoBust';
import RewardsOff from './RewardsOff';
import GiftCardRedeemSheet from './GiftCardRedeem';
import HtmlDescription from './html-description';
import DiscardPost from './DiscardPost';
import MapPoint from './MapPoint';
import BusinessOnboarding from './BusinessOnboarding';
import Select from './Select';
import ChallengeSheet from './ChallengeSheet';

registerSheet('languages', Languages);
registerSheet('signIn', SignIn);
registerSheet('add', Add);
registerSheet('dateTimePicker', DateTimePicker);
registerSheet('genderPicker', GenderPicker);
registerSheet('burned_calories_picker', BurnedCaloriesPicker);
registerSheet('weightPicker', WeightPicker);
registerSheet('heightPicker', HeightPicker);
registerSheet('activity_level_picker', ActivityLevelPicker);
registerSheet('country_list', CountryList);
registerSheet('business_settings', BusinessSettings);
registerSheet('activity_types', ActivityTypes);
registerSheet('challenge_activity_type_list', ChallengeActivityTypeList);
registerSheet('challenge_description', ChallengeDescription);
registerSheet('business_description', BusinessDescription);
registerSheet('html_description', HtmlDescription);
registerSheet('club', Club);
registerSheet('exitJourney', ExitJourney);
registerSheet('journey_event_options', JourneyEventOptions);
registerSheet('survey', Survey);
registerSheet('team_awards', TeamAwards);
registerSheet('solo_awards', SoloAwards);
registerSheet('info_unfiltered', InfoUnfiltered);
registerSheet('info_evidence', InfoEvidence);
registerSheet('activity_summary_all', ActivitySummaryAll);
registerSheet('user_search', UserSearch);
registerSheet('passcode', PasscodeSheet);
registerSheet('distance_picker', DistancePicker);
registerSheet('activity_time_picker', ActivityTimePicker);
registerSheet('find_friends', FindFriendsSheet);
registerSheet('post_reaction_user_list', PostReactionUserListSheet);
registerSheet('comment_menu', CommentMenu);
registerSheet('post_menu', PostMenu);
registerSheet('termsOfUse', TermsOfUse);
registerSheet('privacyPolicy', PrivacyPolicy);
registerSheet('joe_level', JoeLevel);
registerSheet('streak', StreakSheet);
registerSheet('quote', QuoteSheet);
registerSheet('multiplier', RewardsMultiplier);
registerSheet('multiplier_no_bust', RewardsMultiplierInfoNoBust);
registerSheet('multiplier_off', RewardsOff);
registerSheet('gift_card_redeem', GiftCardRedeemSheet);
registerSheet('discard_post', DiscardPost);
registerSheet('map_point', MapPoint);
registerSheet('business_onboarding', BusinessOnboarding);
registerSheet('select', Select);
registerSheet('challenge_sheet', ChallengeSheet);

declare module 'react-native-actions-sheet' {
  interface Sheets {
    languages: SheetDefinition;
    signIn: SheetDefinition;
    add: SheetDefinition;
    dateTimePicker: SheetDefinition<{
      payload: {
        title: string;
        date?: Date;
        onSelect?: (date: Date) => void;
        mode?: 'date' | 'time' | 'datetime';
        minimumDate?: Date;
        maximumDate?: Date;
      };
    }>;
    genderPicker: SheetDefinition<{
      payload: {
        title: string;
        gender?: string;
        onSelect?: (gender: string) => void;
      };
    }>;
    activity_level_picker: SheetDefinition<{
      payload: {
        title: string;
        levelId?: string;
        onSelect?: (levelId: string) => void;
      };
    }>;
    burned_calories_picker: SheetDefinition;
    weightPicker: SheetDefinition<{
      payload: {
        title: string;
        value?: number;
        onSelect?: (value: number) => void;
      };
    }>;
    heightPicker: SheetDefinition<{
      payload: {
        title: string;
        value?: number;
        onSelect?: (value: number) => void;
      };
    }>;
    distance_picker: SheetDefinition<{
      payload: {
        value?: number;
        onSelect?: (value: number) => void;
        activityTypeName: string;
      };
    }>;
    activity_time_picker: SheetDefinition<{
      payload: {
        value: number;
        onSelect: (value: number) => void;
      };
    }>;
    country_list: SheetDefinition<{
      payload: {
        countryId?: string;
        onSelect: (country: CountryDto) => void;
      };
    }>;
    business_settings: SheetDefinition<{
      payload: {
        business: BusinessListDto;
      };
    }>;
    club: SheetDefinition<{
      payload: {
        clubId: string;
      };
    }>;
    activity_types: SheetDefinition<{
      payload: {
        onSelect: (activity: ActivityTypeDto) => void;
        activityTypeId?: string;
        useCategories?: boolean;
        showHistory?: boolean;
      };
    }>;
    exitJourney: SheetDefinition<{
      payload: {
        journeyId: string;
      };
    }>;
    journey_event_options: SheetDefinition<{
      payload: {
        journeyId: string;
        eventId: string;
      };
    }>;
    challenge_activity_type_list: SheetDefinition<{
      payload: {
        activities: ActivityTypeListDto[];
        selectedActivities?: ActivityTypeListDto[];
        onSelect?: (activity: ActivityTypeListDto) => void;
      };
    }>;
    challenge_description: SheetDefinition<{
      payload: {
        description: string;
      };
    }>;
    business_description: SheetDefinition<{
      payload: {
        description: string;
      };
    }>;
    html_description: SheetDefinition<{
      payload: {
        description: string;
      };
    }>;
    survey: SheetDefinition<{
      payload: {
        surveyId: string;
        userId: string;
        onComplete: () => void;
      };
    }>;
    team_awards: SheetDefinition<{
      payload: {
        challengeId: string;
        challengeStartDate: string;
        award: TeamsAwardsDto;
      };
    }>;
    solo_awards: SheetDefinition<{
      payload: {
        challengeId: string;
        challengeStartDate: string;
        award: UsersAwardsDto;
      };
    }>;
    info_unfiltered: SheetDefinition;
    info_evidence: SheetDefinition;
    activity_summary_all: SheetDefinition<{
      payload: {
        summaryData: Record<string, ActivityTypeMetricsDto>;
        distanceUnit: 'km' | 'mi';
        challengeId: string;
        userId: string;
      };
    }>;
    user_search: SheetDefinition<{
      payload: {
        challengeId: string;
        onSelect: (user: SearchUserListDto) => void;
        selectedUsers?: string[];
        title?: string;
        emptyStateText?: string;
        // Team-related data for invite functionality
        teamId?: string;
        teamName?: string;
        challengeName?: string;
        passcode?: string;
      };
    }>;
    passcode: SheetDefinition;
    find_friends: SheetDefinition;
    post_reaction_user_list: SheetDefinition<{
      payload: {
        postId: string;
      };
    }>;
    comment_menu: SheetDefinition<{
      payload: {
        authUserId: string;
        businessId: string;
        commentId: string;
        postId: string;
        canDelete: boolean;
        onSuccessMutate: (update: any) => Promise<any>;
        invalidateQueries?: () => void;
      };
    }>;
    post_menu: SheetDefinition<{
      payload: {
        post: PostListDto;
        onSuccessReportMutate?: () => Promise<[void, void, void]>;
      };
    }>;
    termsOfUse: SheetDefinition<{
      payload?: {
        id?: string;
      };
    }>;
    privacyPolicy: SheetDefinition<{
      payload?: {
        id?: string;
      };
    }>;
    joe_level: SheetDefinition;
    streak: SheetDefinition<{
      payload: {
        count: number;
      };
    }>;
    quote: SheetDefinition;
    multiplier: SheetDefinition<{
      payload: {
        multiplier: string;
        capping: string;
        organisation?: string;
      };
    }>;
    multiplier_no_bust: SheetDefinition<{
      payload: {
        organisation?: string;
      };
    }>;
    multiplier_off: SheetDefinition;
    gift_card_redeem: SheetDefinition<{
      payload: {
        giftCard: GiftCardDto;
        option: GiftCardOptionsDto;
      };
    }>;
    discard_post: SheetDefinition;
    map_point: SheetDefinition<{
      payload: {
        item: ChallengeMapPointsDto;
      };
    }>;
    business_onboarding: SheetDefinition<{
      payload: {
        businessId: string;
        settings: BusinessSettingsDto;
        businessUser: BusinessUserDto;
        businessName: string;
        isRegionRequired: boolean;
        isDepartmentRequired: boolean;
      };
    }>;
    select: SheetDefinition<{
      payload: {
        options: {label: string; value: string}[];
        selectedValue?: string;
        onSelect?: (value: string) => void;
      };
    }>;
    challenge_sheet: SheetDefinition<{
      payload: {
        business: {
          id: string;
          name: string;
        };
        challenge?: FeaturedChallengeListItemDto;
        showWelcomeMessage?: boolean;
      };
    }>;
  }
}

export {};
