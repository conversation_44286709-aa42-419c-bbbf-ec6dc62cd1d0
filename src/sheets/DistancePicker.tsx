import React, {useState, useEffect} from 'react';
import {<PERSON><PERSON><PERSON>ana<PERSON>, SheetProps} from 'react-native-actions-sheet';
import {useTheme, XStack, YStack} from 'tamagui';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {StyleSheet} from 'react-native';
import I18nText from '@/src/components/I18nText';
import Button from '@/src/components/UI/Button';
import {Picker} from '@react-native-picker/picker';
import {getShortDistanceOptions, isShortActivity} from '../utils/helper';
import {DistanceUnit, useLocales} from '../contexts/LocaleContext';
import {useTranslation} from 'react-i18next';

const DistancePicker: React.FC<SheetProps<'distance_picker'>> = ({
  sheetId,
  payload,
}) => {
  const {t} = useTranslation();
  const {locales, setLocales} = useLocales();
  const theme = useTheme();
  const color = theme.color?.get() ?? '#000000';

  const isShort =
    payload && payload.activityTypeName
      ? isShortActivity(payload.activityTypeName)
      : false;

  const initialValue = payload?.value ?? 0;
  const initialWhole = Math.floor(initialValue);
  const initialDecimal = parseFloat((initialValue % 1).toFixed(1));

  const [distance, setDistance] = useState<number>(initialValue);
  const [whole, setWhole] = useState<number>(initialWhole);
  const [decimal, setDecimal] = useState<number>(initialDecimal);

  const shortDistances = getShortDistanceOptions(locales.languageTag);

  const updateDistance = (newWhole: number, newDecimal: number) => {
    // Ensure we're working with numbers, defaulting to 0 if undefined or NaN
    const wholeNum = isNaN(newWhole) ? 0 : newWhole;
    const decimalNum = isNaN(newDecimal) ? 0 : newDecimal;

    // Ensure we get a proper fixed-point number with one decimal place
    const combined = parseFloat((wholeNum + decimalNum).toFixed(1));

    // Update state with the new values, even if they're 0
    setWhole(wholeNum);
    setDecimal(decimalNum);
    setDistance(combined);
  };

  const handleClose = () => {
    if (payload?.onSelect) {
      // Always call onSelect with the current distance value, even if it's 0
      payload.onSelect(distance);
    }
  };

  // We're now handling the value changes directly in the Picker components

  const handlePressDone = () => SheetManager.hide(sheetId);

  const setDistanceUnit = (newUnit: DistanceUnit) => {
    setLocales({
      ...locales,
      distanceUnit: newUnit,
    });
  };

  return (
    <ThemedActionSheet
      sheetId={sheetId}
      gestureEnabled={false}
      onClose={handleClose}>
      <YStack paddingVertical='$4'>
        <XStack
          height='$6'
          borderBottomWidth={StyleSheet.hairlineWidth}
          borderColor='$windowBackground'
          justifyContent='flex-end'
          alignItems='center'>
          <Button
            chromeless
            onPress={handlePressDone}
            backgroundColor='transparent'>
            <I18nText fontSize={12} fontWeight='600' borderBottomWidth={1}>
              Done
            </I18nText>
          </Button>
        </XStack>

        {/* Height Picker */}
        {isShort ? (
          <XStack>
            <YStack flex={1}>
              <Picker
                style={{color: color}}
                itemStyle={{
                  color: color,
                }}
                dropdownIconColor={color}
                selectedValue={whole.toString()}
                onValueChange={(value) => {
                  // Convert the value to a number explicitly
                  const numValue = parseInt(value.toString());
                  // Update the distance directly
                  updateDistance(numValue, decimal);
                }}>
                {shortDistances.map((item) => (
                  <Picker.Item
                    key={item.value.toString()}
                    label={item.label}
                    value={item.value.toString()}
                  />
                ))}
              </Picker>
            </YStack>
            <YStack flex={1}>
              <Picker
                selectedValue={locales.distanceUnit}
                onValueChange={setDistanceUnit}
                style={{color: color}}
                itemStyle={{
                  color: color,
                }}
                dropdownIconColor={color}>
                <Picker.Item label={t('m')} value={DistanceUnit.Kilometers} />
                <Picker.Item label={t('yd')} value={DistanceUnit.Miles} />
              </Picker>
            </YStack>
          </XStack>
        ) : (
          <XStack>
            <YStack flex={1}>
              <Picker
                selectedValue={whole.toString()}
                onValueChange={(value) => {
                  // Convert the value to a number explicitly
                  const numValue = parseInt(value.toString());
                  // Update the distance directly
                  updateDistance(numValue, decimal);
                }}
                style={{color: color}}
                itemStyle={{
                  color: color,
                }}
                dropdownIconColor={color}>
                {Array.from({length: 1000}, (_, i) => (
                  <Picker.Item
                    key={i}
                    label={i.toString()}
                    value={i.toString()}
                  />
                ))}
              </Picker>
            </YStack>
            <YStack flex={1}>
              <Picker
                // Force the selectedValue to be a string on Android to ensure consistent behavior
                selectedValue={decimal.toString()}
                onValueChange={(value) => {
                  // Convert the value to a number explicitly
                  const numValue = parseFloat(value.toString());
                  // Update the distance directly instead of going through onDecimalChange
                  updateDistance(whole, numValue);
                }}
                style={{color: color}}
                itemStyle={{
                  color: color,
                }}
                dropdownIconColor={color}>
                {Array.from({length: 10}, (_, i) => {
                  const value = i / 10;
                  // Always use string values for consistency across platforms
                  const stringValue = value.toFixed(1);

                  return (
                    <Picker.Item
                      key={value}
                      label={stringValue}
                      value={stringValue}
                    />
                  );
                })}
              </Picker>
            </YStack>
            <YStack flex={1}>
              <Picker
                selectedValue={locales.distanceUnit}
                onValueChange={setDistanceUnit}
                style={{color: color}}
                itemStyle={{
                  color: color,
                }}
                dropdownIconColor={color}>
                <Picker.Item
                  label={t(DistanceUnit.Kilometers)}
                  value={DistanceUnit.Kilometers}
                />
                <Picker.Item
                  label={t(DistanceUnit.Miles)}
                  value={DistanceUnit.Miles}
                />
              </Picker>
            </YStack>
          </XStack>
        )}
      </YStack>
    </ThemedActionSheet>
  );
};

export default DistancePicker;
