import React from 'react';
import {Sheet<PERSON>anager, SheetProps} from 'react-native-actions-sheet';
import {useTheme, YStack} from 'tamagui';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {Picker} from '@react-native-picker/picker';
import {useTranslation} from 'react-i18next';
import {BurnedCaloriesUnit, useLocales} from '@/src/contexts/LocaleContext';
import useMutationUserSettings from '@/src/hooks/api/useMutationUserSettings';
import {Platform} from 'react-native';

const BurnedCaloriesPicker: React.FC<SheetProps<'burned_calories_picker'>> = ({
  sheetId,
}) => {
  const {t} = useTranslation();
  const {locales, setLocales} = useLocales();
  const {mutate} = useMutationUserSettings();
  const theme = useTheme();
  const color = theme.color.get();

  const onValueChange = async (newValue: BurnedCaloriesUnit) => {
    mutate({displayCalories: newValue});
    setLocales({
      ...locales,
      burnedCaloriesUnit: newValue,
    });
    await SheetManager.hide(sheetId);
  };

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack paddingVertical='$4'>
        <YStack>
          <Picker
            style={Platform.OS === 'android' ? {color: color} : undefined}
            selectedValue={locales.burnedCaloriesUnit}
            onValueChange={onValueChange}>
            <Picker.Item
              label={t(BurnedCaloriesUnit.Pizza)}
              value={BurnedCaloriesUnit.Pizza}
            />
            <Picker.Item
              label={t(BurnedCaloriesUnit.None)}
              value={BurnedCaloriesUnit.None}
            />
          </Picker>
        </YStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default BurnedCaloriesPicker;
