import React from 'react';
import {ScrollView, SheetProps} from 'react-native-actions-sheet';

import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import Survey from '../components/Survey';
import {YStack} from 'tamagui';
import {KeyboardAvoidingView, Platform} from 'react-native';

const SurveySheet: React.FC<SheetProps<'survey'>> = ({sheetId, payload}) => {
  return (
    <ThemedActionSheet sheetId={sheetId} keyboardHandlerEnabled={false}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'android' ? 'height' : undefined}>
        <YStack width='100%' height='100%'>
          <ScrollView
            keyboardShouldPersistTaps='handled'
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              flexGrow: 1,
            }}>
            <Survey
              surveyId={(payload as {surveyId: string}).surveyId}
              userId={(payload as {userId: string}).userId}
              onComplete={(payload as {onComplete: () => void}).onComplete}
            />
          </ScrollView>
        </YStack>
      </KeyboardAvoidingView>
    </ThemedActionSheet>
  );
};

export default SurveySheet;
