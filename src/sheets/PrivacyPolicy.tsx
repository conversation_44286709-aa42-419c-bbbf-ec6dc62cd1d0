import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import {SheetManager, SheetProps} from 'react-native-actions-sheet';
import {WebView} from 'react-native-webview';
import {Spinner, YStack, XStack, Button} from 'tamagui';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import I18nText from '../components/I18nText';
import logger from '@/src/utils/logger';

interface TermsOfUseProps extends SheetProps<'termsOfUse'> {
  payload?: {
    id?: string;
  };
}

const PrivacyPolicy: React.FC<TermsOfUseProps> = ({sheetId, payload}) => {
  const handleClose = () => SheetManager.hide(sheetId);

  // Use the provided ID or fallback to the sheetId
  const effectiveSheetId = payload?.id || sheetId;

  // Calculate 90% of screen height
  const screenHeight = Dimensions.get('window').height;
  const sheetHeight = screenHeight * 0.9;
  const webViewHeight = sheetHeight - 10;

  return (
    <ThemedActionSheet
      sheetId={effectiveSheetId}
      closeOnTouchBackdrop={false}
      containerStyle={{
        height: sheetHeight,
        maxHeight: sheetHeight,
      }}>
      <YStack height={webViewHeight}>
        <XStack
          height='$6'
          paddingHorizontal='$5'
          borderBottomWidth={StyleSheet.hairlineWidth}
          borderColor='$windowBackground'
          justifyContent='space-between'
          marginBottom={10}
          alignItems='center'>
          <I18nText fontSize={20} fontWeight='600' color='$primary'>
            Privacy Policy
          </I18nText>
          <Button
            chromeless
            onPress={handleClose}
            backgroundColor='transparent'>
            <I18nText
              fontSize={12}
              fontWeight='600'
              borderBottomWidth={1}
              color='$primary'>
              Close
            </I18nText>
          </Button>
        </XStack>

        {/* WebView Container */}
        <YStack flex={1}>
          <WebView
            source={{
              uri: 'https://www.gojoe.com/privacy_policy_app.html',
            }}
            style={{flex: 1}}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            startInLoadingState={true}
            originWhitelist={['*']}
            scrollEnabled={true}
            bounces={true}
            nestedScrollEnabled={true}
            androidHardwareAccelerationDisabled={false}
            injectedJavaScriptBeforeContentLoaded={`
              (function() {
                var style = document.createElement('style');
                style.type = 'text/css';
                style.appendChild(document.createTextNode(\`
                  body {
                    margin: 0 !important;
                    background-color: #ffffff !important;
                  }
                  .navbar,
                  .press-header,
                  .header,
                  .footer,
                  .cc-banner,
                  .footer-section,
                  .hero-heading,
                  #PopupSignupForm_0,
                  #intercom-container,
                  .intercom-lightweight-app,
                  .intercom-launcher-frame,
                  .intercom-launcher {
                    display: none !important;
                  }
                \`));
                document.head.appendChild(style);
              })();
            `}
            onError={({nativeEvent}) => {
              logger.warn('WebView error: ', nativeEvent);
            }}
            onHttpError={({nativeEvent}) => {
              logger.warn('WebView HTTP error: ', nativeEvent.statusCode);
            }}
            renderLoading={() => (
              <YStack flex={1} justifyContent='center' alignItems='center'>
                <Spinner size='large' color='$blue10' />
              </YStack>
            )}
          />
        </YStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default PrivacyPolicy;
