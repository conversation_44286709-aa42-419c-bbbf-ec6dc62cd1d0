import React from 'react';
import {SheetProps} from 'react-native-actions-sheet';
import {YStack} from 'tamagui';
import I18nText from '../components/I18nText';
import ThemedActionSheet from './ThemedActionSheet';

const RewardsOff: React.FC<SheetProps> = ({sheetId}) => {
  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack margin={24} minHeight='50%'>
        <I18nText fontWeight='700' marginBottom={12}>
          Unlock Rewards with Premium
        </I18nText>

        <I18nText lineHeight={24} marginBottom={8}>
          GoJoe rewards are a premium feature, available to Joe<PERSON> with a GoJoe
          Premium account. Premium turns your GoJoe activity into exciting
          rewards.
        </I18nText>

        <I18nText lineHeight={24} marginBottom={8}>
          Organisations can enhance this with earning boosts, increasing the
          points and rewards you can earn every month.
        </I18nText>

        <I18nText lineHeight={24} marginBottom={8}>
          Currently, you don’t have a Premium account or an organisation
          providing rewards or boosts.
        </I18nText>

        <I18nText lineHeight={24} marginBottom={8}>
          Speak to your organisation about setting up GoJoe Premium to start
          earning rewards.
        </I18nText>
      </YStack>
    </ThemedActionSheet>
  );
};

export default RewardsOff;
