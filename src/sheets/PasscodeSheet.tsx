import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {SheetManager, SheetProps} from 'react-native-actions-sheet';
import {Spin<PERSON>, YStack} from 'tamagui';
import {useRouter} from 'expo-router';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import I18nText from '@/src/components/I18nText';
import {StyledInput} from '@/src/components/UI/StyledInput';
import StyledButton from '@/src/components/UI/Button';
import {triggerHaptics} from '@/src/utils/haptics';
import logger from '@/src/utils/logger';

const MIN_CODE_LENGTH = 6;

const PasscodeSheet: React.FC<SheetProps<'passcode'>> = ({sheetId}) => {
  const {t} = useTranslation();
  const [code, setCode] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const handleSubmit = async () => {
    if (code.length < MIN_CODE_LENGTH || isSubmitting) {
      return;
    }

    await triggerHaptics();
    setIsSubmitting(true);

    try {
      // Close the sheet
      await SheetManager.hide(sheetId);

      // Navigate to the passcode modal screen with the code
      // Pass the code as a parameter to the passcode screen
      router.push({
        pathname: '/passcode',
        params: {code},
      });
    } catch (error) {
      logger.error('Error submitting passcode:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack padding='$5'>
        <I18nText fontWeight='600' fontSize={20} marginBottom='$4'>
          Enter Challenge Code
        </I18nText>

        <StyledInput
          fontSize={18}
          fontWeight='600'
          letterSpacing={4}
          placeholder={t('Enter the code')}
          clearButtonMode='always'
          autoFocus
          onChangeText={setCode}
          autoCapitalize='none'
        />

        <StyledButton
          marginTop='$4'
          disabled={code.length < MIN_CODE_LENGTH || isSubmitting}
          onPress={handleSubmit}
          iconAfter={isSubmitting ? <Spinner /> : undefined}>
          <I18nText color='$white1'>Submit code</I18nText>
        </StyledButton>
      </YStack>
    </ThemedActionSheet>
  );
};

export default PasscodeSheet;
