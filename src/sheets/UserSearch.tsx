import React, {useState, useCallback} from 'react';

import {Sheet<PERSON>anager, SheetProps} from 'react-native-actions-sheet';
import {XStack, YStack, Spinner} from 'tamagui';
import {Check, UserPlus, Users, Share} from '@tamagui/lucide-icons';
import {FlashList, ListRenderItem} from '@shopify/flash-list';
import {useTranslation} from 'react-i18next';

import useUserSearch from '@/src/hooks/api/useUserSearch';
import I18nText from '@/src/components/I18nText';
import {StyledInput} from '@/src/components/UI/StyledInput';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {triggerHaptics} from '@/src/utils/haptics';
import {SearchUserListDto} from '@gojoe/typescript-sdk';
import {KeyboardAvoidingView, Platform} from 'react-native';
import logger from '@/src/utils/logger';
import StyledText from '../components/UI/StyledText';
import StyledButton from '@/src/components/UI/Button';
import {
  getChallengeShareMessage,
  shareApp,
  shareInviteLink,
} from '@/src/utils/sharing';
import {useToastController} from '@tamagui/toast';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import segmentClient from '@/src/services/segment';
import useChallenge from '../hooks/api/useChallenge';

const UserSearchSheet: React.FC<SheetProps<'user_search'>> = ({
  sheetId,
  payload,
}) => {
  const {t} = useTranslation();
  const [search, setSearch] = useState('');
  const {data, isLoading} = useUserSearch(search, payload?.challengeId);
  const toast = useToastController();
  const safeAreaInsets = useSafeAreaInsets();
  const {data: challengeData, isLoading: isLoadingChallenge} = useChallenge(
    `${payload?.challengeId}`,
  );

  // Check if this sheet is being used for team member addition
  const isTeamInvite = !!(payload?.teamId && payload?.teamName);

  const handleClose = () => {
    return SheetManager.hide(sheetId);
  };

  const handleInvitePress = useCallback(async () => {
    if (!payload?.challengeId || !payload?.teamName) return;

    await triggerHaptics();

    // Track the team invite event with Segment
    segmentClient.track('Team Invite Initiated', {
      challengeId: payload.challengeId,
      challengeName: payload.challengeName,
      teamId: payload.teamId,
      teamName: payload.teamName,
      hasPasscode: !!payload.passcode,
      source: 'team_search',
    });

    try {
      // Create custom share message for team invites
      const customMessage = getChallengeShareMessage(
        challengeData,
        payload.teamName,
      );
      console.log('challengeData:', challengeData);
      await shareApp(customMessage);

      // Track successful team invite share
      segmentClient.track('Team Invite Shared', {
        challengeId: payload.challengeId,
        challengeName: payload.challengeName,
        teamId: payload.teamId,
        teamName: payload.teamName,
        hasPasscode: !!payload.passcode,
        source: 'team_search',
      });

      // Note: We don't show success toast because React Native's Share API doesn't tell us
      // if the user actually completed the share or just dismissed the dialog
    } catch (error) {
      logger.error('Failed to share challenge invitation:', error);

      // Track failed team invite share
      segmentClient.track('Team Invite Failed', {
        challengeId: payload.challengeId,
        challengeName: payload.challengeName,
        teamId: payload.teamId,
        teamName: payload.teamName,
        hasPasscode: !!payload.passcode,
        source: 'team_search',
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      toast.show(t('Share failed'), {
        message: t('Failed to share challenge invitation'),
        type: 'error',
      });
    }
  }, [payload, toast, t]);

  const renderItem: ListRenderItem<SearchUserListDto> = ({item}) => {
    if (!payload) return null;

    logger.debug(item);
    const isSelected = payload.selectedUsers?.includes(item.id);
    const isInTeam = !!item.team;

    const handleSelect = async () => {
      // Only allow selection if user is not in a team
      if (isInTeam) return;

      await triggerHaptics();
      payload.onSelect(item);
      await handleClose();
    };

    return (
      <XStack
        paddingHorizontal='$5'
        height={56}
        alignItems='center'
        justifyContent='space-between'
        onPress={handleSelect}
        opacity={isInTeam ? 0.5 : 1}>
        <XStack alignItems='center' gap='$3'>
          <UserAvatar
            user={{
              id: item.id,
              name: item.name,
              profilePicture: item.profilePicture,
            }}
            size={40}
            circular
          />
          <YStack>
            <StyledText
              fontWeight={isSelected ? '700' : '500'}
              color={isSelected ? '$primary' : '$color'}>
              {item.name}
            </StyledText>
            {isInTeam && (
              <XStack alignItems='center' gap='$1'>
                <Users size={12} color='$grey1' />
                <StyledText fontSize={12} color='$grey1'>
                  {item.team?.name}
                </StyledText>
              </XStack>
            )}
          </YStack>
        </XStack>
        {isSelected && <Check color='$primary' />}
        {!isInTeam && !isSelected && <UserPlus size={20} color='$primary' />}
      </XStack>
    );
  };

  return (
    <ThemedActionSheet
      sheetId={sheetId}
      gestureEnabled={true}
      safeAreaInsets={{
        ...safeAreaInsets,
        bottom: isTeamInvite ? 0 : safeAreaInsets.bottom,
      }}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'android' ? 64 : 0}>
        <YStack width='100%' height='100%'>
          <XStack
            justifyContent='space-between'
            alignItems='center'
            paddingHorizontal='$5'
            borderBottomWidth={1}
            borderBottomColor='$accentGrey'
            height={56}>
            <I18nText fontWeight='700' fontSize={16}>
              {payload?.title || 'Select User'}
            </I18nText>
            <I18nText fontWeight='500' color='$primary' onPress={handleClose}>
              Dismiss
            </I18nText>
          </XStack>
          <StyledInput
            placeholder={t('Search...')}
            marginTop={24}
            marginBottom={12}
            marginHorizontal={24}
            autoFocus={false}
            value={search}
            onChangeText={setSearch}
          />
          <YStack flex={1}>
            {isLoading || isLoadingChallenge ? (
              <Spinner marginTop='$5' />
            ) : (
              <FlashList
                data={data}
                renderItem={renderItem}
                estimatedItemSize={56}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                  paddingBottom: isTeamInvite ? 160 : 0, // Add padding when invite section is shown
                }}
                ListEmptyComponent={
                  <YStack
                    alignItems='center'
                    justifyContent='center'
                    paddingTop='$10'>
                    <UserPlus size={40} color='$grey1' />
                    <I18nText
                      fontWeight='500'
                      color='$grey1'
                      textAlign='center'
                      marginTop='$3'
                      paddingHorizontal='$5'>
                      {search
                        ? 'No users found matching your search'
                        : payload?.emptyStateText || 'Search for users'}
                    </I18nText>
                  </YStack>
                }
              />
            )}
          </YStack>

          {/* Sticky bottom invite section for team member addition */}
          {isTeamInvite && (
            <YStack
              position='absolute'
              bottom={0}
              left={0}
              right={0}
              backgroundColor='$primary'
              paddingHorizontal='$5'
              paddingVertical='$4'
              paddingBottom={safeAreaInsets.bottom}>
              <I18nText fontWeight='500' color='$white1' marginBottom='$3.5'>
                Or invite friends that aren't on GoJoe yet
              </I18nText>
              <StyledButton
                variant='secondary'
                backgroundColor='$white1'
                borderColor='$white'
                onPress={handleInvitePress}>
                <Share size={16} color='$primary' />
                <I18nText
                  color='$primary'
                  fontWeight='500'
                  textAlign='center'
                  marginLeft='$2'>
                  Invite
                </I18nText>
              </StyledButton>
            </YStack>
          )}
        </YStack>
      </KeyboardAvoidingView>
    </ThemedActionSheet>
  );
};

export default UserSearchSheet;
