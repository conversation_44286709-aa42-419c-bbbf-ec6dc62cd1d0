import React from 'react';
import {SheetProps} from 'react-native-actions-sheet';
import {YStack} from 'tamagui';
import {useTranslation} from 'react-i18next';
import StyledText from '../components/UI/StyledText';
import ThemedActionSheet from './ThemedActionSheet';
import I18nText from '../components/I18nText';

const RewardsMultiplier: React.FC<SheetProps<'multiplier'>> = ({
  sheetId,
  payload,
}) => {
  const {t} = useTranslation();

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack margin={24} minHeight='50%'>
        <StyledText fontWeight='700' marginBottom={12}>
          {t('{{bust}} Earning Boost', {
            bust: payload ? `x${payload.multiplier}` : '',
          })}
        </StyledText>

        <StyledText lineHeight={24} marginBottom={8}>
          {t(
            'Great news! {{name}} has activated an earning boost of x{{multiplier}}, which means you earn {{multiplier}}x the reward points compared to the standard GoJoe Premium allowance. That’s more points, more rewards, and more motivation to stay healthy and engaged.',
            {
              name: payload?.organisation,
              multiplier: payload?.multiplier,
            },
          )}
        </StyledText>

        <I18nText lineHeight={24} marginBottom={8}>
          This generous boost is designed to supercharge your efforts, making
          every action count even more. 🚀
        </I18nText>
      </YStack>
    </ThemedActionSheet>
  );
};

export default RewardsMultiplier;
