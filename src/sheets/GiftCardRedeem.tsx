import React from 'react';
import {She<PERSON><PERSON><PERSON><PERSON>, SheetProps} from 'react-native-actions-sheet';
import {<PERSON>, Spinner, XStack, YStack} from 'tamagui';
import {useTranslation} from 'react-i18next';
import {GiftCardDto, GiftCardOptionsDto} from '@gojoe/typescript-sdk';

import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import I18nText from '@/src/components/I18nText';
import StyledText from '@/src/components/UI/StyledText';
import StyledButton from '@/src/components/UI/Button';
import {getCurrencySymbols} from '@/src/utils/currency';
import {triggerHaptics} from '@/src/utils/haptics';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useGiftCardRedemption} from '../hooks/api/useGiftCardRedemption';
import {useToastController} from '@tamagui/toast';
import {useRouter} from 'expo-router';

interface GiftCardRedeemPayload {
  giftCard: GiftCardDto;
  option: GiftCardOptionsDto;
}

const GiftCardRedeemSheet: React.FC<SheetProps<'gift_card_redeem'>> = ({
  sheetId,
  payload,
}) => {
  const {mutateAsync} = useGiftCardRedemption();
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const [isRedeeming, setIsRedeeming] = React.useState(false);
  const toast = useToastController();
  const router = useRouter();

  if (!payload) {
    return null;
  }

  const {giftCard, option} = payload as GiftCardRedeemPayload;

  const handleClose = async () => {
    await triggerHaptics();
    await SheetManager.hide(sheetId);
  };

  const handleRedeem = async () => {
    await triggerHaptics();
    setIsRedeeming(true);
    await mutateAsync({giftCardOptionId: option.id});

    setIsRedeeming(false);
    await SheetManager.hide(sheetId);
    router.back();
    setTimeout(async () => {
      toast.show(t('{{name}} redeemed!', {name: giftCard.name}), {
        duration: 5000,
        type: 'success',
      });
    }, 400);
  };

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack
        padding='$5'
        width={'100%'}
        height={'100%'}
        paddingBottom={bottom + 20}>
        <YStack alignItems='center' marginBottom='$4'>
          <Image
            source={{uri: giftCard.image}}
            width={120}
            height={120}
            borderRadius={8}
          />
        </YStack>

        <I18nText fontWeight='700' fontSize={24} textAlign='center'>
          Confirm Redemption
        </I18nText>

        <StyledText textAlign='center' color='$grey1'>
          {t(
            'You are about to redeem a {{value}} voucher for {{points}} points',
            {
              value: `${getCurrencySymbols(giftCard.currency)}${Math.round(
                option.value / 100,
              )}`,
              points: option.points,
            },
          )}
        </StyledText>

        <YStack space='$2' marginTop='$4'>
          <XStack justifyContent='space-between'>
            <I18nText fontWeight='500'>Voucher</I18nText>
            <StyledText fontWeight='700'>{giftCard.name}</StyledText>
          </XStack>

          <XStack justifyContent='space-between'>
            <I18nText fontWeight='500'>Value</I18nText>
            <StyledText fontWeight='700'>
              {`${getCurrencySymbols(giftCard.currency)}${Math.round(
                option.value / 100,
              )}`}
            </StyledText>
          </XStack>

          <XStack justifyContent='space-between'>
            <I18nText fontWeight='500'>Points Cost</I18nText>
            <XStack alignItems='center' gap='$2'>
              <StyledText fontWeight='700' color='#FFCE1F'>
                {option.points}
              </StyledText>
              <Image
                source={require('@/assets/images/reword_circle_3.png')}
                width={18}
                height={18}
                tintColor='#FFCE1F'
              />
            </XStack>
          </XStack>
        </YStack>

        <StyledText
          marginTop='$4'
          fontSize={12}
          color='$grey1'
          textAlign='center'>
          {t(
            "You'll get the voucher details on your e-mail and you'll see your redeemed vouchers in history.",
          )}
        </StyledText>

        <YStack marginTop='auto' gap='$3'>
          <StyledButton
            onPress={handleRedeem}
            disabled={isRedeeming}
            loading={isRedeeming}>
            <I18nText color='$white1'>
              {isRedeeming ? 'Processing...' : 'Confirm Redemption'}
            </I18nText>
          </StyledButton>

          <StyledButton variant='outlined' onPress={handleClose}>
            <I18nText>Cancel</I18nText>
          </StyledButton>
        </YStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default GiftCardRedeemSheet;
