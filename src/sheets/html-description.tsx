import React from 'react';
import {YStack} from 'tamagui';
import {ScrollView, SheetProps} from 'react-native-actions-sheet';
import RenderHtml from 'react-native-render-html';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {Dimensions} from 'react-native';

const width = Dimensions.get('window').width;

const HtmlDescription: React.FC<SheetProps<'html_description'>> = ({
  sheetId,
  payload,
}) => {
  if (!payload || !payload.description) {
    return null;
  }
  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack width='100%' height='100%' paddingTop='$5'>
        <ScrollView showsVerticalScrollIndicator={false}>
          <YStack flex={1} padding='$5'>
            <RenderHtml
              contentWidth={width}
              source={{html: payload.description}}
            />
          </YStack>
        </ScrollView>
      </YStack>
    </ThemedActionSheet>
  );
};
export default HtmlDescription;
