import React from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, SheetP<PERSON>} from 'react-native-actions-sheet';
import {useTranslation} from 'react-i18next';
import {Language, languages} from '@/src/locales';
import {XStack, YStack} from 'tamagui';
import {Check} from '@tamagui/lucide-icons';
import {StyledInput} from '@/src/components/UI/StyledInput';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {useLocales} from '@/src/contexts/LocaleContext';
import I18nText from '@/src/components/I18nText';
import StyledText from '@/src/components/UI/StyledText';
import useMutationUserSettings from '@/src/hooks/api/useMutationUserSettings';
import {triggerHaptics} from '@/src/utils/haptics';
import {KeyboardAvoidingView, Platform} from 'react-native';
import {FlashList} from 'react-native-actions-sheet/dist/src/views/FlashList';

const Languages: React.FC<SheetProps<'languages'>> = ({sheetId}) => {
  const [q, setQ] = React.useState('');
  const {t} = useTranslation();
  const {locales, setLocales} = useLocales();
  const {mutate} = useMutationUserSettings();

  const renderItem = ({item}: {item: Language}) => {
    const handleOnPress = async () => {
      setLocales({
        ...locales,
        languageTag: item.code,
        languageCode: item.code.split(/[_-]/)[0],
      });
      mutate({language: item.code});
      await triggerHaptics();
      await SheetManager.hide(sheetId);
    };
    const isSelected = locales.languageTag === item.code;
    return (
      <XStack
        gap='$2'
        paddingHorizontal='$5'
        height='$4'
        alignItems='center'
        onPress={handleOnPress}>
        <StyledText fontSize={18}>{item.flag}</StyledText>
        <I18nText
          flex={1}
          fontWeight={isSelected ? '700' : '500'}
          color={isSelected ? '$primary' : '$color'}>
          {item.name}
        </I18nText>

        {isSelected && <Check color={'$primary'} />}
      </XStack>
    );
  };
  return (
    <ThemedActionSheet sheetId={sheetId}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'android' ? 64 : 0}>
        <YStack paddingVertical='$4' width='100%' height='100%'>
          <YStack paddingHorizontal='$5' paddingVertical='$3'>
            <StyledInput
              placeholder={t('Search...')}
              onChangeText={setQ}
              clearButtonMode='always'
            />
          </YStack>
          <FlashList
            keyboardDismissMode='on-drag'
            keyboardShouldPersistTaps='handled'
            renderItem={renderItem}
            data={
              q
                ? languages.filter((c) =>
                    c.name.toLowerCase().includes(q.toLowerCase()),
                  )
                : languages
            }
            estimatedItemSize={48}
            showsVerticalScrollIndicator={false}
          />
        </YStack>
      </KeyboardAvoidingView>
    </ThemedActionSheet>
  );
};

export default Languages;
