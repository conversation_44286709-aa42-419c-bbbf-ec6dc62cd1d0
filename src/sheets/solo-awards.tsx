import React from 'react';
import {ListRenderItemInfo} from 'react-native';
import {Spinner, XStack, YStack} from 'tamagui';
import {FlatList, SheetProps} from 'react-native-actions-sheet';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {UserAwardsDto, UsersAwardsDto} from '@gojoe/typescript-sdk';
import StyledText from '@/src/components/UI/StyledText';
import AwardValue from '@/src/components/AwardValue';
import AwardHeaderComponent from '@/src/components/AwardHeader';
import {useChallengeSoloAward} from '@/src/hooks/api/useChallengeSoloAward';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {SPACE_BOTTOM} from '../constants/Constants';

const SurveySheet: React.FC<SheetProps<'solo_awards'>> = ({
  sheetId,
  payload,
}) => {
  return (
    <ThemedActionSheet sheetId={sheetId}>
      {payload && (
        <YStack width='100%' height='100%'>
          <Content
            challengeId={payload.challengeId}
            award={payload.award}
            challengeStartDate={payload.challengeStartDate}
          />
        </YStack>
      )}
    </ThemedActionSheet>
  );
};

const ItemSeparatorComponent = () => <YStack height={4} />;

const Content: React.FC<{
  challengeId: string;
  award: UsersAwardsDto;
  challengeStartDate: string;
}> = ({challengeId, award, challengeStartDate}) => {
  const {data, isLoading} = useChallengeSoloAward(challengeId, award.id);
  const {bottom} = useSafeAreaInsets();

  if (isLoading) {
    return <Spinner position='absolute' top={100} alignSelf='center' />;
  }

  if (!data) {
    return null;
  }

  const renderItem = ({item, index}: ListRenderItemInfo<UserAwardsDto>) => {
    return (
      <XStack
        height={56}
        borderRadius={8}
        gap={8}
        alignItems='center'
        paddingHorizontal='$3.5'
        marginHorizontal='$5'
        borderWidth={index === 0 ? 1 : 0}
        borderColor={index === 0 ? '$borderHighlight' : undefined}
        backgroundColor={index === 0 ? '$backgroundHighlight' : '$background'}>
        <StyledText fontWeight='700' fontSize={12}>
          {item.position}
        </StyledText>
        <UserAvatar user={item.user} size={36} circular />
        <StyledText numberOfLines={1} flex={1} fontSize={12} fontWeight='500'>
          {item.user.name}
        </StyledText>
        <AwardValue
          name={award.name}
          value={item.value}
          challengeStartDate={challengeStartDate}
        />
      </XStack>
    );
  };
  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      ListHeaderComponent={<AwardHeaderComponent award={award} label='solo' />}
      ListFooterComponent={<YStack height={bottom + SPACE_BOTTOM * 2} />}
      ItemSeparatorComponent={ItemSeparatorComponent}
      showsVerticalScrollIndicator={false}
    />
  );
};

export default SurveySheet;
