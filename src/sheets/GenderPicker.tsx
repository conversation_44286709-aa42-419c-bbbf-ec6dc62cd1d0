import React, {useState} from 'react';
import {Sheet<PERSON><PERSON><PERSON>, SheetProps} from 'react-native-actions-sheet';
import {useTheme, XStack, YStack} from 'tamagui';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {Platform, StyleSheet} from 'react-native';
import I18nText from '@/src/components/I18nText';
import Button from '@/src/components/UI/Button';
import {Picker} from '@react-native-picker/picker';
import {useTranslation} from 'react-i18next';
import {useGenders} from '@/src/hooks/api/useGenders';

const GenderPicker: React.FC<SheetProps<'genderPicker'>> = ({
  sheetId,
  payload,
}) => {
  const {t} = useTranslation();
  const [value, setValue] = useState(payload?.gender);
  const {data} = useGenders();
  const theme = useTheme();
  const color = theme.color?.get() ?? '#000000';

  const handleClose = () => {
    if (payload?.onSelect && value) {
      payload.onSelect(value);
    }
  };

  const handlePressCancel = () => SheetManager.hide(sheetId);
  return (
    <ThemedActionSheet
      sheetId={sheetId}
      gestureEnabled={false}
      onClose={handleClose}>
      <YStack paddingVertical='$4'>
        <XStack
          height='$6'
          paddingHorizontal='$5'
          borderBottomWidth={StyleSheet.hairlineWidth}
          borderColor='$windowBackground'
          justifyContent='space-between'
          alignItems='center'>
          <I18nText fontSize={20} fontWeight='600'>
            {payload?.title}
          </I18nText>
          <Button
            chromeless
            onPress={handlePressCancel}
            backgroundColor='transparent'>
            <I18nText fontSize={12} fontWeight='600' borderBottomWidth={1}>
              Done
            </I18nText>
          </Button>
        </XStack>
        <YStack paddingHorizontal='$4'>
          <Picker
            style={{color: color}}
            itemStyle={{
              color: color,
            }}
            dropdownIconColor={color}
            selectedValue={value ? value.toLowerCase() : undefined}
            onValueChange={setValue}>
            <Picker.Item
              label={Platform.OS === 'android' ? t('Select gender') : ''}
              value=''
            />
            {data.map((item) => (
              <Picker.Item
                key={item.key}
                label={t(item.value)}
                value={item.key}
              />
            ))}
          </Picker>
        </YStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default GenderPicker;
