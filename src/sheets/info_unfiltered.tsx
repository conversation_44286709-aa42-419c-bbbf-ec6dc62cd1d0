import React from 'react';
import {YStack} from 'tamagui';
import {SheetProps} from 'react-native-actions-sheet';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import I18nText from '@/src/components/I18nText';

const InfoUnfilteredSheet: React.FC<SheetProps<'info_unfiltered'>> = ({
  sheetId,
}) => {
  return (
    <ThemedActionSheet
      sheetId={sheetId}
      snapPoints={[50]}
      gestureEnabled={true}>
      <YStack height='100%' padding='$5'>
        <I18nText fontWeight='bold' fontSize={16} marginTop='$5'>
          Unfiltered Leaderboards
        </I18nText>
        <I18nText marginTop='$5' lineHeight={20} color='$grey1'>
          To keep the challenge fun and fair, we have controls, including GoJoe
          referees. Referees can filter <PERSON><PERSON> out of the main leaderboard to an
          unfiltered version if something didn’t look right or just to cool
          things down and reduce excessive exercise. Don’t worry if you're
          filtered out, you can still toggle to see the unfiltered leaderboard.
        </I18nText>
      </YStack>
    </ThemedActionSheet>
  );
};

export default InfoUnfilteredSheet;
