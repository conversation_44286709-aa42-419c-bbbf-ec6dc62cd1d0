import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON><PERSON>ana<PERSON>, SheetProps} from 'react-native-actions-sheet';
import {XStack, YStack} from 'tamagui';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {Platform, StyleSheet} from 'react-native';
import I18nText from '@/src/components/I18nText';
import Button from '@/src/components/UI/Button';
import RNDateTimePicker, {
  DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import {useTheme} from '../contexts/ThemeContext';
import {useLocales} from '../contexts/LocaleContext';
import {DateTime} from 'luxon';

const DateTimePicker: React.FC<SheetProps<'dateTimePicker'>> = ({
  sheetId,
  payload,
}) => {
  const {themeMode} = useTheme();
  const {locales} = useLocales();
  const [state, setState] = useState({
    date: new Date(),
    isDefault: true,
    dateError: false,
  });

  useEffect(() => {
    if (payload && state.isDefault) {
      let initialDate = payload.date || new Date();
      let hasDateError = false;

      // Ensure the initial date respects the minimum date constraint
      if (payload.minimumDate && initialDate < payload.minimumDate) {
        initialDate = payload.minimumDate;
        hasDateError = true;
      }

      // Ensure the initial date respects the maximum date constraint
      const maxDate = payload.maximumDate || new Date();
      if (initialDate > maxDate) {
        initialDate = maxDate;
        hasDateError = true;
      }

      setState({
        ...state,
        date: initialDate,
        isDefault: false,
        dateError: hasDateError,
      });

      // If there was an error, clear it after a brief delay
      if (hasDateError && Platform.OS === 'ios') {
        setTimeout(() => {
          setState((prevState) => ({
            ...prevState,
            dateError: false,
          }));
        }, 1000);
      }
    }
  }, [payload, state]);

  const handleOnChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    if (selectedDate) {
      // Check if the selected date is within the allowed range
      if (payload?.minimumDate && selectedDate < payload.minimumDate) {
        setState({
          ...state,
          date: payload.minimumDate,
          isDefault: false,
          dateError: true, // Set error flag
        });

        // Show visual feedback for iOS users
        if (Platform.OS === 'ios') {
          // Briefly flash the error state, then reset to the minimum date
          setTimeout(() => {
            setState((prevState) => ({
              ...prevState,
              dateError: false,
            }));
          }, 500);
        }
      } else {
        // Store the selected date in state
        setState({
          ...state,
          date: selectedDate,
          isDefault: false,
          dateError: false,
        });
      }
    }
    if (event.type === 'set' && Platform.OS === 'android') {
      handleClose();
      SheetManager.hide(sheetId);
    } else if (event.type === 'dismissed') {
      SheetManager.hide(sheetId);
    }
  };

  const handleClose = () => {
    if (payload?.onSelect && !state.isDefault) {
      // Final validation before returning the date
      let finalDate = state.date;

      // Ensure the date is not before the minimum date
      if (payload?.minimumDate && finalDate < payload.minimumDate) {
        finalDate = payload.minimumDate;
      }

      // Ensure the date is not after the maximum date
      const maxDate = payload?.maximumDate || new Date();
      if (finalDate > maxDate) {
        finalDate = maxDate;
      }

      // Ensure the date is in the user's timezone
      // This is important because the native date picker might use the device's timezone
      const dateInUserTZ = DateTime.fromJSDate(finalDate)
        .setZone(locales.timezone)
        .toJSDate();

      payload.onSelect(dateInUserTZ);
    }
  };

  const handlePressCancel = () => SheetManager.hide(sheetId);
  return (
    <ThemedActionSheet
      sheetId={sheetId}
      gestureEnabled={false}
      onClose={handleClose}>
      <YStack paddingVertical='$4'>
        <XStack
          height='$6'
          paddingHorizontal='$5'
          borderBottomWidth={StyleSheet.hairlineWidth}
          borderColor='$windowBackground'
          justifyContent='space-between'
          alignItems='center'>
          <I18nText fontSize={20} fontWeight='600'>
            {payload?.title}
          </I18nText>
          <Button
            chromeless
            onPress={handlePressCancel}
            backgroundColor='transparent'>
            <I18nText fontSize={12} fontWeight='600' borderBottomWidth={1}>
              Done
            </I18nText>
          </Button>
        </XStack>
        <YStack justifyContent='center' alignItems='center'>
          <RNDateTimePicker
            themeVariant={themeMode === 'dark' ? 'dark' : 'light'}
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            mode={payload?.mode || 'date'}
            value={state.date}
            onChange={handleOnChange}
            maximumDate={payload?.maximumDate || new Date()}
            minimumDate={payload?.minimumDate}
          />

          {/* Error message for date restrictions */}
          {state.dateError && (
            <I18nText color='$red' marginTop='$2'>
              Date must be within the last 48 hours
            </I18nText>
          )}

          {/* Always show the date restriction information */}
          <I18nText fontSize={12} color='$grey1' marginTop='$2'>
            You can only log activities from the last 48 hours
          </I18nText>
        </YStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default DateTimePicker;
