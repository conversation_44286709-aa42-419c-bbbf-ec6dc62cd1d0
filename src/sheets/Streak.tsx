import React from 'react';
import {Sheet<PERSON>anager, SheetProps} from 'react-native-actions-sheet';
import {Button, Image, YStack} from 'tamagui';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';
import {Trans} from 'react-i18next';
import StyledText from '@/src/components/UI/StyledText';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import StyledButton from '../components/UI/Button';

const StreakSheet: React.FC<SheetProps<'streak'>> = ({sheetId, payload}) => {
  const {top, bottom} = useSafeAreaInsets();
  const handleClose = async () => {
    await triggerHaptics();
    await SheetManager.hide(sheetId);
  };

  return (
    <ThemedActionSheet
      sheetId={sheetId}
      useBottomSafeAreaPadding={false}
      safeAreaInsets={{top: top, left: 0, right: 0, bottom: 0}}>
      <YStack
        width='100%'
        position='relative'
        height='100%'
        backgroundColor='$purple'>
        <YStack flex={1} paddingHorizontal='$5'>
          <Image
            alignSelf='center'
            position='absolute'
            top={top}
            width={342}
            height={180}
            objectFit='cover'
            source={require('@/assets/images/party_bg.png')}
            borderTopLeftRadius={24}
            borderTopRightRadius={24}
          />
          <StyledText
            fontSize={48}
            fontWeight='600'
            color='$white'
            width='90%'
            marginTop='40%'>
            <Trans
              i18nKey="You're on a <b>{{count}}-day</b> GoJoe streak"
              components={{
                b: (
                  <I18nText
                    color='$yellow'
                    fontWeight='700'
                    fontStyle='italic'
                  />
                ),
              }}
              count={payload ? payload.count : 0}
            />
          </StyledText>
          <I18nText
            fontWeight='500'
            color='$grey2'
            marginTop='$3.5'
            lineHeight={19}>
            Keep your streak going by completing activities every day. Your
            streak helps you build consistency in your fitness journey.
          </I18nText>
        </YStack>

        <YStack
          flex={1}
          paddingHorizontal='$5'
          justifyContent='flex-end'
          paddingBottom={bottom}>
          <Image
            alignSelf='center'
            position='absolute'
            top={top}
            width={345}
            height={216}
            objectFit='contain'
            source={require('@/assets/images/streak_calendar_bg.png')}
            borderTopLeftRadius={24}
            borderTopRightRadius={24}
          />
          <StyledButton
            onPress={handleClose}
            backgroundColor='$background'
            color='$primary'
            marginTop='$4'
            marginBottom='$4'>
            <I18nText color='$color' fontWeight='600'>
              Close
            </I18nText>
          </StyledButton>
        </YStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default StreakSheet;
