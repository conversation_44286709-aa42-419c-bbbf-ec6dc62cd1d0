import React, {useCallback} from 'react';
import {FlashList} from 'react-native-actions-sheet/dist/src/views/FlashList';
import {ScrollView, SheetManager, SheetProps} from 'react-native-actions-sheet';
import {XStack, YStack} from 'tamagui';
import {PostReactionDto} from '@gojoe/typescript-sdk';

import usePostReactionUserList from '../hooks/api/usePostReactionUserList';
import ThemedActionSheet from './ThemedActionSheet';
import UserAvatar from '../components/UI/Avatar/UserAvatar';
import StyledText from '../components/UI/StyledText';

const PostReactionUserListSheet: React.FC<
  SheetProps<'post_reaction_user_list'>
> = ({sheetId, payload}) => {
  const {data, isRefetching, refetch, fetchNextPage, isFetchingNextPage} =
    usePostReactionUserList((payload as {postId: string}).postId);

  const onEndReached = useCallback(() => {
    if (!isFetchingNextPage) {
      return fetchNextPage();
    }
  }, [fetchNextPage, isFetchingNextPage]);

  const renderItem = ({item}: {item: PostReactionDto}) => {
    const onPress = async () => {
      await SheetManager.hide(sheetId);
      //await navigateToProfile(item.user.id);
    };
    return (
      <XStack flex={1} alignItems='center' gap={8} onPress={onPress}>
        <UserAvatar user={item.user} size={36} circular />
        <StyledText fontSize={14} fontWeight={700} color='#2F3542'>
          {item.user.name}
        </StyledText>
      </XStack>
    );
  };

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack width='100%' height='100%'>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            flexGrow: 1,
          }}>
          <FlashList
            data={data}
            refreshing={isRefetching}
            onRefresh={refetch}
            renderItem={renderItem}
            onEndReached={onEndReached}
            estimatedItemSize={56}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              paddingVertical: 24,
              paddingHorizontal: 24,
            }}
          />
        </ScrollView>
      </YStack>
    </ThemedActionSheet>
  );
};

export default PostReactionUserListSheet;
