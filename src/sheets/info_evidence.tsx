import React from 'react';
import {YStack} from 'tamagui';
import {SheetManager, SheetProps} from 'react-native-actions-sheet';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import I18nText from '@/src/components/I18nText';
import {InfoOutlineIcon} from '../components/GoJoeIcon';
import StyledButton from '../components/UI/Button';

const InfoEvidencedSheet: React.FC<SheetProps<'info_evidence'>> = ({
  sheetId,
}) => {
  const handleUnderstood = () => SheetManager.hide(sheetId);
  return (
    <ThemedActionSheet
      sheetId={sheetId}
      snapPoints={[50]}
      gestureEnabled={true}>
      <YStack height='100%' padding='$5'>
        <InfoOutlineIcon size={56} alignSelf='center' marginVertical={'$5'} />
        <I18nText
          fontWeight='500'
          color='$grey1'
          textAlign='center'
          marginHorizontal={'$5'}>
          It is recommended to support your manual input with evidence (visible
          only to <PERSON><PERSON>oe and not publicly posted). If another joe flags your
          activity for review, the GoJoe referee will take a look.
        </I18nText>
        <I18nText
          fontWeight='500'
          color='$grey1'
          textAlign='center'
          marginTop={'$5'}
          marginHorizontal={'$5'}>
          If the referee smells a rat or feels there isn't enough supporting
          evidence, the referee may strike the activity off or ask for more
          information.
        </I18nText>
        <StyledButton onPress={handleUnderstood} marginTop={'$5'}>
          <I18nText color='$white1'>Understood</I18nText>
        </StyledButton>
      </YStack>
    </ThemedActionSheet>
  );
};

export default InfoEvidencedSheet;
