import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, TextInput, StyleSheet} from 'react-native';
import {Sheet<PERSON>anager, SheetProps} from 'react-native-actions-sheet';
import {AnimatePresence, XStack, YStack} from 'tamagui';
import {PostListDto} from '@gojoe/typescript-sdk';
import {Flag, Trash2, X} from '@tamagui/lucide-icons';
import {useToastController} from '@tamagui/toast';

import useMutationDeletePost from '../hooks/api/useMutationDeletePost';
import {useSession} from '../contexts/SessionContext';
import ThemedActionSheet from './ThemedActionSheet';
import StyledText from '../components/UI/StyledText';
import I18nText from '@/src/components/I18nText';
import useMutationReportPost from '../hooks/api/useMutationReportPost';

const PostMenu: React.FC<SheetProps<'post_menu'>> = ({sheetId, payload}) => {
  const {user} = useSession();
  const {t} = useTranslation();
  const toast = useToastController();
  const {post, onSuccessReportMutate} = payload as {
    post: PostListDto;
    onSuccessReportMutate: () => Promise<[void, void, void]>;
  };
  const [flagging, setFlagging] = useState(false);
  const [reason, setReason] = useState('');
  const [error, setError] = useState(false);

  const {mutate} = useMutationDeletePost(post.id);
  const {mutate: report} = useMutationReportPost(
    post?.id,
    reason,
    onSuccessReportMutate,
  );

  const onPressDelete = () => {
    SheetManager.hide(sheetId);

    const isActivity = !!post?.activity;
    const itemType = isActivity ? 'activity' : 'post';

    Alert.alert(
      t(`Delete ${itemType}`),
      t(`Are you sure you want to delete this ${itemType}?`)!,
      [
        {
          text: t('No')!,
          style: 'cancel',
        },
        {
          text: t('YES')!,
          style: 'destructive',
          onPress: () => {
            mutate();
            // Show success toast after deletion
            toast.show(
              t(
                `${itemType.charAt(0).toUpperCase() + itemType.slice(1)} deleted successfully`,
              ),
              {
                type: 'success',
              },
            );
          },
        },
      ],
      {cancelable: false},
    );
  };

  const onPressFlag = async () => {
    if (reason.length < 20) {
      setError(true);
      return;
    }

    await SheetManager.hide(sheetId);

    return report();
  };

  const handleCancel = async () => {
    await SheetManager.hide(sheetId);
  };

  if (!user) {
    return null;
  }

  if (flagging) {
    return (
      <ThemedActionSheet sheetId={sheetId}>
        <YStack padding='$5' paddingTop='$7'>
          <I18nText
            fontSize={16}
            fontWeight={700}
            color='#2F3542'
            marginBottom='$1.5'>
            Why are you flagging this {!!post?.activity ? 'activity' : 'post'}?
          </I18nText>

          <StyledText fontSize={12} fontWeight={400} color='#6E747E'>
            {t(
              'Provide specific details - the more the better: include supporting information and any specific details that exhibit issues.',
            )}
          </StyledText>

          <TextInput
            style={styles.inputStyle}
            autoComplete='off'
            value={reason}
            onChangeText={(text) => {
              if (error) {
                setError(false);
              }
              setReason(text);
            }}
            placeholder={t('Min. 20 characters')!}
            numberOfLines={5}
            multiline={true}
            textAlignVertical='top'
          />

          <YStack flex={1}>
            <AnimatePresence>
              {error && (
                <YStack
                  width='100%'
                  backgroundColor='#B54936'
                  animation='quick'
                  enterStyle={{
                    opacity: 0,
                    y: 100,
                  }}
                  exitStyle={{
                    opacity: 0,
                    y: 100,
                  }}
                  position='absolute'
                  top={8}
                  opacity={1}
                  padding='$3.5'
                  borderRadius={4}>
                  <I18nText color='#FFF'>Enter min. 20 characters</I18nText>
                </YStack>
              )}
            </AnimatePresence>
          </YStack>

          <XStack
            alignItems='center'
            justifyContent='space-between'
            marginTop='$12'>
            <XStack onPress={handleCancel} alignItems='center'>
              <X size={16} color='#8A8A8A' />
              <I18nText
                color='#8A8A8A'
                textDecorationColor='#8A8A8A'
                textDecorationLine='underline'
                marginLeft='$1'>
                Cancel
              </I18nText>
            </XStack>
            <XStack
              backgroundColor='#CA3D2A'
              padding='$3.5'
              borderRadius={4}
              onPress={onPressFlag}>
              <I18nText color='#FFF'>
                Flag {!!post?.activity ? 'activity' : 'post'}
              </I18nText>
            </XStack>
          </XStack>
        </YStack>
      </ThemedActionSheet>
    );
  }

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack padding='$5'>
        {post?.user?.id !== user?.id && !post?.business && (
          <XStack
            height={44}
            alignItems='center'
            onPress={() => setFlagging(true)}>
            <Flag size={20} color='#C43F2D' />
            <I18nText
              fontSize='$4'
              fontWeight='$5'
              color='#C43F2D'
              marginLeft='$2'>
              {post?.activity ? 'Flag activity' : 'Flag post'}
            </I18nText>
          </XStack>
        )}

        {post?.user?.id === user?.id && !post?.business && (
          <XStack height={44} alignItems='center' onPress={onPressDelete}>
            <Trash2 size={20} color='#C43F2D' />
            <I18nText
              fontSize='$4'
              fontWeight='$5'
              color='#C43F2D'
              marginLeft='$2'>
              Delete
            </I18nText>
          </XStack>
        )}
        <XStack height={44} alignItems='center' onPress={handleCancel}>
          <X size={20} color='#2F3542' />
          <I18nText fontSize='$4' fontWeight='$5' marginLeft='$2'>
            Cancel
          </I18nText>
        </XStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default PostMenu;

const styles = StyleSheet.create({
  inputStyle: {
    marginTop: 24,
    borderColor: '#DFE4EA',
    borderWidth: 1,
    backgroundColor: '#FFF',
    borderRadius: 8,
    minHeight: 125,
    padding: 8,
  },
});
