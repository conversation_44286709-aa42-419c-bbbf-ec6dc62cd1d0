import React from 'react';
import {KeyboardAvoidingView, Platform} from 'react-native';
import {SheetProps} from 'react-native-actions-sheet';
import {YStack} from 'tamagui';

import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import FindFriends from '../components/FindFriends';

const FindFriendsSheet: React.FC<SheetProps<'find_friends'>> = ({sheetId}) => {
  return (
    <ThemedActionSheet sheetId={sheetId}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'android' ? 'height' : undefined}>
        <YStack width='100%' height='100%'>
          <FindFriends />
        </YStack>
      </KeyboardAvoidingView>
    </ThemedActionSheet>
  );
};

export default FindFriendsSheet;
