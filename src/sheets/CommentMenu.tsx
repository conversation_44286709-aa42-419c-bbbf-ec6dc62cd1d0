import React from 'react';
import {Sheet<PERSON><PERSON>ger, SheetProps} from 'react-native-actions-sheet';
import {XStack, YStack} from 'tamagui';

import useMutationDeleteComment from '../hooks/api/useMutationDeleteComment';
import ThemedActionSheet from './ThemedActionSheet';
import I18nText from '@/src/components/I18nText';

const CommentMenu: React.FC<SheetProps<'comment_menu'>> = ({
  sheetId,
  payload,
}) => {
  const {commentId, postId, canDelete, onSuccessMutate, invalidateQueries} =
    payload as {
      businessId: string;
      commentId: string;
      postId: string;
      canDelete: boolean;
      onSuccessMutate: (update: any) => Promise<any>;
      invalidateQueries?: () => void;
    };

  const {mutateAsync, isPending} = useMutationDeleteComment(
    postId,
    commentId,
    onSuccessMutate,
    invalidateQueries,
  );

  const handleDelete = async () => {
    if (isPending) {
      return null;
    }
    try {
      await mutateAsync();
    } catch (error) {
      console.error('Error deleting comment:', error);
    } finally {
      await SheetManager.hide(sheetId);
    }
  };

  const handleCancel = async () => {
    await SheetManager.hide(sheetId);
  };

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack padding='$5'>
        {canDelete && (
          <XStack height={44} alignItems='center' onPress={handleDelete}>
            <I18nText
              fontSize='$4'
              fontWeight='$5'
              color='#C43F2D'
              marginLeft='$2'>
              Delete
            </I18nText>
          </XStack>
        )}
        <XStack height={44} alignItems='center' onPress={handleCancel}>
          <I18nText fontSize='$4' fontWeight='$5' marginLeft='$2'>
            Cancel
          </I18nText>
        </XStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default CommentMenu;
