import React from 'react';
import {
  KeyboardAvoidingView,
  Dimensions,
  Platform,
  StyleSheet,
} from 'react-native';
import {Sheet<PERSON>anager, SheetProps} from 'react-native-actions-sheet';
import {XStack, YStack, Separator, Text} from 'tamagui';
import {Trans} from 'react-i18next';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import I18nText from '@/src/components/I18nText';
import GoogleSignIn from '@/src/components/SignIn/GoogleSignIn';
import LocalToast from '@/src/components/LocalToast';
import {ToastOptions, useLocalToast} from '@/src/hooks/useLocalToast';

import Button from '@/src/components/UI/Button';
import FacebookSignIn from '../components/SignIn/FacebookSignIn';
import AppleSignIn from '../components/SignIn/AppleSignIn';
import EmailSignIn from '../components/SignIn/EmailSignIn';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const SignIn: React.FC<SheetProps<'signIn'>> = ({sheetId}) => {
  const {toast, showToast, hideToast} = useLocalToast();
  const handlePressCancel = () => SheetManager.hide(sheetId);
  const [sheetHeight, setSheetHeight] = React.useState<number>(0);

  const handlePressTerms = () => {
    // Show the Terms of Use sheet on top of the current sheet
    // Add a unique ID to ensure the sheet can be opened multiple times
    const uniqueId = `termsOfUse-${Date.now()}`;
    SheetManager.show('termsOfUse', {
      payload: {
        id: uniqueId,
      },
    });
  };

  const handlePressPrivacy = () => {
    // Show the Privacy Policy sheet on top of the current sheet
    // Add a unique ID to ensure the sheet can be opened multiple times
    const uniqueId = `privacyPolicy-${Date.now()}`;
    SheetManager.show('privacyPolicy', {
      payload: {
        id: uniqueId,
      },
    });
  };
  const handleOnError = (message: string) => {
    handleShowErrorToast({
      message,
    });
  };
  const handleShowErrorToast = ({
    title,
    message,
    type,
    duration,
  }: ToastOptions) => {
    showToast({
      title: title ?? 'Unable to Sign In',
      message:
        message ??
        "We couldn't sign you in. Please try again or use another sign-in method.",
      type: type ?? 'error',
      duration: duration ?? 4000,
    });
  };
  // top: -screenHeight + insets.top + 20, // Position at top of screen
  const screenHeight = Dimensions.get('window').height;
  const insets = useSafeAreaInsets();
  return (
    <ThemedActionSheet
      sheetId={sheetId}
      closeOnTouchBackdrop={false}
      keyboardHandlerEnabled={Platform.OS !== 'android'}
      containerStyle={{overflow: 'visible'}}>
      <LocalToast
        styles={{top: -screenHeight + sheetHeight + insets.top + 20}}
        visible={toast.visible}
        title={toast.title}
        message={toast.message}
        type={toast.type}
        duration={toast.duration}
        onHide={hideToast}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'android' ? 64 : 0}>
        <YStack
          backgroundColor='$windowBackground'
          onLayout={(event) => {
            const height = event.nativeEvent.layout.height;
            setSheetHeight(height);
          }}>
          <XStack
            height='$6'
            paddingHorizontal='$5'
            borderBottomWidth={StyleSheet.hairlineWidth}
            borderColor='$windowBackground'
            justifyContent='space-between'
            alignItems='center'>
            <I18nText fontSize={20} fontWeight='600'>
              Continue with
            </I18nText>
            <Button
              chromeless
              onPress={handlePressCancel}
              backgroundColor='transparent'>
              <I18nText fontSize={12} fontWeight='600' borderBottomWidth={1}>
                Cancel
              </I18nText>
            </Button>
          </XStack>
          <YStack paddingHorizontal='$5' paddingTop='$5'>
            <EmailSignIn onError={handleOnError} />
            <XStack
              height='$4.5'
              justifyContent='center'
              alignItems='center'
              marginVertical='$2.5'>
              <Separator borderColor='$accentGrey' />
              <I18nText marginHorizontal='$3' color='$grey'>
                or
              </I18nText>
              <Separator borderColor='$accentGrey' />
            </XStack>
            <XStack gap='$2'>
              <FacebookSignIn onError={handleOnError} />
              <GoogleSignIn onError={handleOnError} />
              {Platform.OS === 'ios' && <AppleSignIn onError={handleOnError} />}
            </XStack>
            <I18nText
              fontSize={10}
              lineHeight={16}
              marginTop='$6'
              width='65%'
              marginBottom='$6'
              alignSelf='center'
              textAlign='center'>
              <Trans
                defaults='By pressing continue, you agree to our <t>Terms of Service</t> and <p>Privacy Policy</p>'
                components={{
                  t: (
                    <Text
                      onPress={handlePressTerms}
                      textDecorationLine='underline'
                      color='$primary'
                      padding='$3' // Add more padding to increase the touch area
                      marginHorizontal='-$2' // Negative margin to maintain visual spacing
                    />
                  ),
                  p: (
                    <Text
                      onPress={handlePressPrivacy}
                      textDecorationLine='underline'
                      color='$primary'
                      padding='$3' // Add more padding to increase the touch area
                      marginHorizontal='-$2' // Negative margin to maintain visual spacing
                    />
                  ),
                }}
              />
            </I18nText>
          </YStack>
        </YStack>
      </KeyboardAvoidingView>
    </ThemedActionSheet>
  );
};

export default SignIn;
