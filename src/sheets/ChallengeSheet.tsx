import React from 'react';
import {Sheet<PERSON><PERSON>ger, SheetProps} from 'react-native-actions-sheet';
import {YStack, XStack} from 'tamagui';
import {useRouter} from 'expo-router';

import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import I18nText from '@/src/components/I18nText';
import StyledText from '@/src/components/UI/StyledText';
import StyledButton from '@/src/components/UI/Button';
import {triggerHaptics} from '@/src/utils/haptics';
import {storage} from '@/src/utils/localStorage';

const ChallengeSheet: React.FC<SheetProps<'challenge_sheet'>> = ({
  sheetId,
  payload,
}) => {
  const router = useRouter();

  if (!payload) {
    return null;
  }

  const {business, challenge, showWelcomeMessage = false} = payload;

  // Function to get the business alert pause key
  const getBusinessAlertPauseKey = (businessId: string) =>
    `business_alert_pause_${businessId}`;

  // Function to pause the alert for 3 days
  const pauseAlertForThreeDays = () => {
    const threeDaysInMs = 3 * 24 * 60 * 60 * 1000; // 3 days in milliseconds
    const pauseUntil = Date.now() + threeDaysInMs;
    storage.set(getBusinessAlertPauseKey(business.id), pauseUntil);
    SheetManager.hide(sheetId);
  };

  const handleJoinChallenge = async () => {
    if (challenge) {
      await triggerHaptics();
      SheetManager.hide(sheetId);

      router.push({
        pathname: '/challenge/[challengeId]',
        params: {
          challengeId: challenge.id,
          addPading: 'true',
        },
      });
    }
  };

  return (
    <ThemedActionSheet sheetId={sheetId}>
      <YStack padding='$5'>
        {showWelcomeMessage && (
          <YStack backgroundColor='#F5F5F5' padding='$4' borderRadius={8}>
            <I18nText
              textAlign='center'
              marginTop='$4'
              fontWeight='500'
              fontSize={24}>
              You successfully joined
            </I18nText>
            <StyledText
              textAlign='center'
              marginTop='$2'
              fontWeight='500'
              fontSize={24}>
              {business.name}
            </StyledText>
          </YStack>
        )}

        {challenge && (
          <YStack gap='$2'>
            <I18nText
              textAlign='center'
              color='#646875'
              fontWeight='500'
              marginTop='$4'
              marginBottom='$4'>
              There is an active challenge that you can join
            </I18nText>

            <StyledButton variant='primary' onPress={handleJoinChallenge}>
              <I18nText color='$white1'>Join Challenge</I18nText>
            </StyledButton>
          </YStack>
        )}

        <XStack justifyContent='center' marginTop='$3'>
          <I18nText
            color='$grey2'
            textDecorationLine='underline'
            onPress={pauseAlertForThreeDays}>
            Pause alert for 3 days
          </I18nText>
        </XStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default ChallengeSheet;
