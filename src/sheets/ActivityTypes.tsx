import React from 'react';
import {She<PERSON><PERSON>anager, SheetProps} from 'react-native-actions-sheet';
import {ActivityTypeDto} from '@gojoe/typescript-sdk';

import ThemedActionSheet from './ThemedActionSheet';
import ActivityTypes from '../components/ActivityTypes';

const ActivityTypesSheet: React.FC<SheetProps<'activity_types'>> = ({
  sheetId,
  payload,
}) => {
  const handleClose = () => {
    return SheetManager.hide(sheetId);
  };

  const handleSelect = (activity: ActivityTypeDto) => {
    if (payload?.onSelect) {
      payload.onSelect(activity);
    }
  };

  return (
    <ThemedActionSheet sheetId={sheetId}>
      {payload && (
        <ActivityTypes
          activityTypeId={payload.activityTypeId}
          showHistory={payload.showHistory}
          useCategories={payload.useCategories}
          onClose={handleClose}
          onSelect={handleSelect}
        />
      )}
    </ThemedActionSheet>
  );
};
export default ActivityTypesSheet;
