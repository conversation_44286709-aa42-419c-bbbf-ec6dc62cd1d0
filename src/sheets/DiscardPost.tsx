import React from 'react';
import {useRouter} from 'expo-router';
import {Sheet<PERSON>anager, SheetProps} from 'react-native-actions-sheet';
import {XStack, YStack} from 'tamagui';
import {X as CloseIcon} from '@tamagui/lucide-icons';

import {triggerHaptics} from '../utils/haptics';
import ThemedActionSheet from './ThemedActionSheet';
import I18nText from '../components/I18nText';
import StyledButton from '../components/UI/Button';

const DiscardPost: React.FC<SheetProps<'discard_post'>> = ({sheetId}) => {
  const router = useRouter();

  const handleClose = async () => {
    await triggerHaptics();
    await SheetManager.hide(sheetId);
  };

  const handleDiscard = async () => {
    await triggerHaptics();
    await SheetManager.hide(sheetId);
    router.back();
  };

  return (
    <ThemedActionSheet sheetId={sheetId} closeOnTouchBackdrop={false}>
      <YStack padding='$5'>
        <XStack
          justifyContent='space-between'
          alignItems='center'
          marginBottom='$3.5'>
          <I18nText fontSize={16} fontWeight={700}>
            Discard Post
          </I18nText>
          <XStack onPress={handleClose} padding='$2'>
            <CloseIcon color='$grey1' size={16} />
          </XStack>
        </XStack>

        <I18nText fontSize={14} fontWeight={500}>
          {`If you discard now, you'll lose this post.`}
        </I18nText>

        <XStack alignSelf='flex-end' marginTop='$5' gap='$2'>
          <StyledButton variant='ghost' borderWidth={0} onPress={handleClose}>
            <I18nText>Keep Editing</I18nText>
          </StyledButton>
          <StyledButton onPress={handleDiscard}>
            <I18nText color='$white1'>Discard</I18nText>
          </StyledButton>
        </XStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default DiscardPost;
