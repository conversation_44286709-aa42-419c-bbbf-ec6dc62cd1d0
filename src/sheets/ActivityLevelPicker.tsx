import React, {useState} from 'react';
import {Sheet<PERSON><PERSON><PERSON>, SheetProps} from 'react-native-actions-sheet';
import {useTheme, XStack, YStack} from 'tamagui';
import ThemedActionSheet from '@/src/sheets/ThemedActionSheet';
import {Platform, StyleSheet} from 'react-native';
import I18nText from '@/src/components/I18nText';
import Button from '@/src/components/UI/Button';
import {Picker} from '@react-native-picker/picker';
import {useTranslation} from 'react-i18next';
import {useActivityLevel} from '@/src/hooks/api/useActivityLevel';

const ActivityLevelPicker: React.FC<SheetProps<'activity_level_picker'>> = ({
  sheetId,
  payload,
}) => {
  const {t} = useTranslation();
  const {data} = useActivityLevel();
  const [value, setValue] = useState(payload?.levelId);
  const theme = useTheme();
  const color = theme.color?.get() ?? '#000000';

  const handleClose = () => {
    if (payload?.onSelect && value) {
      payload.onSelect(value);
    }
  };

  const handlePressCancel = () => SheetManager.hide(sheetId);
  return (
    <ThemedActionSheet
      sheetId={sheetId}
      gestureEnabled={false}
      onClose={handleClose}>
      <YStack paddingVertical='$4'>
        <XStack
          height='$6'
          paddingHorizontal='$5'
          borderBottomWidth={StyleSheet.hairlineWidth}
          borderColor='$windowBackground'
          justifyContent='space-between'
          alignItems='center'>
          <I18nText fontSize={20} fontWeight='600'>
            {payload?.title}
          </I18nText>
          <Button
            chromeless
            onPress={handlePressCancel}
            backgroundColor='transparent'>
            <I18nText
              fontSize={12}
              fontWeight='600'
              borderBottomWidth={1}
              borderBottomColor={color}>
              Done
            </I18nText>
          </Button>
        </XStack>
        <YStack>
          <Picker
            selectedValue={value}
            onValueChange={setValue}
            style={Platform.OS === 'android' ? {color: color} : undefined}
            itemStyle={{
              color: color,
            }}
            dropdownIconColor={color}>
            <Picker.Item
              label={
                Platform.OS === 'android' ? t('Select activity level') : ''
              }
              value=''
            />
            {data.map((item) => (
              <Picker.Item key={item.id} label={t(item.name)} value={item.id} />
            ))}
          </Picker>
        </YStack>
      </YStack>
    </ThemedActionSheet>
  );
};

export default ActivityLevelPicker;
