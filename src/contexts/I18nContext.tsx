import React, {createContext, ReactNode, useEffect} from 'react';
import {I18nextProvider, initReactI18next} from 'react-i18next';
import {useLocales} from '@/src/contexts/LocaleContext';
import i18n, {Language, languages} from '@/src/locales';

type I18nContextProps = {
  languages: Language[];
  changeLanguage: (language: string) => void;
};

const I18nContext = createContext<I18nContextProps>({
  languages: [...languages],
  changeLanguage: () => {},
});

export const I18nContextProvider = ({children}: {children: ReactNode}) => {
  const {locales} = useLocales();
  const changeLanguage = async (language: string) => {
    try {
      await i18n.changeLanguage(language);
    } catch (error) {
      // TODO log the error
    }
  };

  useEffect(() => {
    if (locales.languageCode) {
      changeLanguage(locales.languageCode);
    }
  }, [locales]);

  return (
    <I18nContext.Provider value={{languages, changeLanguage}}>
      <I18nextProvider i18n={i18n}>{children}</I18nextProvider>
    </I18nContext.Provider>
  );
};
