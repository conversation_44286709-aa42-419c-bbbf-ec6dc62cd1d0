import {createContext, ReactNode, useContext, useEffect, useState} from 'react';
import {
  CalendarIdentifier,
  Weekday,
  getLocales,
  getCalendars,
} from 'expo-localization';
import {useSession} from './SessionContext';
import useMutationUserSettings from '../hooks/api/useMutationUserSettings';
import {api} from '../services/api';

const userPhoneLocale = getLocales()[0];
const userPhoneRegion = userPhoneLocale.regionCode?.toUpperCase();
const userPhoneCalendar = getCalendars()[0];

export enum TemperatureUnit {
  Celsius = 'celsius',
  Fahrenheit = 'fahrenheit',
}
export enum AccountPrivacy {
  Public = 'PUPLIC',
  Private = 'PRIVATE',
}
export enum TextDirection {
  LTR = 'ltr',
  RTL = 'rtl',
}
export enum DecimalSeparator {
  Dot = '.',
  Comma = ',',
}
export enum BurnedCaloriesUnit {
  Pizza = 'Pizza',
  None = 'None',
}
export enum DigitGroupingSeparator {
  Comma = ',',
  Dot = '.',
  None = '',
  Space = ' ',
}
export enum WeightUnit {
  Kilograms = 'kg',
  Pounds = 'lbs',
  Stones = 'st',
}

export enum HeightUnit {
  Centimeters = 'cm',
  FeetInches = 'ft-in',
}

export enum DistanceUnit {
  Kilometers = 'km',
  Miles = 'mi',
}

export type NumberFormatProps = {
  decimalSeparator: DecimalSeparator;
  digitGroupingSeparator: DigitGroupingSeparator;
};
export type LocalesProps = {
  languageCode: string;
  languageTag: string;
  decimalSeparator: DecimalSeparator;
  weightUnit: WeightUnit;
  heightUnit: HeightUnit;
  distanceUnit: DistanceUnit;
  temperatureUnit: TemperatureUnit;
  textDirection: TextDirection;
  digitGroupingSeparator: DigitGroupingSeparator;
  timezone: string;
  calendar: CalendarIdentifier;
  firstDayOfWeek: Weekday;
  uses24hourClock: boolean;
  numberFormat: NumberFormatProps;
  dateFormat: string;
  burnedCaloriesUnit: BurnedCaloriesUnit;
  accountPrivacy: AccountPrivacy;
  unsubscribePush: boolean;
  hapticFeedback: boolean;
};

const getDefaultDistanceUnit = (): DistanceUnit => {
  const imperialRegions = ['US', 'LR', 'MM']; // Uses miles
  return imperialRegions.includes(userPhoneRegion ?? '')
    ? DistanceUnit.Miles
    : DistanceUnit.Kilometers;
};

const getDefaultWeightUnit = (): WeightUnit => {
  if (!userPhoneRegion) return WeightUnit.Kilograms;

  if (userPhoneRegion === 'US') return WeightUnit.Pounds;
  if (['GB', 'IE'].includes(userPhoneRegion)) return WeightUnit.Stones;

  return WeightUnit.Kilograms;
};
const getDefaultDateFormat = (): string => {
  if (!userPhoneRegion) return 'yyyy-MM-dd';

  if (userPhoneRegion === 'US') return 'MM/dd/yyyy';
  if (['GB', 'IE', 'AU', 'FR'].includes(userPhoneRegion)) return 'dd/MM/yyyy';
  if (['CN', 'JP', 'KR'].includes(userPhoneRegion)) return 'yyyy/MM/dd';

  return 'yyyy-MM-dd';
};
const getDefaultTimezone = (): string => {
  return userPhoneCalendar?.timeZone || 'UTC'; // fallback if unavailable
};
const getDefaultFirstDayOfWeek = (): Weekday => {
  if (!userPhoneRegion) return Weekday.MONDAY;

  if (['US', 'CA', 'JP'].includes(userPhoneRegion)) return Weekday.SUNDAY;
  if (['AE', 'EG', 'SA'].includes(userPhoneRegion)) return Weekday.SATURDAY;

  return Weekday.MONDAY;
};

const getDefaultTemperatureUnit = (): TemperatureUnit => {
  const fahrenheitRegions = ['US', 'BS', 'BZ', 'KY', 'PW', 'FM', 'MH'];

  return userPhoneRegion && fahrenheitRegions.includes(userPhoneRegion)
    ? TemperatureUnit.Fahrenheit
    : TemperatureUnit.Celsius;
};

const getDefaultHeightUnit = (): HeightUnit => {
  const imperialHeightRegions = ['US', 'GB', 'LR', 'MM'];

  return userPhoneRegion && imperialHeightRegions.includes(userPhoneRegion)
    ? HeightUnit.FeetInches
    : HeightUnit.Centimeters;
};
const initialState: LocalesProps = {
  languageCode: 'en',
  languageTag: 'en-UK',
  decimalSeparator: DecimalSeparator.Dot,
  weightUnit: WeightUnit.Kilograms,
  heightUnit: HeightUnit.Centimeters,
  temperatureUnit: TemperatureUnit.Celsius,
  distanceUnit: DistanceUnit.Kilometers,
  textDirection: TextDirection.LTR,
  digitGroupingSeparator: DigitGroupingSeparator.Comma,
  timezone: 'Europe/London',
  calendar: CalendarIdentifier.GREGORIAN,
  firstDayOfWeek: Weekday.MONDAY,
  uses24hourClock: true,
  numberFormat: {
    decimalSeparator: DecimalSeparator.Dot,
    digitGroupingSeparator: DigitGroupingSeparator.Comma,
  },
  dateFormat: 'dd/MM/yyyy',
  burnedCaloriesUnit: BurnedCaloriesUnit.Pizza,
  accountPrivacy: AccountPrivacy.Public,
  unsubscribePush: false,
  hapticFeedback: true,
};
type LocalesContextProps = {
  locales: LocalesProps;
  setLocales: (locales: LocalesProps) => void;
};

const getUserWeightUnit = (input?: string): WeightUnit => {
  if (!input) {
    return getDefaultWeightUnit();
  }
  switch (input.toLocaleLowerCase()) {
    case WeightUnit.Stones:
    case 'stones':
      return WeightUnit.Stones;

    case WeightUnit.Pounds:
    case 'lb':
    case 'pounds':
      return WeightUnit.Pounds;

    case WeightUnit.Kilograms:
    case 'kilograms':
      return WeightUnit.Kilograms;

    default:
      return getDefaultWeightUnit();
  }
};

const getUserInputWeightUnit = (input?: WeightUnit): string => {
  let userInput = input;
  if (!userInput) {
    userInput = getDefaultWeightUnit();
  }
  switch (userInput) {
    case WeightUnit.Stones:
      return 'stones';

    case WeightUnit.Pounds:
      return 'pounds';

    default:
    case WeightUnit.Kilograms:
      return 'kilograms';
  }
};

const getUserDistanceUnit = (input?: string): DistanceUnit => {
  if (!input) {
    return getDefaultDistanceUnit();
  }
  switch (input.toLocaleLowerCase()) {
    case DistanceUnit.Kilometers:
    case 'kilometers':
      return DistanceUnit.Kilometers;

    case DistanceUnit.Miles:
    case 'miles':
      return DistanceUnit.Miles;

    default:
      return getDefaultDistanceUnit();
  }
};

const getUserFirstDayOfTheWeek = (input?: string): Weekday => {
  if (!input) {
    return getDefaultFirstDayOfWeek();
  }
  switch (input.toUpperCase()) {
    case 'MONDAY':
      return Weekday.MONDAY;
    case 'TUESDAY':
      return Weekday.TUESDAY;
    case 'WEDNESDAY':
      return Weekday.WEDNESDAY;
    case 'THURSDAY':
      return Weekday.THURSDAY;
    case 'FRIDAY':
      return Weekday.FRIDAY;
    case 'SATURDAY':
      return Weekday.SATURDAY;
    case 'SUNDAY':
      return Weekday.SUNDAY;
    default:
      return getDefaultFirstDayOfWeek();
  }
};

const getUserTemperatureUnit = (input?: string): TemperatureUnit => {
  if (!input) {
    return getDefaultTemperatureUnit();
  }
  switch (input.toLocaleLowerCase()) {
    case 'fahrenheit':
      return TemperatureUnit.Fahrenheit;
    case 'celsius':
      return TemperatureUnit.Celsius;
    default:
      return getDefaultTemperatureUnit();
  }
};

const getUserInputTemperatureUnit = (input?: TemperatureUnit): string => {
  let userInput = input;
  if (!userInput) {
    userInput = getDefaultTemperatureUnit();
  }
  switch (userInput) {
    case TemperatureUnit.Fahrenheit:
      return 'Fahrenheit';
    case TemperatureUnit.Celsius:
    default:
      return 'Celsius';
  }
};

const getUserLanguage = (input?: string): string => {
  if (!input) {
    return userPhoneLocale.languageCode ?? 'en';
  }
  // split by _ or - and return the first part
  return input.split(/[_-]/)[0];
};

const getUserHeightUnit = (input?: string): HeightUnit => {
  if (!input) {
    return getDefaultHeightUnit();
  }
  switch (input.toLowerCase()) {
    case HeightUnit.Centimeters:
    case 'centimeters':
      return HeightUnit.Centimeters;

    case HeightUnit.FeetInches:
    case 'inches':
    case 'feet':
    case 'feet-inches':
      return HeightUnit.FeetInches;
    default:
      return getDefaultHeightUnit();
  }
};

const LocalesContext = createContext<LocalesContextProps | undefined>({
  locales: {
    ...initialState,
  },
  setLocales: () => {},
});
export const useLocales = () => {
  const context = useContext(LocalesContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const LocalesProvider = ({children}: {children: ReactNode}) => {
  const [locales, setLocalesState] = useState<LocalesProps>({
    ...initialState,
  });
  const {user} = useSession();
  const {mutate: updateUserSettings} = useMutationUserSettings();

  useEffect(() => {
    if (user) {
      setLocalesState({
        ...initialState,
        weightUnit: getUserWeightUnit(user.settings.weightUnit),
        distanceUnit: getUserDistanceUnit(user.settings.distanceUnit),
        timezone: user.settings.timezone ?? getDefaultTimezone(),
        dateFormat: user.settings.dateFormat ?? getDefaultDateFormat(),
        firstDayOfWeek: getUserFirstDayOfTheWeek(user.settings.firstDay),
        temperatureUnit: getUserTemperatureUnit(user.settings.temperatureUnit),
        languageCode: getUserLanguage(user.settings.language),
        languageTag: user.settings.language ?? userPhoneLocale.languageTag,
        heightUnit: getUserHeightUnit(user.settings.heightUnit),
        burnedCaloriesUnit:
          (user.settings.displayCalories as BurnedCaloriesUnit) ??
          BurnedCaloriesUnit.Pizza,
        accountPrivacy:
          (user.settings.privacy as AccountPrivacy) ?? AccountPrivacy.Public,
        unsubscribePush: user.settings.unsubscribePush,
      });
    }
  }, [user]);

  useEffect(() => {
    if (!user) return;

    const timezone = getCalendars()[0]?.timeZone;

    if (timezone && timezone !== locales.timezone) {
      api.getApiClient().userControllerUserSettings({
        userSettingsInputDto: {timezone},
      });

      setLocalesState({...locales, timezone: timezone});
    }
  }, [user, locales, setLocalesState]);

  const setLocales = async (newLocales: LocalesProps) => {
    setLocalesState(newLocales);
    try {
      void updateUserSettings({
        ...newLocales,
        distanceUnit:
          newLocales.distanceUnit === DistanceUnit.Miles
            ? 'miles'
            : 'kilomiters',
        weightUnit: getUserInputWeightUnit(newLocales.weightUnit),
        temperatureUnit: getUserInputTemperatureUnit(
          newLocales.temperatureUnit,
        ),
      });
    } catch (e) {
      console.error('Failed to update settings:', e);
    }
  };

  return (
    <LocalesContext.Provider value={{locales, setLocales}}>
      {children}
    </LocalesContext.Provider>
  );
};
