import React, {ReactNode} from 'react';
import {createContext, useContext} from 'react';
import {showImage as showImageUtil} from '@/src/utils/imageViewer';

interface ImageContextType {
  showImage: (uri: string) => void;
}

const ImageContext = createContext<ImageContextType | undefined>(undefined);

interface ImageProviderProps {
  children: ReactNode;
}

/**
 * ImageProvider now just provides the showImage function without rendering
 * the actual image viewer component. This prevents unnecessary re-renders
 * when showing/hiding images.
 */
export default function ImageProvider({children}: ImageProviderProps) {
  // Simply pass through to the utility function
  const showImage = (imageUri: string) => {
    showImageUtil(imageUri);
  };

  return (
    <ImageContext.Provider value={{showImage}}>
      {children}
    </ImageContext.Provider>
  );
}

export function useImage(): ImageContextType {
  const context = useContext(ImageContext);
  if (!context) {
    throw new Error('useImage must be used within an ImageProvider');
  }
  return context;
}
