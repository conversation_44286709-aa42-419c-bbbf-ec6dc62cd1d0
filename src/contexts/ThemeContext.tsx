import React, {createContext, useContext} from 'react';
import {TamaguiProvider, TamaguiProviderProps} from 'tamagui';
import {DefaultTheme, ThemeProvider} from '@react-navigation/native';
import {ToastProvider} from '@tamagui/toast';
import tamaguiConfig from '@/tamagui.config';
import {StatusBar} from 'expo-status-bar';

type ThemeMode = 'light' | 'dark';
type ThemeContextProps = {
  themeMode: ThemeMode;
  setTheme: (string: ThemeMode) => void;
};

const ThemeContext = createContext<ThemeContextProps | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const AppThemeProvider = ({
  children,
  ...rest
}: Omit<TamaguiProviderProps, 'config'>) => {
  const setTheme = () => {};

  return (
    <ThemeContext.Provider value={{themeMode: 'light', setTheme}}>
      <TamaguiProvider config={tamaguiConfig} defaultTheme='light' {...rest}>
        <ThemeProvider value={DefaultTheme}>
          <StatusBar style={'dark'} />
          <ToastProvider>{children}</ToastProvider>
        </ThemeProvider>
      </TamaguiProvider>
    </ThemeContext.Provider>
  );
};

export default AppThemeProvider;
