import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from 'react';
import {Dimensions, Platform} from 'react-native';
import {useSharedValue, withSpring, SharedValue} from 'react-native-reanimated';
import {YStack} from 'tamagui';
import {Goal} from '@/src/hooks/api/useGoals';
import {AffectingThings} from '@/src/hooks/api/useAffectingThings';
import {triggerHaptics} from '@/src/utils/haptics';
import {useSession} from './SessionContext';
import {LevelListDto} from '@gojoe/typescript-sdk';

type OnboardingData = {
  gender?: string;
  weight?: number;
  height?: number;
  companyCode?: string;
  goals: Goal[];
  activityLevel?: LevelListDto;
  affectingThings: AffectingThings[];
  dateOfBirth?: Date;
};

type OnboardingContextType = {
  step: number;
  setStep: (step: number) => void;
  next: () => void;
  previous: () => void;
  data: OnboardingData;
  setData: (data: OnboardingData) => void;
  translateX: SharedValue<number>;
};

const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined,
);
export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (!context)
    throw new Error('useOnboarding must be used within OnboardingProvider');
  return context;
};

export const OnboardingProvider = ({children}: {children: ReactNode}) => {
  const {user} = useSession();
  const [step, setStep] = useState(0);
  const [data, setData] = useState<OnboardingData>({
    goals: [],
    affectingThings: [],
  });
  const {width} = Dimensions.get('window');
  const contentTranslateX = useSharedValue(0); // Separate animation for content

  // Pre-fill user data if available
  useEffect(() => {
    if (user) {
      setData((prevData) => ({
        ...prevData,
        gender: user.gender,
        weight: user.weight,
        height: user.height,
        dateOfBirth: user.dateOfBirth ? new Date(user.dateOfBirth) : undefined,
      }));
    }
  }, [user]);

  const next = async () => {
    await triggerHaptics();
    contentTranslateX.value = withSpring(-width, {damping: 15, stiffness: 120});

    setTimeout(() => {
      setStep((prev) => prev + 1);
      contentTranslateX.value = width; // Reset off-screen
      contentTranslateX.value = withSpring(0, {damping: 15, stiffness: 120});
    }, 150); // Small delay to avoid flicker
  };

  const previous = async () => {
    if (step > 0) {
      await triggerHaptics();
      contentTranslateX.value = withSpring(width, {
        damping: 15,
        stiffness: 120,
      });

      setTimeout(() => {
        setStep((prev) => prev - 1);
        contentTranslateX.value = -width; // Reset off-screen
        contentTranslateX.value = withSpring(0, {damping: 15, stiffness: 120});
      }, 150);
    }
  };

  return (
    <OnboardingContext.Provider
      value={{
        step,
        setStep,
        next,
        previous,
        data,
        setData,
        translateX: contentTranslateX,
      }}>
      <YStack flex={1} paddingBottom={Platform.OS === 'android' ? '$5' : 0}>
        {children}
      </YStack>
    </OnboardingContext.Provider>
  );
};
