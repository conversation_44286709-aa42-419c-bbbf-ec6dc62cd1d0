import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {CustomerIO} from 'customerio-reactnative';
import {api} from '@/src/services/api';
import {BusinessDto, LoginDto, UserProfileDto} from '@gojoe/typescript-sdk';
import {
  getUserAccessToken,
  removeUserAccessToken,
  removeUserRefreshAccessToken,
  setUserAccessToken,
  setUserRefreshAccessToken,
} from '@/src/utils/auth';
import {fetchUserProfile} from '@/src/hooks/api/useUserProfile';
import {storage} from '@/src/utils/localStorage';
import Intercom from '@intercom/intercom-react-native';
import intercom from '../services/intercom';
import {getCalendars} from 'expo-localization';
import * as Application from 'expo-application';
import {Platform} from 'react-native';
import {getDeviceUUID} from '@/src/utils/device';
import {getBundleId} from '@/src/utils/bundle-id';
import logger, {
  clearLoggerUserId,
  setLoggerDeviceId,
  setLoggerUserId,
} from '@/src/utils/logger';
import appleHealthService from '../services/appleHealthService';
import segmentClient from '../services/segment';

export type SelectedBusinessType = {
  id: string;
  name: string;
  logo?: string;
};
type Headers = {
  'x-gj-device-timezone': string | null;
  'x-gj-app-version': string | null;
  'x-gj-app-bundleid': string | null;
  'x-gj-device-id': string | null;
  'x-gj-device-os': string;
  'x-gj-user-id'?: string;
  authorization?: string;
};

type SessionContextProps = {
  isAuthenticated: boolean;
  isOnboarded?: boolean;
  isReady?: boolean;
  onboarded?: boolean;
  setOnboarded: (onboarded: boolean) => void;
  user?: UserProfileDto;
  business?: SelectedBusinessType;
  isLoading: boolean;
  signIn: (data: LoginDto) => Promise<void>;
  signOut: () => Promise<void>;
  setUser: Dispatch<SetStateAction<UserProfileDto | undefined>>;
  selectBusiness: (business: SelectedBusinessType) => void;
  defaultHeaders?: Headers;
};
const initialHeaders = {
  'x-gj-device-timezone': getCalendars()[0]?.timeZone,
  'x-gj-app-version': Application.nativeApplicationVersion,
  'x-gj-app-bundleid': getBundleId(),
  'x-gj-device-id': '',
  'x-gj-device-os': Platform.OS,
  'x-gj-user-id': undefined,
  authorization: undefined,
};

const SessionContext = createContext<SessionContextProps>({
  isAuthenticated: false,
  user: undefined,
  business: undefined,
  isLoading: true,
  isReady: false,
  onboarded: false,
  setOnboarded: async () => {},
  signIn: async () => {},
  signOut: async () => {},
  setUser(
    value:
      | ((prevState: UserProfileDto | undefined) => UserProfileDto | undefined)
      | UserProfileDto
      | undefined,
  ): void {},
  selectBusiness(): void {},
  defaultHeaders: initialHeaders,
});

export const useSession = () => {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
export const SessionProvider = ({children}: {children: ReactNode}) => {
  const [user, setUser] = useState<UserProfileDto | undefined>(undefined);
  const [onboarded, setOnboarded] = useState(true);
  const [business, setBusiness] = useState<SelectedBusinessType | undefined>(
    undefined,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isReady, setIsReady] = useState(false);
  const [defaultHeaders, setDefaultHeaders] = useState<Headers>(initialHeaders);

  useEffect(() => {
    const fetchDeviceUUID = async () => {
      const uuid = await getDeviceUUID();
      setDefaultHeaders((prev) => {
        const headers = {...prev, 'x-gj-device-id': uuid};
        api.setDefaultHeaders(headers);
        setLoggerDeviceId(uuid);
        return headers;
      });
    };
    fetchDeviceUUID();
  }, []);

  useEffect(() => {
    if (isReady) {
      return;
    }
    const savedBusiness = storage.getObject<BusinessDto>('selected-business');
    if (savedBusiness) {
      selectBusiness(savedBusiness);
    }
    setIsReady(true);
  }, [isReady]);

  const signIn = useCallback(
    async (data: LoginDto) => {
      try {
        if (data.token) {
          await setUserAccessToken(data.token);
        }
        if (data.refreshToken) {
          await setUserRefreshAccessToken(data.refreshToken);
        }
        // First stage: Set Authorization only
        const headersWithAuth = {
          ...defaultHeaders,
          authorization: `Bearer ${data.token}`,
        };
        api.accessToken = data.token;
        api.setDefaultHeaders(headersWithAuth);

        // Now safe to fetch profile
        const userProfile = await fetchUserProfile('me');
        setUser(userProfile);
        setOnboarded(userProfile.settings.onboarded);

        // Second stage: Add user ID
        const fullHeaders = {
          ...headersWithAuth,
          'x-gj-device-id': await getDeviceUUID(),
          'x-gj-user-id': userProfile.id,
          authorization: `Bearer ${await getUserAccessToken()}`,
        };
        logger.debug('full headers', fullHeaders);
        setDefaultHeaders(fullHeaders);
        api.setDefaultHeaders(fullHeaders);
        setLoggerUserId(userProfile.id);
        logger.info('User successfully signed in');
        if (Platform.OS === 'ios') {
          logger.info('Checking apple health status after sign in');
          await appleHealthService.checkStatus();
        }
        // Customer IO
        const profileAttributes = {
          first_name: userProfile.firstName,
          last_name: userProfile.lastName,
          email: userProfile.email,
        };
        void CustomerIO.identify({
          userId: userProfile.id,
          traits: {
            ...profileAttributes,
          },
        });
        void CustomerIO.setProfileAttributes(profileAttributes);
        void segmentClient.identify(userProfile.id, {
          firstName: userProfile.firstName,
          lastName: userProfile.lastName,
        });

        //Intercom
        void intercom.login(userProfile);
      } catch (error) {
        //TODO if 401 form apu, logout
        logger.error('Failed to sign in:', error);
        api.accessToken = undefined;
      }
    },
    [defaultHeaders],
  );

  const signOut = useCallback(async () => {
    api.accessToken = undefined;
    setUser(undefined);
    setBusiness(undefined);
    clearLoggerUserId();
    await Promise.all([Intercom.logout()]);
    await removeUserAccessToken();
    await removeUserRefreshAccessToken();
    storage.remove('selected-business');
  }, []);

  useEffect(() => {
    const loadUser = async () => {
      try {
        const userAccessToken = await getUserAccessToken();
        if (userAccessToken) {
          await signIn({token: userAccessToken});
        }
      } catch (error) {
        logger.error('Failed to load user:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadUser();
  }, []);

  const selectBusiness = (business: SelectedBusinessType) => {
    setBusiness(business);
    storage.setObject('selected-business', business);
  };

  const contextValue = useMemo(() => {
    // Check if onboarding should be shown based on user profile completeness
    return {
      user,
      setUser,
      isAuthenticated: !!user,
      isLoading,
      isReady: isReady && !isLoading,
      onboarded,
      setOnboarded,
      signIn,
      signOut,
      business,
      selectBusiness,
      defaultHeaders,
    };
  }, [
    user,
    isLoading,
    isReady,
    onboarded,
    signIn,
    signOut,
    business,
    defaultHeaders,
  ]);

  return (
    <SessionContext.Provider value={contextValue}>
      {children}
    </SessionContext.Provider>
  );
};
