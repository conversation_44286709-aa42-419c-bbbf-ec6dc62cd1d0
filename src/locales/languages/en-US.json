{"": "", " ": " ", "  ": "  ", " - The responsibility for my health and well-being is my own including in relation to any physical activities, sports or practices that I undertake arising from the GoJoe Platform;": "- The responsibility for my health and well-being is my own including in relation to any physical activities, sports or practices that I undertake arising from the GoJoe Platform;", "(0 times per month)": "(0 times per month)", "(1-2 times per month)": "(1-2 times per month)", "(3-5 times per month)": "(3-5 times per month)", "(6-8 times per month)": "(6-8 times per month)", "(8+ times per month)": "(8+ times per month)", "(Code of Ethics)": "(Code of Ethics)", "(a) I have no medical condition, which I am aware of, that would prevent me from taking part in any physical activities or sports whilst using the GoJoe Platform and I confirm that I have never experienced:": "(a) I have no medical condition, which I am aware of, that would prevent me from taking part in any physical activities or sports whilst using the GoJoe Platform and I confirm that I have never experienced:", "(b) I have experienced one or more of the conditions listed in (a) above and I can confirm that I have taken advice from a medical practitioner or doctor who has given me consent to take part in all physical activities, sports or practices in the GoJoe Platform.": "(b) I have experienced one or more of the conditions listed in (a) above and I can confirm that I have taken advice from a medical practitioner or doctor who has given me consent to take part in all physical activities, sports or practices in the GoJoe Platform.", "(c) I have experienced one or more of the conditions listed in (a) above and I can confirm that I will take advice from a medical practical before I engage in any or the physical activities, sports or practices contained in the GoJoe Platform.": "(c) I have experienced one or more of the conditions listed in (a) above and I can confirm that I will take advice from a medical practical before I engage in any or the physical activities, sports or practices contained in the GoJoe Platform.", "(not public)": "(not public)", "- Auto-flag Activities and Flag": "- Auto-flag Activities and Flag", "- GoJoe is not responsible or liable for any information, services, products or content that I obtain via the GoJoe Platform, further GoJoe is not liable for any direct or indirect losses, fines, compensation, costs, proceedings, deficiencies or damages suffered or incurred as a result of the GoJoe Platform.": "- GoJoe is not responsible or liable for any information, services, products or content that I obtain via the GoJoe Platform, further GoJoe is not liable for any direct or indirect losses, fines, compensation, costs, proceedings, deficiencies or damages suffered orincurred as a result of the GoJoe Platform.", "- Human Referees that are actively watching": "- Human Referees that are actively watching", "- I assume full responsibility for any injury or damages that may result in connection with or arising out of my use of the GoJoe Platform; and": "- I assume full responsibility for any injury or damages that may result in connection with or arising out of my use of the GoJoe Platform; and", "- If I have any medical condition or injury that can possibly worsen with physical activity, sports or other practices relating to the GoJoe Platform, I should consult a medical practitioner or a doctor prior to starting that physical activity, sport or other practice;": "- If I have any medical condition or injury that can possibly worsen with physical activity, sports or other practices relating to the GoJoe Platform, I should consult a medical practitioner or a doctor prior to starting that physical activity, sport or other practice;", "- Max. 6h/day and 15h/week of manual activities added": "- Max. 6h/day and 15h/week of manual activities added", "- Points caps on activities": "- Points caps on activities", "- The GoJoe Platform is for enjoyment, educational and information purposes only;": "- The GoJoe Platform is for enjoyment, educational and information purposes only;", "- The content found on the GoJoe Platform is not a substitute for professional medical or professional financial advice which is based on your individual condition and / or circumstances and is not intended to be relied upon as such or generally;": "- The content found on the GoJoe Platform is not a substitute for professional medical or professional financial advice which is based on your individual condition and / or circumstances and is not intended to be relied upon as such or generally;", "- The responsibility for my health and well-being is my own including in relation to any physical activities, sports or practices that I undertake arising from the GoJoe Platform;": "- The responsibility for my health and well-being is my own including in relation to any physical activities, sports or practices that I undertake arising from the GoJoe Platform;", "- There is a risk of injury associated with participating in any physical activities, sports and other practices relating to the GoJoe Platform;": "- There is a risk of injury associated with participating in any physical activities, sports and other practices relating to the GoJoe Platform;", "- You can't manually input activities that were done more than 48 hours before the input time": "- You can't manually input activities that were done more than 48 hours before the input time", "- any joint or bone problems that could be made worse during physical activity or sports.": "- any joint or bone problems that could be made worse during physical activity or sports.", "- diabetes": "- diabetes", "- dizziness, light-headedness or fainting with physical activity or sports": "- dizziness, light-headedness or fainting with physical activity or sports", "- heart condition": "- heart condition", "- high blood pressure": "- high blood pressure", "- pain or discomfort in my chest, neck, jaw or arms during physical activity or sports": "- pain or discomfort in my chest, neck, jaw or arms during physical activity or sports", "- shortness of breath with mild exertion at rest": "- shortness of breath with mild exertion at rest", "1 Reply": "1 Reply", "1 Thread Reply": "1 Thread Reply", "1st": "1st", "<b>Fun:</b> If it's not fun, it's not GoJoe. Keep things light.": "<b>Fun:</b> If it's not fun, it's not <PERSON><PERSON><PERSON>. Keep things light.", "<b>GoJoe Spirit:</b> Stay positive and never give in. It's the GoJoe way!": "<b><PERSON><PERSON><PERSON> Spirit:</b> Stay positive and never give in. It's the GoJoe way!", "<b>Gojoe</b> multi-team challenges can be unlocked by <l>rubbing the lamp</l> emailing <e><EMAIL></e> - or press to <c>live chat</c> with our team": "<b>Gojoe</b> multi-team challenges can be unlocked by <l>rubbing the lamp</l> emailing <e><EMAIL></e> - or press to <c>live chat</c> with our team", "<b>Never Leave a Joe Behind:</b> We’re a team. We lift weights and each other!": "<b>Never Leave a Joe Behind:</b> We’re a team. We lift weights and each other!", "<b>No Shortcuts:</b> Cheating isn’t cool. Joes really compete with yesterday's us, not each other.": "<b>No Shortcuts:</b> Cheating isn’t cool. Joes really compete with yesterday's us, not each other.", "<b>Respect:</b> Competition is fun, respect is fundamental. We thrive together!": "<b>Respect:</b> Competition is fun, respect is fundamental. We thrive together!", "<bold>{{name}}</bold> just followed you.": "<bold>{{name}}</bold> just followed you.", "<break>Exercise</break><break1>on your own</break1><break2>but not alone</break2>": "<break>Exercise</break><break1>on your own</break1><break2>but not alone</break2>", "<emailUs>Email us</emailUs> or read the <faq>FAQs</faq>": "<emailUs>Email us</emailUs> or read the <faq>FAQs</faq>", "<h>Congratulations!\n</h><b>{{name}}</b>\n<b1>\nhas been created</b1><b2>\nNow get out there and move!</b2>": "<h>Congratulations!\n</h><b>{{name}}</b>\n<b1>\nhas been created</b1><b2>\nNow get out there and move! </b2>", "<head>Congratulations!</head><break>{{name}}</break><break1>has been created</break1><break2>Now get out there and move!</break2>": "<head>Congratulations! </head><break>{{name}}</break><break1>has been created</break1><break2>Now get out there and move!</break2>", "<i>Completed a</i> <b>{{activityType}}</b>": "<i>Completed a</i> <b>{{activityType}}</b>", "<l>I confirm and accept the above and agree to GoJoe’s <t>Terms and Conditions</t> and <p>Privacy Policy</p>.</l>": "<l>I confirm and accept the above and agree to <PERSON><PERSON>oe’s <t>Terms and Conditions</t> and <p>Privacy Policy</p>.</l>", "<t><l>Points caps</l> on activities": "<t><l>Points caps</l> on activities", "<t>All Joes can also <l>flag activities for review</l> by the GoJoe referees": "<t>All Joes can also <l>flag activities for review</l> by the GoJoe referees", "<t>All Joes must agree to the <l>Joe Code of Ethics</l> to play</t>": "<t>All Joes must agree to the <l>Joe Code of Ethics</l> to play</t>", "<t>GoJoe referees have a wide range of superpowers inc. an ability to filter Joes out of the main leaderboard to an <l>unfiltered version</l>": "<t><PERSON><PERSON>oe referees have a wide range of superpowers inc. an ability to filter Joe<PERSON> out of the main leaderboard to an <l>unfiltered version</l>", "<t>Health Connect is not available. Please download it from <l>Google PlayStore</l></t>": "<t>Health Connect is not available. Please download it from <l>Google PlayStore</l></t>", "<t>If you do have issues, do let us know at <l><EMAIL></l></t>": "<t>If you do have issues, do let us know at <l><EMAIL></l></t>", "<t>We understand that there might be exceptions such as epic activities or wearables not syncing - if you’re not able to add these manually, please <l>contact support</l> and we’ll help you.</l>": "<t>We understand that there might be exceptions such as epic activities or wearables not syncing - if you’re not able to add these manually, please <l>contact support</l> and we’ll help you.</l>", "A high-energy fitness class with moves that cater for total beginners to total addicts. We combine athletic movements like running, lunging and jumping with strength exercises such as push-ups and squats. This class will pump out energizing tunes and lead you through the workout – challenging your limits in a good way and leaving you with a sense of achievement.": "A high-energy fitness class with moves that cater for total beginners to total addicts. We combine athletic movements like running, lunging and jumping with strength exercises such as push-ups and squats. This class will pump out energizing tunes and lead you through the workout – challenging your limits in a good way and leaving you with a sense of achievement.", "A high-energy, innovative workout created by dancers to challenge and uplift you.": "A high-energy, innovative workout created by dancers to challenge and uplift you.", "A new version of the app is available. Would you like to update now?": "A new version of the app is available. Would you like to update now?", "A totally unique workout experience that combines a multi-peak cycling workout with a journey through digitally-created worlds.": "A totally unique workout experience that combines a multi-peak cycling workout with a journey through digitally-created worlds.", "APR": "Apr", "AUG": "Aug", "AVG. PACE (min/km)": "AVG. PACE (min/km)", "AVG. PACE (min/mi)": "AVG. PACE (min/mi)", "AVG. PACE (min/{{unit}})": "AVG. PACE (min/{{unit}})", "About": "About", "About the challenge": "About the challenge", "About you": "About You", "Access benefit": "Access benefit", "Access premium content, workouts, meditation audios, updated daily.": "Access premium content, workouts, meditation audios, updated daily.", "Access the content library": "Access the content library", "Account": "Account", "Account Preferences": "Account Preferences", "Account Privacy": "Account Privacy", "Account is locked. Please contact support <NAME_EMAIL>": "Account is locked. Please contact support <NAME_EMAIL>", "Achieve your goals with <b>Journeys</b>": "Achieve your goals with <b>Journeys</b>", "Active": "Active", "Active members": "Active members", "Activities": "Activities", "Activities are automatically weighted by GoJoe to level the playing field. Most points, wins.": "Activities are automatically weighted by GoJoe to level the playing field. Most points, wins.", "Activities are automatically weighted by GoJoe to level the playing field. The highest average team points per participant, wins.": "Activities are automatically weighted by GoJoe to level the playing field. The highest average team points per participant, wins.", "Activities mean points. Points across the last 30 days determines your Joe level.": "Activities mean points. Points across the last 30 days determines your Joe level.", "Activity": "Activity", "Activity Date": "Activity Date", "Activity Details": "Activity Details", "Activity Evidence": "Activity Evidence", "Activity Level": "Activity Level", "Activity Photos": "Activity Photos", "Activity Streak": "Activity Streak", "Activity Type(s)": "Activity Type(s)", "Activity Types": "Activity Types", "Activity added": "Activity added", "Activity added Successfully": "Activity added Successfully", "Activity deleted successfully": "Activity deleted successfully", "Activity level": "Activity level", "Activity level is required.": "Activity level is required.", "Activity start time": "Activity start time", "Activity tracker": "Activity tracker", "Activity types": "Activity Types", "Add": "Add", "Add\nPost": "Add\nPost", "Add Event": "Add Event", "Add Manual Activity": "Add Manual Activity", "Add Members to Team": "Add Members to Team", "Add Photos": "Add Photos", "Add Post": "Add post", "Add a photo": "Add a photo", "Add a title": "Add a title", "Add activity": "Add activity", "Add friends": "Add friends", "Add images to post": "Add images to post", "Add media to activity": "Add media to activity", "Add members": "Add members", "Add members to team": "Add members to team", "Add photos": "Add photos", "Add post": "Add post", "Add team": "Add team", "Add team member": "Add Team member", "Add team members": "Add team members", "Add team-mate": "Add team-mate", "Admin": "Admin", "Admins can <b>post announcements</b> to keep members informed.": "Admins can <b>post announcements</b> to keep members informed.", "Advanced": "Advanced", "Advanced data and engagement analytics and insights, custom branding, 40+ activity types and more.": "Advanced data and engagement analytics and insights, custom branding, 40+ activity types and more.", "Advanced data and engagement analytics and insights, custom branding, 40+ activity types and more. Advanced data and engagement analytics and insights, custom branding, 40+ activity types and more.": "Advanced data and engagement analytics and insights, custom branding, 40+ activity types and more. Advanced data and engagement analytics and insights, custom branding, 40+ activity types and more.", "Adventure": "Adventure", "Aerobics": "Aerobics", "Afghanistan": "Afghanistan", "Agender": "<PERSON><PERSON>", "Albania": "Albania", "Alcohol": "Alcohol", "Algeria": "Algeria", "All": "All", "All activities": "All activities", "Allow access to your Gallery": "Allow access to your Gallery", "Allow camera access in device settings": "Allow camera access in device settings", "Almost there": "Almost there", "Also send to channel": "Also send to channel", "Always looking to improve and we'd love to hear from you": "Always looking to improve and we'd love to hear from you", "American English": "American English", "American Samoa": "American Samoa", "An error occurred": "An error occurred", "And a lot more...": "And a lot more...", "Andorra": "Andorra", "Angola": "Angola", "Anguilla": "<PERSON><PERSON><PERSON>", "Animals": "Animals", "Announcements": "Announcements", "Antarctica": "Antarctica", "Antigua and Barbuda": "Antigua and Barbuda", "Anxiety": "Anxiety", "Anxiety and Stress": "Anxiety and Stress", "Apple watch": "Apple Watch ", "Application and Devices": "Application and Devices", "Application and Devices Application and Devices": "Application and Devices Application and Devices", "Apr": "Apr", "April": "April", "Are sure want to disable push notifications?": "Are sure want to disable push notifications?", "Are you sure you want to cancel?": "Are you sure you want to cancel?", "Are you sure you want to delete this activity?": "Are you sure you want to delete this activity?", "Are you sure you want to delete this event?": "Are you sure you want to delete this event?", "Are you sure you want to delete this post?": "Are you sure you want to delete this post?", "Are you sure you want to disconnect from Apple?": "Are you sure you want to disconnect from Apple?", "Are you sure you want to disconnect from Coros?": "Are you sure you want to disconnect from <PERSON>ros?", "Are you sure you want to disconnect from FitBit?": "Are you sure you want to disconnect from FitBit?", "Are you sure you want to disconnect from Garmin?": "Are you sure you want to disconnect from <PERSON><PERSON><PERSON>?", "Are you sure you want to disconnect from HealthConnect?": "Are you sure you want to disconnect from HealthConnect?", "Are you sure you want to disconnect from Polar?": "Are you sure you want to disconnect from Polar?", "Are you sure you want to disconnect from Suunto?": "Are you sure you want to disconnect from <PERSON><PERSON><PERSON>?", "Are you sure you want to disconnect from Wahoo?": "Are you sure you want to disconnect from <PERSON><PERSON><PERSON>?", "Are you sure you want to disconnect from Whoop?": "Are you sure you want to disconnect from <PERSON><PERSON>?", "Are you sure you want to end this Journey?": "Are you sure you want to end this Journey?", "Are you sure you want to go back? You will loose the progress for this activity.": "Are you sure you want to go back? You will loose the progress for this activity.", "Are you sure you want to leave the community? You will lose access to the community’s page, as well as to premium content and features.": "Are you sure you want to leave the community? You will lose access to the community’s page, as well as to premium content and features.", "Are you sure you want to leave this club?": "Are you sure you want to leave this club?", "Are you sure you want to leave this team?": "Are you sure you want to leave this team?", "Are you sure you want to log out?": "Are you sure you want to log out?", "Are you sure you want to permanently delete this message?": "Are you sure you want to permanently delete this message?", "Are you sure you wish to delete your account?": "Are you sure you wish to delete your account?", "Are you sure?": "Are you sure?", "Argentina": "Argentina", "Armenia": "Armenia", "Aruba": "Aruba", "Athlete": "Athlete", "Athletes": "Athletes", "Aug": "Aug", "August": "August", "Australia": "Australia", "Austria": "Austria", "Auto-flagging of activities": "Auto-flagging of activities", "Available soon": "Available soon", "Avg Pts": "Avg Pts", "Awards": "awards", "Awards are hidden until the challenge ends.": "Awards are hidden until the challenge ends.", "Azerbaijan": "Azerbaijan", "BACKED BY": "BACKED BY", "BETA": "Beta", "BORN TO MOVE": "BORN TO MOVE", "Back": "Back", "Bad Habits": "Bad Habits", "Badminton": "Bad<PERSON>ton", "Bahamas": "Bahamas", "Bahrain": "Bahrain", "Balance": "Balance", "Bandit": "Bandit", "Bangladesh": "Bangladesh", "Barbados": "Barbados", "Barre": "<PERSON><PERSON>", "Baseball": "Baseball", "Basketball": "Basketball", "Be a": "Be a", "Be the first to comment": "Be the first to comment", "Beginner": "<PERSON><PERSON><PERSON>", "Behaviour": "Behaviour", "Belarus": "Belarus", "Belgium": "Belgium", "Belize": "Belize", "Benin": "Benin", "Bermuda": "Bermuda", "Better Health": "Better Health", "Bhutan": "Bhutan", "Big HIITer": "Big HIITer", "Biking": "Biking", "Block User": "Block user", "Body Metrics": "Body Metrics", "Bolivia": "Bolivia", "Books": "Books", "Boom!": "Boom!", "Boom! The challenge has started <bold>{{name}}</bold> has has started. You are in team <bold>{{teamName}}</bold>": "Boom! The challenge has started <bold>{{name}}</bold> has has started. You are in team <bold>{{teamName}}</bold>", "Bosnia and Herzegovina": "Bosnia and Herzegovina", "Botswana": "Botswana", "Bouvet Island": "Bouvet Island", "Boxing": "Boxing", "Brazil": "Brazil", "Brazilian Portuguese": "Brazilian Portuguese", "Breathwork": "Breathwork", "British Indian Ocean Territory": "British Indian Ocean Territory", "Browse Community Page": "Browse Community Page", "Browse Organization Page": "Browse Organization Page", "Browse all Classes": "Browse all Classes", "Browse the GoJoe Library": "Browse the GoJoe Library", "Brunei": "Brunei", "Brunei Darussalam": "Brunei Darussalam", "Bulgaria": "Bulgaria", "Bulgarian": "Bulgarian", "Burkina Faso": "Burkina Faso", "Burundi": "Burundi", "Business": "Business", "Business Go to Business": "Business Go to Business", "Bussiness avatar for {{name}}": "Bussiness avatar for {{name}}", "By joining the challenge you agree to respect the Joe Code": "By joining the challenge you agree to respect the Joe Code", "By personal trainers, experts and Olympians": "By personal trainers, experts and Olympians", "By pressing continue, you agree to our": "By pressing continue, you agree to our", "By pressing continue, you agree to our <t>Terms of Service</t> and <p>Privacy Policy</p>": "By pressing continue, you agree to our <t>Terms of Service</t> and <p>Privacy Policy</p>", "By pressing continue, you agree to our <terms>Terms of Service</terms> and <policy>Privacy Policy</policy>": "By pressing continue, you agree to our <terms>Terms of Service</terms> and <policy>Privacy Policy</policy>", "By pressing the proceed button below:": "By pressing the proceed button below:", "CALORIES (kcal)": "CALORIES (kcal)", "CHALLENGES": "CHALLENGES", "CHAT": "Cha<PERSON>", "CLUBS": "Clubs", "COMING SOON": "Coming Soon", "COMMUNITY": "Community", "CONNECTED": "Connected", "CONNECTION...": "CONNECTION...", "Cabo Verde": "Cabo Verde", "Caffeine": "Caffeine", "Calories": "Calories", "Calories (cal)": "Calories (cal)", "Calories Burned": "Calories Burned", "Cambodia": "Cambodia", "Cameroon": "Cameroon", "Can we": "Can we", "Can we\ncontact you?": "Can we\ncontact you?", "Can we contact you?": "Can we contact you?", "Canada": "Canada", "Canadian French": "Canadian French", "Cancel": "Cancel", "Cannot Flag Message": "Cannot Flag Message", "Canoe": "Canoe", "Canoeing": "Canoeing", "Cape Verde": "Cape Verde", "Cardio": "Cardio", "Career": "Career", "Cat Cow": "Cat Cow", "Cayman Islands": "Cayman Islands", "Central African Republic": "Central African Republic", "Chad": "Chad", "Chair Workout": "Chair Workout", "Challenge": "Challenge", "Challenge Type": "Challenge Type", "Challenge avatar for {{name}}": "Challenge avatar for {{name}}", "Challenge code": "Challenge code", "Challenge name": "Challenge name", "Challenge name here": "challenge name here", "Challenge yourself and others with our unique virtual team challenges.": "Challenge yourself and others with our unique virtual team challenges.", "Challenges": "CHALLENGES", "Change name of team": "Change name of team", "Change team": "Change team", "Change team image": "Change team image", "Change team name": "Change team name", "Chat": "Cha<PERSON>", "Cheating isn't cool. Joes really compete with yesterday's us, not each other.": "Cheating isn't cool. Joes really compete with yesterday's us, not each other.", "Check back soon or enable <b>notifications</b> to stay updated": "Check back soon or enable <b>notifications</b> to stay updated", "Check back soon or enable <b>notifications</b> to stay updated.": "Check back soon or enable <b>notifications</b> to stay updated.", "Check out our recommendations below.": "Check out our recommendations below.", "Check your e-mail for an invite or contact your HR if you didn't receive an invite.": "Check your e-mail for an invite or contact your HR if you didn't receive an invite.", "Check your overview here!": "Check your overview here!", "Checking code...": "Checking code...", "Children": "Children", "Chile": "Chile", "China": "China", "Chinese Simplified": "Chinese Simplified", "Chinese Traditional (Hong Kong)": "Chinese Traditional (Hong Kong)", "Chinese Traditional (Taiwan)": "Chinese Traditional (Taiwan)", "Choose activity type": "Choose activity type", "Choose another activity type": "Choose another activity type", "Choose country": "Choose country", "Choose sport": "Choose sport", "Choose your country": "Choose your country", "Choose your next workout": "Choose your next workout", "Choreographed Dance": "Choreographed Dance", "Christmas Island": "Christmas Island", "Climb": "Climb", "Climbing": "Climbing", "Close": "Close", "Clubs": "Clubs", "Cocos (Keeling) Islands": "Cocos (Keeling) Islands", "Cold Water": "Cold Water", "Collections": "Collections", "Colombia": "Colombia", "Combat sports": "Combat sports", "Coming in {{month}}": "Coming in {{month}}", "Comments": "Comments", "Comming in {{month}}": "Comming in {{month}}", "Communities": "Communities", "Community": "Community", "Comoros": "Comoros", "Competition is fun, respect is fundamental. We thrive together!": "Competition is fun, respect is fundamental. We thrive together!", "Complete survey": "Complete survey", "Completed": "Completed", "Confidence": "Confidence", "Confirm": "Confirm", "Confirm terms and privacy": "I confirm and accept the above and agree to <PERSON><PERSON>oe’s <terms>Terms and Conditions</terms> and <policy>Privacy Policy</policy>.", "Congo (Congo-Brazzaville)": "Congo (Congo-Brazzaville)", "Congo, Democratic Republic of the": "Congo, Democratic Republic of the", "Congratulations!": "Congratulations!", "Connect": "Connect", "Connect\nwearable": "Connect\nwearable", "Connect Wearable": "Connect Wearable", "Connect wearable if you haven’t": "Connect wearable if you haven’t", "Connect with Apple Health": "Connect with Apple Health", "Connect with Coros": "Connect with Coros", "Connect with Fitbit": "Connect with Fitbit", "Connect with Garmin": "Connect with Garmin", "Connect with HealthConnect": "Connect with HealthConnect", "Connect with Oura": "Connect with Oura", "Connect with Polar": "Connect with Polar", "Connect with Suunto": "Connect with Suunto", "Connect with Wahoo": "Connect with Wahoo", "Connect with Whoop": "Connect with Whoop", "Connect with Withings": "Connect with Withings", "Connect with others who share your health & fitness interests—cheer each other on, swap stories, and reach goals together.": "Connect with others who share your health & fitness interests—cheer each other on, swap stories, and reach goals together.", "Connect your wearable": "Connect your wearable", "Connect your wearable device to track your activities automatically": "Connect your wearable device to track your activities automatically", "Connected": "Connected", "Connection Issue": "Connection Issue", "Consider how your comment might make others feel and be sure to follow our Community Guidelines": "Consider how your comment might make others feel and be sure to follow our Community Guidelines", "Contact support": "Contact support", "Content Library": "Content Library", "Continue": "Continue", "Continue reading": "Continue reading", "Continue with": "Continue with", "Continue with Apple": "Continue with Apple", "Continue with Facebook": "Continue with Facebook", "Continue with Google": "Continue with Google", "Continue with notifications": "Continue with notifications", "Continue without a code": "Continue without a code", "Cook Islands": "Cook Islands", "Cooking": "Cooking", "Cool Boarder": "<PERSON>", "Cool Down": "Cool Down", "Copied to clipboard": "Copied to clipboard", "Copy Message": "Copy Message", "Core": "Core", "Core Conditioning": "Core Conditioning", "Costa Rica": "Costa Rica", "Could you help us spread the word by rating us in the app store (takes 2 secs)?": "Could you help us spread the word by rating us in the app store (takes 2 secs)?", "Country": "Country", "Create": "Create", "Create\nChallenge": "Create\nChallenge", "Create\nyour profile": "Create\nyour profile", "Create Challenge": "Create Challenge", "Create Team": "Create team", "Create a private challenge for you and your colleagues or friends": "Create a private challenge for you and your colleagues or friends", "Create a team": "Create a team", "Create a team now": "Create a team now", "Create a team-based exercise challenge with people you know": "Create a team-based exercise challenge with people you know", "Create event": "Create event", "Create invite-only challenge": "Create invite-only challenge", "Create or join a team now": "Create or join a team now", "Create your own team for this challenge": "Create your own team for this challenge", "Create your profile": "<break>Create </break><break1>your profile</break1>", "Creative Expression": "Creative Expression", "Creative expression": "Creative Expression", "Cricket": "Cricket", "Croatia": "Croatia", "Croatia (Hrvatska)": "Croatia (Hrvatska)", "Croatian": "Croatian", "Crossfit": "Crossfit", "Cuba": "Cuba", "Culture": "Culture", "Currently, you don’t have a Premium account or an organisation providing rewards or boosts.": "Currently, you don’t have a Premium account or an organisation providing rewards or boosts.", "Currently, {{organisation}} has not opted to provide an earning boost or may be unable to do so… (but let’s not forget, those legends have already set you up with GoJoe Premium access 😉).": "Currently, {{organisation}} has not opted to provide an earning boost or may be unable to do so… (but let’s not forget, those legends have already set you up with GoJoe Premium access 😉).", "Custom Team Challenges": "Custom Team Challenges", "Cycle": "cycle", "Cycling": "Cycling", "Cycling Distance": "Cycling Distance", "Cycling workout where you control the intensity. Dial up the challenge factor to match your fitness level.": "Cycling workout where you control the intensity. Dial up the challenge factor to match your fitness level.", "Cyprus": "Cyprus", "Czech": "Czech", "Czech Republic": "Czech Republic", "Czechia (Czech Republic)": "Czechia (Czech Republic)", "DEC": "Dec", "DISCOVER": "Discover", "DISCOVER GOJOE": "DISCOVER GOJOE", "DISTANCE (km)": "DISTANCE (km)", "DISTANCE (mi)": "DISTANCE (mi)", "Dairy": "Dairy", "Dance": "Dance", "Dancing": "Dancing", "Danish": "Danish", "Date of Birth": "Date of Birth", "Date of birth": "Date of Birth", "Date of birth is invalid": "Date of birth is invalid", "Dates": "Dates", "Day": "Day", "Dec": "Dec", "December": "December", "Delete": "Delete", "Delete Event": "Delete Event", "Delete Message": "Delete message", "Delete account": "Delete Account", "Delete activity": "Delete activity", "Delete it": "Delete it", "Delete post": "Delete Post", "Democratic Republic of the Congo": "Democratic Republic of the Congo", "Denmark": "Denmark", "Depression": "Depression", "Designed to nurture a life-long love of physical activity, BORN TO MOVE™ helps children experience the joy and vitality of moving to music. Featuring programming for children aged 4-16, each session feeds young people’s natural appetite for action, movement and play – and lets them enjoy the energy,": "Designed to nurture a life-long love of physical activity, BORN TO MOVE™ helps children experience the joy and vitality of moving to music. Featuring programming for children aged 4-16, each session feeds young people’s natural appetite for action, movement and play – and lets them enjoy the energy,", "Details": "Details", "Device camera is used to take photos or videos.": "Device camera is used to take photos or videos.", "Device connected": "Device connected", "Device gallery permissions is used to take photos or videos.": "Device gallery permissions is used to take photos or videos.", "Device not connected": "Device not connected", "Diet": "Diet", "Diets": "Diets", "Direct": "Direct", "Discard": "Discard", "Discard Post": "Discard Post", "Disconnect": "Disconnect", "Discover": "Discover", "Discover GoJoe": "DISCOVER GOJOE", "Discover breathwork, meditation, yoga and recovery sessions to complement your physical workouts. These calming practices increase energy and vitality, improve focus, and boost your emotional strength.": "Discover breathwork, meditation, yoga and recovery sessions to complement your physical workouts. These calming practices increase energy and vitality, improve focus, and boost your emotional strength.", "Dismiss": "<PERSON><PERSON><PERSON>", "Distance": "Distance", "Distance (km)": "Distance (km)", "Distance (mi)": "Distance (mi)", "Distance (mp)": "Distance (mp)", "Diving": "Diving", "Djibouti": "Djibouti", "Do you want to send a copy of this message to a moderator for further investigation?": "Do you want to send a copy of this message to a moderator for further investigation?", "Dominica": "Dominica", "Dominican Republic": "Dominican Republic", "Don't be an ordinary joe, be GoJoe!": "Don’t be an ordinary joe, be <PERSON><PERSON><PERSON>!", "Don't have an community code invite?": "Don't have an community code invite?", "Don't have an organisation code invite?": "Don't have an organisation code invite?", "Done": "Done", "Don’t be an ordinary joe, be GoJoe!": "Don’t be an ordinary joe, be <PERSON><PERSON><PERSON>!", "Downloading Update": "Downloading Update", "Duration": "Duration", "Duration (days)": "Duration (days)", "Duration (hh:mm:ss)": "Duration (hh:mm:ss)", "Duration (in minutes)": "Duration (in minutes)", "Dutch": "Dutch", "E-mail address": "E-mail address", "ELEV": "ELEV", "EV Runners": "EV Runners", "Eager Beaver": "Eager Beaver", "Earliest average workouts": "Earliest average workouts", "Early Bird": "Early Bird", "Earned": "Earned", "Earning Boost": "Earning <PERSON>", "Ease your body into exercise with these simple low-impact workouts. These workouts are perfect if you’re a beginner, if you’re returning to exercise after injury, or if you just want low-impact movement.": "Ease your body into exercise with these simple low-impact workouts. These workouts are perfect if you’re a beginner, if you’re returning to exercise after injury, or if you just want low-impact movement.", "East Timor": "East Timor", "Ecuador": "Ecuador", "Edit": "Edit", "Edit Event": "Edit Event", "Edit Message": "Edit message", "Edit Profile": "Edit Profile", "Edit event": "Edit Event", "Edit post": "Edit Post", "Edit profile": "Edit profile", "Edited": "Edited", "Editing Message": "Editing Message", "Egypt": "Egypt", "El Salvador": "El Salvador", "Elev": "ELEV", "Email": "Email", "Email is required": "Email is required", "Email us": "Email us", "Emoji matching": "Emoji matching", "Emotions": "Emotions", "Empty message...": "Empty message...", "Enable Push Notifications": "Enable Push Notifications", "Enable push notifications": "Enable push notifications", "End": "End", "Ended": "Ended", "Ends today": "Ends today", "Engagement": "Engagement", "English": "English", "Enjoy <b>Challenges</b>": "Enjoy <b>Challenges</b>", "Enjoying GoJoe?": "Enjoying <PERSON><PERSON><PERSON>?", "Enter Challenge Code": "Enter Challenge Code", "Enter Code": "Enter code", "Enter at least 2 characters": "Enter at least 2 characters", "Enter code": "Enter code", "Enter invite code": "Enter invite code", "Enter min. 20 characters.": "Enter min. 20 characters.", "Enter search text": "Enter search text", "Enter the code": "Enter the Code", "Enter the email address": "Enter the email address", "Enter verification code": "Enter verification code", "Enter your challenge code below": "Enter your challenge code below", "Enter your org code invite": "Enter your org code invite", "Environment": "environment", "Equatorial Guinea": "Equatorial Guinea", "Eritrea": "Eritrea", "Error": "Error", "Error loading": "Error loading", "Error loading channel list...": "Error loading channel list...", "Error loading messages for this channel...": "Error loading messages for this channel...", "Error while loading, please reload/refresh": "Error while loading, please reload/refresh", "Error: Unable to contact GoJoe server": "Error: Unable to contact GoJoe server", "Estonia": "Estonia", "Estonian": "Estonian", "Eswatini": "<PERSON><PERSON><PERSON><PERSON>", "Eswatini (fmr. Swaziland)": "<PERSON><PERSON><PERSON><PERSON> (fmr. Swaziland)", "Ethiopia": "Ethiopia", "Event Title": "Event Title", "Event Type": "Event Type", "Event Type (eg. Webinar, Virtual Run, etc.)": "Event Type (eg. Webinar, Virtual Run, etc.)", "Event title is required": "Event title is required", "Event type": "Event Type", "Event type is required": "Event type is required", "Exercise": "Exercise", "Exercise\non your own\nbut not alone": "Exercise\non your own\nbut not alone", "Exercise on your own but not alone, with every activity you log powering you and your team forward.": "Exercise on your own but not alone, with every activity you log powering you and your team forward.", "Exercises Preview": "Exercises Preview", "Exercises Preview!!!": "Exercises Preview!!!", "Exit Journey": "Exit Journey", "Explore": "Explore", "Explore GoJoe": "Explore GoJoe", "External Page": "External Page", "Extraordinary Joe": "Extraordinary Joe", "FAQ": "FAQ", "FAQs": "FAQs", "FEB": "Feb", "FIND PEOPLE": "FIND PEOPLE", "FT": "FT", "Failed to join the team. Please try again.": "Failed to join the team. Please try again.", "Fair question.": "Fair question.", "Falkland Islands (Malvinas)": "Falkland Islands (Malvinas)", "Fancy creating and running a club for you and your crew?": "Fancy creating and running a club for you and your crew?", "Faroe Islands": "Faroe Islands", "Fears and Phobias": "Fears and Phobias", "Featured": "Featured", "Feb": "Feb", "February": "February", "Feed": "Feed", "Feed me": "Feed me", "Feed us a message or image to proceed.": "Feed us a message or image to proceed.", "Feel": "Feel", "Feel free to connect with your organisation to explore the possibility of enhancing GoJoe rewards.": "Feel free to connect with your organisation to explore the possibility of enhancing GoJoe rewards.", "Feel stressed": "Feel stressed", "Female": "Female", "Female Specific": "Female Specific", "Fence": "<PERSON><PERSON>", "Fencing": "Fencing", "Fiji": "Fiji", "File is too large: {{ size }}, maximum upload size is {{ limit }}": "File is too large: {{ size }}, maximum upload size is {{ limit }}", "File type not supported": "File type not supported", "Filipino": "Filipino", "Find Clubs": "Find Clubs", "Find Friends": "Find Friends", "Find People": "FIND PEOPLE", "Find a Journey that's right for you": "Find a Journey that's right for you ", "Find friends": "Find friends", "Find friends that use GoJoe": "Find friends that use GoJoe", "Find out more": "Find out more", "Find out more, here.": "Find out more, here.", "Find stuff you need, all from one place.": "Find stuff you need, all from one place.", "Finish": "Finish", "Finland": "Finland", "Finnish": "Finnish", "First name": "First Name", "First name is required": "First Name is required", "First name must be at least 2 characters.": "First name must be at least 2 characters.", "First to log an activity": "First to log an activity", "Fitness": "Fitness", "Flag": "Flag", "Flag Activity": "Flag activity", "Flag Cancel": "Flag Cancel", "Flag Message": "Flag Message", "Flag Post": "Flag Post", "Flag action failed either due to a network issue or the message is already flagged": "Flag action failed either due to a network issue or the message is already flagged.", "Flag activities for review": "Flag activities for review", "Flag activity": "Flag activity", "Flag post": "Flag Post", "Flagged activity": "Flagged activity", "Flagged post": "Flagged post", "Flexibility": "Flexibility", "Focus": "Focus", "Follow": "Follow", "Follow  +": "Follow  +", "Follow creators": "Follow creators", "Follow people you know...see their activities and compete in challenges with and against them!": "Follow people you know... see their activities and compete in challenges with and against them!", "Followers": "Followers", "Followers ({{countFollowing}})": "Followers ({{countFollowing}})", "Following": "Following", "Following ({{countFollowed}})": "Following ({{countFollowed}})", "Food": "Food", "Football": "Football", "For Ordinary Joes everywhere": "For Ordinary Joes everywhere", "For You": "For You", "For example, if you start a challenge at 3pm and then go to manually input a 30 minute run, the start time of the run will be 2:30pm so will not count.": "For example, if you start a challenge at 3pm and then go to manually input a 30 minute run, the start time of the run will be 2:30pm so will not count.", "For now this activity has been marked as flagged.": "For now this activity has been marked as flagged.", "For now this post has been marked as flagged.": "For now this post has been marked as flagged.", "For the best GoJoe experience, we strongly recommend enabling push notifications.": "For the best GoJoe experience, we strongly recommend enabling push notifications.", "France": "France", "France, Metropolitan": "France, Metropolitan", "Free challenge": "Free challenge", "French": "French", "French Guiana": "French Guiana", "French Polynesia": "French Polynesia", "French Southern Territories": "French Southern Territories", "Fri": "<PERSON><PERSON>", "Friendly, social competition with people you know - whether friends, colleagues or celebrities - to get you motivated.": "Friendly, social competition with people you know - whether friends, colleagues or celebrities - to get you motivated.", "From now on, any activity will add you reward points": "From now on, any activity will add you reward points", "Fuel": "Fuel", "Fullbody": "Fullbody", "Fun": "Fun", "Fun-loving and insanely addictive dance workout. No dance experience required!": "Fun-loving and insanely addictive dance workout. No dance experience required!", "Future": "Future", "G.I. Joe": "<PERSON><PERSON><PERSON><PERSON>", "GPS": "GPS", "GPS Signal Acquired": "GPS Signal Acquired", "GPS signal acquired": "GPS signal acquired", "GPS signal waiting": "GPS signal waiting", "Gabon": "Gabon", "Gambia": "Gambia", "Gardening": "Gardening", "Gender": "Gender", "Gender is required": "Gender is required", "General": "General", "Georgia": "Georgia", "German": "German", "Germany": "Germany", "Get Premium": "GET PREMIUM", "Get Premium Now!": "Get Premium Now!", "Get Started": "Get Started", "Get fast, fit and strong using non-contact martial arts-inspired exercises to fuel cardio fitness and train the whole body. This high-energy martial-arts inspired workout is totally non-contact and there are no complex moves to master.  You’ll release stress, have a blast and feel like a champ.": "Get fast, fit and strong using non-contact martial arts-inspired exercises to fuel cardio fitness and train the whole body. This high-energy martial-arts inspired workout is totally non-contact and there are no complex moves to master.  You’ll release stress, have a blast and feel like a champ.", "Get on a Journey": "Get on a Journey", "Get stronger": "Get stronger", "Get up to date with our Feed": "Get up to date with our Feed", "Get your\n<black>daily picks on {{type}}</black>\nfrom our content collections": "Get your\n<black>daily picks on {{type}}</black>\nfrom our content collections", "Get your challenge": "Get your challenge", "Get your daily picks from our content collections": "Get your daily picks from our content collections", "Get your latest picks from our content collections": "Get your latest picks from our content collections", "Ghana": "Ghana", "Gibraltar": "Gibraltar", "Give it a strong push in final days and get back once the challenge ends to check where you ended up.": "Give it a strong push in final days and get back once the challenge ends to check where you ended up.", "Give us more info about yourself to get the most from GoJoe (don't worry, it will be kept private!)": "Give us more info about yourself to get the most from GoJoe (don't worry, it will be kept private!)", "Give your body a nice break!": "Give your body a nice break!", "Go Back": "Go Back", "Go to Business": "Go to Business", "Go to business page": "Go to business page", "Go to your team": "Go to your team", "GoJoe Premium": "GoJoe Premium", "GoJoe Spirit": "GoJoe Spirit", "GoJoe Tracker": "<PERSON><PERSON><PERSON>", "GoJoe collects location data to enable you to track your fitness activities, even when the app is not in use": "GoJoe collects location data to enable you to track your fitness activities, even when the app is not in use", "GoJoe houses <b>content and resources</b> from all four pillars": "GoJoe houses <b>content and resources</b> from all four pillars", "GoJoe is a social fitness community with features, information and resources provided through the GoJoe app, website, blog, emails, social media posts and other products and services (‘the GoJoe Platform’).": "GoJoe is a social fitness community with features, information and resources provided through the GoJoe app, website, blog, emails, social media posts and other products and services (‘the GoJoe Platform’).", "GoJoe is a truly social digital fitness app, allowing you to stay connected and exercise with and against friends and celebrities.": "GoJoe is a truly social digital fitness app, allowing you to stay connected and exercise with and against friends and celebrities.", "GoJoe is not a step tracker": "<PERSON><PERSON><PERSON> is not a step tracker", "GoJoe rewards are a premium feature, available to Joes with a GoJoe Premium account. Premium turns your GoJoe activity into exciting rewards.": "GoJoe rewards are a premium feature, available to Joe<PERSON> with a GoJoe Premium account. Premium turns your GoJoe activity into exciting rewards.", "GoJoe syncs the Samsung Galaxy watch via Health Connect": "GoJoe syncs the Samsung Galaxy watch via Health Connect", "GoJoe syncs the following Apple Watch (only) activities via Apple Health:": "GoJoe syncs the following Apple Watch (only) activities via Apple Health:", "Goal": "Goal", "Going": "Going", "Goldfish": "Goldfish", "Golf": "Golf", "Got a Vision? Start Your Own Club!": "Got a Vision? Start Your Own Club!", "Got a code from your community?": "Got a code from your community?", "Got a code from your organization?": "Got a code from your organization?", "Got a code?": "Got a code?", "Got a code? Enter here": "Got a code? Enter here", "Got a code? Enter it here": "Got a code? Enter it here", "Got a code? Enter your code here": "Got a code? Enter your code here", "Got a code? Input here   >": "Got a code? Input here   >", "Got an idea for a new club? Get in touch with us to make it happen!": "Got an idea for a new club? Get in touch with us to make it happen!", "Got it": "Got it", "Great job, get some rest now": "Great job, get some rest now", "Great news! {{name}} has activated an earning boost of x{{multiplier}}, which means you earn {{multiplier}}x the reward points compared to the standard GoJoe Premium allowance. That’s more points, more rewards, and more motivation to stay healthy and engaged.": "Great news! {{name}} has activated an earning boost of x{{multiplier}}, which means you earn {{multiplier}}x the reward points compared to the standard GoJoe Premium allowance. That’s more points, more rewards, and more motivation to stay healthy and engaged.", "Great, that makes us happy!": "Great, that makes us happy!", "Greece": "Greece", "Greenland": "Greenland", "Grenada": "Grenada", "Group Activity": "Group Activity", "Guadeloupe": "Guadeloupe", "Guam": "Guam", "Guatemala": "Guatemala", "Guernsey": "Guernsey", "Guided Meditation": "Guided Meditation", "Guinea": "Guinea", "Guinea-Bissau": "Guinea-Bissau", "Gut Health": "Gut Health", "Guyana": "Guyana", "Gym": "Gym", "Gym/studio": "Gym/studio", "Gymnastics": "Gymnastics", "HH": "HH", "HIIT": "HIIT", "HIIT Activity": "HIIT Activity", "HR": "hr", "Habits": "Habits", "Haiti": "Haiti", "Handcycle": "Handcycle", "Happiness": "Happiness", "Haptic Feedback": "Haptic <PERSON>", "Have a Code? Add Code": "Have a Code? Add Code", "Have a code? Add Code": "Have a Code? Add Code", "Having trouble?": "Having trouble?", "Having troubles?": "Having troubles?", "Health": "Health", "Health Connect": "Health Connect", "Healthy Living": "Healthy Living", "Heard and Mc Donald Islands": "Heard and Mc Donald Islands", "Heart rate": "Heart rate", "Hectic Life": "Hectic Life", "Height": "Height", "Height (cm)": "Height (cm)", "Height (in)": "Height (in)", "Height is required": "Height is required", "Help": "Help", "Hey {{name}}, welcome!": "Hey {{name}}, welcome!", "Hide filters": "Hide filters", "High Intensity Interval Training": "High Intensity Interval Training", "High-intensity interval training designed to improve strength and build lean muscle.  This workout uses barbell, weight plate and bodyweight exercises to blast all major muscle groups. LES MILLS GRIT Strength takes cutting-edge HIIT and combines it with powerful music and inspirational coaching, motivating you to go harder to get fit, fast.": "High-intensity interval training designed to improve strength and build lean muscle.  This workout uses barbell, weight plate and bodyweight exercises to blast all major muscle groups. LES MILLS GRIT Strength takes cutting-edge HIIT and combines it with powerful music and inspirational coaching, motivating you to go harder to get fit, fast.", "High-intensity interval training on a bike. It's a short, intense style of training where the thrill and motivation comes from pushing your physical and mental limits.": "High-intensity interval training on a bike. It's a short, intense style of training where the thrill and motivation comes from pushing your physical and mental limits.", "High-intensity interval training that focuses on sports conditioning training to improve your overall athletic performance. This workout often uses a step, bodyweight exercises and multi-dimensional sports conditioning training to increase your overall athletic performance: strength, agility, speed and power.": "High-intensity interval training that focuses on sports conditioning training to improve your overall athletic performance. This workout often uses a step, bodyweight exercises and multi-dimensional sports conditioning training to increase your overall athletic performance: strength, agility, speed and power.", "High-intensity interval training that improves cardiovascular fitness, increases speed and maximizes calorie burn. This workout uses a variety of body weight exercises and provides the challenge and intensity you need to get results fast.": "High-intensity interval training that improves cardiovascular fitness, increases speed and maximizes calorie burn. This workout uses a variety of body weight exercises and provides the challenge and intensity you need to get results fast.", "Hiit": "HIIT", "Hiking": "Hiking", "Hindi": "Hindi", "History": "History", "Hockey": "Hockey", "Hold to start recording.": "Hold to start recording.", "Holistic": "Holistic", "Home": "Home", "Homeworkout": "Homeworkout", "Honduras": "Honduras", "Hong Kong": "Hong Kong", "Hormones": "Hormones", "Horse": "Horse", "Horseriding": "Horseriding", "Hours": "Hours", "House of Wellbeing": "House of Wellbeing", "How about sending your first message to a friend?": "How about sending your first message to a friend?", "How active are you currently?": "How active are you currently?", "How do you feel?": "How do you feel?", "How do you feel?\nGive your body a nice break!\nLooks like you've been pushing yourself lately, please prioritise rest for the rest of the day": "How do you feel?\nGive your body a nice break!\nLooks like you've been pushing yourself lately, please prioritise rest for the rest of the day", "How levelling works": "How levelling works", "Hungarian": "Hungarian", "Hungary": "Hungary", "Hydration": "Hydration", "Hypnotherapy": "Hypnotherapy", "I also confirm that I do not suffer with:": "I also confirm that I do not suffer with:", "I also confirm that either (a), (b) or (c) below apply:": "I also confirm that either (a), (b) or (c) below apply:", "I confirm and accept the above and agree to GoJoe’s <terms>Terms and Conditions</terms> and <policy>Privacy Policy</policy>.": "I confirm and accept the above and agree to <PERSON><PERSON>oe’s <terms>Terms and Conditions</terms> and <policy>Privacy Policy</policy>.", "I understand, acknowledge and accept that:": "I understand, acknowledge and accept that:", "I'll do it later": "I'll do it later", "I'm an animal": "I'm an animal", "I'm an animal (8+ times per month)": "I'm an animal (8+ times per month)", "I'm going": "I'm going", "I'm in the {challengeName} challenge! Join me by: downloading the GoJoe app > creating an account > press ‘got a code’ > enter code {challengeCode} > create/join a team.": "I'm in the {challengeName} challenge! Join me by: downloading the GoJoe app > creating an account > press ‘got a code’ > enter code {challengeCode} > create/join a team.", "I'm in the {challengeName} challenge! Join me by: downloading the GoJoe app > creating an account > press ‘got a code’ > enter the ‘org code’ sent by {challengeBusinessName} > create/join a team.": "I'm in the {challengeName} challenge! Join me by: downloading the GoJoe app > creating an account > press ‘got a code’ > enter the ‘org code’ sent by {challengeBusinessName} > create/join a team.", "I'm in the {{name}} team fitness challenge! Join me by: downloading the GoJoe app > creating an account > press the below link > create/join a team.": "I'm in the {{name}} team fitness challenge! Join me by: downloading the GoJoe app > creating an account > press the below link > create/join a team.", "Iceland": "Iceland", "Ideal for anyone and everyone, this yoga-based class that will improve your mind, your body and your life.\nDuring BODYBALANCE an inspired soundtrack plays as you bend and stretch through a series of simple yoga moves and embrace elements of Tai Chi and Pilates. Breathing control is a part of all the exercises, and instructors will always provide options for those just getting started. You’ll strengthen your entire body and finish feeling calm and centered.": "Ideal for anyone and everyone, this yoga-based class that will improve your mind, your body and your life.\nDuring BODYBALANCE an inspired soundtrack plays as you bend and stretch through a series of simple yoga moves and embrace elements of Tai Chi and Pilates. Breathing control is a part of all the exercises, and instructors will always provide options for those just getting started. You’ll strengthen your entire body and finish feeling calm and centered.", "If another joe flags your activity for review, the GoJoe referee will take a look.": "If another joe flags your activity for review, the GoJoe referee will take a look.", "If it's not fun, it's not GoJoe.\nKeep things light.": "If it's not fun, it's not <PERSON><PERSON><PERSON>.\nKeep things light.", "If the referee smells a rat or feels there isn't enough supporting evidence, the referee may strike the activity off or ask for more information.": "If the referee smells a rat or feels there isn't enough supporting evidence, the referee may strike the activity off or ask for more information.", "If you are joining a community or company, enter your invite code. You should find it in your company invite or email.": "If you are joining a community or company, enter your invite code. You should find it in your company invite or email.", "If you are part of a challenge your activities will appear in the challenge history but not on your profile page.": "If you are part of a challenge your activities will appear in the challenge history but not on your profile page.", "If you discard now, you'll lose this post.": "If you discard now, you'll lose this post.", "If you do have issues, do let us know at": "If you do have issues, do let us know at", "If you have any issues please let us know.": "If you have any issues please let us know.", "If you select yes, your account and personal data will be deleted within 30 days, unless you reactivate it within that period": "If you select yes, your account and personal data will be deleted within 30 days, unless you reactivate it within that period", "If you think you should, click the link below to start a chat with our team.": "If you think you should, click the link below to start a chat with our team.", "If you're a VIP and have been invited to join a challenge via a code, press the 'Have a code?' button.": "If you're a VIP and have been invited to join a challenge via a code, press the 'Have a code?' button.", "If you're a VIP and have been invited to join a challenge via a code, press the 'got a code' button above and enter it to get access": "If you're a VIP and have been invited to join a challenge via a code, press the 'got a code' button above and enter it to get access", "If you're a VIP and have been invited to join a challenge via a code, press the 'got a code' button above and enter it to get access.": "If you're a VIP and have been invited to join a challenge via a code, press the 'got a code' button above and enter it to get access.", "If you're looking to join community challenges press the 'Featured' tab above or create your own by pressing the 'O' button below.": "If you're looking to join community challenges press the 'Featured' tab above or create your own by pressing the 'O' button below.", "Illness": "Illness", "Immune System": "Immune System", "Imperial": "Imperial", "Important Information and Disclaimer": "Important Information and Disclaimer", "Improve cardio": "Improve cardio", "Improve mental health": "Improve mental health", "Improve sleep": "Improve sleep", "In all circumstances, I acknowledge that if at any time, I feel unwell, experience pain or discomfort I should stop the activity and should seek professional medical assistance.": "In all circumstances, I acknowledge that if at any time, I feel unwell, experience pain or discomfort I should stop the activity and should seek professional medical assistance.", "In order to keep challenges fun and fair, the following rules apply:-": "In order to keep challenges fun and fair, the following rules apply:-", "In order to keep it fair and not abuse manual activities, we have a couple of simple rules when it comes to them:": "In order to keep it fair and not abuse manual activities, we have a couple of simple rules when it comes to them:", "In order to show you the rewards that you can redeem, please choose your country.": "In order to show you the rewards that you can redeem, please choose your country.", "Increase concentration, boost productivity, improve health and wellness and bring a greater sense of equanimity to your life.": "Increase concentration, boost productivity, improve health and wellness and bring a greater sense of equanimity to your life.", "India": "India", "Indonesia": "Indonesia", "Indonesian": "Indonesian", "Info": "Info", "Inner animal": "Inner animal", "Input": "Input", "Insights": "Insights", "Inspiration, tips and content from the best in the game.": "Inspiration, tips and content from the best in the game.", "Inspiring people to do better every day": "Inspiring people to do better every day", "Instant Commands": "Instant Commands", "Interface": "Interface", "Intermediate": "Intermediate", "Intro": "Intro", "Introducing": "Introducing", "Invalid code": "Invalid code", "Invalid email": "Invalid email", "Invite Code": "Invite Code", "Invite Friends": "Invite Friends", "Invite URL": "Invite URL", "Invite a Friend": "Invite a friend", "Invite a friend": "Invite a friend", "Invite sent": "<PERSON><PERSON><PERSON> sent", "Iran": "Iran", "Iran (Islamic Republic of)": "Iran (Islamic Republic of)", "Iraq": "Iraq", "Ireland": "Ireland", "Isle of Man": "Isle of Man", "Israel": "Israel", "It is recommended to support your manual input with evidence (visible only to GoJoe and not publicly posted).": "It is recommended to support your manual input with evidence (visible only to <PERSON><PERSON><PERSON> and not publicly posted).", "It is recommended to support your manual input with evidence (visible only to GoJoe and not publicly posted). If another joe flags your activity for review, the GoJoe referee will take a look.": "It is recommended to support your manual input with evidence (visible only to <PERSON><PERSON>oe and not publicly posted). If another joe flags your activity for review, the GoJoe referee will take a look.", "It's not a valid email": "It's not a valid email", "It's not a valid pin code": "It's not a valid pin code", "Italian": "Italian", "Italy": "Italy", "It’s all about the team! You’ve been invited to the <b>{{name}}</b>.": "It’s all about the team! You’ve been invited to the <b>{{name}}</b>.", "It’s good to talk": "It’s good to talk", "It’s not possible to add or sync activities which overlap": "It’s not possible to add or sync activities which overlap", "Ivory Coast": "Ivory Coast", "JAN": "Jan", "JOIN": "Join", "JOURNEYS": "Journeys", "JUL": "Jul", "JUN": "Jun", "Jamaica": "Jamaica", "Jan": "Jan", "January": "January", "Japan": "Japan", "Japanese": "Japanese", "Jersey": "Jersey", "Joe Code of Ethics": "Joe Code of Ethics", "Join": "Join", "Join Challenge": "Join Challenge", "Join Club": "Join Club", "Join Community": "Join Community", "Join Now": "Join Now", "Join Organization": "Join Organization", "Join Team": "Join Team", "Join a Challenge": "Join a Challenge", "Join a Journey relevant to you - led by an expert, athlete or PT - and complete it on your own, but not alone.": "Join a Journey relevant to you - led by an expert, athlete or PT - and complete it on your own, but not alone.", "Join a team": "Join a team", "Join a team now": "Join a team now", "Join cohorts/groups, and work towards goals as a community.": "Join cohorts/groups, and work towards goals as a community.", "Join me on this challenge": "Join me on this challenge", "Join or create a team to enjoy it!": "Join or create a team to enjoy it!", "Join or create your own team": "Join or create your own team", "Join public groups and meet people that have the same interests as you!": "Join public groups and meet people that have the same interests as you!", "Join the challenge to access the challenge chat": "Join the challenge to access the challenge chat", "Joined": "Joined", "Joining...": "Joining...", "Jordan": "Jordan", "Journaling": "Journaling", "Journey": "Journey", "Journeys": "Journeys", "Jul": "Jul", "July": "July", "Jun": "Jun", "June": "June", "KM": "KM", "Kayak": "<PERSON><PERSON>", "Kayaking": "Kayaking", "Kazakhstan": "Kazakhstan", "Kcal": "Kcal", "Keep Editing": "Keep editing", "Keep your streak going by completing activities every day. Your streak helps you build consistency in your fitness journey.": "Keep your streak going by completing activities every day. Your streak helps you build consistency in your fitness journey.", "Kenya": "Kenya", "Kg": "Kg", "Kilometers": "Kilometers", "Kiribati": "Kiribati", "Km": "KM", "Kms": "Kms", "Korea, Democratic People's Republic of": "Korea, Democratic People's Republic of", "Korea, Republic of": "Korea, Republic of", "Korean": "Korean", "Kosovo": "Kosovo", "Kuwait": "Kuwait", "Kyrgyzstan": "Kyrgyzstan", "LES MILLS BODYATTACK": "LES MILLS BODYATTACK", "LES MILLS BODYBALANCE": "LES MILLS BODYBALANCE", "LES MILLS BODYCOMBAT": "LES MILLS BODYCOMBAT", "LES MILLS BODYPUMP": "LES MILLS BODYPUMP", "LES MILLS CORE": "LES MILLS CORE", "LES MILLS DANCE": "LES MILLS DANCE", "LES MILLS GRIT ATHLETIC": "LES MILLS GRIT ATHLETIC", "LES MILLS GRIT CARDIO": "LES MILLS GRIT CARDIO", "LES MILLS GRIT STRENGTH": "LES MILLS GRIT STRENGTH", "LES MILLS MINDFULNESS": "LES MILLS MINDFULNESS", "LES MILLS RPM": "LES MILLS RPM", "LES MILLS SH'BAM": "LES MILLS SH'BAM", "LES MILLS SPRINT": "LES MILLS SPRINT", "LES MILLS STRETCH": "LES MILLS STRETCH", "LES MILLS STRETCH features a carefully curated combination of equipment-assisted stretching designed to increase your range of motion and relax your muscles.  Combining a variety of mobility and stretching techniques, this zero-impact workout will transform your flexibility, increase your range of motion, relax your muscles and help you maximize athletic performance.": "LES MILLS STRETCH features a carefully curated combination of equipment-assisted stretching designed to increase your range of motion and relax your muscles.  Combining a variety of mobility and stretching techniques, this zero-impact workout will transform your flexibility, increase your range of motion, relax your muscles and help you maximize athletic performance.", "LES MILLS TECHNIQUE": "LES MILLS TECHNIQUE", "LES MILLS THE TRIP": "LES MILLS THE TRIP", "LES MILLS TRAINER SERIES": "LES MILLS TRAINER SERIES", "LES MILLS WELLNESS": "LES MILLS WELLNESS", "LIBRARY": "LIBRARY", "LOCKER": "LOCKER", "LOW IMPACT": "LOW IMPACT", "Language": "Language", "Lao People's Democratic Republic": "Lao People's Democratic Republic", "Laos": "Laos", "Last name": "Last Name", "Last name is required": "Last Name is required", "Last name must be at least 2 characters.": "Last name must be at least 2 characters.", "Last question": "Last question", "Later": "Later", "Latest Challenge": "Latest Challenge", "Latest average workouts": "Latest average workouts", "Latest in {{businessName}}": "Latest in {{businessName}}", "Latvia": "Latvia", "Latvian": "Latvian", "Lbs": "Lbs", "Leaderboard": "Leaderboard", "Leaderboards": "Leaderboards", "Leaderboards are hidden until the challenge ends.": "Leaderboards are hidden until the challenge ends.", "Learn": "Learn", "Learn more": "Learn more", "Leave": "Leave", "Leave Club": "Leave Club", "Leave Community": "Leave Community", "Leave Organisation": "Leave Organisation", "Leave Team": "Leave Team", "Leave team": "Leave Team", "Leave team error": "Leave team error", "LeaveTeamPrefix": "This will remove you from the team", "LeaveTeamSufix": "and all your activities will not count anymore in the challenge.", "Leaving...": "Leaving...", "Lebanon": "Lebanon", "Legal": "Legal", "Legendary Joe": "Legendary <PERSON>", "Les Mills": "<PERSON>", "Les Mills Programs": "Les <PERSON> Programmes", "Lesotho": "Lesotho", "Let GoJoe guide your wellbeing": "Let <PERSON><PERSON><PERSON> guide your wellbeing", "Let us know! Just send us the details - what type of club you're thinking (public or organisation-only) and which activity it's for - and we'll do our best to make it happen.": "Let us know! Just send us the details - what type of club you're thinking (public or organisation-only) and which activity it's for - and we'll do our best to make it happen.", "Let us tailor GoJoe to your needs": "Let us tailor <PERSON><PERSON><PERSON> to your needs", "Let's begin with": "Let's begin with", "Let's go": "Let's go", "Let's start chatting!": "Let's start chatting!", "Let’s focus on": "Let’s focus on", "Let’s focus on...": "Let’s focus on...", "Let’s get to know each other. What’s your name?": "Let’s get to know each other. What’s your name?", "Level": "Level", "Level {{level}}": "Level {{level}}", "Liberia": "Liberia", "Library": "LIBRARY", "Libya": "Libya", "Libyan Arab Jamahiriya": "Libyan Arab Jam<PERSON>riya", "Liechtenstein": "Liechtenstein", "Lifestyle": "Lifestyle", "Lifter": "Lifter", "Link": "Link", "Links are disabled": "Links are disabled", "Lithuania": "Lithuania", "Live Chat": "Live Chat", "Live Chat Support": "Live Chat Support", "Loading channels...": "Loading channels...", "Loading messages...": "Loading messages...", "Loading...": "Loading...", "Location": "Location", "Locker": "Locker", "Log Out": "Log out", "Log out": "Log out", "Logging your first activity": "Logging your first activity", "Loneliness": "Loneliness", "Looking to join a club?": "Looking to join a club?", "Looks like": "Looks like", "Looks like\nyour code belongs to": "Looks like\nyour code belongs to", "Looks like the code belongs to": "Looks like the code belongs to", "Looks like this code belongs to a challenge restricted to community members only": "Looks like this code belongs to a challenge restricted to community members only", "Looks like this code belongs to a challenge restricted to organisation members only": "Looks like this code belongs to a challenge restricted to organisation members only", "Looks like your code belongs to a challenge.": "Looks like your code belongs to a challenge.", "Looks like you’re having a rest week?": "Looks like you’re having a rest week?", "Looks like you’ve been pushing yourself lately, please prioritise rest for the rest of the day": "Looks like you’ve been pushing yourself lately, please prioritise rest for the rest of the day", "Low Impact": "LOW IMPACT", "Lower Body": "Lower body", "Luxembourg": "Luxembourg", "MAR": "Mar", "MAY": "May", "MI": "MI", "MM": "MM", "MORE": "More", "MY TEAM": "My team", "Macau": "Macau", "Macro Nutrients": "Macro Nutrients", "Madagascar": "Madagascar", "Malawi": "Malawi", "Malay": "Malay", "Malaysia": "Malaysia", "Maldives": "Maldives", "Male": "Male", "Mali": "Mali", "Malta": "Malta", "Manage Team": "Manage Team", "Manage weight": "Manage weight", "Manual\nActivity": "Manual\nActivity", "Manual Activity": "Manual Activity", "Maori": "<PERSON><PERSON>", "Map Type": "Map Type", "Mar": "Mar", "March": "March", "Mark Completed": "Mark Completed", "Marshall Islands": "Marshall Islands", "Martial Arts": "Martial arts", "Martial arts": "Martial arts", "Martinique": "Martinique", "Master the correct technique to perform set moves in this series of short videos.": "Master the correct technique to perform set moves in this series of short videos.", "Mauritania": "Mauritania", "Mauritius": "Mauritius", "Max team size": "Max team size", "Max. 6h/day and 15h/week of manual activities added": "Max. 6h/day and 15h/week of manual activities added", "Max. 8h/day and 36h/week of manual activities added": "Max. 8h/day and 36h/week of manual activities added", "Maximum of weight is {{size}} kg": "Maximum of weight is {{size}} kg", "May": "May", "Maybe Later": "Maybe Later", "Mayotte": "Mayotte", "Measurement": "Measurement", "Measurement Units": "Measurement Units", "Measurement is required.": "Measurement is required.", "Meditation": "Meditation", "Meditation audios, workouts and tips from experts and athletes, on topics from sleep to mental health.": "Meditation audios, workouts and tips from experts and athletes, on topics from sleep to mental health.", "Members": "Members", "Menopause": "Menopause", "Mental Fitness": "Mental Fitness", "Mental Health": "Mental Health", "Mental fitness": "Mental Fitness", "Message Reactions": "Message Reactions", "Message deleted": "Message deleted", "Message flagged": "Message flagged", "Metric": "Metric", "Mexico": "Mexico", "Mi": "<PERSON>", "Micronesia": "Micronesia", "Micronesia, Federated States of": "Micronesia, Federated States of", "Mighty Joe": "Mighty Joe", "Miles": "<PERSON>", "Min. 20 characters": "Min. 20 characters", "Mind Bod": "Mind Bod", "Mind Body": "Mind Body", "Mindfulness": "Mindfulness", "Mindset": "Mindset", "Minimum of weight allowed is {{size}} 111kg": "Minimum of weight allowed is {{size}} kg", "Minimum of weight allowed is {{size}} kg": "Minimum of weight allowed is {{size}} kg", "Minutes": "minutes", "Mobility": "Mobility", "Moldova": "Moldova", "Moldova, Republic of": "Moldova, Republic of", "Mon": "Mon", "Monaco": "Monaco", "Mongolia": "Mongolia", "Montenegro": "Montenegro", "Month": "Month", "Montserrat": "Montserrat", "More Settings": "More Settings", "More details": "More details", "Morning": "Morning", "Morocco": "Morocco", "Most Km's": "Most Km's", "Most Points": "Most Points", "Most activities": "Most activities", "Most different activity types": "Most different activity types", "Most km": "Most km", "Most kms": "Most kms", "Most points": "Most Points", "Most points Most points": "Most points Most points", "Motivation": "Motivation", "Move": "Move", "Movement": "Movement", "Mozambique": "Mozambique", "Multi team": "Multi team", "Music": "Music", "Mute User": "Mute User", "My Profile": "My profile", "Myanmar": "Myanmar", "Myanmar (formerly Burma)": "Myanmar (formerly Burma)", "Mythical Joe": "Mythical Joe", "NFTs (Non-Fungible Tokens) are unique, digital assets that you earn through certain achievements or challenges in the GoJoe app. They can be used to redeem certain rewards now and in the future.": "NFTs (Non-Fungible Tokens) are unique, digital assets that you earn through certain achievements or challenges in the GoJoe app. They can be used to redeem certain rewards now and in the future.", "NOV": "Nov", "Name": "Name", "Namibia": "Namibia", "Nature": "Nature", "Nauru": "Nauru", "Need Help?": "Need Help?", "Need help getting started with training?": "Need help getting started with training?", "Need help getting started?": "Need help getting started?", "Need help gettingstarted with training?": "Need help gettingstarted with training?", "Nepal": "Nepal", "Netball": "Netball", "Netherlands": "Netherlands", "Netherlands Antilles": "Netherlands Antilles", "Never": "Never", "Never (0 times per month)": "Never (0 times per month)", "Never Leave a Joe Behind": "Never Leave a Joe Behind", "Never miss an activity": "Never miss an activity", "New": "new", "New Caledonia": "New Caledonia", "New Message": "New Message", "New Zealand": "New Zealand", "Next": "Next", "Nicaragua": "Nicaragua", "Niger": "Niger", "Nigeria": "Nigeria", "Night Owl": "Night Owl", "Nike RC": "Nike RC", "Niue": "Niue", "No": "No", "No Announcements Yet": "No Announcements Yet", "No Earning Boost": "No Earning <PERSON>ost", "No Internet Connection": "No internet connection", "No Shortcuts": "No Shortcuts", "No activities logged this week yet. It’s never too late. Get one on the board": "No activities logged this week yet. It’s never too late. Get one on the board", "No chat client": "No chat client", "No chats here yet…": "No chats here yet…", "No earning boost provided by {{name}}.": "No earning boost provided by {{name}}.", "No journeys yet.": "No journeys yet.", "No points": "No points", "No points earned": "No points earned", "No points spent": "No points spent", "No rewards are available in this country yet.": "No rewards are available in this country yet.", "No, Let me Continue": "No, Let me Continue", "No, thanks": "No, Thanks", "Non-binary": "Non-binary", "None": "None", "Norfolk Island": "Norfolk Island", "North Korea": "North Korea", "North Macedonia": "North Macedonia", "Northern Mariana Islands": "Northern Mariana Islands", "Norway": "Norway", "Not Now": "Not Now", "Not enough points": "Not enough points", "Not received an email? Check your junk..., but if not in there, <NAME_EMAIL>": "Not received an email? Check your junk..., but if not in there, <NAME_EMAIL>", "Not supported": "Not supported", "Nothing yet...": "Nothing yet...", "Notifications": "Notifications", "Notifications turned OFF! (We're not offended)": "Notifications turned OFF! (We're not offended)", "Notifications turned ON!": "Notifications turned ON!", "Nov": "Nov", "November": "November", "Now get out there and move!": "Now get out there and move!", "Nutrients": "Nutrients", "Nutrition": "Nutrition", "OCT": "Oct", "OK": "OK", "Oct": "Oct", "October": "October", "Offered by": "Offered by", "Often": "Often", "Often (6-8 times per month)": "Often (6-8 times per month)", "Ok": "OK", "Oman": "Oman", "On the home straight": "On the home straight", "On track": "On track", "On your own, but not alone": "On your own, but not alone", "Ongoing": "Ongoing", "Only visible to you": "Only visible to you", "Oops!": "Oops!", "Open Settings": "Open Settings", "Open inbox": "Open inbox", "Or continue without a code": "Or continue without a code", "Or invite users that aren't on GoJoe yet": "Or invite users that aren't on GoJoe yet", "Ordinary Joe": "Ordinary Joe", "Organisation": "Organisation", "Organisations": "Organisations", "Organisations can choose to contribute additional reward funds, enabling Joes to earn more points (and therefore rewards) beyond the standard GoJoe Premium allowance of up to 200 points per month. This is known as an earning boost.": "Organisations can choose to contribute additional reward funds, enabling Joes to earn more points (and therefore rewards) beyond the standard GoJoe Premium allowance of up to 200 points per month. This is known as an earning boost.", "Organisations can enhance this with earning boosts, increasing the points and rewards you can earn every month.": "Organisations can enhance this with earning boosts, increasing the points and rewards you can earn every month.", "Other": "Other", "Our multi-team, community-wide challenges are best-in-class.": "Our multi-team, community-wide challenges are best-in-class.", "Our multi-team, organisation-wide challenges are best-in-class.": "Our multi-team, organisation-wide challenges are best-in-class.", "Out the blocks": "Out the blocks", "Overview": "Overview", "Overwhelm": "Overwhelm", "P.S.: Costi is close, if you keep slacking off, he’ll pass you soon": "P.S.: <PERSON><PERSON><PERSON> is close, if you keep slacking off, he’ll pass you soon", "PACE": "Pace", "PLAY": "Play", "PLease fix the following errors": "PLease fix the following errors", "Pace": "Pace", "Paddleboard": "Paddleboard", "Paddleboarding": "Paddleboarding", "Padel": "Pa<PERSON>", "Pakistan": "Pakistan", "Palau": "<PERSON><PERSON>", "Palestine": "Palestine", "Palestine State": "Palestine State", "Panama": "Panama", "Papua New Guinea": "Papua New Guinea", "Paraguay": "Paraguay", "Part of": "Part of", "Part of Team\n{{team}}": "Part of Team\n{{team}}", "Part of:": "Part of:", "Partial Connection": "Partial Connection", "Past days": "Past days", "Patience": "Patience", "Pause alert for 3 days": "Pause alert for 3 days", "Pawsitivity": "Pawsitivity", "People you want to add not on GoJoe yet? They can join later (after you create your team) by following the sign-up instructions → inputting the code → pressing join team → searching for and pressing into {{name}} → pressing join.": "People you want to add not on GoJoe yet? They can join later (after you create your team) by following the sign-up instructions → inputting the code → pressing join team → searching for and pressing into {{name}} → pressing join.", "Perfect for corporate, school or charity events.": "Perfect for corporate, school or charity events.", "Personal Focus": "Personal Focus", "Personal Growth": "Personal growth", "Peru": "Peru", "Philippines": "Philippines", "Photo": "Photo", "Photos and Videos": "Photos and Videos", "Pickle": "Pickle", "Pickleball": "Pickleball", "Pilates": "Pilates", "Pin code": "PIN Code", "Pin code is required": "Pin code is required", "Pin to Conversation": "Pin to Conversation", "Pinned by": "Pinned by", "Pitcairn": "Pitcairn", "Pizza": "Pizza", "Please allow Audio permissions in settings.": "Please allow Audio permissions in settings.", "Please check your signal level and try again.": "Please check your signal level and try again.", "Please enable access to your photos and videos so you can share them.": "Please enable access to your photos and videos so you can share them.", "Please fill in your answer": "Please fill in your answer", "Please fix the following errors": "PLease fix the following errors", "Please select a channel first": "Please select a channel first", "Please select country": "Please select country", "Please select team": "Please select team", "Please specify": "Please specify", "Please try again": "Please try again", "Please try again later": "Please try again later", "Please try again later.": "Please try again later.", "Please wait for the app to reconnect to the chat server.": "Please wait for the app to reconnect to the chat server.", "Please wait while we download the latest version of the app.": "Please wait while we download the latest version of the app.", "Points": "Points", "Points Caps": "Points Caps", "Points cap": "Points cap", "Poland": "Poland", "Polish": "Polish", "Poor quality network. ": "Poor quality network. ", "Popular tags": "Popular tags", "Portugal": "Portugal", "Portuguese": "Portuguese", "Pos": "Pos", "Position": "Position", "Positivity": "Positivity", "Post": "Post", "Post deleted successfully": "Post deleted successfully", "Postie": "<PERSON><PERSON>", "Posts": "Posts", "Powered by community, for the masses.": "Powered by community, for the masses.", "Prefer not to say": "Prefer not to say", "Preferences": "Preferences", "Preferred activity": "Preferred activity", "Premium challenge": "Premium challenge", "Premium subscription to 350+ ready-to-stream workouts": "Premium subscription to 350+ ready-to-stream workouts", "Press Go to Business button to go to the business screen.": "Press Go to Business button to go to the business screen.", "Press the <b>' + '</b>button to add or <b>track an activity.</b>": "Press the <b>' + '</b>button to add or <b>track an activity.</b>", "Press the <b>' + '</b>button to add or <b>track an activity.</b> Press the <b>' + '</b>button to add or <b>track an activity.</b>": "Press the <b>' + '</b>button to add or <b>track an activity.</b> Press the <b>' + '</b>button to add or <b>track an activity.</b>", "Press the <b>' O '</b>button to add or <b>track an activity.</b> Press the <b>' O '</b>button to add or <b>track an activity.</b>": "Press the <b>' O '</b>button to add or <b>track an activity.</b> Press the <b>' O '</b>button to add or <b>track an activity.</b>", "Press the <o></o> button to add or track an activity.": "Press the <o></o> button to add or track an activity.", "Press to change the image": "Press to change the image", "Press to filter by activity types": "Press to filter by activity types", "Preview": "Preview", "Privacy Controls": "Privacy Controls", "Privacy Policy": "Privacy Policy", "Private account": "Private account", "Pro Joe": "Pro Joe", "Pro Joe's": "Pro Joe's", "Probiotic": "Probiotic", "Proceed": "Proceed", "Professional & Financial Growth": "Professional & Financial Growth", "Professional Growth": "Professional Growth", "Profile": "Profile", "Protein": "<PERSON><PERSON>", "Proteins": "<PERSON>teins", "Provide specific details - the more the better: include supporting information and any specific details that exhibit issues.": "Provide specific details - the more the better: include supporting information and any specific details that exhibit issues.", "Pts": "Pts", "Public Groups": "Public Groups", "Publish": "Publish", "Puerto Rico": "Puerto Rico", "Push Notification enabled": "Push Notification enabled", "Push Notifications": "Push Notifications", "Push notifications": "Push notifications", "Push notifications are not activated.": "Push notifications are not activated.", "Put the focus on your abdominal muscles in a short workout designed to improve your core strength and tone your abs.": "Put the focus on your abdominal muscles in a short workout designed to improve your core strength and tone your abs.", "QUOTE OF THE DAY": "QUOTE OF THE DAY", "Qatar": "Qatar", "Quick Links": "Quick Links", "Quick personality-packed sessions featuring popular moves, exercise innovations, and plenty of banter to keep your motivation at an all-time high. Options for all fitness levels and no equipment needed.": "Quick personality-packed sessions featuring popular moves, exercise innovations, and plenty of banter to keep your motivation at an all-time high. Options for all fitness levels and no equipment needed.", "Quote of the Day": "Quote of the day", "Quote of the day": "Quote of the day", "READY": "Ready", "Racket sports": "Racket sports", "Rarely": "Rarely", "Rarely (1-2 times per month)": "Rarely (1-2 times per month)", "Reactivate": "Reactivate", "Read more": "Read more", "Ready": "Ready", "Recipe": "Recipe", "Recommended clubs for you": "Recommended clubs for you", "Reconnecting...": "Reconnecting...", "Record\nActivity": "Record\nActivity", "Record Activity": "Record Activity", "Record activities and start collecting points.": "Record activities and start collecting points.", "Recovery": "Recovery", "Redeem": "Redeem", "Redeem {{value}} voucher": "Redeem {{value}} voucher", "Regular Joe": "Regular Joe", "Relationships": "Relationships", "Relaxation": "Relaxation", "Remove": "Remove", "Remove photo": "Remove photo", "Remove this section": "Remove this section", "Reply": "Reply", "Reply to Message": "Reply to Message", "Republic of Congo": "Republic of Congo", "Request a Club": "Request a Club", "Request a club": "Request a club", "Requirements": "Requirements", "Resend": "Resend", "Resilience": "RESILIENCE", "Resources": "resources", "Respect": "Respect", "Respect:": "Respect:", "Rest": "rest", "Restart Now": "Restart Now", "Resume": "RESUME", "Resume Activity?": "Resume Activity?", "Reunion": "Reunion", "Rewards are only available for our premium users.": "Rewards are only available for our premium users.", "Rewards have been restricted by your organisation in your country.": "Rewards have been restricted by your organisation in your country.", "Road Runner": "Road Runner", "Romania": "Romania", "Romanian": "Romanian", "Routines": "Routines", "Row": "Row", "Rowing": "Rowing", "Rugby": "Rugby", "Run": "Run", "Running": "Running", "Running Distance": "Running Distance", "Russia": "Russia", "Russian Federation": "Russian Federation", "Rwanda": "Rwanda", "SEP": "Sep", "SET": "Set", "SS": "SS", "SSL Pinning Error": "SSL <PERSON><PERSON>", "Sail": "Sail", "Sailing": "Sailing", "Saint Kitts and Nevis": "Saint Kitts and Nevis", "Saint Lucia": "Saint Lucia", "Saint Vincent and the Grenadines": "Saint Vincent and the Grenadines", "Samoa": "Samoa", "San Marino": "San Marino", "Sao Tome and Principe": "Sao Tome and Principe", "Sat": "Sat", "Saudi Arabia": "Saudi Arabia", "Save": "Save", "Save Activity": "Save Activity", "Save changes": "Save Changes", "Saving...": "Saving...", "Science": "Science", "Screen?!": "Screen?!", "Search": "Search", "Search GIFs": "Search GIFs", "Search for a team and tap on it to join this challenge": "Search for a team and tap on it to join this challenge", "Search for a team or create your own team": "Search for a team or create your own team", "Search for a team to join this challenge": "Search for a team to join this challenge", "Search for activity type": "Search for activity type", "Search for community members": "Search for community members", "Search for groups or chats": "Search for groups or chats", "Search for members": "Search for members", "Search for organisation members": "Search for organisation members", "Search for organization members": "Search for organization members", "Search...": "Search...", "Searching for GPS signal...": "Searching for GPS signal...", "Select Country": "Select Country", "Select Date": "Select date", "Select More Photos": "Select More Photos", "Select Time": "Select Time", "Select another sport": "Select another sport", "Select country": "Select country", "Select date": "Select date", "Select discipline": "Select discipline", "Select discipline(s)": "Select discipline(s)", "Select distance": "Select distance", "Select language": "Select language", "Select your voucher amount": "Select your voucher amount", "Self Improvement": "Self Improvement", "Send Anyway": "Send Anyway", "Send a message": "Send a message", "Sending links is not allowed in this conversation": "Sending links is not allowed in this conversation", "Senegal": "Senegal", "Sep": "Sep", "September": "September", "Serbia": "Serbia", "Set challenge": "Set challenge", "Set profile as private": "Set profile as private", "Settings": "Settings", "Setup your challenge rules": "Setup your challenge rules", "Seychelles": "Seychelles", "Short description": "Short description", "Show Map": "Show Map", "Show burned calories as": "Show burned calories as", "Show less": "Show less", "Show more": "Show more", "Sierra Leone": "Sierra Leone", "Since you last opened the app:": "Since you last opened the app:", "Singapore": "Singapore", "Skate": "Skate", "Skateboard": "Skateboard", "Skateboarding": "Skateboarding", "Skating": "Skating", "Ski": "Ski", "Ski Activity": "Ski Activity", "Skiing": "Skiing", "Skin": "Skin", "Skip": "<PERSON><PERSON>", "Skip survey": "Skip survey", "Slammer": "<PERSON><PERSON>", "Sleep": "Sleep", "Slide into DMs": "Slide into DMs", "Slovak": "Slovak", "Slovakia": "Slovakia", "Slovenia": "Slovenia", "Slovenian": "Slovenian", "Slow mode ON": "Slow mode ON", "Slowing Down": "Slowing Down", "Smashed stated fitness level": "Smashed stated fitness level", "Snacks": "Snacks", "Snow sports": "Snow sports", "Snowboard": "Snowboard", "Snowboarding": "Snowboarding", "Social Media": "Social Media", "Solo": "Solo", "Solo Awards": "Solo Awards", "Solo Standings": "Solo Standings", "Solomon Islands": "Solomon Islands", "Somalia": "Somalia", "Something else": "Something else", "Something went wrong": "Something went wrong", "Something went wrong. Please try again": "Something went wrong. Please try again", "Something went wrong. Please try again.": "Something went wrong. Please try again.", "Sometimes": "Sometimes", "Sometimes (3-5 times per month)": "Sometimes (3-5 times per month)", "Sorry": "Sorry", "South Africa": "South Africa", "South Georgia South Sandwich Islands": "South Georgia South Sandwich Islands", "South Korea": "South Korea", "South Sudan": "South Sudan", "Space Race Challenge": "Space Race Challenge", "Spain": "Spain", "Spanish": "Spanish", "Speak to your organisation about setting up GoJoe Premium to start earning rewards.": "Speak to your organisation about setting up GoJoe Premium to start earning rewards.", "Spend": "Spend", "Spent": "Spent", "Spin": "Spin", "Splits": "Splits", "Sports Conditioning": "Sports Conditioning", "Spotlight": "Spotlight", "Squash": "Squash", "Sri Lanka": "Sri Lanka", "St": "St", "St. Helena": "St. Helena", "St. Pierre and Miquelon": "St. Pierre and Miquelon", "Start Chat": "Start chat", "Start Time": "Start time", "Start a Journey": "Start a Journey", "Start date": "Start Date", "Start now": "Start now", "Start survey": "Start survey", "Start this Journey": "Start this Journey", "Start time": "Start time", "Start time will be adjusted based on the duration": "Start time will be adjusted based on the duration", "Starts in": "Starts in", "Starts in 3 days": "Starts in 3 days", "Starts in {{count}} day": "Starts in {{count}} day", "Starts in {{count}} day_other": "Starts in {{count}} days", "Starts in {{days}} day": "Starts in {{days}} day", "Starts in {{days}} days": "Starts in {{days}} days", "Stay positive and never give in.\nIt's the GoJoe way!": "Stay positive and never give in.\nIt's the GoJoe way!", "Stay updated with the latest club news, events, and important updates here.": "Stay updated with the latest club news, events, and important updates here.", "Step {{step}} of {{steps}} - GO - Challenge Preview": "Step {{step}} of {{steps}} - GO - Challenge Preview", "Step {{step}} of {{steps}} - Get teams READY": "Step {{step}} of {{steps}} - Get teams READY", "Step {{step}} of {{steps}} - SET Challenge": "Step {{step}} of {{steps}} - SET Challenge", "Streak": "Streak", "Strength": "Strength", "Strength Activity": "Strength Activity", "Strength Training": "Strength Training", "Strength Training:": "Strength Training", "Stress": "Stress", "Stretching": "Stretching", "Submit": "Submit", "Submit code": "Submit code", "Success": "Success", "Success!": "Success!", "Sudan": "Sudan", "Suggested to follow": "Suggested to follow", "Suggestions": "Suggestions", "Sun": "Sun", "Super Joe": "Super Joe", "Supermarket": "Supermarket", "Support": "Support", "Surf": "Surf", "Surfing": "Surfing", "Suriname": "Suriname", "Svalbard and Jan Mayen Islands": "Svalbard and Jan Mayen Islands", "Sweden": "Sweden", "Swedish": "Swedish", "Swim": "Swim", "Swim Distance": "Swim Distance", "Swimming": "Swimming", "Swipe to Start": "Swipe to Start", "Swipe to Stop": "Swipe to Stop", "Switzerland": "Switzerland", "Sync Now": "Sync Now", "Sync your wearable and record future activities to GoJoe": "Sync your wearable and record future activities to GoJoe", "Sync your wearable and record future activities to GoJoe or read the <pressing>FAQs</pressing>": "Sync your wearable and record future activities to GoJoe or read the <pressing>FAQs</pressing>", "Syria": "Syria", "Syrian Arab Republic": "Syrian Arab Republic", "TEAM FULL": "TEAM FULL", "TEAM IS FULL": "TEAM IS FULL", "TEAM SIZE {{teamUserCount}} / {{maxTeamSize}}": "TEAM SIZE {{teamUserCount}} / {{maxTeamSize}}", "THIS WEEK": "This week", "TIP": "TIP", "Table Tennis": "Table Tennis", "Taiwan": "Taiwan", "Tajikistan": "Tajikistan", "Take a 5 minute break and meditate": "Take a 5 minute break and meditate", "Take a photo": "Take a photo", "Take a solo health and fitness journey bespoke to your goal": "Take a solo health and fitness journey bespoke to your goal", "Taking Care Of Yourself": "Taking Care Of Yourself", "Tanzania": "Tanzania", "Tanzania, United Republic of": "Tanzania, United Republic of", "Target": "Target", "Target Practice": "Target Practice", "Team": "Team", "Team Average": "Team Average", "Team Averages": "Team Averages", "Team Awards": "Team Awards", "Team Capitan": "Team Capitan", "Team Captain": "Team Captain", "Team Size": "Team Size", "Team Standings": "Team Standings", "Team invitation shared successfully": "Team invitation shared successfully", "Team name": "Team Name", "Team name is required Team name is required": "Team name is required Team name is required", "Team size": "Team size", "Team sports": "Team sports", "Team {{teamName}}": "Team {{team<PERSON>ame}}", "Teams": "Teams", "Teamwork": "Teamwork", "Technique": "Technique", "Tell us how we can level up?": "Tell us how we can level up?", "Tell us in the chat how it was": "Tell us in the chat how it was", "Tell us who you are": "Tell us who you are", "Temperature": "Temperature", "Tennis": "Tennis", "Terms of Service": "Terms of Service", "Terms of Use": "Terms of use", "Thai": "Thai", "Thailand": "Thailand", "The Fair Play Book of Rules": "The Fair Play Book of Rules", "The Fair Play book of Rules": "The Fair Play Book of Rules", "The Great Outdoors": "The Great Outdoors", "The Joe Code": "The Joe Code", "The Office": "The Office", "The Science of Fitness": "The Science of Fitness", "The challenge <bold>{{challangeName}}</bold> has finished. Phew!": "The challenge <bold>{{challange<PERSON><PERSON>}}</bold> has finished. Phew!", "The code belongs to a Community": "The code belongs to a Community", "The comment was deleted": "The comment was deleted", "The feed is where activities you log and other content relevant to you will appear.": "The feed is where activities you log and other content relevant to you will appear.", "The message has been reported to a moderator.": "The message has been reported to a moderator.", "The organisations you are part of do not currently contribute additional reward funds.": "The organisations you are part of do not currently contribute additional reward funds.", "The team which logs the most kms between the start and end time, wins!": "The team which logs the most kms between the start and end time, wins!", "The update has been downloaded and is ready to install. The app will restart to apply the update.": "The update has been downloaded and is ready to install. The app will restart to apply the update.", "There is an active challenge that you can join": "There is an active challenge that you can join", "There is no community challenge available right now. You can create your own by pressing the 'O' button below.": "There is no community challenge available right now. You can create your own by pressing the 'O' button below.", "There was an SSL Pinning error. Please check your connection.": "There was an SSL Pinning error. Please check your connection.", "These are shown to improve motivation by over 45%.": "These are shown to improve motivation by over 45%.", "These include updates on GoJoe challenges messages from friends, follower activity and other personalisations to keep you motivated.": "These include updates on GoJ<PERSON> challenges messages from friends, follower activity and other personalisations to keep you motivated.", "These include updates on GoJoe challenges, messages from friends, follower activity and other personalisation to keep you motivated.": "These include updates on <PERSON><PERSON>oe challenges, messages from friends, follower activity and other personalisation to keep you motivated.", "Things currently affecting you": "Things currently affecting you", "This account is due to be deleted in {{count}} day": "This account is due to be deleted in {{count}} day", "This account is due to be deleted in {{count}} days": "This account is due to be deleted in {{count}} days", "This account is private. Follow the account to see their activities and posts.": "This account is private. Follow the account to see their activities and posts.", "This account is private. You cannot see his activites": "This account is private. You cannot see his activites", "This activity has been flagged and is under review. Activity still appears in the leaderboard and is part of the challenge until we review it.": "This activity has been flagged and is under review. Activity still appears in the leaderboard and is part of the challenge until we review it.", "This activity is flagged and is under review": "This activity is flagged and is under review", "This challenge is restricted to community members only so you can only add org users": "This challenge is restricted to community members only so you can only add org users", "This challenge is restricted to organization members only so you can only add org users": "This challenge is restricted to organization members only so you can only add org users", "This content is only available for our workplace users.": "This content is only available for our workplace users.", "This email is used to login to your account. The email is not visible to other users.": "This email is used to login to your account. The email is not visible to other users.", "This generous boost is designed to supercharge your efforts, making every action count even more. 🚀": "This generous boost is designed to supercharge your efforts, making every action count even more. 🚀", "This post has been flagged and is under review. Post still appears in the leaderboard and is part of the challenge until we review it.": "This post has been flagged and is under review. <PERSON> still appears in the leaderboard and is part of the challenge until we review it.", "This post is flagged and is under review": "This post is flagged and is under review", "This week in a {{nameBusiness}}": "This week in a {{nameBusiness}}", "This week in {{nameBusiness}}": "This week in {{nameBusiness}}", "This will help you find friends": "This will help you find friends", "This will help you find friends and take part in Challenges": "This will help you find friends and take part in Challenges", "Thread Reply": "<PERSON><PERSON><PERSON><PERSON>", "Thu": "<PERSON>hu", "Time": "Time", "Time is required": "Time is required", "Time to slow down": "Time to slow down", "Timor-Leste": "Timor-Leste", "To activate them, press here.": "To activate them, press here.", "To get you started, we’ve outlined a few steps on how to get the most from your GoJoe account.": "To get you started, we’ve outlined a few steps on how to get the most from your GoJoe account.", "To get you started, we’ve outlined a few things on how to get the most from your GoJoe account": "To get you started, we’ve outlined a few things on how to get the most from your GoJoe account", "To keep the challenge fun and fair, we have controls, including GoJoe referees. Referees can filter Joes out of the main leaderboard to an unfiltered version if something didn't look right or just to cool things down and reduce excessive exercise. Don't worry if you're filtered out, you can still toggle to see the unfiltered leaderboard.": "To keep the challenge fun and fair, we have controls, including <PERSON><PERSON>oe referees. Referees can filter <PERSON><PERSON> out of the main leaderboard to an unfiltered version if something didn't look right or just to cool things down and reduce excessive exercise. Don't worry if you're filtered out, you can still toggle to see the unfiltered leaderboard.", "To make sure your notifications are switched on, go to Account > ⚙ > Push Notifications > Turn on To make sure your notifications are switched on, go to Account > ⚙ > Push Notifications > Turn on": "To make sure your notifications are switched on, go to Account > ⚙ > Push Notifications > Turn on To make sure your notifications are switched on, go to Account > ⚙ > Push Notifications > Turn on", "To reactivate the account, press the button.": "To reactivate the account, press the button.", "To send a direct message, press the": "To send a direct message, press the", "Today": "Today", "Together is Better: Join a Club": "Together is Better: Join a <PERSON>", "Togo": "Togo", "Tokelau": "Tokelau", "Tonga": "Tonga", "Too many attempts. Please log in again.": "Too many attempts. Please log in again.", "Too many requests. Please try again later.": "Too many requests. Please try again later.", "Top creators": "Top creators", "Total distance logged in the Cycling Activity": "Total distance logged in the Cycling Activity", "Total distance logged in the Running Activity": "Total distance logged in the Running Activity", "Total distance logged in the Swim Activity": "Total distance logged in the Swim Activity", "Total distance logged in the Walk Activity": "Total distance logged in the Walk Activity", "Total time logged in the HIIT Activity": "Total time logged in the HIIT Activity", "Total time logged in the Ski Activity": "Total time logged in the Ski Activity", "Total time logged in the Strength Activity": "Total time logged in the Strength Activity", "Total time logged in the Yoga Activity": "Total time logged in the Yoga Activity", "Track": "Track", "Train with GoJoe": "Train with Go<PERSON>oe", "Train with Gojoe": "Train with <PERSON><PERSON><PERSON>", "Training": "Training", "Trans-gender": "Trans-gender", "Trauma": "<PERSON>rauma", "Travel frequently": "Travel frequently", "Triathlon": "Triathlon", "Trinidad and Tobago": "Trinidad and Tobago", "Trouble with Samsung Health?": "Trouble with Samsung Health?", "Try again": "Try again", "Tue": "<PERSON><PERSON>", "Tunisia": "Tunisia", "Turkey": "Turkey", "Turkmenistan": "Turkmenistan", "Turks and Caicos Islands": "Turks and Caicos Islands", "Turn on Notifications": "Turn on Notifications", "Tuvalu": "Tuvalu", "Uganda": "Uganda", "Ukraine": "Ukraine", "Unable to Sign In": "Unable to Sign In", "Unblock User": "Unblock user", "Understood": "Understood", "Understood, Let’s Begin": "Understood, Let’s Begin", "Unfiltered Leaderboards": "Unfiltered Leaderboards", "Unfiltered leaderboard": "Unfiltered leaderboard", "Unfiltered leaderboards": "Unfiltered leaderboards", "Unfollow": "Unfollow", "United Arab Emirates": "United Arab Emirates", "United Kingdom": "United Kingdom", "United States": "United States", "United States minor outlying islands": "United States Minor Outlying Islands", "United States of America": "United States of America", "Unknown User": "Unknown User", "Unlimited": "Unlimited", "Unlimited team and participants.": "Unlimited team and participants.", "Unlock Rewards with Premium": "Unlock Rewards with Premium", "Unmute User": "Unmute User", "Unpin from Conversation": "Unpin from Conversation", "Unread Messages": "Unread Messages", "Unwind for the day with in-app hypnotherapy meditation audios": "Unwind for the day with in-app hypnotherapy meditation audios", "Unwind with in-app hypnotherapy meditation audios": "Unwind with in-app hypnotherapy meditation audios", "Upcoming": "Upcoming", "Upcoming Club Events": "Upcoming Club Events", "Update Available": "Update Available", "Update Now": "Update Now", "Update Ready to Install": "Update Ready to Install", "Upload from gallery": "Upload from gallery", "Upper Body": "Upper body", "Uruguay": "Uruguay", "User avatar for {{name}}": "User avatar for {{name}}", "Users": "Users", "Uzbekistan": "Uzbekistan", "VERSUS": "VERSUS", "Vanuatu": "Vanuatu", "Vatican City State": "Vatican City State", "Venezuela": "Venezuela", "Video": "Video", "Vietnam": "Vietnam", "Vietnamese": "Vietnamese", "View Community Page": "View Community Page", "View Organisation Page": "View Organisation Page", "View Points Allocation": "View Points Allocation", "View Points Caps": "View Points Caps", "View all": "View all", "View all Communities": "View all Communities", "View all Organisations": "View all Organisations", "View all posts": "View all posts", "Virgin Islands (British)": "Virgin Islands (British)", "Virgin Islands (U.S.)": "Virgin Islands (U.S.)", "Volleyball": "Volleyball", "WEEK": "week", "WINNER": "WINNER", "Walk": "Walk", "Walk Distance": "Walk Distance", "Walking": "Walking", "Wallis and Futuna Islands": "Wallis and Futuna Islands", "Want to see it? Give your Head of HR a nudge and send him/ her our way": "Want to see it? Give your Head of HR a nudge and send him/ her our way", "Want to see it? Give your Head of HR a nudge and send him/her our way": "Want to see it? Give your Head of HR a nudge and send him/her our way", "Warm Up": "Warm Up", "Warrior Joe": "Warrior Joe", "Water Polo": "Water Polo", "Water sports": "Water sports", "We are having trouble establishing a secure connection. Please check your internet connection and try again.": "We are having trouble establishing a secure connection. Please check your internet connection and try again.", "We couldn't sign you in. Please try again or use another sign-in method.": "We couldn't sign you in. Please try again or use another sign-in method.", "We encourage manual inputs to be supported by evidence (which can be examined by GoJoe referees)": "We encourage manual inputs to be supported by evidence (which can be examined by GoJoe referees)", "We encourage manual inputs to supported by evidence (which can be examined by GoJoe referees)": "We encourage manual inputs to supported by evidence (which can be examined by GoJoe referees)", "We pull workouts (walk, run, cycle, swim etc) which are logged as activities - from your wearable.": "We pull workouts (walk, run, cycle, swim etc) which are logged as activities - from your wearable.", "We will review this post and will delete it if we find out it was wrongfully added.": "We will review this post and will delete it if we find out it was wrongfully added.", "We're a team.\nWe lift weights and each other!": "We're a team.\nWe lift weights and each other!", "Wearable Only": "Wearable Only", "Wearable only": "Wearable Only", "Wearable only?": "Wearable only?", "Wearables only": "Wearables only", "Wearables only?": "Wearables only?", "Wed": "Wed", "Week": "Week", "Weekend Warriors": "Weekend Warriors", "Weight": "Weight", "Weight (kg)": "Weight (kg)", "Weight (lb)": "Weight (lb)", "Weight Loss": "Weight Loss", "Weight is required.": "Weight is required.", "Welcome": "Welcome", "Welcome back 👋": "Welcome back 👋", "Welcome back, {{name}}": "Welcome back, {{name}}", "Welcome to": "Welcome to", "Welcome to\nthe party": "Welcome to\nthe party", "Welcome to GoJoe, your health and fitness sidekick helping you compete, train, connect and earn all year round.": "Welcome to <PERSON><PERSON><PERSON>, your health and fitness sidekick helping you compete, train, connect and earn all year round.", "Welcome to team {{teamName}}!": "Welcome to team {{teamName}}!", "Welcome to the party": "Welcome to the party", "Welcome, let’s help you discover GoJoe": "Welcome, let’s help you discover <PERSON><PERSON><PERSON>", "Wellbeing": "Wellbeing", "Wellness": "Wellness", "Western Sahara": "Western Sahara", "We’d love to send you cool offers and discounts from GoJoe and our partners by email.": "We’d love to send you cool offers and discounts from GoJoe and our partners by email.", "We’ve got you covered:": "We’ve got you covered:", "We’ve just sent you a one-time pin code to ": "We’ve just sent you a one-time pin code to ", "We’ve just sent you a one-time pin code to {{email}}": "We’ve just sent you a one-time pin code to {{email}}", "We’ve seen that there might be exceptions of epic activities or the wearable just didn’t sync, so if you are not able to add these manually, please reach out and we’ll help you.": "We’ve seen that there might be exceptions of epic activities or the wearable just didn’t sync, so if you are not able to add these manually, please reach out and we’ll help you.", "What Premium unlocks": "What Premium unlocks", "What Premium unlocks:": "What Premium unlocks:", "What are your current goals?": "What are your current goals?", "What on earth are NFTs?": "What on earth are NFTs?", "What's going on?": "What's going on?", "What’s going on?": "What’s going on?", "Wheelchair": "Wheelchair", "When you create or join a challenge, chat channels will magically appear here so you can engage with other joes - whether as friends (teammates) or foes (opponents)": "When you create or join a challenge, chat channels will magically appear here so you can engage with other joes - whether as friends (teammates) or foes (opponents)", "Where do you belong in": "Where do you belong in", "Where do you belong in {{ businessName}}": "Where do you belong in {{ businessName}}", "Where do you belong in {{businessName}}": "Where do you belong in {{businessName}}", "Whether you want to Improve your athletic performances, manage stress, get more focused or be more productive and energetic, we’ve got you covered.": "Whether you want to Improve your athletic performances, manage stress, get more focused or be more productive and energetic, we’ve got you covered.", "Whether you want to improve your athletic performances, manage stress, get more focused or be more productive and energetic, we’ve got you covered.": "Whether you want to Improve your athletic performances, manage stress, get more focused or be more productive and energetic, we’ve got you covered.", "Whether you’re into epic runs, downward dogs, or pumping iron, there’s a crew waiting just for you.": "Whether you’re into epic runs, downward dogs, or pumping iron, there’s a crew waiting just for you.", "Whoops": "Whoops", "Whoops - we don't recognise that pin. Please try again.": "Whoops - we don't recognise that pin. Please try again.", "Whoops! Looks like you don't have permission to access this part of the app.": "Whoops! Looks like you don't have permission to access this part of the app.", "Whoops.": "Whoops.", "Why are you flagging this activity?": "Why are you flagging this activity?", "Why are you flagging this post?": "Why are you flagging this post?", "Winter": "Winter", "Women's & Men's Health": "Women's & Men's Health", "Women's Health": "Women's Health", "Woodpecker": "Wood<PERSON>ker", "Work nights": "Work nights", "Worklife": "Worklife", "Workout": "Workout", "Workout analysis": "Workout analysis", "Write a comment": "Write a comment", "Write a comment...": "Write a comment...", "YES": "Yes", "Yellow Jersey": "Yellow Jersey", "Yemen": "Yemen", "Yes": "Yes", "Yes, End Journey": "Yes, End Journey", "Yes, disable them please": "Yes, disable them please", "Yes, please": "Yes, please", "Yesterday": "Yesterday", "Yoga": "Yoga", "Yoga Activity": "Yoga Activity", "Yoga Fusion": "Yoga Fusion", "You": "You", "You are already part of the business.": "You are already part of the business.", "You are part of": "You are part of", "You are too old!": "You are too old!", "You are too short, the minim input is {{size}} cm.": "You are too short, the minim input is {{size}} cm.", "You are too tall, the max input is {{size}} cm.": "You are too tall, the max input is {{size}} cm.", "You are too young!": "You are too young!", "You can also": "You can also", "You can also <b>sync a wearable</b> from Profile > Settings > Applications and Devices.": "You can also <b>sync a wearable</b> from Profile > Settings > Applications and Devices.", "You can also <b>sync a wearable</b> from Profile > Settings > Applications and Devices. You can also <b>sync a wearable</b> from Profile > Settings > Applications and Devices.": "You can also <b>sync a wearable</b> from Profile > Settings > Applications and Devices. You can also <b>sync a wearable</b> from Profile > Settings > Applications and Devices.", "You can also join later, after you register, if you can’t find it.": "You can also join later, after you register, if you can’t find it.", "You can also sync a wearable by pressing here, or from Profile Icon (top left) > Settings > <l>Applications and Devices</l>.": "You can also sync a wearable by pressing here, or from Profile Icon (top left) &gt; Settings &gt; <l>Applications and Devices</l>.", "You can join GoJoe solo and build your own community with your friends.": "You can join <PERSON><PERSON><PERSON> solo and build your own community with your friends.", "You can only log activities from the last 3 days": "You can only log activities from the last 3 days", "You can only log activities from the last 48 hours": "You can only log activities from the last 48 hours", "You can't manually input activities that were done more than 48 hours before the input time": "You can't manually input activities that were done more than 48 hours before the input time", "You can't send messages in this channel": "You can't send messages in this channel", "You cannot backdate an activity more than 48 hours.": "You cannot backdate an activity more than 48 hours.", "You cannot backdate an activity more than 48 hours. You cannot backdate an activity more than 48 hours.": "You cannot backdate an activity more than 48 hours. You cannot backdate an activity more than 48 hours.", "You don’t belong to an community": "You don’t belong to an community", "You don’t belong to an organization": "You don’t belong to an organization", "You have": "You have", "You have a {{count}}-day streak going on.": "You have a {{count}}-day streak going on.", "You have a {{count}}-day streak going on._other": "You have a {{count}}-day streak going on.", "You have changed the image of team!": "You have changed the image of team!", "You have changed the name of team to {{teamName}}!": "You have changed the name of team to {{teamName}}!", "You have granted some permissions.": "You have granted some permissions.", "You have no new notifications.": "You have no new notifications.", "You have stumbled across something magical.": "You have stumbled across something magical.", "You have successfully joined the challenge and you're part of": "You have successfully joined the challenge and you're part of", "You have successfully joined the team.": "You have successfully joined the team.", "You have successfully left the team.": "You have successfully left the team.", "You recorded 4 activities:": "You recorded 4 activities:", "You successfully added {{userName}} to your {{teamName}} team.": "You successfully added {{userName}} to your {{teamName}} team.", "You successfully joined": "You successfully joined", "You successfully left team {{teamName}}": "You successfully left team {{teamName}}", "You were tracking before the app was closed. Would you like to resume?": "You were tracking before the app was closed. Would you like to resume?", "You will loose all progress. You can restart it anytime": "You will loose all progress. You can restart it anytime", "You're in": "You're in", "You're on a <b>{{count}}-day</b> GoJoe streak": "You're on a <b>{{count}}-day</b> <PERSON><PERSON><PERSON> streak", "You're on a <b>{{count}}-day</b> GoJoe streak_one": "You're on a <b>{{count}}-day</b> <PERSON><PERSON><PERSON> streak", "You're on a <b>{{count}}-day</b> GoJoe streak_other": "You're on a <b>{{count}}-day</b> <PERSON><PERSON><PERSON> streak", "You've been added to a team – <bold>{{teamName}}</bold> in the <bold>{{challengeName}}</bold> challenge.": "You've been added to a team – <bold>{{teamName}}</bold> in the <bold>{{challengeName}}</bold> challenge.", "Your <bold>{{sportTile}}</bold> activity from <bold>{{date}}</bold> was successfully uploaded to GoJoe": "Your <bold>{{sportTile}}</bold> activity from <bold>{{date}}</bold> was successfully uploaded to GoJoe", "Your Circle": "Your Circle", "Your Club Awaits - Join the Fun!": "Your Club Awaits - Join the Fun!", "Your Clubs": "Your Clubs", "Your Communities": "Your communities", "Your Reward Boost is Active!": "Your Reward Boost is Active!", "Your Streak": "Your Streak", "Your Week In Numbers": "Your Week In Numbers", "Your communities": "Your communities", "Your organisation doesn’t allow GoJoe rewards.": "Your organisation doesn’t allow GoJoe rewards.", "Your organisations": "Your organisations", "Your organizations": "Your organizations", "Your position": "Your position", "Your profile page displays information about you, such as name, activities, followers, photos and stats.": "Your profile page displays information about you, such as name, activities, followers, photos and stats.", "Your recent chats": "Your recent chats", "Your team is in": "Your team is in", "Your team position": "Your team position", "Your top activities:": "Your top activities:", "Your week in numbers:": "Your week in numbers:", "You’ll get the voucher details on your e-mail and you’ll see your redeemed vouchers in history.": "You’ll get the voucher details on your e-mail and you’ll see your redeemed vouchers in history.", "You’ve been pretty active since you last opened the app.": "You’ve been pretty active since you last opened the app.", "Yummy": "<PERSON><PERSON>", "Zambia": "Zambia", "Zimbabwe": "Zimbabwe", "activities": "Activities", "add event": "Add Event", "add post": "Add post", "added this month": "added this month", "and": "and", "and I confirm that I am not on any medication prescribed for blood pressure or a heart condition.": "and I confirm that I am not on any medication prescribed for blood pressure or a heart condition.", "and take part in Challenges": "and take part in Challenges", "ascent (meters)": "ascent (meters)", "at": "AT", "average speed": "average speed", "but not alone": "but not alone", "but not alone...": "but not alone...", "calories": "Calories", "celsius": "celsius", "challenge name here": "challenge name here", "changer": "changer", "cm": "cm", "completed": "Completed", "contact you?": "contact you?", "createTeamPrefix": "People you want to add not on GoJoe yet? They can join later (after you create your team) by following the sign-up instructions → inputting the code → pressing join team → searching for and pressing into ", "createTeamSufix": " → pressing join.", "creator": "Creator", "day": "day", "dayWithCount_one": "{{count}} day", "dayWithCount_other": "{{count}} days", "days": "days", "description:Sync your wearable and record future activities to GoJoe": "Sync your wearable and record future activities to GoJoe or read the <pressing>FAQs</pressing>", "didn't record it with a wearable?": "didn't record it with a wearable?", "dismiss": "<PERSON><PERSON><PERSON>", "distanceComparison": {"amazonTreks": "You’ve trekked across the Amazon rainforest {{count}} time.", "amazonTreks_other": "You’ve trekked across the Amazon rainforest {{count}} times.", "atlanticCrossings": "That’s like crossing the Atlantic Ocean {{count}} time.", "atlanticCrossings_other": "That’s like crossing the Atlantic Ocean {{count}} times.", "earthTimes": "That’s like going around the world {{count}} time.", "earthTimes_other": "That’s like going around the world {{count}} times.", "everestClimbs": "You've climbed Mount Everest {{count}} time.", "everestClimbs_other": "You've climbed Mount Everest {{count}} times.", "moonTrips": "You’ve traveled enough to reach the Moon and back {{count}} time.", "moonTrips_other": "You’ve traveled enough to reach the Moon and back {{count}} times.", "tourDeFrance": "That’s like completing the Tour de France {{count}} time.", "tourDeFrance_other": "That’s like completing the Tour de France {{count}} times."}, "don't relax, 2nd place is near!": "don't relax, 2nd place is near!", "en": "en", "equivalent_pizza_one": "Equivalent to <b>{{count}}</b> slices of pizza", "equivalent_pizza_other": "Equivalent to <b>{{count}}</b> slices of pizza", "fahrenheit": "fahrenheit", "female": "female", "from": "From", "from Profile &gt; Settings &gt; Applications and Devices.": "from Profile &gt; Settings &gt; Applications and Devices.", "ft-in": "ft-in", "future": "Future", "game": "game", "h": "h", "has been created": "has been created", "header:Can we contact you?": "<break>Can we <break><break1>contact you?</break1>", "header:Connect wearable": "<break>Connect <break><break1>wearable</break1>", "hour": "hour", "hours": "Hours", "hrs": "hrs", "in-person": "in-person", "inc unlimited teams/ppl and more.": "inc unlimited teams/ppl and more.", "inc x2 teams, x10 ppl per team.": "inc x2 teams, x10 ppl per team.", "insights": {"distance": "Distance", "kilometers": "Kms", "miles": "<PERSON>", "mostActiveTimeOfDay": "Most active time of day", "totalTime": "Total Time"}, "kg": "kg", "km": "KM", "kms": "Kms", "lb": "lb", "lbs": "Lbs", "left ": "left ", "m": "m", "male": "male", "mi": "<PERSON>", "min": "min", "min/km": "min/km", "min/km avg pace": "min/km avg pace", "min/km pace": "min/km pace", "minutes": "minutes", "more": "More", "most km": "Most km", "most kms": "Most kms", "n/a": "n/a", "non-binary": "non-binary", "not": "NOT", "notification_activity_loaded": "Your <b>{{wearable}}</b> <b>{{title}}</b> activity was successfully imported as a <b>{{activityType}}</b>.", "notification_activity_loaded_no_format": "Your {{wearable}} {{title}} activity was successfully imported as a <b>{{activityType}}.", "on your own": "on your own", "or": "or", "or continue with": "or continue with", "other": "other", "post": "Post", "prefer not to say": "prefer not to say", "run": "Run", "s": "s", "sec": "sec", "see less": "See less", "solo": "solo", "st": "St", "started": "Started", "survey_take_time_one": "It takes less than a minute", "survey_take_time_other": "It only takes {{count}} minutes", "sync a wearable": "sync a wearable", "tag:Step {{step}} of {{steps}} - Get teams READY": "Step {{step}} of {{steps}} - Get teams READY", "team": "team", "team full": "TEAM FULL", "the party": "the party", "themselves – premium)": "themselves – premium)", "timeComparison": {"bohemian": "Like listening to Bohemian Rhapsody {{count}} times.", "book": "Time to read {{count}} full books cover to cover!", "coffee": "That’s {{count}} coffee breaks ☕.", "football": "You've got time for {{count}} full football matches.", "marathons": "Same as walking 5 marathons at slow pace.", "netflix": "Equivalent to {{count}} Netflix binge nights.", "pizza": "Or {{count}} pizza nights with friends 🍕.", "sleep": "That's the same as getting {{count}} full nights of sleep.", "titanic": "Just like watching <PERSON> on the couch for {{count}} times.", "workWeeks": "As long as {{count}} full work weeks."}, "timeOfDay": {"afternoon": "afternoon", "early": "early", "evening": "evening", "eveningBlock": "evening", "midday": "midday", "middayBlock": "midday", "morning": "morning"}, "timestamp/ChannelPreviewStatus": "{{ timestamp | timestampFormatter(calendar: true; calendarFormats: {\"lastDay\":\"[Yesterday]\", \"lastWeek\":\"dddd\", \"nextDay\":\"[Tomorrow]\", \"nextWeek\":\"dddd [at] LT\", \"sameDay\":\"LT\", \"sameElse\":\"L\"}) }}", "timestamp/ImageGalleryHeader": "{{ timestamp | timestampFormatter(calendar: true) }}", "timestamp/InlineDateSeparator": "{{ timestamp |  timestampFormatter(calendar: true) }}", "timestamp/MessageEditedTimestamp": "{{ timestamp |  timestampFormatter(calendar: true) }}", "timestamp/MessageSystem": "{{ timestamp | timestampFormatter(calendar: true) }}", "timestamp/MessageTimestamp": "{{ timestamp | timestampFormatter(format: LT) }}", "timestamp/StickyHeader": "{{ timestamp | timestampFormatter(calendar: true) }}", "track an activity": "track an activity", "unlimited": "Unlimited", "view profile": "VIEW PROFILE", "virtual": "VIRTUAL", "wearable": "wearable", "webinar": "webinar", "workouts": "Workouts", "yd": "yd", "your code belongs to": "your code belongs to", "your profile": "Your Profile", "{{ firstUser }} and {{ nonSelfUserLength }} more are typing": "{{ firstUser }} and {{ nonSelfUserLength }} more are typing", "{{ index }} of {{ photoLength }}": "{{ index }} of {{ photoLength }}", "{{ replyCount }} Replies": "{{ replyCount }} Replies", "{{ replyCount }} Thread Replies": "{{ replyCount }} Thread Replies", "{{ user }} is typing": "{{ user }} is typing", "{{activityType}} activity": "{{activityType}} activity", "{{activity}} activity": "{{activity}} Activity", "{{avgPace}} min/{{unit}}": "{{avgPace}} min/{{unit}}", "{{bust}} Earning Boost": "{{bust}} Earning <PERSON>", "{{count}} Activities": "{{count}} Activities", "{{count}} Activities this month": "{{count}} Activity this month", "{{count}} Activities this month_other": "{{count}} Activities this month", "{{count}} Activity": "{{count}} Activity", "{{count}} Activity_other": "{{count}} Activities", "{{count}} People": "{{count}} people", "{{count}} People {{count}}": "{{count}} People {{count}}", "{{count}} Person": "{{count}} person", "{{count}} Person {{count}}": "{{count}} Person {{count}}", "{{count}} Team": "{{count}} Team", "{{count}} Team_other": "{{count}} Teams", "{{count}} Teams": "{{count}} Teams", "{{count}} activity": "{{count}} activity", "{{count}} activity_other": "{{count}} Activities", "{{count}} day": "{{count}} day", "{{count}} day left": "{{count}} day left", "{{count}} day left_other": "{{count}} days left", "{{count}} day_other": "{{count}} days", "{{count}} days": "{{count}} days", "{{count}} days left": "{{count}} days left", "{{count}} hour": "{{count}} hour", "{{count}} hour_other": "{{count}} hours", "{{count}} hours": "{{count}} hour", "{{count}} hours_other": "{{count}} hours", "{{count}} joe": "{{count}} joe", "{{count}} joe_other": "{{count}} joes", "{{count}} members": "{{count}} member", "{{count}} members_other": "{{count}} members", "{{count}} min": "{{count}} min", "{{count}} minute": "{{count}} minute", "{{count}} minutes": "{{count}} minutes", "{{count}} minutes_other": "{{count}} Minutes", "{{count}} people": "{{count}} people", "{{count}} people_other": "{{count}} people", "{{count}} pst": "{{count}} pt", "{{count}} pts_one": "{{count}} pts", "{{count}} pts_other": "{{count}} pts", "{{count}} started": "{{count}} started", "{{count}} team": "{{count}} team", "{{count}} team_other": "{{count}} Teams", "{{count}} upcoming events": "{{count}} upcoming event", "{{count}} upcoming events_other": "{{count}} upcoming events", "{{days}} day left": "{{days}} day left", "{{days}} days left": "{{days}} days left", "{{name}} redeemed!": "{{name}} redeemed!", "{{time}} day": "{{time}} day", "{{time}} days": "{{time}} days", "{{value}} voucher": "{{value}} voucher", "{{wearableName}} is now synced with GoJoe": "{{wearableName}} is now synced with <PERSON><PERSON><PERSON>", "°C": "°C", "°F": "°F", "🏙 Attachment...": "🏙 Attachment...", "👍 Thanks for letting us know": "👍 Thanks for letting us know"}