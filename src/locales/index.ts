import i18n from 'i18next';
import {initReactI18next} from 'react-i18next';

// Import all language files
import en from './languages/en.json';
import enUS from './languages/en-US.json';
import ptBR from './languages/pt-BR.json';
import zh from './languages/zh-Hans-CN.json';
import zhHantTW from './languages/zh-Hant-CN.json'; // Using zh-Hant-CN as fallback for zh-Hant-TW
import zhHantHK from './languages/zh-Hant-CN.json'; // Using zh-Hant-CN as fallback for zh-Hant-HK
import koKR from './languages/ko-KR.json';
import frCA from './languages/fr-CA.json';
import hi from './languages/hi.json';
import nl from './languages/nl.json';
import fr from './languages/fr.json';
import filPH from './languages/fil-PH.json';
import de from './languages/de.json';
import it from './languages/it.json';
import ja from './languages/ja.json';
import pt from './languages/pt.json';
import es from './languages/es.json';
import bg from './languages/bg.json';
import ro from './languages/ro.json';
import pl from './languages/pl.json';
import sv from './languages/sv.json';
import da from './languages/da.json';
import fi from './languages/fi.json';
import lv from './languages/lv.json';
import ms from './languages/ms.json';
import mi from './languages/mi.json';
import hu from './languages/hu.json';
import id from './languages/id.json';
import cs from './languages/cs.json';
import et from './languages/et.json';
import hr from './languages/hr.json';
import sk from './languages/sk.json';
import sl from './languages/sl.json';
import th from './languages/th.json';
import vi from './languages/vi.json';
i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: en,
      },
      'en-US': {
        translation: enUS,
      },
      'pt-BR': {
        translation: ptBR,
      },
      zh: {
        translation: zh,
      },
      'zh-Hant-TW': {
        translation: zhHantTW,
      },
      'zh-Hant-HK': {
        translation: zhHantHK,
      },
      'ko-KR': {
        translation: koKR,
      },
      'fr-CA': {
        translation: frCA,
      },
      hi: {
        translation: hi,
      },
      nl: {
        translation: nl,
      },
      fr: {
        translation: fr,
      },
      'fil-PH': {
        translation: filPH,
      },
      de: {
        translation: de,
      },
      it: {
        translation: it,
      },
      ja: {
        translation: ja,
      },
      pt: {
        translation: pt,
      },
      es: {
        translation: es,
      },
      bg: {
        translation: bg,
      },
      ro: {
        translation: ro,
      },
      pl: {
        translation: pl,
      },
      sv: {
        translation: sv,
      },
      da: {
        translation: da,
      },
      fi: {
        translation: fi,
      },
      lv: {
        translation: lv,
      },
      ms: {
        translation: ms,
      },
      mi: {
        translation: mi,
      },
      hu: {
        translation: hu,
      },
      id: {
        translation: id,
      },
      cs: {
        translation: cs,
      },
      et: {
        translation: et,
      },
      hr: {
        translation: hr,
      },
      sk: {
        translation: sk,
      },
      sl: {
        translation: sl,
      },
      th: {
        translation: th,
      },
      vi: {
        translation: vi,
      },
    },
    lng: 'en',
    fallbackLng: 'en',
    debug: __DEV__,
    saveMissing: __DEV__,
    interpolation: {
      escapeValue: false, // react already safes from xss
    },
    missingKeyHandler: (lng, ns, key, fallbackValue) => {
      if (__DEV__) {
        // Send missing key to the server
        fetch('http://localhost:3000/api/translations/missing', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            lng: lng[0],
            ns,
            key,
            fallbackValue: fallbackValue || key,
          }),
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.error) {
              console.warn(`[i18n server] Error: ${data.error}`);
            } else {
              console.log(`[i18n server] ${data.message}: ${key}`);
            }
          })
          .catch((error) => {
            console.warn(
              `[i18n server] Failed to save missing key: ${error.message}`,
            );
          });
      }
    },
  })
  .catch((err) => {
    // TODO log error
    console.error('i18n initialization error:', err);
  });
export type LanguageCode =
  | 'en'
  | 'en-US'
  | 'pt-BR'
  | 'zh'
  | 'zh-Hant-TW'
  | 'zh-Hant-HK'
  | 'ko-KR'
  | 'fr-CA'
  | 'hi'
  | 'nl'
  | 'fr'
  | 'fil-PH'
  | 'de'
  | 'it'
  | 'ja'
  | 'pt'
  | 'es'
  | 'bg'
  | 'ro'
  | 'pl'
  | 'sv'
  | 'da'
  | 'fi'
  | 'lv'
  | 'ms'
  | 'mi'
  | 'hu'
  | 'id'
  | 'cs'
  | 'et'
  | 'hr'
  | 'sk'
  | 'sl'
  | 'th'
  | 'vi';
export type Language = {
  code: LanguageCode;
  name: string;
  flag: string;
};

const languagesUnsorted: Language[] = [
  {
    code: 'en',
    name: 'English',
    flag: '🇬🇧',
  },
  {
    code: 'en-US',
    name: 'American English',
    flag: '🇺🇸',
  },
  {
    code: 'pt-BR',
    name: 'Brazilian Portuguese',
    flag: '🇧🇷',
  },
  {
    code: 'zh',
    name: 'Chinese Simplified',
    flag: '🇨🇳',
  },
  {
    code: 'zh-Hant-TW',
    name: 'Chinese Traditional (Taiwan)',
    flag: '🇹🇼',
  },
  {
    code: 'zh-Hant-HK',
    name: 'Chinese Traditional (Hong Kong)',
    flag: '🇭🇰',
  },
  {
    code: 'ko-KR',
    name: 'Korean',
    flag: '🇰🇷',
  },
  {
    code: 'fr-CA',
    name: 'Canadian French',
    flag: '🇨🇦',
  },
  {
    code: 'hi',
    name: 'Hindi',
    flag: '🇮🇳',
  },
  {
    code: 'nl',
    name: 'Dutch',
    flag: '🇳🇱',
  },
  {
    code: 'fr',
    name: 'French',
    flag: '🇫🇷',
  },
  {
    code: 'fil-PH',
    name: 'Filipino',
    flag: '🇵🇭',
  },
  {
    code: 'de',
    name: 'German',
    flag: '🇩🇪',
  },
  {
    code: 'it',
    name: 'Italian',
    flag: '🇮🇹',
  },
  {
    code: 'ja',
    name: 'Japanese',
    flag: '🇯🇵',
  },
  {
    code: 'pt',
    name: 'Portuguese',
    flag: '🇵🇹',
  },
  {
    code: 'es',
    name: 'Spanish',
    flag: '🇪🇸',
  },
  {
    code: 'bg',
    name: 'Bulgarian',
    flag: '🇧🇬',
  },
  {
    code: 'ro',
    name: 'Romanian',
    flag: '🇷🇴',
  },
  {
    code: 'pl',
    name: 'Polish',
    flag: '🇵🇱',
  },
  {
    code: 'sv',
    name: 'Swedish',
    flag: '🇸🇪',
  },
  {
    code: 'da',
    name: 'Danish',
    flag: '🇩🇰',
  },
  {
    code: 'fi',
    name: 'Finnish',
    flag: '🇫🇮',
  },
  {
    code: 'lv',
    name: 'Latvian',
    flag: '🇱🇻',
  },
  {
    code: 'ms',
    name: 'Malay',
    flag: '🇲🇾',
  },
  {
    code: 'mi',
    name: 'Maori',
    flag: '🇳🇿',
  },
  {
    code: 'hu',
    name: 'Hungarian',
    flag: '🇭🇺',
  },
  {
    code: 'id',
    name: 'Indonesian',
    flag: '🇮🇩',
  },
  {
    code: 'cs',
    name: 'Czech',
    flag: '🇨🇿',
  },
  {
    code: 'et',
    name: 'Estonian',
    flag: '🇪🇪',
  },
  {
    code: 'hr',
    name: 'Croatian',
    flag: '🇭🇷',
  },
  {
    code: 'sk',
    name: 'Slovak',
    flag: '🇸🇰',
  },
  {
    code: 'sl',
    name: 'Slovenian',
    flag: '🇸🇮',
  },
  {
    code: 'th',
    name: 'Thai',
    flag: '🇹🇭',
  },
  {
    code: 'vi',
    name: 'Vietnamese',
    flag: '🇻🇳',
  },
];

export const languages = languagesUnsorted.sort((a, b) => {
  const priority = ['en', 'en-US'];

  const aPriority = priority.indexOf(a.code);
  const bPriority = priority.indexOf(b.code);

  if (aPriority !== -1 || bPriority !== -1) {
    return (
      (aPriority === -1 ? Infinity : aPriority) -
      (bPriority === -1 ? Infinity : bPriority)
    );
  }

  return a.name.localeCompare(b.name);
});

export const getLanguage = (code: string): Language => {
  const selectedLanguage = languagesUnsorted.find((lang) => lang.code === code);
  if (selectedLanguage) {
    return selectedLanguage;
  }
  return languages[0];
};

export default i18n;
