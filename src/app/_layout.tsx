import '@/tamagui-web.css';
import '@/src/polifills';
import '@/src/sheets';
import React, {useEffect} from 'react';
import {isRunningInExpoGo} from 'expo';
import * as Sentry from '@sentry/react-native';
import * as Application from 'expo-application';
import {SheetProvider} from 'react-native-actions-sheet';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {QueryClient, QueryClientProvider} from '@tanstack/react-query';
import {
  useTrackingPermissions,
  PermissionStatus,
} from 'expo-tracking-transparency';
import ThemeProvider from '@/src/contexts/ThemeContext';
import {LocalesProvider} from '@/src/contexts/LocaleContext';
import {I18nContextProvider} from '@/src/contexts/I18nContext';
import {SessionProvider} from '@/src/contexts/SessionContext';
import {ToastViewport} from '@tamagui/toast';
import ToastRoot from '@/src/components/Toast';
import RootLayout from '@/src/components/Layouts/RootLayout';
import UpdateManager from '@/src/components/UpdateManager';
import {
  CioConfig,
  CioLogLevel,
  CioRegion,
  CustomerIO,
} from 'customerio-reactnative';
import Constants from 'expo-constants';
import {
  NativeEventEmitter,
  NativeModules,
  Platform,
  StyleSheet,
} from 'react-native';

import {ChatProvider} from '@/src/contexts/ChatContext';
import {useNavigationContainerRef} from 'expo-router';
import TrackPlayer from 'react-native-track-player';
import {PlaybackService} from '@/src/services/playbackService';
import {setupPlayer} from '../utils/setupPlayer';
import {
  registerBackgroundTasks,
  triggerManualSync,
} from '../services/backgroundTasks';
import logger from '@/src/utils/logger';
import ImageProvider from '../contexts/ImageContext';
import ImageViewer from '@/src/components/ImageViewer';
import {imageViewerRef} from '@/src/utils/imageViewer';
import {networkStatus} from '@/src/utils/network';
import {useScreenTracking} from '../hooks/useScreenTracking';
import {
  getLatestBranchParams,
  handleBranchLink,
  initializeBranch,
} from '@/src/services/branchService';
import {waitForAppToBeActive} from '../utils/waitForAppToBeActive';

if (__DEV__) {
  require('../../ReactotronConfig');
}
const version = Application.nativeApplicationVersion;
const buildNumber = Application.nativeBuildVersion;

const navigationIntegration = Sentry.reactNavigationIntegration({
  enableTimeToInitialDisplay: !isRunningInExpoGo(),
});
Sentry.init({
  dsn: Constants.expoConfig?.extra?.sentry.dns,
  environment: Constants.expoConfig?.extra?.sentry.environment,
  dist: `${buildNumber}`,
  release: `${version} (${buildNumber})`,
  debug: false,
  tracesSampleRate: 0.1,
  replaysSessionSampleRate: 0,
  replaysOnErrorSampleRate: 1.0,
  integrations: [
    Sentry.mobileReplayIntegration({
      maskAllText: false,
    }),
    navigationIntegration,
  ],
  enableNativeFramesTracking: !isRunningInExpoGo(),
});
const queryClient = new QueryClient();

export default function Root() {
  useScreenTracking();
  const [permissionResponse, requestTrackingPermission] =
    useTrackingPermissions();
  const ref = useNavigationContainerRef();

  useEffect(() => {
    if (
      permissionResponse &&
      permissionResponse.status === PermissionStatus.UNDETERMINED
    ) {
      requestTrackingPermission();
    }
  }, [permissionResponse, requestTrackingPermission]);

  useEffect(() => {
    if (ref?.current) {
      navigationIntegration.registerNavigationContainer(ref);
    }
  }, [ref]);

  useEffect(() => {
    // Ensure TrackPlayer is setup when the app starts
    const init = async () => {
      await setupPlayer();

      // Only register the service on Android if needed
      // This helps prevent the "registerHeadlessTask called multiple times" error
      if (Platform.OS === 'android') {
        try {
          TrackPlayer.registerPlaybackService(() => PlaybackService);
        } catch (error) {
          logger.warn('Error registering TrackPlayer service:', error);
        }
      } else {
        // On iOS, always register
        TrackPlayer.registerPlaybackService(() => PlaybackService);
      }
    };
    init();
  }, []);

  useEffect(() => {
    // Initialize CustomerIO
    const config: CioConfig = {
      cdpApiKey: Constants.expoConfig?.extra?.customerIo.cdpApiKey,
      migrationSiteId: Constants.expoConfig?.extra?.customerIo.siteId,
      region: CioRegion.EU,
      logLevel: CioLogLevel.None,
      inApp: {
        siteId: Constants.expoConfig?.extra?.customerIo.siteId,
      },
    };
    CustomerIO.initialize(config).catch((e) => logger.error(e));
  }, []);

  useEffect(() => {
    if (Platform.OS === 'android') {
      registerBackgroundTasks().catch(logger.error);
    }
  }, []);

  useEffect(() => {
    const setupBranch = async () => {
      try {
        const latestParams = await getLatestBranchParams();

        if (
          latestParams &&
          latestParams['+clicked_branch_link'] &&
          typeof latestParams?.$deeplink_path === 'string'
        ) {
          logger.info('🌱 Handling cold start Branch link...');
          await waitForAppToBeActive();
          handleBranchLink(latestParams);
        }
      } catch (err) {
        logger.error('❌ Failed to handle initial Branch link:', err);
      }

      // Always initialize listeners after cold start check
      initializeBranch().catch((error) => {
        logger.error('Failed to initialize Branch SDK:', error);
      });
    };

    setupBranch();
  }, []);

  // Initialize network status monitoring
  useEffect(() => {
    // Just accessing the networkStatus singleton will initialize it
    const isConnected = networkStatus.getIsConnected();
    logger.info(
      `🌐 Initial network status: ${isConnected ? 'Connected' : 'Disconnected'}`,
    );

    // Clean up network status monitoring on app unmount
    return () => {
      networkStatus.cleanup();
    };
  }, []);

  useEffect(() => {
    // Set up a timer to periodically sync Health Connect data when the app is in the foreground
    if (Platform.OS === 'android') {
      logger.debug('🔄 Setting up foreground sync timer...');

      // Initial sync after a short delay
      const initialSyncTimeout = setTimeout(() => {
        logger.debug('🔄 Executing initial foreground sync...');
        triggerManualSync().catch(logger.error);
      }, 5000);

      // Regular interval sync (every 2 minutes when app is in foreground)
      const intervalId = setInterval(
        () => {
          logger.debug('⏰ Foreground sync timer triggered');
          triggerManualSync().catch(logger.error);
        },
        2 * 60 * 1000,
      ); // 2 minutes in milliseconds

      // Clean up on unmount
      return () => {
        clearTimeout(initialSyncTimeout);
        clearInterval(intervalId);
        logger.debug('🛑 Foreground sync timer cleared');
      };
    }
  }, []);
  React.useEffect(() => {
    if (Platform.OS !== 'ios') {
      return;
    }
    const {GJRNEventEmitter} = NativeModules;
    if (!GJRNEventEmitter) {
      return;
    }

    const rNEventEmitter = new NativeEventEmitter(GJRNEventEmitter);
    rNEventEmitter.addListener('logMessage', async (message) => {
      logger.info(message);
    });
    rNEventEmitter.addListener('onPushSuccess', (event) => {
      if (!event.data) {
        return;
      }
      try {
        const data = JSON.parse(event.data);
        logger.info('Workout saved', data);
      } catch (e: any) {
        logger.error(e);
      }
    });
    rNEventEmitter.addListener('onPushFailure', (event) => logger.error(event));
    return () => {
      rNEventEmitter.removeAllListeners('onPushSuccess');
      rNEventEmitter.removeAllListeners('onPushFailure');
      rNEventEmitter.removeAllListeners('logMessage');
    };
  }, []);

  return (
    <GestureHandlerRootView style={styles.container}>
      <ThemeProvider>
        <QueryClientProvider client={queryClient}>
          <SessionProvider>
            <LocalesProvider>
              <I18nContextProvider>
                <ChatProvider>
                  <SheetProvider>
                    <ImageProvider>
                      <RootLayout />
                      <ToastViewport justifyContent='center' width='100%' />
                      <ToastRoot />
                      <ImageViewer ref={imageViewerRef} />
                      <UpdateManager />
                    </ImageProvider>
                  </SheetProvider>
                </ChatProvider>
              </I18nContextProvider>
            </LocalesProvider>
          </SessionProvider>
        </QueryClientProvider>
      </ThemeProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
