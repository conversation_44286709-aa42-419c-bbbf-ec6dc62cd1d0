import {useLocalSearchPara<PERSON>, useRouter} from 'expo-router';
import React, {useState} from 'react';
import {ScrollView, YStack} from 'tamagui';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {StyledInput} from '../components/UI/StyledInput';
import StyledButton from '../components/UI/Button';
import I18nText from '../components/I18nText';
import {useAuthWithEmail} from '../hooks/api/useAuthWithEmail';
import {useSession} from '../contexts/SessionContext';
import {triggerHaptics} from '../utils/haptics';
import {isWeb} from 'tamagui';
import {Controller, useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {
  registerSchema,
  RegisterFormInterface,
  defaultValues,
} from '../forms/schema/register';
import {useTranslation} from 'react-i18next';
import {getUserFriendlyErrorMessage} from '../utils/getError';
import logger from '../utils/logger';

const SignInWithEmailScreen = () => {
  const {top, bottom} = useSafeAreaInsets();
  const {email} = useLocalSearchParams<{email: string}>();
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isNewUser, setIsNewUser] = useState(false);
  const {isPending, emailLogin, register} = useAuthWithEmail();
  const {signIn} = useSession();
  const router = useRouter();
  const {t} = useTranslation();

  // Form for registration (new user)
  const {
    control,
    handleSubmit: handleRegisterSubmit,
    formState: {errors},
    setValue,
  } = useForm<RegisterFormInterface>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      ...defaultValues,
      email: email as string,
      password: password,
    },
    mode: 'onChange',
  });

  // Update password in form when it changes
  React.useEffect(() => {
    if (password) {
      setValue('password', password);
    }
  }, [password, setValue]);

  const handleVerificationSubmit = async () => {
    if (!password.trim()) {
      setError('Please enter your verification code');
      return;
    }

    try {
      await triggerHaptics();
      setError(null);

      const apiResponse = await emailLogin({
        email: email as string,
        password: password,
      });

      // If we have tokens, user exists and we can sign in
      if (apiResponse.token && apiResponse.refreshToken) {
        await signIn(apiResponse);
      } else {
        // No tokens means new user, show registration form
        setIsNewUser(true);
      }
    } catch (error) {
      logger.error('Login error:', error);

      // Get a user-friendly error message based on the error type
      const errorMessage = getUserFriendlyErrorMessage(error);
      setError(errorMessage);
    }
  };

  const onRegisterSubmit = async (data: RegisterFormInterface) => {
    try {
      await triggerHaptics();
      setError(null);

      const apiResponse = await register({
        email: data.email,
        password: data.password,
        firstName: data.firstName,
        lastName: data.lastName,
      });

      await signIn(apiResponse);
    } catch (error) {
      logger.error('Registration error:', error);

      // Get a user-friendly error message based on the error type
      const errorMessage = getUserFriendlyErrorMessage(error);
      setError(errorMessage);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  // Verification code screen
  if (!isNewUser) {
    return (
      <YStack
        flex={1}
        marginTop={top}
        marginBottom={bottom}
        padding='$5'
        gap='$4'>
        <YStack gap='$2'>
          <I18nText fontSize='$6' fontWeight='bold'>
            Enter verification code
          </I18nText>
          <I18nText color='$grey2'>
            We've sent a verification code to {email}
          </I18nText>
        </YStack>

        <YStack gap='$3'>
          <StyledInput
            autoFocus
            keyboardType={isWeb ? 'number-pad' : 'numeric'}
            onChangeText={setPassword}
            value={password}
            placeholder={t('Enter verification code')}
            autoCapitalize='none'
          />

          {error && <I18nText color='$primary'>{error}</I18nText>}

          <StyledButton
            variant='primary'
            loading={isPending}
            onPress={handleVerificationSubmit}
            disabled={!password.trim() || isPending}>
            <I18nText fontWeight='700' color='$white'>
              Continue
            </I18nText>
          </StyledButton>

          <StyledButton
            variant='secondary'
            onPress={handleCancel}
            disabled={isPending}>
            <I18nText fontWeight='700'>Cancel</I18nText>
          </StyledButton>
        </YStack>
      </YStack>
    );
  }

  // Registration form for new users
  return (
    <YStack
      flex={1}
      marginTop={top}
      marginBottom={bottom}
      padding='$5'
      gap='$4'>
      <ScrollView showsVerticalScrollIndicator={false}>
        <YStack gap='$2'>
          <I18nText fontSize='$6' fontWeight='bold'>
            Create your account
          </I18nText>
          <I18nText color='$grey2'>
            Please provide your details to complete registration
          </I18nText>
        </YStack>

        <YStack gap='$3' marginTop='$4'>
          <Controller
            control={control}
            name='firstName'
            render={({field: {onChange, value}}) => (
              <StyledInput
                placeholder={t('First name')}
                onChangeText={onChange}
                value={value}
                autoCapitalize='words'
                borderColor={errors.firstName ? '$primary' : '$grey3'}
              />
            )}
          />
          {errors.firstName && (
            <I18nText color='$primary'>{errors.firstName.message}</I18nText>
          )}

          <Controller
            control={control}
            name='lastName'
            render={({field: {onChange, value}}) => (
              <StyledInput
                placeholder={t('Last name')}
                onChangeText={onChange}
                value={value}
                autoCapitalize='words'
                borderColor={errors.lastName ? '$primary' : '$grey3'}
              />
            )}
          />
          {errors.lastName && (
            <I18nText color='$primary'>{errors.lastName.message}</I18nText>
          )}

          {error && <I18nText color='$primary'>{error}</I18nText>}

          <StyledButton
            variant='primary'
            loading={isPending}
            onPress={handleRegisterSubmit(onRegisterSubmit)}
            disabled={isPending}>
            <I18nText fontWeight='bold' color='$white1'>
              Create Account
            </I18nText>
          </StyledButton>

          <StyledButton
            variant='secondary'
            onPress={handleCancel}
            disabled={isPending}>
            <I18nText fontWeight='700'>Cancel</I18nText>
          </StyledButton>
        </YStack>
      </ScrollView>
    </YStack>
  );
};

export default SignInWithEmailScreen;
