import React, {useEffect} from 'react';
import {StyleSheet} from 'react-native';
import {XStack, YStack} from 'tamagui';
import Feather from '@expo/vector-icons/Feather';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import {SheetManager} from 'react-native-actions-sheet';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {LinearGradient} from 'expo-linear-gradient';
import {useSession} from '@/src/contexts/SessionContext';
import {useRouter} from 'expo-router';
import {useVideoPlayer, VideoView} from 'expo-video';
import {AudioModule} from 'expo-audio';
import {triggerHaptics} from '@/src/utils/haptics';
import I18nText from '@/src/components/I18nText';
import {useTheme as useCustomTheme} from '../contexts/ThemeContext';
import Button from '@/src/components/UI/Button';
import {SPACE_BOTTOM} from '@/src/constants/Constants';
import {StatusBar} from 'expo-status-bar';
import logger from '@/src/utils/logger';

const videoSource = require('@/assets/videos/welcome.mov');

const OnboardingScreen = () => {
  const {setTheme, themeMode} = useCustomTheme();

  const {bottom, top} = useSafeAreaInsets();
  const {isAuthenticated} = useSession();
  const router = useRouter();
  const player = useVideoPlayer(videoSource, (player) => {
    player.loop = true;
    player.muted = true;
    player.play();
  });

  useEffect(() => {
    AudioModule.setAudioModeAsync({
      allowsRecording: false,
      playsInSilentMode: false,
    });
  }, []);
  useEffect(() => {
    if (isAuthenticated) {
      router.replace('/');
    }
  }, [isAuthenticated, router]);

  const handleToggleTheme = async () => {
    try {
      await triggerHaptics();
      setTheme(themeMode === 'light' ? 'dark' : 'light');
    } catch (e) {
      logger.error(e);
    }
  };
  const handlePressGetStarted = async () => {
    try {
      await triggerHaptics();
      await SheetManager.show('signIn');
    } catch (e) {
      logger.error(e);
    }
  };

  const handlePressLanguage = async () => {
    await SheetManager.show('languages');
  };
  return (
    <YStack flex={1}>
      <StatusBar style='light' translucent backgroundColor='transparent' />
      <YStack
        position='absolute'
        zIndex={100}
        flex={1}
        width='100%'
        height='100%'>
        <XStack
          paddingHorizontal='$5'
          gap='$2.5'
          flex={1}
          paddingTop={top}
          justifyContent='flex-end'>
          <Button
            width='$4.5'
            height='$4.5'
            padding={0}
            variant='secondary'
            icon={<Feather name='globe' size={18} />}
            onPress={handlePressLanguage}
          />
          {/* <Button
            width='$4.5'
            height='$4.5'
            padding={0}
            variant='secondary'
            icon={<MaterialCommunityIcons name='theme-light-dark' size={18} />}
            onPress={handleToggleTheme}
          /> */}
        </XStack>
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.5)']}
          style={styles.flex}>
          <YStack
            paddingBottom={bottom + SPACE_BOTTOM}
            flex={1}
            paddingHorizontal='$5'>
            <YStack flex={1} justifyContent='flex-end' paddingBottom='$8'>
              <I18nText
                color='$white'
                fontWeight='500'
                fontSize={80}
                lineHeight={80}>
                Better Health
              </I18nText>
              <I18nText
                color='$white'
                fontWeight='500'
                fontSize={24}
                lineHeight={24}>
                For Ordinary Joes everywhere
              </I18nText>
            </YStack>
            <YStack gap='$4'>
              <Button onPress={handlePressGetStarted} variant='secondary'>
                <I18nText fontWeight='600'>Get Started</I18nText>
              </Button>
            </YStack>
          </YStack>
        </LinearGradient>
      </YStack>
      <VideoView
        style={styles.video}
        player={player}
        contentFit='cover'
        allowsFullscreen
        allowsPictureInPicture={false}
        nativeControls={false}
      />
    </YStack>
  );
};
export default OnboardingScreen;

const styles = StyleSheet.create({
  flex: {
    flex: 1,
  },
  video: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
});
