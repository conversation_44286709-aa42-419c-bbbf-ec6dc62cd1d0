import React, {useCallback} from 'react';
import {useQueryClient} from '@tanstack/react-query';
import {useLocalSearchParams} from 'expo-router';
import {FlashList, ListRenderItem} from '@shopify/flash-list';
import {KeyboardAvoidingView, Platform} from 'react-native';
import {YStack} from 'tamagui';
import {PostCommentsListDto} from '@gojoe/typescript-sdk';

import {useSession} from '@/src/contexts/SessionContext';
import usePostComments from '@/src/hooks/api/usePostComments';
import Comment from '@/src/components/Comments/Comment';
import EmptyComponent from '@/src/components/Comments/EmptyComponent';
import InputComponent from '@/src/components/Comments/InputComponent';
import {updateCollectionItem, updateSingleItem} from '@/src/utils/query';

const ItemSeparatorComponent = () => <YStack height={16} />;

const CommentsScreen = () => {
  const queryClient = useQueryClient();
  const {postId, postUserId, postBusinessId} = useLocalSearchParams<{
    postId: string;
    postUserId: string;
    postBusinessId: string;
  }>();
  const {user} = useSession();
  const {data: comments} = usePostComments(postId);

  const invalidateQueries = useCallback(async () => {
    const queries = [
      queryClient.invalidateQueries({
        queryKey: ['feed'],
      }),
      queryClient.invalidateQueries({
        queryKey: ['business', 'posts', postBusinessId || ''],
      }),
      queryClient.invalidateQueries({
        queryKey: ['comments', 'post', postId],
      }),
    ];

    await Promise.all(queries);
  }, [queryClient, postBusinessId, postId]);

  const onSuccessMutate = useCallback(
    async (update: any) => {
      await updateCollectionItem({
        queryClient,
        id: postId,
        queryKey: ['feed'],
        update,
        fields: ['commentsCountTotal', 'myComments'],
      });
      await updateCollectionItem({
        queryClient,
        id: postId,
        queryKey: ['posts', postUserId],
        update,
        fields: ['commentsCountTotal', 'myComments'],
      });
      await updateSingleItem({
        queryClient,
        queryKey: ['post', postId],
        update,
        fields: ['commentsCountTotal', 'myComments'],
      });
      if (postBusinessId) {
        await updateCollectionItem({
          queryClient,
          id: postId,
          queryKey: ['business', 'posts', postBusinessId],
          update,
          fields: ['commentsCountTotal', 'myComments'],
        });
      }
    },
    [queryClient, postId, postUserId, postBusinessId],
  );

  const keyExtractor = useCallback((item: PostCommentsListDto) => item.id, []);

  const renderItem: ListRenderItem<any> = useCallback(
    ({item}) => (
      <Comment
        item={item}
        authUserId={user?.id}
        businessId={postBusinessId}
        postId={postId}
        onSuccessMutate={onSuccessMutate}
        invalidateQueries={invalidateQueries}
      />
    ),
    [user?.id, postBusinessId, postId, onSuccessMutate, invalidateQueries],
  );

  if (!comments || !user) {
    return null;
  }

  return (
    <KeyboardAvoidingView
      style={{flex: 1}}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : undefined}>
      <FlashList
        data={comments}
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        estimatedItemSize={56}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={Platform.OS === 'android'}
        ItemSeparatorComponent={ItemSeparatorComponent}
        ListEmptyComponent={EmptyComponent}
        inverted={comments.length > 0}
        contentContainerStyle={{
          paddingHorizontal: 24,
          paddingVertical: 12,
        }}
      />

      <InputComponent
        user={user}
        postId={postId}
        onSuccessMutate={onSuccessMutate}
        invalidateQueries={invalidateQueries}
      />
    </KeyboardAvoidingView>
  );
};

export default CommentsScreen;
