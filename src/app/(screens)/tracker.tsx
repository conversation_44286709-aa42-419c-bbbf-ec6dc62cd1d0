import React, {useEffect, useState} from 'react';
import {<PERSON>ner, XStack, YStack} from 'tamagui';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import {ActivityTypeDto} from '@gojoe/typescript-sdk';

import BackButton from '@/src/components/BackButton';
import {useActivityTypes} from '@/src/hooks/api/useActivityTypes';
import TrackerScreen from '@/src/screens/Tracker';
import {useLocales} from '@/src/contexts/LocaleContext';

const Tracker: React.FC = () => {
  const {top} = useSafeAreaInsets();
  const {locales} = useLocales();
  const [activityType, setActivityType] = useState<ActivityTypeDto | null>(
    null,
  );
  const {data} = useActivityTypes();

  useEffect(() => {
    if (activityType || !data || data.length === 0) {
      return;
    }

    setActivityType(data[0]);
  }, [activityType, data]);

  if (!activityType) {
    return (
      <YStack flex={1}>
        <XStack
          paddingTop={top}
          zIndex={1}
          paddingHorizontal='$5'
          alignItems='center'
          justifyContent='space-between'>
          <BackButton />
        </XStack>
        <Spinner marginTop='$5' />
      </YStack>
    );
  }
  return <TrackerScreen activityType={activityType} locales={locales} />;
};

export default Tracker;
