import React, {useEffect, useState} from 'react';
import {useLocalSearchParams} from 'expo-router';
import {Image, Spinner, XStack, YStack} from 'tamagui';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import {GiftCardDto, GiftCardOptionsDto} from '@gojoe/typescript-sdk';
import {SheetManager} from 'react-native-actions-sheet';

import {useGiftCards} from '@/src/hooks/api/useGiftCards';
import ParallaxScrollView from '@/src/components/ParallaxScrollView';
import StyledText from '@/src/components/UI/StyledText';
import StyledButton from '@/src/components/UI/Button';
import {getCurrencySymbols} from '@/src/utils/currency';
import {triggerHaptics} from '@/src/utils/haptics';

const GiftCardScreen = () => {
  const {bottom} = useSafeAreaInsets();
  const {t} = useTranslation();
  const {giftCardId, optionId} = useLocalSearchParams<{
    giftCardId: string;
    optionId: string;
  }>();
  const {data, fetchNextPage} = useGiftCards();
  const [giftCard, setGiftCard] = useState<GiftCardDto | undefined>();
  const [option, setOptions] = useState<GiftCardOptionsDto | undefined>();

  useEffect(() => {
    if (giftCard) {
      for (const item of giftCard.options) {
        if (item.id === optionId) {
          setOptions(item);
          break;
        }
      }
    }
  }, [giftCard, optionId]);

  useEffect(() => {
    // TODO replace when we have the api to get the details of a single gift card
    if (giftCard) {
      return;
    }
    if (data) {
      for (const item of data) {
        if (item.id === giftCardId) {
          setGiftCard(item);
          break;
        }
      }
      if (!giftCard) {
        fetchNextPage();
      }
    }
  }, [data, fetchNextPage, giftCard, giftCardId]);

  const handleOnPress = async () => {
    await triggerHaptics();

    // Open the gift card redeem sheet and pass the gift card and option details
    if (giftCard && option) {
      await SheetManager.show('gift_card_redeem', {
        payload: {
          giftCard,
          option,
        },
      });
    }
  };

  if (!giftCard || !option) {
    return <Spinner marginTop={4} />;
  }
  return (
    <YStack flex={1} paddingBottom={bottom}>
      <ParallaxScrollView
        headerImage={
          <Image
            source={{uri: giftCard.image}}
            style={{width: '100%', height: 220}}
          />
        }>
        <YStack paddingHorizontal='$5'>
          <StyledText fontWeight='700' fontSize={24}>
            {giftCard.name}
          </StyledText>
          <StyledText marginTop={8} fontWeight='500' lineHeight={18}>
            {giftCard.description}
          </StyledText>
        </YStack>
      </ParallaxScrollView>
      <YStack
        borderTopWidth={1}
        borderTopColor='$grey'
        paddingHorizontal='$5'
        paddingTop='$5'>
        <StyledButton
          onPress={handleOnPress}
          iconAfter={() => {
            return (
              <XStack paddingHorizontal='$5' gap='$2'>
                <StyledText fontWeight='700' color='#FFCE1F'>
                  {option.points}
                </StyledText>
                <Image
                  source={require('@/assets/images/reword_circle_3.png')}
                  width={18}
                  height={18}
                  tintColor='#FFCE1F'
                />
              </XStack>
            );
          }}>
          <StyledText color='#FFFFFF' flex={1}>
            {t('Redeem {{value}} voucher', {
              value: `${getCurrencySymbols(giftCard.currency)}${Math.round(
                option.value / 100,
              )}`,
            })}
          </StyledText>
        </StyledButton>
      </YStack>
    </YStack>
  );
};

export default GiftCardScreen;
