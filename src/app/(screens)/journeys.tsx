import React, {useCallback} from 'react';
import {XStack, YStack} from 'tamagui';
import {useRouter} from 'expo-router';
import {FlashList, ListRenderItem} from '@shopify/flash-list';
import {JourneyDto} from '@gojoe/typescript-sdk';

import {triggerHaptics} from '@/src/utils/haptics';
import {useJourneys} from '@/src/hooks/api/useJourneys';
import Journey from '@/src/components/Journey';
import ApiFetchError from '@/src/components/ApiFetchError';
import EmptyState from '@/src/screens/Journeys/EmptyState';
import LoadingState from '@/src/screens/Journeys/LoadingState';
import {useScreenPadding} from '@/src/hooks/useScreenPadding';

const Journeys: React.FC = () => {
  const {paddingBottom} = useScreenPadding();
  const {data, error, isLoading, isRefetching, refetch} = useJourneys();
  const router = useRouter();

  const handleNavigate = useCallback(
    async (journeyId: string) => {
      await triggerHaptics();
      router.push({
        pathname: '/journey/[journeyId]',
        params: {journeyId},
      });
    },
    [router],
  );

  const renderItem: ListRenderItem<JourneyDto> = useCallback(
    ({item}) => (
      <XStack onPress={() => handleNavigate(item.id)}>
        <Journey journey={item} />
      </XStack>
    ),
    [handleNavigate],
  );

  const keyExtractor = useCallback((item: JourneyDto) => item.id, []);

  if (error) {
    return <ApiFetchError error={error} />;
  }

  if (isLoading) {
    return <LoadingState />;
  }

  if (!data) return null;

  if (!data || data.length === 0) {
    return <EmptyState />;
  }

  return (
    <YStack flex={1} paddingHorizontal='$5' paddingBottom={paddingBottom}>
      <FlashList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        showsVerticalScrollIndicator={false}
        estimatedItemSize={140}
        refreshing={isRefetching}
        onRefresh={refetch}
      />
    </YStack>
  );
};

export default Journeys;
