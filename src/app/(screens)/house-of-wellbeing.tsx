import React from 'react';
import {FlatList, ListRenderItemInfo, StyleSheet} from 'react-native';
import {useRouter} from 'expo-router';
import {Image} from 'expo-image';
import {XStack, YStack} from 'tamagui';
import {useTranslation} from 'react-i18next';
import {
  HouseOfWellbeingAudio,
  useHouseOfWellbeing,
} from '@/src/hooks/api/useHouseOfWellbeing';
import StyledText from '@/src/components/UI/StyledText';
import {AudioLines} from '@tamagui/lucide-icons';
import {triggerHaptics} from '@/src/utils/haptics';
import I18nText from '@/src/components/I18nText';

const HouseOfWellbeing: React.FC = () => {
  const {t} = useTranslation();
  const [category, setCategory] = React.useState<string>('All');
  const {data, categories} = useHouseOfWellbeing();
  const router = useRouter();

  const renderItem = ({item}: ListRenderItemInfo<HouseOfWellbeingAudio>) => {
    const handlePressItem = async () => {
      await triggerHaptics();
      router.push({
        pathname: '/house-of-wellbeing/[trackId]',
        params: {trackId: item.id},
      });
    };

    return (
      <XStack
        onPress={handlePressItem}
        backgroundColor='$background'
        height={80}
        borderRadius='$5'
        marginBottom='$2'
        padding='$2'
        justifyContent='center'
        gap='$2'>
        <Image source={{uri: item.artwork}} style={styles.image} />
        <YStack flex={1} justifyContent='space-between' padding='$2'>
          <YStack gap='$1'>
            <StyledText color='$color' fontWeight='700' numberOfLines={1}>
              {item.title}
            </StyledText>
          </YStack>
          <XStack justifyContent='space-between' alignItems='flex-end'>
            <XStack gap='$2'>
              <AudioLines size={14} color='$accentGrey' />
              <StyledText fontWeight='600' fontSize={12} color='$accentGrey'>
                {t('{{count}} min', {count: item.length})}
              </StyledText>
            </XStack>
          </XStack>
        </YStack>
      </XStack>
    );
  };

  const renderCategory = ({item}: ListRenderItemInfo<string>) => {
    const isSelected = item === category;
    const onPress = async () => {
      await triggerHaptics();
      setCategory(item);
    };
    return (
      <YStack
        onPress={onPress}
        borderWidth={1}
        borderColor='$color'
        backgroundColor={isSelected ? '$color' : undefined}
        paddingHorizontal={12}
        height={30}
        borderRadius={15}
        justifyContent='center'
        marginRight='$2'>
        <I18nText
          color={isSelected ? '$background' : '$color'}
          fontWeight='600'>
          {item}
        </I18nText>
      </YStack>
    );
  };

  return (
    <YStack flex={1}>
      <YStack marginVertical='$5'>
        <FlatList
          ListHeaderComponent={<YStack width={24} />}
          data={categories}
          renderItem={renderCategory}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
        />
      </YStack>
      <YStack paddingHorizontal='$5'>
        <FlatList
          data={
            category === 'All'
              ? data
              : data.filter((item) => item.category === category)
          }
          renderItem={renderItem}
          showsVerticalScrollIndicator={false}
        />
      </YStack>
    </YStack>
  );
};

const styles = StyleSheet.create({
  image: {
    width: 64,
    height: 64,
    borderRadius: 8,
  },
});

export default HouseOfWellbeing;
