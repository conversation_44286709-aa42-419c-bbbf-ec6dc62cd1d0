import React, {useCallback, useEffect, useState} from 'react';
import {XStack, YStack} from 'tamagui';
import {Image} from 'expo-image';
import {useLocalSearchParams} from 'expo-router';
import {FlashList, ListRenderItem} from '@shopify/flash-list';
import {JourneyStepsDto} from '@gojoe/typescript-sdk';

import useJourney from '@/src/hooks/api/useJourney';
import JourneyEvent from '@/src/components/JourneyEvent';
import useJourneySteps from '@/src/hooks/api/useJourneySteps';
import ParallaxScrollView from '@/src/components/ParallaxScrollView';
import Overview from '@/src/screens/Journey/Overview';
import Weeks from '@/src/screens/Journey/Weeks';
import Join<PERSON>our<PERSON> from '@/src/screens/Journey/JoinJourney';
import FooterMenu from '@/src/screens/Journey/FooterMenu';
import {Dimensions} from 'react-native';
import {useScreenPadding} from '@/src/hooks/useScreenPadding';

const screenHeight = Dimensions.get('window').height;

const JourneyScreen = () => {
  const {paddingBottom} = useScreenPadding();
  const [step, setStep] = useState(0);
  const {journeyId} = useLocalSearchParams<{
    journeyId: string;
  }>();

  const {data, isLoading} = useJourney(journeyId);
  const {data: journeySteps} = useJourneySteps(journeyId);

  useEffect(() => {
    let newStep = 1;
    if (data && data.myJourney && data.myJourney.week > newStep) {
      newStep = data.myJourney.week;
    }
    setStep(newStep);
  }, [data]);

  const renderStep: ListRenderItem<JourneyStepsDto> = useCallback(
    ({item}) => (
      <XStack>
        <JourneyEvent journey={data} event={item} />
      </XStack>
    ),
    [data],
  );

  if (isLoading || !data || !journeySteps) return null;

  return (
    <YStack
      flex={1}
      backgroundColor='$windowBackground'
      paddingBottom={paddingBottom}>
      <ParallaxScrollView
        backgroundColor='$windowBackground'
        headerImage={
          <Image
            source={{uri: data?.cover ?? ''}}
            style={{width: '100%', height: 220}}
          />
        }>
        <YStack
          backgroundColor='$windowBackground'
          paddingHorizontal='$5'
          paddingTop='$5'>
          <Overview data={data} />
          <Weeks
            length={data.weeks}
            useSteps={[step, setStep]}
            titleHeader={data.stageName}
          />
          <FlashList
            data={journeySteps.filter((item) => item.week === step)}
            renderItem={renderStep}
            estimatedItemSize={100}
            keyExtractor={(item) => item.id}
            contentContainerStyle={{paddingVertical: 16}}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={<YStack minHeight={screenHeight * 0.5} />}
          />
        </YStack>
      </ParallaxScrollView>

      {data.myJourney ? (
        <FooterMenu journey={data} />
      ) : (
        <JoinJourney id={data.id} />
      )}
    </YStack>
  );
};

export default JourneyScreen;

export const getJourneyScreenTitle = () => 'Journey';
