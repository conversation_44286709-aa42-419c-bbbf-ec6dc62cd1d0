import {useLocalSearchParams, useNavigation} from 'expo-router';
import useUserProfile from '@/src/hooks/api/useUserProfile';
import {useLayoutEffect} from 'react';
import {Spinner} from 'tamagui';
import UserActivities from '@/src/screens/challenge/UserActivities';

const ChallengeUserScreen = () => {
  const {challengeId, userId} = useLocalSearchParams<{
    userId: string;
    challengeId: string;
  }>();
  const {data: userProfile, isLoading: isLoadingUser} = useUserProfile(userId);
  const navigation = useNavigation();

  useLayoutEffect(() => {
    if (userProfile) {
      navigation.setOptions({
        title: `${userProfile.firstName} ${userProfile.lastName}`,
      });
    }
  }, [userProfile, navigation]);

  if (isLoadingUser) {
    return <Spinner marginTop='$5' />;
  }

  if (!userProfile) {
    return null;
  }
  return <UserActivities challengeId={challengeId} user={userProfile} />;
};

export default ChallengeUserScreen;
