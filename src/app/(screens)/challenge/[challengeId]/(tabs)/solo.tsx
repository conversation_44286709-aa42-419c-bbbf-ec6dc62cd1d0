import React from 'react';
import {useLocalSearchParams} from 'expo-router';
import useChallenge from '@/src/hooks/api/useChallenge';
import {Spinner} from 'tamagui';
import {useLocales} from '@/src/contexts/LocaleContext';
import {canDisplayLeaderboard, getColumnType} from '@/src/utils/challenge';
import LeaderboardLocked from '@/src/components/LeaderboardLocked';
import ChallengeSoloStandings from '@/src/screens/challenge/Solo';
import logger from '@/src/utils/logger';

const Page: React.FC = () => {
  const {challengeId, passcode} = useLocalSearchParams<{
    challengeId: string;
    passcode?: string;
  }>();
  const {data: challenge, isLoading} = useChallenge(challengeId, passcode);
  const {locales} = useLocales();
  logger.debug('SOLO');
  if (isLoading) {
    return <Spinner marginTop='$5' />;
  }
  if (!challenge) {
    return null;
  }
  const isLeaderboardActive = canDisplayLeaderboard(challenge);
  if (!isLeaderboardActive) {
    return <LeaderboardLocked />;
  }

  const columnType = getColumnType(challenge.event.name);
  return (
    <ChallengeSoloStandings
      challengeId={challenge.id}
      activityTypes={challenge.activityTypes}
      inChallenge={challenge.inChallenge}
      unit={locales.distanceUnit}
      columnType={columnType}
    />
  );
};

export default Page;
