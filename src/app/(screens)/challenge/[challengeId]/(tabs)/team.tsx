import React from 'react';
import {useLocalSearchParams} from 'expo-router';
import useChallenge from '@/src/hooks/api/useChallenge';
import {Spinner} from 'tamagui';
import ChallengeTeamStandings from '@/src/screens/challenge/Team';
import {useLocales} from '@/src/contexts/LocaleContext';
import {canDisplayLeaderboard, getColumnType} from '@/src/utils/challenge';
import LeaderboardLocked from '@/src/components/LeaderboardLocked';

const Page: React.FC = () => {
  const {challengeId, passcode} = useLocalSearchParams<{
    challengeId: string;
    passcode?: string;
  }>();
  const {data: challenge, isLoading} = useChallenge(challengeId, passcode);
  const {locales} = useLocales();

  if (isLoading) {
    return <Spinner marginTop='$5' />;
  }
  if (!challenge) {
    return null;
  }
  const isLeaderboardActive = canDisplayLeaderboard(challenge);
  if (!isLeaderboardActive) {
    return <LeaderboardLocked />;
  }

  const columnType = getColumnType(challenge.event.name);
  return (
    <ChallengeTeamStandings
      challengeId={challenge.id}
      activityTypes={challenge.activityTypes}
      inChallenge={challenge.inChallenge}
      unit={locales.distanceUnit}
      columnType={columnType}
    />
  );
};

export default Page;
