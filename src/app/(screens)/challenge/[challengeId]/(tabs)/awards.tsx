import React from 'react';
import {useLocalSearchParams} from 'expo-router';
import ChallengeAwards from '@/src/screens/challenge/Awards';
import useChallenge from '@/src/hooks/api/useChallenge';
import {Spinner} from 'tamagui';

const Page: React.FC = () => {
  const {challengeId} = useLocalSearchParams<{
    challengeId: string;
  }>();
  const {data: challenge, isLoading} = useChallenge(challengeId);

  if (isLoading) {
    return <Spinner marginTop='$5' />;
  }
  if (!challenge) {
    return null;
  }

  return (
    <ChallengeAwards
      challengeId={challengeId}
      challengeStartDate={challenge.startDate}
      challengeEndDate={challenge.endDate}
    />
  );
};

export default Page;
