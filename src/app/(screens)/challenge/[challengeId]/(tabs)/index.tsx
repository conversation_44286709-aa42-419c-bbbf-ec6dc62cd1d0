import React, {useCallback, useEffect, useRef} from 'react';
import {Platform} from 'react-native';
import {SheetManager} from 'react-native-actions-sheet';
import {useLocalSearchParams} from 'expo-router';
import {Spinner, XStack, YStack} from 'tamagui';

import useChallenge from '@/src/hooks/api/useChallenge';
import useSurveyChallenges from '@/src/hooks/api/useSurveyChallenges';
import {useSession} from '@/src/contexts/SessionContext';
import {checkSurveys} from '@/src/utils/surveys';
import Overview from '@/src/screens/challenge/Overview';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import StyledButton from '@/src/components/UI/Button';
import I18nText from '@/src/components/I18nText';
import {Share} from '@tamagui/lucide-icons';
import {getChallengeShareMessage, shareApp} from '@/src/utils/sharing';
import {useToast<PERSON>ontroller} from '@tamagui/toast';
import {useTranslation} from 'react-i18next';
import {triggerHaptics} from '@/src/utils/haptics';
import logger from '@/src/utils/logger';
import segmentClient from '@/src/services/segment';
import {useChallengeLeaderboardTeams} from '@/src/hooks/api/useChallengeLeaderboardTeams';

const Page: React.FC = () => {
  const {challengeId, passcode, addPading} = useLocalSearchParams<{
    challengeId: string;
    passcode?: string;
    addPading?: string;
  }>();

  const {data, isLoading} = useChallenge(challengeId, passcode);
  const {bottom} = useSafeAreaInsets();
  const toast = useToastController();
  const {t} = useTranslation();
  const {user} = useSession();
  const {surveys, refetch} = useSurveyChallenges(challengeId);
  const hasShownSheet = useRef<Record<string, boolean>>({});

  useEffect(() => {
    if (data && user && surveys && !hasShownSheet.current[challengeId]) {
      const surveyId = checkSurveys(user.id, surveys, data.runningStatus);
      if (surveyId) {
        hasShownSheet.current[challengeId] = true;
        SheetManager.show('survey', {
          payload: {
            surveyId,
            userId: user.id,
            onComplete: refetch,
          },
        });
      }
    }
  }, [data, user, surveys, challengeId, refetch]);
  let myTeamName = '';
  const myTeamData = useChallengeLeaderboardTeams({
    challengeId: challengeId,
    userBelongsToTeam: true,
    enabled: !!data?.inChallenge,
  });
  if (data?.inChallenge) {
    logger.debug('User is in challenge. finding his team');
    myTeamName = myTeamData?.data?.[0]?.team?.name;
  }

  const handleInvitePress = useCallback(
    async (myTeamName: string) => {
      if (!data) return;
      await triggerHaptics();

      // Track the invite event with Segment
      segmentClient.track('Challenge Invite Initiated', {
        challengeId: data.id,
        challengeName: data.name,
        hasPasscode: data.business ? !!data.business?.passcode : !!data.code,
        source: 'challenge_overview',
        challengeType: data.event?.name,
        isTeamChallenge: data.maxTeamSize ? data.maxTeamSize > 1 : false,
        wearablesOnly: data.wearablesOnly,
      });

      try {
        console.log('myTeamName:', myTeamName);
        await shareApp(getChallengeShareMessage(data, myTeamName));
        // Track successful share
        segmentClient.track('Challenge Invite Shared', {
          challengeId: data.id,
          challengeName: data.name,
          hasPasscode: data.business ? !!data.business?.passcode : !!data.code,
          source: 'challenge_overview',
          challengeType: data.event?.name,
          isTeamChallenge: data.maxTeamSize ? data.maxTeamSize > 1 : false,
          wearablesOnly: data.wearablesOnly,
        });

        // Note: We don't show success toast because React Native's Share API doesn't tell us
        // if the user actually completed the share or just dismissed the dialog
      } catch (error) {
        logger.error('Failed to share challenge invitation:', error);

        // Track failed share
        segmentClient.track('Challenge Invite Failed', {
          challengeId: data.id,
          challengeName: data.name,
          hasPasscode: !!data.code, // Use challenge.code instead of URL parameter
          source: 'challenge_overview',
          error: error instanceof Error ? error.message : 'Unknown error',
        });

        toast.show(t('Share failed'), {
          message: t('Failed to share challenge invitation'),
          type: 'error',
        });
      }
    },
    [data, toast, t],
  );

  if (isLoading) {
    return <Spinner marginTop='$5' />;
  }
  if (!data) {
    return null;
  }

  return (
    <YStack flex={1}>
      <Overview challenge={data} />
      {data.inChallenge && myTeamName && (
        <XStack
          backgroundColor='$background'
          paddingHorizontal='$5'
          paddingTop='$5'
          paddingBottom={
            Platform.OS === 'android' && addPading === 'true'
              ? (bottom || 0) + 24 // Add extra padding on Android when coming from modal
              : bottom || '$3'
          }
          borderTopWidth={1}
          borderTopColor='$grey3'>
          <StyledButton
            variant='primary'
            flex={1}
            onPress={() => handleInvitePress(myTeamName)}>
            <Share size={16} color='$white1' />
            <I18nText
              color='$white1'
              fontWeight='500'
              textAlign='center'
              marginLeft='$2'>
              Invite Friends
            </I18nText>
          </StyledButton>
        </XStack>
      )}
    </YStack>
  );
};

export default Page;
