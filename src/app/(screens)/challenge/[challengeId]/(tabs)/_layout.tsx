import {
  createMaterialTopTabNavigator,
  MaterialTopTabNavigationEventMap,
  MaterialTopTabNavigationOptions,
} from '@react-navigation/material-top-tabs';

import {useNavigation, useRouter, withLayoutContext} from 'expo-router';
import {Platform} from 'react-native';
import useChallenge from '@/src/hooks/api/useChallenge';
import React, {
  ReactNode,
  useCallback,
  useEffect,
  useLayoutEffect,
  useState,
} from 'react';
import {useTheme, XStack, YStack} from 'tamagui';
import {useSession} from '@/src/contexts/SessionContext';
import {checkSurveys} from '@/src/utils/surveys';
import {SheetManager} from 'react-native-actions-sheet';
import useSurveyChallenges from '@/src/hooks/api/useSurveyChallenges';
import I18nText from '@/src/components/I18nText';
import {NAVIGATION_HEADER_HEIGHT} from '@/src/constants/navigation';
import {ParamListBase, NavigationState} from '@react-navigation/native';
import StyledButton from '@/src/components/UI/Button';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {triggerHaptics} from '@/src/utils/haptics';
import {useToastController} from '@tamagui/toast';
import {useTranslation} from 'react-i18next';
import {useParsedParams} from '@/src/hooks/useParsedParams';

const {Navigator} = createMaterialTopTabNavigator();

export const MaterialTopTabNavigator = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator,
  NavigationState<ParamListBase>,
  MaterialTopTabNavigationEventMap
>(Navigator);

const LayoutChallenge = () => {
  const {challengeId, passcode, addPading} = useParsedParams();
  const {bottom} = useSafeAreaInsets();
  const navigation = useNavigation();
  const {data: challenge} = useChallenge(challengeId, passcode);
  const {user} = useSession();
  const {surveys, refetch} = useSurveyChallenges(challengeId);
  const [surveyId, setSurveyId] = useState<string | undefined>();
  const theme = useTheme();
  const toast = useToastController();
  const {t} = useTranslation();
  const router = useRouter();

  const color = theme.color?.get() ?? '#000000';
  const inactiveColor = theme.grey1?.get() ?? '#656874';
  const backgroundColor = theme.background?.get() ?? 'black';

  useLayoutEffect(() => {
    if (challenge) {
      navigation.setOptions({
        title: challenge.name,
      });
    }
  }, [challenge, navigation]);

  useEffect(() => {
    if (surveyId) {
      return;
    }
    if (user && surveys && challenge) {
      const surveyId = checkSurveys(user.id, surveys, challenge.runningStatus);
      setSurveyId(surveyId);
    }
  }, [challenge, surveyId, surveys, user]);

  useEffect(() => {
    if (surveyId) {
      void SheetManager.show('survey', {
        payload: {
          surveyId,
          userId: user?.id ?? '',
          onComplete: refetch,
        },
      });
    }
  }, [surveyId, user, refetch]);

  const handleJoinTeamPress = useCallback(async () => {
    await triggerHaptics();

    router.push({
      pathname: '/challenge/[challengeId]/team',
      params: {
        challengeId,
      },
    });

    toast.show(t('Join a team'), {
      message: t('Search for a team and tap on it to join this challenge'),
      type: 'info',
    });
  }, [router, challengeId, toast, t]);

  const handleCreateTeamPress = useCallback(async () => {
    await triggerHaptics();

    router.push({
      pathname: '/challenge/[challengeId]/create-team',
      params: {
        challengeId,
        passcode,
      },
    });
  }, [router, challengeId, passcode]);

  const showTeamButtonsBox =
    !challenge || challenge.inChallenge
      ? false
      : challenge.canJoinTeam || challenge.canCreateTeam;

  return (
    <YStack flex={1}>
      <MaterialTopTabNavigator
        initialRouteName='solo'
        screenOptions={{
          lazy: true,
          tabBarLabel: ({
            color,
            children,
          }: {
            color: string;
            children: ReactNode;
          }) => (
            <I18nText
              color={color}
              allowFontScaling={false}
              fontWeight='500'
              textTransform='none'
              fontSize={13}>
              {children}
            </I18nText>
          ),
          tabBarStyle: {
            paddingHorizontal: 24,
            backgroundColor: backgroundColor,
            height: NAVIGATION_HEADER_HEIGHT,
          },
          tabBarIndicatorContainerStyle: {
            width: 'auto',
            marginHorizontal: 24,
          },
          tabBarScrollEnabled: true,
          tabBarIndicatorStyle: {
            backgroundColor: color,
            height: 4,
          },
          tabBarInactiveTintColor: inactiveColor,
          tabBarItemStyle: {
            width: 'auto',
            padding: 0,
            marginHorizontal: 8,
          },
          tabBarContentContainerStyle: {
            alignItems: 'center',
          },
        }}>
        <MaterialTopTabNavigator.Screen
          name='index'
          options={{
            title: 'Overview',
          }}
          initialParams={{challengeId, passcode}}
        />
        <MaterialTopTabNavigator.Screen
          name='team'
          options={{
            title: 'Team',
          }}
          initialParams={{challengeId, passcode}}
        />
        <MaterialTopTabNavigator.Screen
          name='solo'
          options={{
            title: 'Solo',
          }}
          initialParams={{challengeId, passcode}}
        />
        <MaterialTopTabNavigator.Screen
          name='insights'
          options={{
            title: 'Insights',
          }}
          initialParams={{challengeId}}
        />
        <MaterialTopTabNavigator.Screen
          name='awards'
          options={{
            title: 'Awards',
          }}
          initialParams={{challengeId}}
        />
        <MaterialTopTabNavigator.Screen
          name='chat'
          options={{
            title: 'Chat',
          }}
          initialParams={{challengeId}}
        />
      </MaterialTopTabNavigator>
      {showTeamButtonsBox && challenge && (
        <XStack
          backgroundColor='$background'
          paddingHorizontal='$5'
          paddingTop='$5'
          paddingBottom={
            Platform.OS === 'android' && addPading === 'true'
              ? (bottom || 0) + 24 // Add extra padding on Android when coming from modal
              : bottom || '$3'
          }
          gap='$3'
          borderTopWidth={1}
          borderTopColor='$grey3'>
          {!!challenge.canCreateTeam && (
            <StyledButton
              variant='secondary'
              flex={1}
              onPress={handleCreateTeamPress}>
              <I18nText fontWeight='500' textAlign='center'>
                Create Team
              </I18nText>
            </StyledButton>
          )}
          {!!challenge.canJoinTeam && (
            <StyledButton
              variant='primary'
              flex={1}
              onPress={handleJoinTeamPress}>
              <I18nText color='$white1' fontWeight='500' textAlign='center'>
                Join Team
              </I18nText>
            </StyledButton>
          )}
        </XStack>
      )}
    </YStack>
  );
};

export default LayoutChallenge;
