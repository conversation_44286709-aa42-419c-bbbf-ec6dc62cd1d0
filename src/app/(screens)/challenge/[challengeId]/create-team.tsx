import React, {useCallback, useEffect, useLayoutEffect, useState} from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Spin<PERSON>, XStack, YStack} from 'tamagui';
import * as ImagePicker from 'expo-image-picker';
import {useLocalSearchParams, useNavigation, useRouter} from 'expo-router';
import {useTranslation} from 'react-i18next';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Controller, useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {SheetManager} from 'react-native-actions-sheet';
import {Camera, CirclePlus, User2, X} from '@tamagui/lucide-icons';
import {useToastController} from '@tamagui/toast';

import {StyledInput} from '@/src/components/UI/StyledInput';
import StyledButton from '@/src/components/UI/Button';
import I18nText from '@/src/components/I18nText';
import TeamAvatar from '@/src/components/UI/Avatar/TeamAvatar';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';
import useChallenge from '@/src/hooks/api/useChallenge';
import useTeam from '@/src/hooks/api/useTeam';
import {useSession} from '@/src/contexts/SessionContext';
import {triggerHaptics} from '@/src/utils/haptics';
import {
  createTeamSchema,
  editTeamSchema,
  defaultValues,
} from '@/src/forms/schema/create-team';
import useCreateTeamMutation from '@/src/hooks/api/useCreateTeamMutation';
import useEditTeamMutation from '@/src/hooks/api/useEditTeamMutation';
import {api} from '@/src/services/api';
import StyledText from '@/src/components/UI/StyledText';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import {SearchUserListDto} from '@gojoe/typescript-sdk';
import {uploadImage} from '@/src/utils/upload';
import logger from '@/src/utils/logger';
import {Platform} from 'react-native';

export default function CreateTeamScreen() {
  const {challengeId, teamId, isEdit, passcode} = useLocalSearchParams<{
    challengeId: string;
    teamId?: string;
    isEdit?: string;
    passcode?: string;
  }>();
  const isEditMode = isEdit === 'true' && !!teamId;
  const {data: challenge, isLoading: isLoadingChallenge} = useChallenge(
    challengeId,
    passcode,
  );
  const {data: team, isLoading: isLoadingTeam} = useTeam(
    challengeId,
    teamId || '',
  );
  const {user} = useSession();
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const router = useRouter();
  const toast = useToastController();
  const navigation = useNavigation();
  const [selectedUsers, setSelectedUsers] = useState<
    {
      id: string;
      name: string;
      profilePicture?: string;
    }[]
  >([]);
  const createMutation = useCreateTeamMutation();
  const editMutation = useEditTeamMutation();

  // We'll use either createMutation or editMutation based on the mode

  // Use different form type based on mode
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors, isValid},
  } = useForm({
    // Use different schema based on mode
    resolver: zodResolver(isEditMode ? editTeamSchema : createTeamSchema),
    defaultValues: {
      ...defaultValues,
      name: `Team ${user?.firstName}`,
      challengeCode: challenge?.code,
    },
    mode: 'onChange',
  });

  const teamName = watch('name');
  const logo = watch('logo');
  const users = watch('users');

  useLayoutEffect(() => {
    if (challenge) {
      navigation.setOptions({
        title: challenge.name,
      });
    }
  }, [challenge, navigation]);

  // Initialize form with team data if in edit mode, otherwise add current user as first team member and leader
  useEffect(() => {
    if (isEditMode && team) {
      // Initialize form with team data for editing
      setValue('name', team.name);
      setValue('logo', team.logo);

      // In edit mode, we don't need to set users as we're only editing name and logo
      if (user) {
        setValue(
          'users',
          [
            {
              userId: user.id,
              isLeader: true,
              isAdmin: true,
              status: true,
              position: 0,
            },
          ],
          {
            shouldValidate: true,
            shouldDirty: true,
          },
        );

        setSelectedUsers([
          {
            id: user.id,
            name: `${user.firstName} ${user.lastName}`,
            profilePicture: user.profilePicture,
          },
        ]);
      }
    } else if (user) {
      // For create mode, add current user as first team member and leader
      setValue(
        'users',
        [
          {
            userId: user.id,
            isLeader: true,
            isAdmin: true,
            status: true,
            position: 0,
          },
        ],
        {
          shouldValidate: true,
          shouldDirty: true,
        },
      );

      setSelectedUsers([
        {
          id: user.id,
          name: `${user.firstName} ${user.lastName}`,
          profilePicture: user.profilePicture,
        },
      ]);
    }
  }, [user, setValue, isEditMode, team]);

  // Get default avatar when component mounts (only in create mode)
  useEffect(() => {
    // Only fetch default avatar in create mode, not in edit mode
    if (!isEditMode) {
      const fetchDefaultAvatar = async () => {
        try {
          const response = await api.getApiClient().avatarControllerGenerate();
          setValue('logo', response.data.data.uri, {
            shouldDirty: true,
          });
        } catch (error) {
          logger.error('Error fetching default avatar:', error);
        }
      };

      fetchDefaultAvatar();
    }
  }, [isEditMode, setValue]);

  const handleAddTeammate = useCallback(async () => {
    await triggerHaptics();

    await SheetManager.show('user_search', {
      payload: {
        challengeId,
        selectedUsers: selectedUsers.map((user) => user.id),
        onSelect: (user: SearchUserListDto) => {
          // Check if user is already selected
          if (selectedUsers.some((u) => u.id === user.id)) {
            return;
          }

          // Add user to form
          const newUsers = [
            ...(users || []),
            {
              userId: user.id,
              isLeader: false,
              isAdmin: false,
              status: true,
              position: users?.length || 0,
            },
          ];

          setValue('users', newUsers);
          setSelectedUsers([...selectedUsers, user]);
        },
      },
    });
  }, [challengeId, selectedUsers, users, setValue]);

  const handleRemoveUser = useCallback(
    (userId: string) => {
      return async () => {
        await triggerHaptics();

        // Remove user from form
        const newUsers = users?.filter((u) => u.userId !== userId) || [];
        setValue('users', newUsers);

        // Remove user from selected users
        const newSelectedUsers = selectedUsers.filter((u) => u.id !== userId);
        setSelectedUsers(newSelectedUsers);
      };
    },
    [users, selectedUsers, setValue],
  );

  const pickImage = useCallback(async () => {
    await triggerHaptics();

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      const logo = await uploadImage(result.assets[0], 'team');
      setValue('logo', logo, {
        shouldDirty: true,
      });
    }
  }, [setValue]);

  const handleCreateTeam = useCallback(
    async (data: any) => {
      await triggerHaptics();

      if (isEditMode && teamId) {
        // Edit existing team
        editMutation.mutate(
          {
            teamId,
            editTeamDto: {
              name: data.name,
              logo: data.logo,
            },
          },
          {
            onSuccess: () => {
              toast.show(t('Team Updated Successfully'), {
                type: 'success',
              });

              // Redirect to the team/challenge screen
              router.back();
              router.push({
                pathname: '/team/[teamId]',
                params: {
                  teamId,
                  challengeId: challengeId,
                },
              });
            },
            onError: (error) => {
              logger.error('Error updating team:', error);
              toast.show(t('Failed to update team'), {
                type: 'error',
              });
            },
          },
        );
      } else {
        // Create new team
        createMutation.mutate(
          {
            challengeId,
            createChallengeTeamDto: data,
          },
          {
            onSuccess: (data) => {
              logger.debug('data', data);
              toast.show(t('Team Created Successfully'), {
                type: 'success',
              });

              // Redirect to the team/challenge screen
              router.back();
              router.push({
                pathname: '/team/[teamId]',
                params: {
                  teamId: data.id,
                  challengeId: challengeId,
                },
              });
            },
            onError: (error) => {
              logger.error('Error creating team:', error);
              toast.show(t('Failed to create team'), {
                type: 'error',
              });
            },
          },
        );
      }
    },
    [
      challengeId,
      teamId,
      isEditMode,
      createMutation,
      editMutation,
      router,
      t,
      toast,
    ],
  );

  if (isLoadingChallenge || !challenge || (isEditMode && isLoadingTeam)) {
    return <Spinner marginTop='$5' />;
  }

  const isTeamFull =
    challenge.maxTeamSize !== null &&
    (users?.length || 0) >= challenge.maxTeamSize;
  const maxTeamSizeText = challenge.maxTeamSize
    ? challenge.maxTeamSize
    : t('Unlimited');

  return (
    <>
      <ScrollView flex={1}>
        <YStack paddingHorizontal='$5' paddingTop='$5' gap='$4'>
          <I18nText fontWeight='700' fontSize={18}>
            {isEditMode ? 'Edit team' : 'Create team'}
          </I18nText>
          {/* Team Name Input */}
          <YStack
            backgroundColor='$background'
            borderWidth={1}
            borderRadius={16}
            padding='$3.5'
            borderColor='$grey3'>
            {errors.name?.message && (
              <I18nText
                color='$error'
                fontSize={12}
                fontWeight='500'
                marginBottom='$2'>
                {errors.name.message}
              </I18nText>
            )}
            <XStack gap='$3.5'>
              <Controller
                control={control}
                name='name'
                render={({field: {onChange, value}}) => (
                  <StyledInput
                    flex={1}
                    placeholder={t('Team Name')}
                    value={value}
                    onChangeText={onChange}
                  />
                )}
              />
              <YStack onPress={pickImage}>
                <TeamAvatar
                  borderRadius={8}
                  size={48}
                  team={{
                    id: 'new-team',
                    name: teamName || t('Team'),
                    logo: logo,
                  }}
                />
                <Camera
                  size={20}
                  color='$white1'
                  position='absolute'
                  top={14}
                  left={15}
                />
              </YStack>
            </XStack>
          </YStack>

          {/* Team Members - Only show in create mode, not in edit mode */}
          {!isEditMode && (
            <YStack
              backgroundColor='$grey3'
              padding='1'
              borderRadius={16}
              overflow='hidden'>
              <YStack
                gap='$3'
                borderTopLeftRadius={16}
                borderTopRightRadius={16}
                backgroundColor='$background'
                padding='$3.5'
                borderColor='$grey3'>
                <XStack gap={8}>
                  <I18nText fontWeight='600'>Members</I18nText>
                  <StyledText>
                    {`${selectedUsers.length} / ${maxTeamSizeText}`}
                  </StyledText>
                </XStack>

                {/* Member List */}
                <YStack gap='$2'>
                  {selectedUsers.map((selectedUser) => (
                    <XStack
                      alignItems='center'
                      gap='$2'
                      height={32}
                      marginVertical={8}
                      key={selectedUser.id}>
                      <UserAvatar
                        user={{
                          id: selectedUser.id,
                          name: selectedUser.name,
                          firstName: selectedUser.name,
                          lastName: selectedUser.name,
                          profilePicture: selectedUser.profilePicture,
                        }}
                        size={32}
                        circular
                      />
                      <StyledText fontSize={12} fontWeight='500' flex={1}>
                        {selectedUser.name}
                      </StyledText>
                      {/* Only allow removing other users, not yourself */}
                      {selectedUser.id !== user?.id && (
                        <X
                          size={18}
                          color='$grey1'
                          onPress={handleRemoveUser(selectedUser.id)}
                        />
                      )}
                    </XStack>
                  ))}
                </YStack>

                {/* Add Teammate Button */}
                {!isTeamFull && (
                  <XStack
                    onPress={handleAddTeammate}
                    alignItems='center'
                    gap='$2'>
                    <YStack
                      borderWidth={1}
                      borderColor='$primary'
                      borderRadius={16}
                      padding={4}>
                      <User2 color='$primary' />
                    </YStack>
                    <I18nText
                      fontSize={12}
                      fontWeight='500'
                      color='$primary'
                      flex={1}>
                      Add a team-mate
                    </I18nText>
                    <CirclePlus color='$primary' size={18} />
                  </XStack>
                )}
              </YStack>
              {/* Footer Note */}
              <XStack padding='$3.5' gap={8} alignItems='flex-start'>
                <StyledText color='$color'>
                  <FontAwesome name='info-circle' />
                </StyledText>
                <I18nText
                  fontWeight='500'
                  color='$color'
                  lineHeight={19}
                  flex={1}>
                  {t(
                    "Want to invite someone who isn't yet using GoJoe? You can do this once you've created the team.",
                  )}
                </I18nText>
              </XStack>
            </YStack>
          )}
        </YStack>
      </ScrollView>
      {/* Create Team Button */}
      <YStack
        height={bottom + (Platform.OS === 'android' ? 120 : 64)}
        paddingHorizontal='$5'
        paddingTop='$3'
        paddingBottom={bottom || '$3'}
        backgroundColor='$background'
        borderTopWidth={1}
        borderTopColor='$grey3'>
        <StyledButton
          disabled={
            !isValid || createMutation.isPending || editMutation.isPending
          }
          onPress={handleSubmit(handleCreateTeam)}>
          {createMutation.isPending || editMutation.isPending ? (
            <Spinner color='$white' />
          ) : (
            <I18nText color='$white1' fontWeight='500'>
              {isEditMode ? 'Update Team' : 'Create Team'}
            </I18nText>
          )}
        </StyledButton>
      </YStack>
    </>
  );
}
