import React from 'react';
import {Spin<PERSON>, YStack} from 'tamagui';

import I18nText from '@/src/components/I18nText';
import {useWearableProviders} from '@/src/hooks/api/useWearableProviders';
import WearablesList from '@/src/components/Wearables/WearablesList';

export default function ApplicationAndDevicesScreen() {
  const {data = [], isRefetching, refetch} = useWearableProviders();

  if (!data.length) return <Spinner />;

  return (
    <YStack
      flex={1}
      paddingHorizontal='$5'
      paddingTop='$5'
      paddingBottom={'$5'}>
      <I18nText fontSize={14} color='$gray10' marginBottom='$5'>
        Sync your wearable and{' '}
        <I18nText fontWeight='700'>record future</I18nText> activities with
        GoJoe
      </I18nText>
      <WearablesList
        data={data}
        refetch={refetch}
        isRefetching={isRefetching}
      />
    </YStack>
  );
}
