import React from 'react';
import {StyleSheet} from 'react-native';
import * as ScreenOrientation from 'expo-screen-orientation';
import {useLocalSearchParams, useRouter} from 'expo-router';
import {useVideoPlayer, VideoView} from 'expo-video';
import {YStack} from 'tamagui';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {MaterialCommunityIcons} from '@expo/vector-icons';
import {triggerHaptics} from '@/src/utils/haptics';

const VideoScreen = () => {
  const {uri} = useLocalSearchParams<{uri: string}>();
  const {top} = useSafeAreaInsets();
  const router = useRouter();
  const [showClose, setShowClose] = React.useState(false);

  const player = useVideoPlayer(uri, (player) => {
    player.loop = false;
    player.muted = false;
    player.play();
  });

  const handleTouch = () => {
    setShowClose(true);
    setTimeout(() => {
      setShowClose(false);
    }, 3000);
  };
  const handleBack = async () => {
    await triggerHaptics();
    await ScreenOrientation.lockAsync(
      ScreenOrientation.OrientationLock.PORTRAIT_UP,
    );
    player.pause();
    router.back();
  };

  return (
    <YStack flex={1} backgroundColor='#000000' onPress={handleTouch}>
      {showClose && (
        <YStack
          top={top}
          left={8}
          position='absolute'
          borderRadius={20}
          width={40}
          height={40}
          zIndex={1}
          justifyContent='center'
          alignItems='center'
          onPress={handleBack}>
          <MaterialCommunityIcons name='close' color='#FFFFFF' size={24} />
        </YStack>
      )}
      <VideoView style={styles.video} player={player} allowsFullscreen />
    </YStack>
  );
};
const styles = StyleSheet.create({
  video: {
    width: '100%',
    height: '100%',
  },
});
export default VideoScreen;
