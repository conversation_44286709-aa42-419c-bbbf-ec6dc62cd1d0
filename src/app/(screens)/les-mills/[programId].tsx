import {Spin<PERSON>, YStack} from 'tamagui';
import {useLesMillsProgram} from '@/src/hooks/api/useLesMillsProgram';
import {useLocalSearchParams} from 'expo-router';
import {useEffect} from 'react';
import {storage} from '@/src/utils/localStorage';
import LesMillsProgramList from '@/src/components/LesMillsProgramList';

const LesMillsProgramScreen = () => {
  const {programId} = useLocalSearchParams<{programId: string}>();
  const {data, isLoading} = useLesMillsProgram(programId);

  useEffect(() => {
    const storeKey = 'les-mills-user-favorite-programs';
    const newStoreValue: string[] = [];
    const storeValue = storage.getString(storeKey);
    if (storeValue) {
      newStoreValue.push(...storeValue.split(','));
    }
    const index = newStoreValue.indexOf(programId);
    if (index > -1) {
      newStoreValue.splice(index, 1);
    }
    newStoreValue.unshift(programId);
    storage.set(storeKey, newStoreValue.join(','));
  }, [programId]);

  if (isLoading) {
    return <Spinner marginTop='$4' />;
  }
  return (
    <YStack padding='$5' flex={1}>
      <LesMillsProgramList
        programId={programId}
        description={data?.description}
      />
    </YStack>
  );
};

export default LesMillsProgramScreen;
