import React, {useCallback, useState} from 'react';
import {StyleSheet, TextInput} from 'react-native';
import {SheetManager} from 'react-native-actions-sheet';
import {useTranslation} from 'react-i18next';
import {Spinner, XStack, YStack} from 'tamagui';
import {FlashList, ListRenderItem} from '@shopify/flash-list';
import {ChevronRight} from '@tamagui/lucide-icons';
import {BusinessListDto} from '@gojoe/typescript-sdk';

import {useSession} from '@/src/contexts/SessionContext';
import {triggerHaptics} from '@/src/utils/haptics';
import useUserBusinesses from '@/src/hooks/api/useUserBusinesses';
import StyledText from '@/src/components/UI/StyledText';
import useDebounce from '@/src/hooks/useDebounce';
import BusinessAvatar from '@/src/components/UI/Avatar/BusinessAvatar';

const ItemSeparatorComponent = () => <YStack height={4} />;

const Businesses: React.FC = () => {
  const {t} = useTranslation();
  const {selectBusiness} = useSession();
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm || '', 500);
  const {data, isRefetching, isFetchingNextPage, fetchNextPage, refetch} =
    useUserBusinesses({name: debouncedSearchTerm});

  const handlePressBusiness = useCallback(
    async (business: BusinessListDto) => {
      await triggerHaptics();
      await SheetManager.show('business_settings', {
        payload: {
          business: business,
        },
      });
      selectBusiness({
        id: business.id,
        name: business.name,
        logo: business.avatar,
      });
    },
    [selectBusiness],
  );

  const handleOnSearch = (value: string) => {
    setSearchTerm(value);
  };

  const keyExtractor = useCallback((item: BusinessListDto) => item.id, []);

  const renderItem: ListRenderItem<any> = useCallback(
    ({item}) => (
      <XStack
        borderWidth={1}
        borderColor='#DADCE7'
        borderRadius={8}
        padding='$3'
        alignItems='center'
        justifyContent='space-between'
        onPress={() => handlePressBusiness(item)}>
        <XStack alignItems='center' gap={8}>
          <BusinessAvatar business={item} size={32} borderRadius={4} />
          <StyledText fontSize={14} fontWeight={500} numberOfLines={1}>
            {item.name}
          </StyledText>
        </XStack>

        <ChevronRight color='#ACAFBB' />
      </XStack>
    ),
    [handlePressBusiness],
  );

  const onEndReached = () => fetchNextPage();

  const listFooterComponent = useCallback(() => {
    if (isFetchingNextPage) {
      return <Spinner marginVertical='$3' />;
    }
    return <YStack height={48} />;
  }, [isFetchingNextPage]);

  return (
    <YStack flex={1} paddingHorizontal='$5' paddingTop={8}>
      <XStack marginBottom={8}>
        <TextInput
          placeholder={t('Search...')}
          onChangeText={handleOnSearch}
          style={styles.input}
        />
      </XStack>

      <FlashList
        data={data}
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        estimatedItemSize={56}
        onEndReached={onEndReached}
        onEndReachedThreshold={0.5}
        onRefresh={refetch}
        refreshing={isRefetching}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={ItemSeparatorComponent}
        ListFooterComponent={listFooterComponent}
      />
    </YStack>
  );
};

export default Businesses;

const styles = StyleSheet.create({
  input: {
    flex: 1,
    borderWidth: 1,
    height: 44,
    borderRadius: 8,
    paddingHorizontal: 16,
    backgroundColor: '#FFF',
    borderColor: '#E8E8EA',
  },
});
