import {useLocalSearchParams} from 'expo-router';
import useBusiness from '@/src/hooks/api/useBusiness';
import BusinessScreen from '@/src/screens/Business';

const BusinessRoute = () => {
  const {businessId} = useLocalSearchParams<{
    businessId: string;
  }>();
  const {data} = useBusiness(businessId);
  if (!data) {
    return null;
  }
  return <BusinessScreen business={data} />;
};

export default BusinessRoute;
