import React, {useCallback, useEffect, useState} from 'react';
import {Linking, StyleSheet, useWindowDimensions} from 'react-native';
import {Image} from 'expo-image';
import {useLocalSearchParams} from 'expo-router';
import {YStack} from 'tamagui';
import RenderHtml from 'react-native-render-html';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import {BenefitDto} from '@gojoe/typescript-sdk';
import {useBenefits} from '@/src/hooks/api/useBenefits';
import StyledButton from '@/src/components/UI/Button';
import ParallaxScrollView from '@/src/components/ParallaxScrollView';
import I18nText from '@/src/components/I18nText';
import StyledText from '@/src/components/UI/StyledText';

const BenefitsScreen = () => {
  const {width} = useWindowDimensions();
  const {bottom} = useSafeAreaInsets();
  const {benefitId} = useLocalSearchParams<{benefitId: string}>();
  const [benefit, setBenefit] = useState<BenefitDto | undefined>();
  const {data} = useBenefits();

  useEffect(() => {
    setBenefit(data.find((item) => item.id === benefitId));
  }, [benefitId, data]);

  const handlePressUrl = useCallback(() => {
    if (!benefit) {
      return;
    }
    Linking.canOpenURL(benefit.url).then(async (supported) => {
      if (supported) {
        await Linking.openURL(benefit.url);
      }
    });
  }, [benefit]);

  if (!benefit) {
    return null;
  }
  return (
    <YStack flex={1}>
      <ParallaxScrollView
        headerImage={
          <Image
            source={{uri: benefit.cover ?? benefit.image}}
            style={{width: '100%', height: 220}}
          />
        }>
        <YStack flex={1} paddingHorizontal='$5' backgroundColor='$background'>
          <StyledText fontWeight='700' fontSize='$5' marginTop="$3.5">
            {benefit.name}
          </StyledText>
          {!!benefit.description && (
            <RenderHtml
              baseStyle={styles.content}
              contentWidth={width}
              source={{
                html: benefit.description,
              }}
            />
          )}
        </YStack>
      <YStack paddingHorizontal='$5' paddingBottom={bottom}>
        {!!benefit.url && (
          <StyledButton onPress={handlePressUrl}><I18nText color='$white1'>Access benefit</I18nText></StyledButton>
        )}
      </YStack>
      </ParallaxScrollView>
    </YStack>
  );
};

const styles = StyleSheet.create({
  content: {
    lineHeight: 22,
  },
});

export default BenefitsScreen;
