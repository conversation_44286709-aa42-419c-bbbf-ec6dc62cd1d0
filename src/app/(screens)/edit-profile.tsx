import React, {useEffect} from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Stack, YStack} from 'tamagui';
import * as ImagePicker from 'expo-image-picker';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';
import {useSession} from '@/src/contexts/SessionContext';
import {
  CalendarDays,
  Camera,
  ChevronDown,
  CircleX,
  MapPin,
} from '@tamagui/lucide-icons';
import {DateTimePickerAndroid} from '@react-native-community/datetimepicker';
import {Alert, Platform} from 'react-native';
import {useTranslation} from 'react-i18next';
import StyledButton from '@/src/components/UI/Button';
import I18nText from '@/src/components/I18nText';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Controller, useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {FormInterface, formSchema} from '@/src/forms/schema/personal-details';
import {StyledInput} from '@/src/components/UI/StyledInput';
import {SheetManager} from 'react-native-actions-sheet';
import LocaleDate from '@/src/components/LocaleDate';
import Weight from '@/src/components/Weight';
import Height from '@/src/components/Height';
import useMutationUserProfile from '@/src/hooks/api/useMutationUserProfile';
import {useRouter} from 'expo-router';
import {triggerHaptics} from '@/src/utils/haptics';
import {getSelectedGenderName} from '@/src/hooks/api/useGenders';
import useCountries from '@/src/hooks/api/useCountries';
import {CountryDto} from '@gojoe/typescript-sdk';

import {uploadImage} from '@/src/utils/upload';
import logger from '@/src/utils/logger';
import {useToastController} from '@tamagui/toast';
import {useActivityLevel} from '@/src/hooks/api/useActivityLevel';

const getDefaultDataWeight = (weight?: number) => {
  if (!weight) return undefined;
  return weight < 1000 ? weight * 1000 : weight;
};

export default function EditProfileScreen() {
  const {user, setUser} = useSession();
  const {data: activityLevel} = useActivityLevel();
  const {data: countries} = useCountries();
  const {mutateAsync, isPending} = useMutationUserProfile();
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const router = useRouter();
  const toast = useToastController();
  const {
    control,
    handleSubmit,
    setValue,
    reset,
    watch,
    formState: {errors},
  } = useForm<FormInterface>({
    resolver: zodResolver(formSchema),
    mode: 'onChange',
  });
  const [isUploadingImage, setUploadingImage] = React.useState(false);
  useEffect(() => {
    if (!user) {
      return;
    }
    reset({
      firstName: user.firstName,
      lastName: user.lastName,
      profilePicture: user.profilePicture,
      dateOfBirth: user.dateOfBirth ? new Date(user.dateOfBirth) : undefined,
      height: user.height ?? undefined,
      weight: getDefaultDataWeight(user.weight),
      gender: user.gender ?? undefined,
      levelId: user.level ? user.level.id : undefined,
      countryId: user.countryId ?? undefined,
    });
  }, [reset, user]);

  // Submit form data to update user profile
  const onSubmit = async (data: FormInterface) => {
    if (!user) {
      return;
    }
    await triggerHaptics();
    const input = {
      firstName: data.firstName,
      lastName: data.lastName,
      profilePicture: data.profilePicture ?? undefined,
      dateOfBirth: data.dateOfBirth
        ? data.dateOfBirth.toJSON().split('T')[0]
        : undefined,
      gender: data.gender ?? undefined,
      weight: data.weight ?? undefined,
      height: data.height ?? undefined,
      levelId: data.levelId,
      countryId: data.countryId,
    };

    try {
      await mutateAsync(input);
      // Update local user state with the new values
      const level = activityLevel?.find((l) => l.id === input.levelId);
      setUser({
        ...user,
        firstName: input.firstName || user.firstName,
        lastName: input.lastName || user.lastName,
        profilePicture: input.profilePicture || user.profilePicture,
        dateOfBirth: input.dateOfBirth || user.dateOfBirth,
        gender: input.gender || user.gender,
        weight: input.weight || user.weight,
        height: input.height || user.height,
        level: level,
        countryId: input.countryId || user.countryId,
      });
      router.back();
    } catch (error) {
      logger.error('Error updating user profile:', error);
      toast.show(t('Failed to update profile. Please try again.'), {
        type: 'error',
      });
    }
  };

  const takePhotoWithCamera = async () => {
    await triggerHaptics();
    setUploadingImage(true);
    // Request camera permissions
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

    if (!permissionResult.granted) {
      alert(t('Camera access is required to take a photo.'));
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      const imageUrl = await uploadImage(result.assets[0], 'user');
      if (imageUrl) {
        setValue('profilePicture', imageUrl, {
          shouldDirty: true,
        });
      }
    }
    setUploadingImage(false);
  };

  const pickImageFromLibrary = async () => {
    await triggerHaptics();
    setUploadingImage(true);
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });
    if (!result.canceled) {
      const imageUrl = await uploadImage(result.assets[0], 'user');
      if (imageUrl) {
        logger.debug('imageUrl', imageUrl);
        setValue('profilePicture', imageUrl, {
          shouldDirty: true,
        });
      }
    }
    setUploadingImage(false);
  };

  const showImagePickerOptions = async () => {
    // Example using a simple alert (use ActionSheet or modal for better UX)
    Alert.alert('Update Profile Picture', 'Choose an option', [
      {text: t('Take Photo'), onPress: takePhotoWithCamera},
      {text: t('Choose from Library'), onPress: pickImageFromLibrary},
      {text: t('Cancel'), style: 'cancel'},
    ]);
  };

  const clearDateOfBirth = () => {
    setValue('dateOfBirth', undefined);
  };

  if (!user) {
    return <Spinner marginTop='$5' />;
  }
  const profilePicture = watch('profilePicture');
  return (
    <YStack flex={1} paddingHorizontal='$5' paddingBottom={bottom}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <YStack alignItems='center' paddingVertical='$8'>
          <YStack onPress={showImagePickerOptions}>
            <UserAvatar
              user={{
                ...user,
                profilePicture: profilePicture || undefined,
              }}
              size='$11'
              borderRadius='50%'
            />
            <YStack
              position='absolute'
              alignItems='center'
              justifyContent='center'
              bottom={0}
              end={0}
              width={36}
              height={36}
              backgroundColor='#00000070'
              borderRadius='$3'>
              {isUploadingImage ? (
                <Spinner size='small' color='#FFFFFF' />
              ) : (
                <Camera color='$background' />
              )}
            </YStack>
          </YStack>
        </YStack>
        <YStack marginBottom='$3.5'>
          <Controller
            control={control}
            name='firstName'
            render={({field: {onChange, onBlur, value}}) => (
              <StyledInput
                placeholder={t('First name')}
                onChangeText={onChange}
                onBlur={onBlur}
                value={value}
                borderColor={errors.firstName ? '$primary10' : '$grey'}
                clearButtonMode='while-editing'
              />
            )}
          />
          <Controller
            control={control}
            name='lastName'
            render={({field: {onChange, onBlur, value}}) => (
              <StyledInput
                placeholder={t('Last name')}
                onChangeText={onChange}
                onBlur={onBlur}
                value={value}
                borderColor={errors.firstName ? '$primary10' : '$grey'}
                marginTop='$3.5'
              />
            )}
          />
          <Controller
            control={control}
            name='dateOfBirth'
            render={({field: {value}}) => {
              const handleOnPress = async () => {
                if (Platform.OS === 'android') {
                  DateTimePickerAndroid.open({
                    value: value || new Date(),
                    mode: 'date',
                    is24Hour: true,
                    maximumDate: new Date(),
                    onChange: (_, selectedDate) => {
                      if (selectedDate) {
                        setValue('dateOfBirth', selectedDate);
                      }
                    },
                  });
                } else {
                  await SheetManager.show('dateTimePicker', {
                    payload: {
                      title: 'Date of Birth',
                      date: value,
                      onSelect: (selectedDate: Date) => {
                        setValue('dateOfBirth', selectedDate);
                      },
                    },
                  });
                }
              };
              return (
                <StyledButton
                  marginTop='$3.5'
                  onPress={handleOnPress}
                  variant='secondary'
                  borderColor='$grey'
                  justifyContent='flex-start'
                  gap='$1'
                  icon={<CalendarDays color='$grey' size={14} />}
                  iconAfter={
                    value ? (
                      <CircleX
                        color='$grey'
                        size={16}
                        onPress={clearDateOfBirth}
                      />
                    ) : (
                      <ChevronDown color='$grey' size='$1' />
                    )
                  }
                  paddingHorizontal='$3.5'>
                  {value ? (
                    <LocaleDate date={value} flex={1} color='$color' />
                  ) : (
                    <I18nText color='$grey' flex={1}>
                      Date of Birth
                    </I18nText>
                  )}
                </StyledButton>
              );
            }}
          />
          {/* Custom wrapper for FormControllerGender to handle type compatibility */}
          <Controller
            control={control}
            name='gender'
            render={({field: {value}}) => {
              const handleOnPress = async () => {
                await SheetManager.show('genderPicker', {
                  payload: {
                    title: 'Gender',
                    gender: value,
                    onSelect: (selectedGender: string) => {
                      setValue('gender', selectedGender);
                    },
                  },
                });
              };

              const clear = () => {
                setValue('gender', undefined);
              };

              const selectedName = value ? getSelectedGenderName(value) : '';

              return (
                <StyledButton
                  marginTop='$3.5'
                  onPress={handleOnPress}
                  variant='secondary'
                  borderColor='$grey'
                  justifyContent='flex-start'
                  gap='$1'
                  iconAfter={
                    value ? (
                      <CircleX color='$grey' size={16} onPress={clear} />
                    ) : (
                      <ChevronDown color='$grey' size='$1' />
                    )
                  }
                  paddingHorizontal='$3.5'>
                  <I18nText color='$grey' flex={1}>
                    {value ? selectedName : 'Gender'}
                  </I18nText>
                </StyledButton>
              );
            }}
          />
          <I18nText marginTop='$5'>Body Metrics</I18nText>
          <XStack gap='$4'>
            {/* Weight */}
            <Controller
              control={control}
              name='weight'
              render={({field: {value}}) => {
                const handleOnPress = async () => {
                  await SheetManager.show('weightPicker', {
                    payload: {
                      title: 'Weight',
                      value: value,
                      onSelect: (selectedWeight: number) => {
                        setValue('weight', parseInt(selectedWeight.toString()));
                      },
                    },
                  });
                };
                const handleClear = () => {
                  setValue('weight', undefined);
                };

                return (
                  <StyledButton
                    onPress={handleOnPress}
                    variant='secondary'
                    borderColor='$grey'
                    marginTop='$3.5'
                    justifyContent='flex-start'
                    gap='$1'
                    flex={1}
                    iconAfter={
                      value ? (
                        <CircleX
                          color='$grey'
                          size={16}
                          onPress={handleClear}
                        />
                      ) : (
                        <ChevronDown color='$grey' size='$1' />
                      )
                    }
                    paddingHorizontal='$3.5'>
                    <Weight value={value} placeholder={t('Weight')} />
                  </StyledButton>
                );
              }}
            />
            {/* Height */}
            <Controller
              control={control}
              name='height'
              render={({field: {value}}) => {
                const handleOnPress = async () => {
                  await SheetManager.show('heightPicker', {
                    payload: {
                      title: 'Height',
                      value: value,
                      onSelect: (selectedWeight: number) => {
                        setValue('height', parseInt(selectedWeight.toString()));
                      },
                    },
                  });
                };
                const handleClear = () => {
                  setValue('height', undefined);
                };

                return (
                  <StyledButton
                    onPress={handleOnPress}
                    variant='secondary'
                    borderColor='$grey'
                    marginTop='$3.5'
                    justifyContent='flex-start'
                    gap='$1'
                    flex={1}
                    iconAfter={
                      value ? (
                        <CircleX
                          color='$grey'
                          size={16}
                          onPress={handleClear}
                        />
                      ) : (
                        <ChevronDown color='$grey' size='$1' />
                      )
                    }
                    paddingHorizontal='$3.5'>
                    <Height value={value} placeholder={t('Height')} />
                  </StyledButton>
                );
              }}
            />
          </XStack>
          <I18nText marginTop='$5'>Activity level</I18nText>
          {/* Custom wrapper for FormControllerActivityLevel to handle type compatibility */}
          <Controller
            control={control}
            name='levelId'
            render={({field: {value}}) => {
              const handleOnPress = async () => {
                await SheetManager.show('activity_level_picker', {
                  payload: {
                    title: 'Activity Level',
                    levelId: value,
                    onSelect: (levelId: string) => {
                      setValue('levelId', levelId);
                    },
                  },
                });
              };

              const clear = () => {
                setValue('levelId', undefined);
              };

              return (
                <StyledButton
                  marginTop='$3.5'
                  onPress={handleOnPress}
                  variant='outlined'
                  borderColor='$grey'
                  justifyContent='flex-start'
                  gap='$1'
                  iconAfter={
                    value ? (
                      <CircleX color='$grey' size={16} onPress={clear} />
                    ) : (
                      <ChevronDown color='$grey' size='$1' />
                    )
                  }
                  paddingHorizontal='$3.5'>
                  <I18nText color='$grey' flex={1}>
                    {value
                      ? activityLevel?.find((item) => item.id === value)?.name
                      : 'Activity Level'}
                  </I18nText>
                </StyledButton>
              );
            }}
          />

          <I18nText marginTop='$5'>Country</I18nText>
          {/* Country selection */}
          <Controller
            control={control}
            name='countryId'
            render={({field: {value}}) => {
              const handleOnPress = async () => {
                const handleSelectCountry = (selectedCountry: CountryDto) => {
                  setValue('countryId', selectedCountry.id);
                };

                await SheetManager.show('country_list', {
                  payload: {
                    countryId: value,
                    onSelect: handleSelectCountry,
                  },
                });
              };

              const clear = () => {
                setValue('countryId', undefined);
              };

              const selectedCountry = countries?.find((c) => c.id === value);

              return (
                <StyledButton
                  marginTop='$3.5'
                  onPress={handleOnPress}
                  variant='outlined'
                  borderColor='$grey'
                  justifyContent='flex-start'
                  gap='$1'
                  icon={<MapPin color='$grey' size={14} />}
                  iconAfter={
                    value ? (
                      <CircleX color='$grey' size={16} onPress={clear} />
                    ) : (
                      <ChevronDown color='$grey' size='$1' />
                    )
                  }
                  paddingHorizontal='$3.5'>
                  <I18nText color='$grey' flex={1}>
                    {selectedCountry ? selectedCountry.name : 'Select Country'}
                  </I18nText>
                </StyledButton>
              );
            }}
          />
        </YStack>
      </ScrollView>
      <StyledButton
        onPress={handleSubmit(onSubmit)}
        loading={isPending}
        variant='primary'>
        <I18nText color={isPending ? '$grey1' : '$white1'}>
          {isPending ? 'Saving...' : 'Save'}
        </I18nText>
      </StyledButton>
    </YStack>
  );
}
