import React, {useEffect} from 'react';
import {useLocalSearchParams, useRouter} from 'expo-router';
import {useQueryClient} from '@tanstack/react-query';
import {Controller, useForm} from 'react-hook-form';
import {XStack, YStack} from 'tamagui';
import {useTranslation} from 'react-i18next';
import {ScrollView} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import {zodResolver} from '@hookform/resolvers/zod';
import {defaultValues} from '@/src/forms/schema/add-club-post';
import I18nText from '@/src/components/I18nText';
import CustomInput from '@/src/components/UI/CustomInput';
import StyledButton from '@/src/components/UI/Button';
import {
  AddClubPostFormInterface,
  addClubPostSchema,
} from '@/src/forms/schema/add-club-post';
import CustomTextarea from '@/src/components/UI/CustomTexarea';
import MediaPicker from '@/src/components/MediaPicker';
import {useToastController} from '@tamagui/toast';
import betterStack from '@/src/services/betterStack';
import useMutationCreateClubPost from '@/src/hooks/api/useMutationCreateClubPost';
import {triggerHaptics} from '@/src/utils/haptics';
import {SheetManager} from 'react-native-actions-sheet';

const AddClubPost = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const {bottom} = useSafeAreaInsets();
  const {t} = useTranslation();
  const toast = useToastController();
  const {clubId} = useLocalSearchParams<{
    clubId: string;
  }>();

  const {
    control,
    handleSubmit,
    formState: {errors, isValid},
  } = useForm<AddClubPostFormInterface>({
    resolver: zodResolver(addClubPostSchema),
    defaultValues: {
      ...defaultValues,
    },
  });
  const {mutateAsync, isPending} = useMutationCreateClubPost(clubId);

  useEffect(() => {
    if (errors.description) {
      toast.show(t('Error'), {
        message: t('Feed us a message or image to proceed.'),
        type: 'error',
      });
    }
  }, [errors.description, toast, t]);

  const onCancel = async () => {
    await triggerHaptics();

    await SheetManager.show('discard_post');
  };

  const onSubmit = async (data: AddClubPostFormInterface) => {
    await triggerHaptics();

    if (clubId) {
      try {
        const createClubPostDto = {
          caption: data.caption ?? '',
          message: data.description ?? '',
          attachments:
            data.media
              ?.filter((item): item is {id: string; url: string} => !!item)
              .map((item) => item.id) || [],
        };

        await mutateAsync(createClubPostDto);

        await Promise.all([
          queryClient.invalidateQueries({
            queryKey: ['feed'],
          }),
          queryClient.invalidateQueries({
            queryKey: ['clubs', 'posts'],
          }),
        ]);

        router.back();
      } catch (e) {
        await betterStack(e);
      }
    }
  };

  console.log('errorss', isPending);

  return (
    <YStack flex={1}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <YStack paddingHorizontal='$5' paddingTop='$5' gap='$2.5'>
          <Controller
            control={control}
            name='caption'
            render={({field: {onChange, value}}) => (
              <CustomInput
                placeholder={t('Add a title')}
                value={value ?? ''}
                onChange={onChange}
              />
            )}
          />

          <Controller
            control={control}
            name='description'
            render={({field: {onChange, value}}) => (
              <>
                {errors.description &&
                  toast.show(t('Error'), {
                    message: t('Feed us a message or image to proceed.s'),
                    type: 'error',
                  })}
                <CustomTextarea
                  onChange={onChange}
                  value={value}
                  placeholder={t(`What's going on?`)}
                  height='$14'
                />
              </>
            )}
          />

          <YStack marginTop='$5'>
            <Controller
              control={control}
              name='media'
              render={({field: {value, onChange}}) => (
                <MediaPicker
                  media={value}
                  onMediasChange={onChange}
                  label='Add photos'
                  maxImages={10}
                  canTakePhoto={false}
                />
              )}
            />
          </YStack>
        </YStack>
      </ScrollView>

      <XStack padding='$5' gap='$3.5' paddingBottom={bottom ? bottom : '$5'}>
        <StyledButton
          flex={1}
          variant='ghost'
          borderRadius='$5'
          onPress={onCancel}>
          <I18nText color='$grey2' fontWeight='500'>
            Cancel
          </I18nText>
        </StyledButton>
        <StyledButton
          flex={1}
          borderRadius='$5'
          disabled={!isValid || isPending}
          loading={isPending}
          onPress={handleSubmit(onSubmit)}>
          <I18nText color='$white1' fontWeight='500'>
            Publish
          </I18nText>
        </StyledButton>
      </XStack>
    </YStack>
  );
};

export default AddClubPost;
