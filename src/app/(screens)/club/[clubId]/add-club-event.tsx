import React from 'react';
import {useLoc<PERSON><PERSON>earch<PERSON>ara<PERSON>, useRouter} from 'expo-router';
import {useQueryClient} from '@tanstack/react-query';
import {Controller, useForm} from 'react-hook-form';
import {XStack, YStack} from 'tamagui';
import {useTranslation} from 'react-i18next';
import {Platform, ScrollView} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {DateTimePickerAndroid} from '@react-native-community/datetimepicker';
import {DateTime} from 'luxon';
import {SheetManager} from 'react-native-actions-sheet';
import {CalendarDays, ChevronDown} from '@tamagui/lucide-icons';

import useMutationCreateClubEvent from '@/src/hooks/api/useMutationCreateClubEvent';
import {zodResolver} from '@hookform/resolvers/zod';
import CustomCheckbox from '@/src/components/UI/CustomCheckbox';
import {
  AddClubEventFormInterface,
  addClubEventSchema,
  defaultValues,
} from '@/src/forms/schema/add-club-event';
import {triggerHaptics} from '@/src/utils/haptics';
import {useLocales} from '@/src/contexts/LocaleContext';
import StyledText from '@/src/components/UI/StyledText';
import I18nText from '@/src/components/I18nText';
import CustomInput from '@/src/components/UI/CustomInput';
import StyledButton from '@/src/components/UI/Button';
import betterStack from '@/src/services/betterStack';
import CustomTextarea from '@/src/components/UI/CustomTexarea';

const AddClubEvent = () => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const {locales} = useLocales();
  const {bottom} = useSafeAreaInsets();
  const {t} = useTranslation();
  const {clubId} = useLocalSearchParams<{
    clubId: string;
  }>();

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors, isValid},
  } = useForm<AddClubEventFormInterface>({
    resolver: zodResolver(addClubEventSchema),
    defaultValues: {
      ...defaultValues,
      date: new Date(),
    },
  });

  const date = watch('date');
  const type = watch('type');
  const {mutateAsync, isPending} = useMutationCreateClubEvent(clubId);

  const handleDateSelect = async () => {
    await triggerHaptics();

    // Set current date as minimum date
    const minDate = new Date();
    const maxDate = new Date();
    maxDate.setFullYear(maxDate.getFullYear() + 15); // Set maximum date to one year from now

    if (Platform.OS === 'android') {
      // For Android, we'll show date picker first, then time picker
      const showTimePicker = (selectedDate: Date) => {
        // Keep the selected date and show time picker
        DateTimePickerAndroid.open({
          value: selectedDate,
          mode: 'time',
          is24Hour: true,
          minimumDate: new Date(),
          maximumDate: maxDate,
          onChange: (_, selectedTime) => {
            if (selectedTime) {
              // Combine the date and time
              const combinedDate = new Date(selectedDate);
              combinedDate.setHours(selectedTime.getHours());
              combinedDate.setMinutes(selectedTime.getMinutes());
              combinedDate.setSeconds(selectedTime.getSeconds());

              // Ensure the date is in the user's timezone
              const dateInUserTZ = DateTime.fromJSDate(combinedDate)
                .setZone(locales.timezone)
                .toJSDate();

              // Update the form value with the timezone-adjusted date
              setValue('date', dateInUserTZ);
            }
          },
        });
      };

      // Show date picker first
      DateTimePickerAndroid.open({
        value: date || new Date(),
        mode: 'date',
        minimumDate: minDate,
        maximumDate: maxDate,
        onChange: (_, selectedDate) => {
          if (selectedDate) {
            // After date selection, show time picker
            showTimePicker(selectedDate);
          }
        },
      });
    } else {
      // For iOS, use the existing sheet implementation
      await SheetManager.show('dateTimePicker', {
        payload: {
          title: 'Activity Date',
          date: date,
          mode: 'datetime',
          minimumDate: minDate,
          maximumDate: maxDate,
          onSelect: (selectedDate: Date) => {
            setValue('date', selectedDate);
          },
        },
      });
    }
  };

  const onSubmit = async (data: AddClubEventFormInterface) => {
    await triggerHaptics();

    if (clubId) {
      try {
        const createClubEventDto = {
          type: data.type,
          startTime: DateTime.fromJSDate(data.date).toFormat(
            'yyyy-MM-dd HH:mm:ss',
          ),
          duration: data.duration ? Number(data.duration) : undefined,
          title: data.title,
          description: data.description,
          inviteUrl:
            data.type === 'virtual' && data.inviteUrl
              ? data.inviteUrl
              : undefined,
        };

        await mutateAsync(createClubEventDto);

        await queryClient.invalidateQueries({
          queryKey: ['clubs', 'events', clubId],
        });

        router.back();
      } catch (e) {
        await betterStack(e);
      }
    }
  };

  return (
    <YStack flex={1}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <YStack paddingHorizontal='$5' gap='$2.5'>
          <Controller
            control={control}
            name='type'
            render={({field: {onChange, value}}) => {
              const handlePressVirtual = () => onChange('virtual');
              const handlePressInPerson = () => onChange('in-person');

              return (
                <XStack
                  height='$4'
                  alignItems='center'
                  justifyContent='space-between'
                  marginBottom='$-2.5'>
                  <I18nText fontWeight={700}>Event Type</I18nText>
                  <XStack alignItems='center' gap='$2'>
                    <CustomCheckbox
                      label='virtual'
                      isChecked={value === 'virtual'}
                      onChange={handlePressVirtual}
                    />
                    <CustomCheckbox
                      label='in-person'
                      isChecked={value === 'in-person'}
                      onChange={handlePressInPerson}
                    />
                  </XStack>
                </XStack>
              );
            }}
          />

          <XStack
            onPress={handleDateSelect}
            backgroundColor='$background'
            borderRadius={8}
            flex={1}
            height='$4'
            borderWidth={1}
            borderColor='$grey3'
            paddingHorizontal='$3'
            gap='$2'
            alignItems='center'>
            <CalendarDays size={16} color='$grey1' />
            {date ? (
              <StyledText color='$grey1' flex={1}>
                {DateTime.fromJSDate(date)
                  .setZone(locales.timezone) // Ensure we're using the user's timezone
                  .setLocale(locales.languageTag)
                  .toLocaleString(DateTime.DATETIME_MED)}
              </StyledText>
            ) : (
              <I18nText flex={1}>Select date</I18nText>
            )}
            <ChevronDown color='$grey1' />
          </XStack>

          <Controller
            control={control}
            name='title'
            render={({field: {onChange, value}}) => (
              <YStack>
                {errors.title && (
                  <StyledText color='$primary' marginBottom='$1'>
                    {errors.title.message}
                  </StyledText>
                )}
                <CustomInput
                  placeholder={t('Event Title')}
                  value={value}
                  onChange={onChange}
                />
              </YStack>
            )}
          />

          <Controller
            control={control}
            name='duration'
            render={({field: {onChange, value}}) => (
              <CustomInput
                placeholder={t('Duration (in minutes)')}
                value={value ?? ''}
                onChange={onChange}
                numeric
              />
            )}
          />

          <Controller
            control={control}
            name='description'
            render={({field: {onChange, value}}) => (
              <YStack>
                {errors.description && (
                  <StyledText color='$primary' marginBottom='$1'>
                    {errors.description.message}
                  </StyledText>
                )}
                <CustomTextarea
                  onChange={onChange}
                  value={value}
                  placeholder={t(`Short description`)}
                />
              </YStack>
            )}
          />

          {type === 'virtual' && (
            <Controller
              control={control}
              name='inviteUrl'
              render={({field: {onChange, value}}) => (
                <CustomInput
                  placeholder={t('Invite URL')}
                  value={value ?? ''}
                  onChange={onChange}
                />
              )}
            />
          )}
        </YStack>
      </ScrollView>

      <YStack padding='$5' paddingBottom={bottom ? bottom : '$5'}>
        <StyledButton
          disabled={!isValid || isPending}
          loading={isPending}
          onPress={handleSubmit(onSubmit)}>
          <I18nText color='$white1' fontWeight='500'>
            Publish
          </I18nText>
        </StyledButton>
      </YStack>
    </YStack>
  );
};

export default AddClubEvent;
