import {ScrollView, useTheme, XStack, YStack} from 'tamagui';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import * as Application from 'expo-application';
import I18nText from '@/src/components/I18nText';
import {
  Bell,
  ChevronRight,
  FileText,
  Lock,
  LogOut,
  MoreHorizontal,
  Settings,
  Smartphone,
  User2,
} from '@tamagui/lucide-icons';
import {triggerHaptics} from '@/src/utils/haptics';
import Businesses from '@/src/components/Businesses';
import React from 'react';
import {FontAwesome5, MaterialIcons} from '@expo/vector-icons';
import {useSession} from '@/src/contexts/SessionContext';
import intercom from '@/src/services/intercom';
import {useRouter} from 'expo-router';
import StyledText from '@/src/components/UI/StyledText';
import {Alert, Platform} from 'react-native';
import {useTranslation} from 'react-i18next';
import {BusinessListDto} from '@gojoe/typescript-sdk';
import {SheetManager} from 'react-native-actions-sheet';
import useUserBusinesses from '@/src/hooks/api/useUserBusinesses';

const UserSettingsScreen = () => {
  const {bottom} = useSafeAreaInsets();
  const {user, signOut} = useSession();
  const {t} = useTranslation();
  const router = useRouter();
  const theme = useTheme();
  const {data} = useUserBusinesses({limit: 4});
  const iconColor = theme.grey ? theme.grey.get() : '#ACAFBB';

  const handlePressSignOut = async () => {
    await triggerHaptics();
    Alert.alert(t('Log Out')!, t('Are you sure you want to log out?')!, [
      {
        text: t('Cancel')!,
        style: 'cancel',
      },
      {
        text: t('Log Out')!,
        style: 'destructive',
        onPress: async () => {
          await signOut();
          router.replace('/sign-in');
        },
      },
    ]);
  };

  const handlePressLiveSupport = async () => {
    if (!user) {
      return;
    }
    await triggerHaptics();
    await intercom.openChat(user);
  };

  const handlePressFaq = async () => {
    await triggerHaptics();
    router.push({pathname: '/faq'});
  };

  const handlePressEditProfile = async () => {
    await triggerHaptics();
    router.push('/edit-profile');
  };
  const handlePressSettings = async () => {
    await triggerHaptics();
    router.push('/account-preferences');
  };
  const handlePressPrivacy = async () => {
    await triggerHaptics();
    router.push('/account-privacy');
  };
  const handlePressWearables = async () => {
    await triggerHaptics();
    router.push('/application-and-devices');
  };
  const handlePressNotification = async () => {
    await triggerHaptics();
    router.push('/push-notifications');
  };

  const handlePressViewAllOrgs = async () => {
    await triggerHaptics();
    router.push('/businesses');
  };
  const handlePressOther = async () => {
    await triggerHaptics();
    router.push('/other');
  };

  const handlePressPrivacyPolicy = async () => {
    await triggerHaptics();
    router.push('/privacy-policy');
  };

  const handlePressTerms = async () => {
    await triggerHaptics();
    router.push('/terms-of-use');
  };

  const handlePressBusiness = async (business: BusinessListDto) => {
    await triggerHaptics();
    await SheetManager.show('business_settings', {
      payload: {
        business: business,
      },
    });
  };

  return (
    <YStack flex={1} marginBottom={bottom} paddingHorizontal='$5'>
      <ScrollView showsVerticalScrollIndicator={false}>
        <I18nText fontSize={18} marginTop='$5' fontWeight='700'>
          General
        </I18nText>
        <YStack
          backgroundColor='$background'
          borderRadius={8}
          marginVertical={16}>
          <XStack
            onPress={handlePressEditProfile}
            paddingHorizontal='$5'
            paddingVertical='$3'
            alignItems='center'
            gap={8}>
            <YStack width={32}>
              <User2 color='$grey' />
            </YStack>
            <I18nText fontWeight='500' flex={1}>
              Edit profile
            </I18nText>
            <ChevronRight color='$grey' />
          </XStack>
          <XStack
            onPress={handlePressSettings}
            paddingHorizontal='$5'
            paddingVertical='$3'
            alignItems='center'
            gap={8}>
            <YStack width={32}>
              <Settings color='$grey' />
            </YStack>
            <I18nText fontWeight='500' flex={1}>
              Preferences
            </I18nText>
            <ChevronRight color='$grey' />
          </XStack>
          <XStack
            onPress={handlePressPrivacy}
            paddingHorizontal='$5'
            paddingVertical='$3'
            alignItems='center'
            gap={8}>
            <YStack width={32}>
              <Lock color='$grey' />
            </YStack>
            <I18nText fontWeight='500' flex={1}>
              Privacy Controls
            </I18nText>
            <ChevronRight color='$grey' />
          </XStack>
          <XStack
            onPress={handlePressWearables}
            paddingHorizontal='$5'
            paddingVertical='$3'
            alignItems='center'
            gap={8}>
            <YStack width={32}>
              <Smartphone color='$grey' />
            </YStack>
            <I18nText fontWeight='500' flex={1}>
              Application and Devices
            </I18nText>
            <ChevronRight color='$grey' />
          </XStack>
          <XStack
            onPress={handlePressNotification}
            paddingHorizontal='$5'
            paddingVertical='$3'
            alignItems='center'
            gap={8}>
            <YStack width={32}>
              <Bell color='$grey' />
            </YStack>
            <I18nText fontWeight='500' flex={1}>
              Push notifications
            </I18nText>
            <ChevronRight color='$grey' />
          </XStack>
        </YStack>
        <I18nText fontSize={18} marginTop='$5' fontWeight='700'>
          Communities
        </I18nText>
        <YStack
          backgroundColor='$background'
          paddingHorizontal='$5'
          paddingVertical='$2'
          borderRadius={8}
          marginVertical={16}>
          <Businesses data={data} onPress={handlePressBusiness} />
          <XStack
            onPress={handlePressViewAllOrgs}
            marginTop='$3'
            paddingVertical='$3'
            alignItems='center'
            gap={8}>
            <YStack width={32} />
            <I18nText flex={1} color='$primary' fontWeight='500'>
              View all Communities
            </I18nText>
            <ChevronRight color='$primary' />
          </XStack>
        </YStack>
        <I18nText fontSize={18} marginTop='$5' fontWeight='700'>
          Help
        </I18nText>
        <YStack
          backgroundColor='$background'
          borderRadius={8}
          marginVertical='$5'>
          <XStack height={48} onPress={handlePressFaq}>
            <YStack
              justifyContent='center'
              alignItems='center'
              width={48}
              height={48}>
              <FontAwesome5
                name='question-circle'
                size={18}
                color={iconColor}
              />
            </YStack>
            <XStack
              flex={1}
              alignItems='center'
              justifyContent='space-between'
              paddingEnd='$4'>
              <I18nText>FAQs</I18nText>
              <ChevronRight color='$grey' />
            </XStack>
          </XStack>
          <XStack height={48} onPress={handlePressLiveSupport}>
            <YStack
              justifyContent='center'
              alignItems='center'
              width={48}
              height={48}>
              <MaterialIcons
                name='chat-bubble-outline'
                size={18}
                color={iconColor}
              />
            </YStack>
            <XStack
              flex={1}
              alignItems='center'
              justifyContent='space-between'
              paddingEnd='$4'>
              <I18nText>Live Chat Support</I18nText>
              <ChevronRight color='$grey' />
            </XStack>
          </XStack>
        </YStack>
        <I18nText fontSize={18} marginTop='$5' fontWeight='700'>
          Legal
        </I18nText>
        <YStack
          backgroundColor='$background'
          borderRadius={8}
          marginVertical={16}>
          <XStack
            onPress={handlePressPrivacyPolicy}
            paddingHorizontal='$5'
            paddingVertical='$3'
            alignItems='center'
            gap={8}>
            <YStack width={32}>
              <FileText color='$grey' />
            </YStack>
            <I18nText fontWeight='500' flex={1}>
              Privacy Policy
            </I18nText>
            <ChevronRight color='$grey' />
          </XStack>
          <XStack
            onPress={handlePressTerms}
            paddingHorizontal='$5'
            paddingVertical='$3'
            alignItems='center'
            gap={8}>
            <YStack width={32}>
              <FileText color='$grey' />
            </YStack>
            <I18nText fontWeight='500' flex={1}>
              Terms of Use
            </I18nText>
            <ChevronRight color='$grey' />
          </XStack>
          <XStack
            onPress={handlePressOther}
            paddingHorizontal='$5'
            paddingVertical='$3'
            alignItems='center'
            gap={8}>
            <YStack width={32}>
              <MoreHorizontal color='$grey' />
            </YStack>
            <I18nText fontWeight='500' flex={1}>
              Other
            </I18nText>
            <ChevronRight color='$grey' />
          </XStack>
        </YStack>
        <XStack
          marginTop='$4'
          backgroundColor='$background'
          onPress={handlePressSignOut}
          paddingHorizontal='$5'
          paddingVertical='$3'
          alignItems='center'
          gap={8}
          borderRadius={8}>
          <YStack width={32}>
            <LogOut color='$primary' />
          </YStack>
          <I18nText fontWeight='500' flex={1} color='$primary'>
            Log out
          </I18nText>
        </XStack>
        <YStack marginTop='$4'>
          <StyledText textAlign='center'>{`GoJoe ${Platform.OS} ${Application.nativeApplicationVersion}.${Application.nativeBuildVersion}`}</StyledText>
        </YStack>
      </ScrollView>
    </YStack>
  );
};

export default UserSettingsScreen;
