import React, {useEffect, useLayoutEffect, useState} from 'react';
import {<PERSON><PERSON>, YStack} from 'tamagui';
import {Channel, MessageInput, MessageList} from 'stream-chat-expo';
import {Channel as ChannelType} from 'stream-chat';
import {useLocalSearchParams, useNavigation} from 'expo-router';
import {useChat} from '@/src/contexts/ChatContext';
import {Platform} from 'react-native';
import {useScreenPadding} from '@/src/hooks/useScreenPadding';

const ChannelScreen = () => {
  const {paddingBottom} = useScreenPadding();
  const {channelId} = useLocalSearchParams<{channelId: string}>();
  const [channel, setChannel] = useState<ChannelType | undefined>();
  const {client} = useChat();
  const navigation = useNavigation();

  useLayoutEffect(() => {
    if (channel) {
      // For direct messages, show the other user's name
      if (channel.data?.isDirect) {
        // Get the other user's ID (not the current user)
        const otherMembers = Object.values(channel.state.members)
          .filter((member) => member.user?.id !== client?.user?.id)
          .map((member) => member.user);

        if (otherMembers.length > 0) {
          // Use the other user's name as the title
          navigation.setOptions({
            title: otherMembers[0]?.name || channel.data?.name,
          });
        } else {
          navigation.setOptions({
            title: channel.data?.name,
          });
        }
      } else {
        // For group chats, use the channel name
        navigation.setOptions({
          title: channel.data?.name,
        });
      }
    }
  }, [navigation, channel, client?.user?.id]);

  useEffect(() => {
    if (!client) {
      return;
    }
    const createAndWatchChannel = async () => {
      const newChannel = client.channel('messaging', channelId);
      await newChannel.watch();
      setChannel(newChannel);
    };
    createAndWatchChannel();
  }, [channelId, client]);

  if (!channel) {
    return <Spinner />;
  }

  return (
    <YStack flex={1} paddingBottom={paddingBottom}>
      <Channel channel={channel}>
        <MessageList />
        <YStack paddingBottom={Platform.OS === 'ios' ? 0 : 12}>
          <MessageInput />
        </YStack>
      </Channel>
    </YStack>
  );
};

export default ChannelScreen;
