import {ChannelList} from 'stream-chat-expo';
import {Channel, ChannelFilters, ChannelSort} from 'stream-chat';
import {useSession} from '@/src/contexts/SessionContext';
import {useEffect, useState, useLayoutEffect, useCallback} from 'react';
import {useRouter, useNavigation} from 'expo-router';
import {YStack} from 'tamagui';
import {UserPlus} from '@tamagui/lucide-icons';
import {SheetManager} from 'react-native-actions-sheet';
import {SearchUserListDto} from '@gojoe/typescript-sdk';
import {useChat} from '@/src/contexts/ChatContext';
import {triggerHaptics} from '@/src/utils/haptics';

const sort: ChannelSort = {
  last_updated: -1,
};
const options = {limit: 20, messages_limit: 30};

const ChatScreen = () => {
  const {user} = useSession();
  const {client} = useChat();
  const [filters, setFilters] = useState<ChannelFilters | undefined>();
  const router = useRouter();
  const navigation = useNavigation();

  useEffect(() => {
    if (user) {
      setFilters({members: {$in: [user.id]}});
    }
  }, [user]);

  const handleUserSelected = useCallback(
    async (selectedUser: SearchUserListDto) => {
      if (!user || !client) return;

      // Create a direct channel between the current user and the selected user
      const directChannel = client.channel('messaging', {
        members: [user.id, selectedUser.id],
        isDirect: true,
      });

      try {
        // Create the channel if it doesn't exist
        await directChannel.create();

        // Update channel properties
        await directChannel.updatePartial({
          set: {
            isDirect: true,
          },
        });

        // Navigate to the channel
        if (directChannel.id) {
          router.push({
            pathname: '/chat/[channelId]',
            params: {channelId: directChannel.id},
          });
        }
      } catch (error) {
        console.error('Error creating direct channel:', error);
      }
    },
    [user, client, router],
  );

  const handleNewDirectMessage = useCallback(async () => {
    await triggerHaptics();
    await SheetManager.show('user_search', {
      payload: {
        challengeId: '', // Empty string as it's required but not needed for our use case
        onSelect: handleUserSelected,
        title: 'New Message',
        emptyStateText: 'Search for users to start a conversation',
      },
    });
  }, [handleUserSelected]);

  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <YStack
          onPressIn={handleNewDirectMessage}
          width={32}
          height={32}
          justifyContent='center'
          alignItems='center'
          marginRight='$2'>
          <UserPlus size={24} color='$color' />
        </YStack>
      ),
    });
  }, [navigation, handleNewDirectMessage]);

  const handleSelect = (channel: Channel) => {
    if (!channel.id) {
      return null;
    }
    return router.push({
      pathname: '/chat/[channelId]',
      params: {channelId: channel.id},
    });
  };

  return (
    <ChannelList
      filters={filters}
      sort={sort}
      options={options}
      onSelect={handleSelect}
    />
  );
};

export default ChatScreen;
