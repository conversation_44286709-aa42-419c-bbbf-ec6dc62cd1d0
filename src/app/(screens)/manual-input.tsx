import React from 'react';
import {<PERSON><PERSON><PERSON>iew, YStack, XStack} from 'tamagui';
import {useRouter} from 'expo-router';
import {Controller, useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {SheetManager} from 'react-native-actions-sheet';
import {ActivityTypeDto, CreateActivityDto} from '@gojoe/typescript-sdk';
import {DateTime} from 'luxon';
import convert from 'convert';
import {Platform} from 'react-native';
import {DateTimePickerAndroid} from '@react-native-community/datetimepicker';

import I18nText from '@/src/components/I18nText';
import StyledButton from '@/src/components/UI/Button';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import ActivityTypeSelector from '@/src/components/ActivityTypeSelector';
import TimeInput from '@/src/components/TimeInput';
import DistanceInput from '@/src/components/DistanceInput';
import MediaPicker from '@/src/components/MediaPicker';
import {
  manualInputSchema,
  defaultValues,
  ManualInputFormInterface,
} from '@/src/forms/schema/manual-input';

import {useMutationManualActivity} from '@/src/hooks/api/useMutationManualActivity';
import {triggerHaptics} from '@/src/utils/haptics';
import {useLocales} from '@/src/contexts/LocaleContext';
import StyledText from '@/src/components/UI/StyledText';
import {CalendarDays, ChevronDown} from '@tamagui/lucide-icons';
import {getDistanceUnit, getTimeOfDayLabel} from '@/src/utils/helper';
import {useTranslation} from 'react-i18next';
import {useToastController} from '@tamagui/toast';
import {InfoIcon} from '@/src/components/GoJoeIcon';
import {api} from '@/src/services/api';
import logger from '@/src/utils/logger';

const ManualInput: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const {bottom} = useSafeAreaInsets();
  const {mutateAsync, isPending} = useMutationManualActivity();
  const {locales} = useLocales();
  const toast = useToastController();

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors, isValid},
  } = useForm<ManualInputFormInterface>({
    resolver: zodResolver(manualInputSchema),
    defaultValues: {
      ...defaultValues,
      date: new Date(),
    },
    mode: 'onChange',
  });

  const activityType = watch('activityType');
  const evidences = watch('evidences');
  const date = watch('date');

  const handlePressEvidenceInfo = () => {
    SheetManager.show('info_evidence');
  };
  const handleDateSelect = async () => {
    await triggerHaptics();

    // Calculate the minimum date (48 hours ago)
    const minDate = new Date();
    minDate.setDate(minDate.getDate() - 2);

    // Set time to beginning of the day to ensure full 48 hours
    minDate.setHours(0, 0, 0, 0);

    if (Platform.OS === 'android') {
      // For Android, we'll show date picker first, then time picker
      const showTimePicker = (selectedDate: Date) => {
        // Keep the selected date and show time picker
        DateTimePickerAndroid.open({
          value: selectedDate,
          mode: 'time',
          is24Hour: true,
          maximumDate: new Date(),
          onChange: (_, selectedTime) => {
            if (selectedTime) {
              // Combine the date and time
              const combinedDate = new Date(selectedDate);
              combinedDate.setHours(selectedTime.getHours());
              combinedDate.setMinutes(selectedTime.getMinutes());
              combinedDate.setSeconds(selectedTime.getSeconds());

              // Ensure the date is in the user's timezone
              const dateInUserTZ = DateTime.fromJSDate(combinedDate)
                .setZone(locales.timezone)
                .toJSDate();

              // Update the form value with the timezone-adjusted date
              setValue('date', dateInUserTZ);
            }
          },
        });
      };

      // Show date picker first
      DateTimePickerAndroid.open({
        value: date || new Date(),
        mode: 'date',
        maximumDate: new Date(),
        minimumDate: minDate, // Set minimum date to 48 hours ago
        onChange: (_, selectedDate) => {
          if (selectedDate) {
            // After date selection, show time picker
            showTimePicker(selectedDate);
          }
        },
      });
    } else {
      // For iOS, use the existing sheet implementation
      await SheetManager.show('dateTimePicker', {
        payload: {
          title: 'Activity Date',
          date: date,
          mode: 'datetime',
          minimumDate: minDate, // Set minimum date to 48 hours ago
          maximumDate: new Date(),
          onSelect: (selectedDate: Date) => {
            setValue('date', selectedDate);
          },
        },
      });
    }
  };

  const onSubmit = async (data: ManualInputFormInterface) => {
    await triggerHaptics();
    const indexPoints = data.activityType.indexPoints;
    const title = `${t(getTimeOfDayLabel(data.date))} ${t(data.activityType.name)}`;
    const distanceUnit = getDistanceUnit(
      locales.distanceUnit,
      data.activityType.name,
    );
    const distance = convert(data.distance, distanceUnit).to('m');

    // Calculate end time - ensure it doesn't exceed current time
    const startDateTime = DateTime.fromJSDate(data.date);
    let endTime = startDateTime.plus({seconds: data.time});

    // If end time is in the future, adjust it to current time
    const now = DateTime.now();
    if (endTime > now) {
      // Set end time to now
      endTime = now;
      // Adjust start time to maintain the duration
      const adjustedStartTime = endTime.minus({seconds: data.time});
      // Update the form value with the adjusted start time
      setValue('date', adjustedStartTime.toJSDate());
      // Log this adjustment for debugging
      logger.debug(
        'Adjusted start time to maintain duration within time constraints',
      );
    }

    try {
      const createActivityDto: CreateActivityDto = {
        sportId: data.activityType.id,
        title: title,
        time: data.time,
        distance: distance,
        points: Math.round(distance / indexPoints),
        startTime: DateTime.fromJSDate(data.date).toUTC().toISO() || '',
        endTime: endTime.toUTC().toISO() || '',
        date: endTime.toUTC().toISODate() || '',
        calories: 0, // Optional
        heartRate: 0, // Optional
        timezone: locales.timezone,
        attachments: data.media.map((media) => media.id),
      };

      await mutateAsync(createActivityDto, {
        onSuccess: async ({data}) => {
          if (evidences.length > 0) {
            const activityId = data.data.id;
            await api
              .getApiClient()
              .activitiesControllerCreateActivityEvidence({
                createActivityEvidenceDto: {
                  activityId: activityId,
                  pictureUrl: evidences[0].url,
                },
                activityId: activityId,
              });
          }
          toast.show(t('Activity added Successfully'), {
            type: 'success',
          });

          setTimeout(() => {
            // Redirect to the team/challenge screen
            router.back();
          }, 1000);
        },
        onError: (error: any) => {
          logger.error('Error adding activity:', JSON.stringify(error));
          toast.show(
            t(error.response?.data?.message ?? 'Failed to add activity'),
            {
              type: 'error',
            },
          );
        },
      });
    } catch (error) {
      logger.error('Error creating activity:', error);
    }
  };

  return (
    <YStack flex={1}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <YStack padding='$5' gap='$4'>
          {/* Activity Type Section */}
          <Controller
            control={control}
            name='activityType'
            render={() => {
              const handleSelectActivityType = (selected: ActivityTypeDto) => {
                setValue(
                  'activityType',
                  {
                    id: selected.id,
                    name: selected.name,
                    hasDistance: selected.hasDistance,
                    indexPoints: selected.indexPoints ?? 1000,
                  },
                  {shouldValidate: true, shouldDirty: true},
                );
              };

              return (
                <ActivityTypeSelector
                  selectedActivityType={
                    activityType.id ? (activityType as ActivityTypeDto) : null
                  }
                  onSelectActivityType={handleSelectActivityType}
                />
              );
            }}
          />
          {errors.activityType && (
            <I18nText color='$red'>{errors.activityType.message}</I18nText>
          )}

          {activityType.id && (
            <YStack marginTop='$2'>
              <I18nText fontSize={18} fontWeight='700' marginBottom='$3'>
                Activity Details
              </I18nText>

              {activityType.hasDistance && (
                <YStack marginBottom='$2' gap='$1'>
                  {/* Distance Picker */}
                  <Controller
                    control={control}
                    name='distance'
                    render={({field: {value, onChange}}) => {
                      return (
                        <DistanceInput
                          value={value}
                          activityTypeName={activityType.name}
                          onChange={onChange}
                          label='Distance'
                        />
                      );
                    }}
                  />
                  {errors.distance && (
                    <I18nText color='$red'>{errors.distance.message}</I18nText>
                  )}
                </YStack>
              )}

              {/* Activity Duration */}
              <YStack marginBottom='$2' gap='$1'>
                <Controller
                  control={control}
                  name='time'
                  render={({field: {value, onChange}}) => (
                    <TimeInput
                      value={value}
                      onChange={(seconds) => {
                        // Update the time value
                        onChange(seconds);

                        // Adjust the start time based on the duration
                        // Set start time to current time minus the duration
                        const now = new Date();
                        const startTime = new Date(
                          now.getTime() - seconds * 1000,
                        );

                        // Ensure the date is in the user's timezone
                        const startTimeInUserTZ = DateTime.fromJSDate(startTime)
                          .setZone(locales.timezone)
                          .toJSDate();

                        // Update the form value with the adjusted start time
                        setValue('date', startTimeInUserTZ);
                      }}
                      label='Time'
                    />
                  )}
                />
                {errors.time && (
                  <I18nText color='$primary'>{errors.time.message}</I18nText>
                )}
                <I18nText fontSize={12} color='$grey1'>
                  Start time will be adjusted based on the duration
                </I18nText>
              </YStack>
              {/* Activity Date Time Start */}
              <YStack marginBottom='$2' gap='$1'>
                <XStack
                  onPress={handleDateSelect}
                  backgroundColor='$background'
                  borderRadius={8}
                  flex={1}
                  height='$4'
                  borderWidth={1}
                  borderColor='$grey3'
                  paddingHorizontal='$3'
                  gap='$2'
                  alignItems='center'>
                  <CalendarDays size={16} color='$grey1' />
                  {date ? (
                    <StyledText color='$grey1' flex={1}>
                      {DateTime.fromJSDate(date)
                        .setZone(locales.timezone) // Ensure we're using the user's timezone
                        .setLocale(locales.languageTag)
                        .toLocaleString(DateTime.DATETIME_MED)}
                    </StyledText>
                  ) : (
                    <I18nText flex={1}>Select date</I18nText>
                  )}
                  <ChevronDown color='$grey1' />
                </XStack>
                <I18nText fontSize={12} color='$grey1'>
                  You can only log activities from the last 48 hours
                </I18nText>
              </YStack>
              {/* Media Picker */}
              <YStack marginTop='$7'>
                <Controller
                  control={control}
                  name='media'
                  render={({field: {value, onChange}}) => (
                    <MediaPicker
                      media={value}
                      onMediasChange={onChange}
                      label='Add photos'
                    />
                  )}
                />
              </YStack>
              <YStack marginTop='$8' gap='$3.5'>
                <XStack justifyContent='space-between' alignItems='center'>
                  <I18nText fontWeight='700' fontSize={18}>
                    Activity Evidence
                  </I18nText>
                  <XStack
                    gap='$2'
                    alignItems='center'
                    onPress={handlePressEvidenceInfo}>
                    <I18nText fontStyle='italic' color='$grey1'>
                      (not public)
                    </I18nText>
                    <InfoIcon size={16} color='$grey1' />
                  </XStack>
                </XStack>
                <Controller
                  control={control}
                  name='evidences'
                  render={({field: {value, onChange}}) => (
                    <MediaPicker
                      media={value}
                      onMediasChange={onChange}
                      maxImages={1}
                    />
                  )}
                />
              </YStack>
            </YStack>
          )}
        </YStack>
      </ScrollView>

      {/* Save Button */}
      <YStack padding='$5' paddingBottom={bottom ? bottom : '$5'}>
        <StyledButton
          onPress={handleSubmit(onSubmit)}
          disabled={!isValid || isPending}
          loading={isPending}>
          <I18nText color='$white1' fontWeight='500'>
            Save Activity
          </I18nText>
        </StyledButton>
      </YStack>
    </YStack>
  );
};

export default ManualInput;
