import React from 'react';
import {Alert} from 'react-native';
import {<PERSON><PERSON>, XStack, YStack} from 'tamagui';
import {Delete} from '@tamagui/lucide-icons';
import {useTranslation} from 'react-i18next';
import I18nText from '@/src/components/I18nText';
import {api} from '@/src/services/api';
import {DateTime} from 'luxon';
import {useRouter} from 'expo-router';
import {useSession} from '@/src/contexts/SessionContext';

export default function OtherAccountScreen() {
  const [loading, setLoading] = React.useState(false);
  const {t} = useTranslation();
  const router = useRouter();
  const {signOut} = useSession();
  const handlePressDelete = () => {
    Alert.alert(
      t('Are you sure you wish to delete your account?'),
      t(
        'If you select yes, your account and personal data will be deleted within 30 days, unless you reactivate it within that period',
      ),
      [
        {
          text: t('Cancel'),
          style: 'cancel',
        },
        {
          text: t('Delete it'),
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            await api.getApiClient().userControllerDeleteAccount({
              deleteAccountInput: {
                deletedAt: DateTime.now().toFormat('yyyy-MM-dd HH:mm:ss'),
              },
            });
            await signOut();
            router.push('/');
          },
        },
      ],
    );
  };
  return (
    <YStack flex={1} paddingHorizontal='$5'>
      <I18nText fontSize={18} marginTop='$5' fontWeight='700'>
        Account
      </I18nText>
      <XStack
        disabled={loading}
        marginTop='$4'
        backgroundColor='$background'
        onPress={handlePressDelete}
        paddingHorizontal='$5'
        alignItems='center'
        height={48}
        gap={8}
        borderRadius={8}>
        <YStack width={32} height={32} justifyContent='center'>
          {loading ? <Spinner color='$primary' /> : <Delete color='$primary' />}
        </YStack>
        <I18nText fontWeight='500' flex={1} color='$primary'>
          Delete account
        </I18nText>
      </XStack>
    </YStack>
  );
}
