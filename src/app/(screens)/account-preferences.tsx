import React from 'react';
import {<PERSON><PERSON>V<PERSON>w, Switch, XStack, YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';
import {ChevronRight} from '@tamagui/lucide-icons';
import {
  BurnedCaloriesUnit,
  DistanceUnit,
  HeightUnit,
  TemperatureUnit,
  useLocales,
  WeightUnit,
} from '@/src/contexts/LocaleContext';
import UnitSettings from '@/src/components/UnitSettings';
import useMutationUserSettings from '@/src/hooks/api/useMutationUserSettings';
import {SheetManager} from 'react-native-actions-sheet';
import {getLanguage} from '@/src/locales';
import StyledText from '@/src/components/UI/StyledText';
import {HAPTIC_FEEDBACK_KEY, triggerHaptics} from '@/src/utils/haptics';
import {storage} from '@/src/utils/localStorage';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

export default function AccountSettingsScreen() {
  const {locales, setLocales} = useLocales();
  const {mutate} = useMutationUserSettings();
  const selectedLanguage = getLanguage(locales.languageTag);
  const insets = useSafeAreaInsets();

  const handleHapticToggle = async (enabled: boolean) => {
    // Save to storage
    storage.set(HAPTIC_FEEDBACK_KEY, enabled ? 'true' : 'false');

    // Update locales context
    setLocales({
      ...locales,
      hapticFeedback: enabled,
    });

    // Trigger haptic feedback if enabled
    if (enabled) {
      await triggerHaptics();
    }
  };

  const handleDistance = async (value: DistanceUnit) => {
    mutate({distanceUnit: value});
    setLocales({
      ...locales,
      distanceUnit: value,
    });
    await triggerHaptics();
  };

  const handlePressKilometers = () => {
    return handleDistance(DistanceUnit.Kilometers);
  };
  const handlePressMiles = () => {
    return handleDistance(DistanceUnit.Miles);
  };

  const handleTemperature = async (value: TemperatureUnit) => {
    mutate({temperatureUnit: value});
    setLocales({
      ...locales,
      temperatureUnit: value,
    });
    await triggerHaptics();
  };
  const handlePressCelsius = () => {
    return handleTemperature(TemperatureUnit.Celsius);
  };
  const handlePresFahrenheit = () => {
    return handleTemperature(TemperatureUnit.Fahrenheit);
  };

  const handleHeight = async (value: HeightUnit) => {
    setLocales({
      ...locales,
      heightUnit: value,
    });
    await triggerHaptics();
  };
  const handlePressCentimeters = () => {
    return handleHeight(HeightUnit.Centimeters);
  };
  const handlePressFeetInches = () => {
    return handleHeight(HeightUnit.FeetInches);
  };

  const handleWeight = async (value: WeightUnit) => {
    mutate({weightUnit: value});
    setLocales({
      ...locales,
      weightUnit: value,
    });
    await triggerHaptics();
  };

  const handlePressKilograms = () => {
    return handleWeight(WeightUnit.Kilograms);
  };

  const handlePressStones = () => {
    return handleWeight(WeightUnit.Stones);
  };

  const handlePressPounds = () => {
    return handleWeight(WeightUnit.Pounds);
  };
  const handlePressLanguage = async () => {
    await triggerHaptics();
    await SheetManager.show('languages');
  };
  const handlePressBurnedCalories = async () => {
    await triggerHaptics();
    await SheetManager.show('burned_calories_picker');
  };

  return (
    <ScrollView
      flex={1}
      paddingHorizontal='$5'
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{paddingBottom: insets.bottom}}>
      <I18nText fontSize={18} marginTop='$5' fontWeight='700'>
        Measurement Units
      </I18nText>
      <YStack
        backgroundColor='$background'
        padding='$5'
        marginTop='$3.5'
        borderRadius='$3.5'>
        <XStack alignItems='center' height='$5'>
          <I18nText color='$color' fontWeight='500' flex={1}>
            Distance
          </I18nText>
          <XStack
            backgroundColor='$windowBackground'
            height='$2.5'
            paddingHorizontal='$1.5'
            borderRadius='$3'
            alignItems='center'>
            <UnitSettings
              value={DistanceUnit.Kilometers}
              isActive={locales.distanceUnit === DistanceUnit.Kilometers}
              onPress={handlePressKilometers}
            />
            <UnitSettings
              value={DistanceUnit.Miles}
              isActive={locales.distanceUnit === DistanceUnit.Miles}
              onPress={handlePressMiles}
            />
          </XStack>
        </XStack>
        <XStack alignItems='center' height='$5'>
          <I18nText color='$color' fontWeight='500' flex={1}>
            Weight
          </I18nText>
          <XStack
            backgroundColor='$windowBackground'
            height='$2.5'
            paddingHorizontal='$1.5'
            borderRadius='$3'
            alignItems='center'>
            <UnitSettings
              value={WeightUnit.Kilograms}
              isActive={locales.weightUnit === WeightUnit.Kilograms}
              onPress={handlePressKilograms}
            />
            <UnitSettings
              value={WeightUnit.Stones}
              isActive={locales.weightUnit === WeightUnit.Stones}
              onPress={handlePressStones}
            />
            <UnitSettings
              value={WeightUnit.Pounds}
              isActive={locales.weightUnit === WeightUnit.Pounds}
              onPress={handlePressPounds}
            />
          </XStack>
        </XStack>
        <XStack alignItems='center' height='$5'>
          <I18nText color='$color' fontWeight='500' flex={1}>
            Height
          </I18nText>
          <XStack
            backgroundColor='$windowBackground'
            height='$2.5'
            paddingHorizontal='$1.5'
            borderRadius='$3'
            alignItems='center'>
            <UnitSettings
              value={HeightUnit.Centimeters}
              isActive={locales.heightUnit === HeightUnit.Centimeters}
              onPress={handlePressCentimeters}
            />
            <UnitSettings
              value={HeightUnit.FeetInches}
              isActive={locales.heightUnit === HeightUnit.FeetInches}
              onPress={handlePressFeetInches}
            />
          </XStack>
        </XStack>
        <XStack alignItems='center' height='$5'>
          <I18nText color='$color' fontWeight='500' flex={1}>
            Temperature
          </I18nText>
          <XStack
            backgroundColor='$windowBackground'
            height='$2.5'
            paddingHorizontal='$1.5'
            borderRadius='$3'
            alignItems='center'>
            <UnitSettings
              value={TemperatureUnit.Celsius}
              isActive={locales.temperatureUnit === TemperatureUnit.Celsius}
              onPress={handlePressCelsius}
            />
            <UnitSettings
              value={TemperatureUnit.Fahrenheit}
              isActive={locales.temperatureUnit === TemperatureUnit.Fahrenheit}
              onPress={handlePresFahrenheit}
            />
          </XStack>
        </XStack>
      </YStack>
      <I18nText fontSize={18} marginTop='$5' fontWeight='700'>
        Language
      </I18nText>
      <XStack
        gap='$2'
        backgroundColor='$background'
        padding='$5'
        marginTop='$3.5'
        borderRadius='$3.5'
        alignItems='center'
        onPress={handlePressLanguage}>
        <StyledText>{selectedLanguage.flag}</StyledText>
        <I18nText color='$primary' fontWeight='500' flex={1}>
          {selectedLanguage.name}
        </I18nText>
        <ChevronRight color='$grey1' />
      </XStack>
      <I18nText fontSize={18} marginTop='$5' fontWeight='700'>
        Interface
      </I18nText>
      <YStack
        backgroundColor='$background'
        padding='$5'
        marginTop='$3.5'
        borderRadius='$3.5'>
        {/* <XStack alignItems='center' height='$5'>
          <I18nText color='$primary' fontWeight='500' flex={1}>
            Theme
          </I18nText>
          <XStack
            backgroundColor='$windowBackground'
            height='$2.5'
            paddingHorizontal='$1.5'
            borderRadius='$3'
            alignItems='center'>
            <UnitSettings
              value='light'
              isActive={themeMode === 'light'}
              onPress={handlePressLight}
            />
            <UnitSettings
              value='dark'
              isActive={themeMode === 'dark'}
              onPress={handlePressDark}
            />
          </XStack>
        </XStack> */}
        <XStack alignItems='center' height='$5'>
          <I18nText
            color='$primary'
            fontWeight='500'
            flex={1}
            htmlFor='HAPTIC_FEEDBACK'>
            Haptic Feedback
          </I18nText>
          <Switch
            id='HAPTIC_FEEDBACK'
            onCheckedChange={handleHapticToggle}
            checked={locales.hapticFeedback}>
            <Switch.Thumb
              animation='bouncy'
              backgroundColor={locales.hapticFeedback ? '$primary' : undefined}
            />
          </Switch>
        </XStack>
      </YStack>
      <I18nText fontSize={18} marginTop='$5' fontWeight='700'>
        Show burned calories as
      </I18nText>
      <XStack
        gap='$2'
        backgroundColor='$background'
        padding='$5'
        marginTop='$3.5'
        borderRadius='$3.5'
        alignItems='center'
        onPress={handlePressBurnedCalories}>
        <StyledText>
          {locales.burnedCaloriesUnit === BurnedCaloriesUnit.Pizza
            ? '🍕'
            : '🚫'}
        </StyledText>
        <I18nText color='$primary' fontWeight='500' flex={1}>
          {locales.burnedCaloriesUnit}
        </I18nText>
        <ChevronRight color='$grey1' />
      </XStack>
    </ScrollView>
  );
}
