import React from 'react';
import {Stack, useLocalSearchParams} from 'expo-router';
import {useTranslation} from 'react-i18next';
import {XStack, YStack} from 'tamagui';
import BackButton from '@/src/components/BackButton';
import {NativeStackNavigationOptions} from '@react-navigation/native-stack';
import {triggerHaptics} from '@/src/utils/haptics';
import {FontAwesome5} from '@expo/vector-icons';
import {SheetManager} from 'react-native-actions-sheet';

const defaultOptions: NativeStackNavigationOptions = {
  headerShown: true,
  headerTransparent: true, // Makes the header transparent
  headerBackground: () => null, // Ensures there's no background
  headerTitle: '',
  headerTintColor: '#FFFFFF',
  headerLeft: () => <BackButton rounded />,
};
export default function GlobalStackLayout() {
  const {t} = useTranslation();
  const {clubId} = useLocalSearchParams<{
    clubId: string;
  }>();

  const handleClubInfo = async () => {
    await triggerHaptics();
    await SheetManager.show('club', {
      payload: {
        clubId,
      },
    });
  };

  return (
    <Stack
      screenOptions={{
        headerLeft: () => (
          <XStack>
            <BackButton />
          </XStack>
        ),
      }}>
      <Stack.Screen name='les-mills' options={{title: 'Les Mills'}} />
      <Stack.Screen
        name='les-mills/[programId]'
        options={{title: 'Les Mills'}}
      />
      <Stack.Screen
        name='house-of-wellbeing'
        options={{
          title: 'House of Wellbeing',
        }}
      />
      <Stack.Screen
        name='benefits/[benefitId]'
        options={{
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='club/[clubId]/index'
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name='club/[clubId]/add-club-event'
        options={{
          title: t('Create event'),
        }}
      />
      <Stack.Screen
        name='club/[clubId]/add-club-post'
        options={{
          title: t('Add post'),
        }}
      />
      <Stack.Screen
        name='business/[businessId]/index'
        options={{
          ...defaultOptions,
          title: '',
        }}
      />
      <Stack.Screen
        name='business/[businessId]/posts'
        options={{
          title: t('Posts'),
        }}
      />
      <Stack.Screen
        name='business/[businessId]/members'
        options={{
          title: t('Members'),
        }}
      />
      <Stack.Screen
        name='business/[businessId]/resources'
        options={{
          title: t('Members'),
        }}
      />
      <Stack.Screen
        name='video/index'
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name='chat/index'
        options={{
          title: t('Chat'),
        }}
      />
      <Stack.Screen
        name='chat/[channelId]'
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name='fair-play/index'
        options={{
          title: t('The Fair Play Book of Rules'),
        }}
      />
      <Stack.Screen
        name='fair-play/code'
        options={{
          title: t('Joe Code of Ethics'),
        }}
      />
      <Stack.Screen
        name='journeys'
        options={{
          title: t('Journeys'),
        }}
      />
      <Stack.Screen
        name='fair-play/points-cap'
        options={{
          title: t('Points cap'),
        }}
      />
      <Stack.Screen
        name='fair-play/flag-activities'
        options={{
          title: t('Flag activities for review'),
        }}
      />
      <Stack.Screen
        name='fair-play/unfiltered-leaderboards'
        options={{
          title: t('Unfiltered leaderboards'),
        }}
      />
      <Stack.Screen
        name='fair-play/support'
        options={{
          title: t('Support'),
        }}
      />
      <Stack.Screen
        name='user/settings'
        options={{
          title: t('Settings'),
        }}
      />
      <Stack.Screen
        name='gift-card/[giftCardId]'
        options={{
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='edit-profile'
        options={{
          title: t('Edit Profile'),
        }}
      />
      <Stack.Screen
        name='account-preferences'
        options={{
          title: t('Preferences'),
        }}
      />
      <Stack.Screen
        name='account-privacy'
        options={{
          title: t('Account Privacy'),
        }}
      />
      <Stack.Screen
        name='application-and-devices'
        options={{
          title: t('Application and Devices'),
        }}
      />
      <Stack.Screen
        name='push-notifications'
        options={{
          title: t('Push Notifications'),
        }}
      />
      <Stack.Screen
        name='tracker'
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name='journey/[journeyId]/index'
        options={{
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='journey-event/[eventId]/index'
        options={{
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='challenge/[challengeId]/(tabs)'
        options={{
          title: '',
          headerShadowVisible: false,
        }}
      />
      <Stack.Screen
        name='challenge/[challengeId]/create-team'
        options={{
          title: '',
          headerShadowVisible: false,
        }}
      />
      <Stack.Screen
        name='challenge/[challengeId]/[userId]'
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name='team/[teamId]'
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name='map/[mapId]'
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name='manual-input'
        options={{
          title: t('Add Manual Activity'),
        }}
      />
      <Stack.Screen
        name='post/[postId]'
        options={{
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='comments/[postId]'
        options={{
          title: t('Comments'),
        }}
      />
      <Stack.Screen
        name='user/[userId]/index'
        options={{
          title: '',
          headerShadowVisible: false,
        }}
      />
      <Stack.Screen
        name='user/[userId]/activities'
        options={{
          title: t('Activities'),
        }}
      />
      <Stack.Screen
        name='user/[userId]/posts'
        options={{
          title: t('Posts'),
        }}
      />
      <Stack.Screen
        name='businesses/index'
        options={{
          title: t('Communities'),
        }}
      />
    </Stack>
  );
}
