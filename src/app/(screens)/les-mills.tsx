import React from 'react';
import {<PERSON><PERSON>, YStack} from 'tamagui';
import {Image} from 'expo-image';
import {FlatList, ListRenderItemInfo, StyleSheet} from 'react-native';
import {useLesMillsCategories} from '@/src/hooks/api/useLesMillsCategories';
import {LesMillsCategoryDto, LesMillsProgramDto} from '@gojoe/typescript-sdk';
import I18nText from '@/src/components/I18nText';
import {useRouter} from 'expo-router';

const blackListedPrograms = ['Mind Body'];

const LesMillsScreen: React.FC = () => {
  const {data, isLoading} = useLesMillsCategories();
  const router = useRouter();

  const renderProgramsItem = ({
    item,
    index,
  }: ListRenderItemInfo<LesMillsProgramDto>) => {
    const handleOnPress = () => {
      router.push({
        pathname: '/les-mills/[programId]',
        params: {programId: item.id},
      });
    };
    const paddingStart = index === 0 ? 24 : 0;
    return (
      <YStack
        onPress={handleOnPress}
        paddingStart={paddingStart}
        marginEnd={16}>
        <Image
          source={{uri: item.thumbnail}}
          style={styles.image}
          contentFit='cover'
        />
      </YStack>
    );
  };

  const renderItem = ({item}: ListRenderItemInfo<LesMillsCategoryDto>) => {
    if (
      !item.programs ||
      item.programs.length < 2 ||
      blackListedPrograms.includes(item.name)
    ) {
      return null;
    }
    return (
      <YStack marginBottom={32}>
        <I18nText
          fontWeight='700'
          color='$color'
          marginStart={24}
          marginBottom={8}
          fontSize={18}>
          {item.name}
        </I18nText>
        <FlatList
          data={item.programs}
          renderItem={renderProgramsItem}
          horizontal
          showsHorizontalScrollIndicator={false}
        />
      </YStack>
    );
  };

  if (isLoading) {
    return <Spinner />;
  }
  return (
    <YStack flex={1} marginTop='$5'>
      <FlatList
        renderItem={renderItem}
        data={data}
        showsVerticalScrollIndicator={false}
      />
    </YStack>
  );
};

const styles = StyleSheet.create({
  image: {
    height: 127,
    width: 127,
  },
});
export default LesMillsScreen;
