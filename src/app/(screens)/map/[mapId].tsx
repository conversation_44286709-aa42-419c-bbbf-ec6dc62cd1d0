import React from 'react';
import {StyleSheet} from 'react-native';
import {useLocalSearchParams} from 'expo-router';
import ChallengeMap from '@/src/components/ChallengeMap';

const ChallengeMapScreen = () => {
  const {mapId, challengeId, overallPoints} = useLocalSearchParams<{
    mapId: string;
    challengeId: string;
    overallPoints: string;
  }>();

  return (
    <ChallengeMap
      challengeId={challengeId}
      mapId={mapId}
      overallPoints={parseInt(overallPoints ?? 0, 10)}
      style={styles.container}
      backButton={true}
    />
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
export default ChallengeMapScreen;
