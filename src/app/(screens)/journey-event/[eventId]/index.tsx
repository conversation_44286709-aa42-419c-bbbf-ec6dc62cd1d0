import React from 'react';
import {useLocalSearchParams} from 'expo-router';

import {YStack} from 'tamagui';
import ParallaxScrollView from '@/src/components/ParallaxScrollView';
import useJourney from '@/src/hooks/api/useJourney';
import useJourneyStep from '@/src/hooks/api/useJourneyStep';
import Overview from '@/src/screens/JourneyEvent/Overview';
import CompleteEvent from '@/src/screens/JourneyEvent/CompleteEvent';
import FooterMenu from '@/src/screens/JourneyEvent/FooterMenu';
import Preview from '@/src/screens/JourneyEvent/Preview';
import Reels from '@/src/screens/JourneyEvent/Reels';
import {useScreenPadding} from '@/src/hooks/useScreenPadding';

const WorkoutScreen = () => {
  const {paddingBottom} = useScreenPadding();
  const {journeyId, eventId} = useLocalSearchParams<{
    journeyId: string;
    eventId: string;
  }>();

  const {data: journey} = useJourney(journeyId);
  const {data: event} = useJourneyStep(eventId);

  if (!journey || !event) return null;

  return (
    <YStack
      flex={1}
      backgroundColor='$background'
      paddingBottom={paddingBottom}>
      <ParallaxScrollView
        backgroundColor='$windowBackground'
        headerImage={<Preview event={event} />}>
        <YStack paddingHorizontal='$5' paddingTop='$5'>
          <Overview journey={journey} event={event} />
          <Reels reels={event.reels} title={event.reelsTitle} />
        </YStack>
      </ParallaxScrollView>

      {journey.myJourney &&
        (journey.myJourney.step < event.position ? (
          <CompleteEvent journeyId={journey.id} eventId={event.id} />
        ) : (
          <FooterMenu journeyId={journeyId} eventId={event.id} />
        ))}
    </YStack>
  );
};

export default WorkoutScreen;
