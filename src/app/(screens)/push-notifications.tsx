import React from 'react';
import {YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';
import PushNotifications from '@/src/components/PushNotifications';

export default function PushNotificationsScreen() {
  return (
    <YStack flex={1} paddingHorizontal='$5'>
      <I18nText fontSize={18} marginTop='$5' fontWeight='700'>
        Push Notifications
      </I18nText>
      <PushNotifications />
      <I18nText marginTop='$5' lineHeight='$3'>
        These include updates on GoJoe challenges, messages from friends,
        follower activity and other personalisation to keep you motivated.
      </I18nText>
    </YStack>
  );
}
