import React, {useEffect} from 'react';
import {YStack} from 'tamagui';
import {useRouter, usePathname} from 'expo-router';
import logger from '@/src/utils/logger';

export default function NotFoundScreen() {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    logger.info('Route not found, redirecting to home:', pathname);

    // Check if this looks like a Branch short link (random characters)
    const isBranchShortLink = /^\/[a-zA-Z0-9]{8,15}$/.test(pathname);

    if (isBranchShortLink) {
      logger.info(
        'Detected Branch short link, waiting for Branch to process:',
        pathname,
      );
      // Give Branch SDK time to process the link and redirect
      setTimeout(() => {
        logger.info('Branch processing timeout, redirecting to home');
        router.replace('/');
      }, 3000); // Wait 3 seconds for Branch to process
    } else {
      // For other invalid routes, add a small delay to ensure app is mounted
      logger.info('Invalid route, redirecting to home after mount delay');
      setTimeout(() => {
        router.replace('/');
      }, 100); // Small delay to ensure Root Layout is mounted
    }
  }, [pathname, router]);

  // Show a minimal loading state while redirecting
  return (
    <YStack
      flex={1}
      justifyContent='center'
      alignItems='center'
      backgroundColor='$background'>
      {/* Empty - just redirect */}
    </YStack>
  );
}
