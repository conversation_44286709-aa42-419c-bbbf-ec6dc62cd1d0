import {Tabs} from 'expo-router';
import React, {useState} from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import {Image} from 'expo-image';
import {BottomTabBarButtonProps} from '@react-navigation/bottom-tabs';
import AuthUserAvatar from '@/src/components/AuthUserAvatar';
import {useTheme, View, XStack} from 'tamagui';
import {triggerHaptics} from '@/src/utils/haptics';
import {SheetManager} from 'react-native-actions-sheet';
import ChatIcon from '@/src/components/ChatIcon';
import TopBarNotifications from '@/src/components/TopBarNotification';
import TopBarRewards from '@/src/components/TopBarRewards';
import BusinessTopBar from '@/src/components/BusinessTopBar';
import {useTranslation} from 'react-i18next';
import {homeListRef} from '@/src/screens/Home';

export default function TabLayout() {
  const theme = useTheme();
  const color = theme.color.get();
  const {t} = useTranslation();
  const [currentTab, setCurrentTab] = useState('index');
  return (
    <Tabs
      screenOptions={{
        headerLeft: () => (
          <XStack paddingHorizontal='$5' paddingBottom={8} gap={8}>
            <AuthUserAvatar />
            <BusinessTopBar />
          </XStack>
        ),
        headerRight: () => (
          <XStack gap='$1' paddingHorizontal='$5' paddingBottom={8}>
            <ChatIcon />
            {/* <TopBarNotifications /> */}
            <TopBarRewards />
          </XStack>
        ),
        headerTitle: '',
        tabBarActiveTintColor: '#C43F2D',
        tabBarInactiveTintColor: color,
        headerTitleAlign: 'left',
        headerTitleStyle: {
          fontSize: 24,
          fontWeight: 'bold',
        },
      }}>
      <Tabs.Screen
        name='index'
        options={{
          title: t('Home'),
          headerStyle: {
            borderBottomWidth: 0,
            elevation: 0,
            shadowOpacity: 0,
          },
          tabBarIcon: ({color}) => (
            <Image
              source={require('@/assets/images/mobile/icons/home.png')}
              style={styles.icon}
              tintColor={color}
            />
          ),
        }}
        listeners={{
          tabPress: async () => {
            await triggerHaptics();

            // If we're already on the Home tab, scroll to top
            if (currentTab === 'index') {
              homeListRef.scrollToTop();
            }

            // Update current tab
            setCurrentTab('index');
          },
        }}
      />
      <Tabs.Screen
        name='challenges'
        options={{
          title: t('Challenges'),
          headerStyle: {
            borderBottomWidth: 0,
            elevation: 0,
            shadowOpacity: 0,
          },
          tabBarIcon: ({color}) => (
            <Image
              source={require('@/assets/images/mobile/icons/challenges.png')}
              style={styles.icon}
              tintColor={color}
            />
          ),
        }}
        listeners={{
          tabPress: async () => {
            await triggerHaptics();
            setCurrentTab('challenges');
          },
        }}
      />
      <Tabs.Screen
        name='add' // The middle screen for the larger button
        options={{
          title: '',
          tabBarIcon: () => (
            <Image
              source={require('@/assets/images/mobile/icons/plus.png')}
              style={styles.plusIcon}
            />
          ),
          tabBarButton: (props: BottomTabBarButtonProps) => (
            <CustomTabBarButton {...props} />
          ),
        }}
        listeners={{
          tabPress: async (event) => {
            event.preventDefault();
            await triggerHaptics();
            await SheetManager.show('add');
          },
        }}
      />
      <Tabs.Screen
        name='clubs'
        options={{
          title: t('Clubs'),
          headerStyle: {
            borderBottomWidth: 0,
            elevation: 0,
            shadowOpacity: 0,
          },
          tabBarIcon: ({color}) => (
            <Image
              source={require('@/assets/images/mobile/icons/clubs.png')}
              style={styles.icon}
              tintColor={color}
            />
          ),
        }}
        listeners={{
          tabPress: async () => {
            await triggerHaptics();
            setCurrentTab('clubs');
          },
        }}
      />
      <Tabs.Screen
        name='locker'
        options={{
          title: t('Locker'),
          headerStyle: {
            borderBottomWidth: 0,
            elevation: 0,
            shadowOpacity: 0,
          },
          tabBarIcon: ({color}) => (
            <Image
              source={require('@/assets/images/mobile/icons/locker.png')}
              style={styles.icon}
              tintColor={color}
            />
          ),
        }}
        listeners={{
          tabPress: async () => {
            await triggerHaptics();
            setCurrentTab('locker');
          },
        }}
      />
    </Tabs>
  );
}

const CustomTabBarButton = ({children, onPress}: BottomTabBarButtonProps) => (
  <View style={styles.customButton}>
    <TouchableOpacity style={styles.customIconContainer} onPress={onPress}>
      {children}
    </TouchableOpacity>
  </View>
);

const styles = StyleSheet.create({
  customButton: {
    top: -35,
    borderRadius: 50,
    height: 100,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    left: -10,
  },
  customIconContainer: {
    width: 68,
    height: 68,
    alignSelf: 'center',
    justifyContent: 'center',
    marginLeft: 38,
  },
  shadow: {
    shadowColor: '#7F5DF0',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.5,
    elevation: 5,
  },
  icon: {
    width: 24,
    height: 24,
  },
  plusIcon: {
    width: 68,
    height: 68,
  },
  headerIconContainer: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    marginHorizontal: 16,
  },
});
