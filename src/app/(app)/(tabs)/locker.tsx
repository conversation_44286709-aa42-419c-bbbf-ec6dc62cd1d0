import React from 'react';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import StyledTopTabButtonsNavigator from '@/src/components/StyledTopTabButtons';
import RewardsScreen from '@/src/screens/Rewards';
import BenefitsScreen from '@/src/screens/Benefits';

const Tab = createMaterialTopTabNavigator();

export default function Index() {
  return (
    <StyledTopTabButtonsNavigator>
      <Tab.Screen
        name='Rewards'
        component={RewardsScreen}
        options={{lazy: true}}
      />
      <Tab.Screen
        name='Benefits'
        component={BenefitsScreen}
        options={{lazy: true}}
      />
    </StyledTopTabButtonsNavigator>
  );
}
