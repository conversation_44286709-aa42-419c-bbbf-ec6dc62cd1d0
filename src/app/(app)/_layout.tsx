import React from 'react';
import {Redirect} from 'expo-router';
import {Image, View} from 'tamagui';
import {useSession} from '@/src/contexts/SessionContext';
import LottieAnimation from '@/src/components/LottieAnimation';
import AppLayout from '@/src/components/Layouts/AppLayout';
import {Platform} from 'react-native';

export default function App() {
  const {isAuthenticated, isReady, onboarded} = useSession();

  if (!isReady) {
    return (
      <View
        width='100%'
        height='100%'
        flex={1}
        justifyContent='center'
        alignItems='center'
        backgroundColor='$primary'>
        {Platform.OS === 'ios' && (
          <LottieAnimation
            autoPlay
            loop={false}
            animationData={require('@/assets/animations/welcome.json')}
          />
        )}
        <Image
          position='absolute'
          source={require('@/assets/logos/gojoe_white.png')}
          width={230}
          height={50}
          tintColor='#FFFFFF'
        />
      </View>
    );
  }

  if (!isAuthenticated) {
    return <Redirect href='/sign-in' />;
  }

  if (!onboarded) {
    return <Redirect href='/onboarding' />;
  }

  // This layout can be deferred because it's not the root layout.
  return <AppLayout />;
}
