import React, {useEffect, useState} from 'react';
import {StyleSheet} from 'react-native';
import {useLocalSearchParams} from 'expo-router';
import {
  HouseOfWellbeingAudio,
  useHouseOfWellbeing,
} from '@/src/hooks/api/useHouseOfWellbeing';
import {Image, Slider, Spinner, XStack, YStack} from 'tamagui';
import {ImageBackground} from 'expo-image';
import TrackPlayer, {
  State,
  usePlaybackState,
  useProgress,
} from 'react-native-track-player';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {MaterialIcons, FontAwesome6} from '@expo/vector-icons';
import StyledText from '@/src/components/UI/StyledText';
import {GestureReponderEvent} from '@tamagui/web';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';

const HouseOfWellbeingTrackId = () => {
  const {trackId} = useLocalSearchParams<{trackId: string}>();
  const {data, isLoading} = useHouseOfWellbeing();
  const [track, setTrack] = useState<HouseOfWellbeingAudio | undefined>();
  const playbackState = usePlaybackState();
  const {position, duration} = useProgress();
  const [isPlaying, setIsPlaying] = useState(false);
  const {bottom} = useSafeAreaInsets();

  useEffect(() => {
    setTrack(data.find((item) => item.id === trackId));
  }, [trackId, data]);

  useEffect(() => {
    if (track) {
      const init = async () => {
        await TrackPlayer.reset();
        await TrackPlayer.add(track);
        await TrackPlayer.seekTo(0);
        await TrackPlayer.play();
        setIsPlaying(true);
      };
      init();
    }

    return () => {
      TrackPlayer.pause(); // Stop playback when the screen is closed
    };
  }, [track]);

  const togglePlayPause = async () => {
    await triggerHaptics();
    if (playbackState.state === State.Playing) {
      await TrackPlayer.pause();
      setIsPlaying(false);
    } else {
      await TrackPlayer.play();
      setIsPlaying(true);
    }
  };

  const seekForward = async () => {
    await triggerHaptics();
    await TrackPlayer.seekTo(Math.floor(position + 10));
  };

  const seekBackward = async () => {
    await triggerHaptics();
    await TrackPlayer.seekTo(Math.floor(Math.max(0, position - 10)));
  };

  // Format Time
  const formatTime = (secs: number) => {
    const min = Math.floor(secs / 60);
    const sec = Math.floor(secs % 60);
    return `${min}:${sec < 10 ? '0' : ''}${sec}`;
  };

  const onSlideEnd = async (event: GestureReponderEvent, value: number) => {
    await TrackPlayer.seekTo(Math.floor(value));
  };
  const onSlideMove = async (event: GestureReponderEvent, value: number) => {
    await triggerHaptics();
    await TrackPlayer.seekTo(Math.floor(value));
  };

  if (isLoading) {
    return <Spinner marginTop='$4' />;
  }
  if (!track) {
    // TODO display a not found page
    return null;
  }

  return (
    <ImageBackground source={{uri: track.artwork}} style={styles.image}>
      <YStack backgroundColor='#00000095' flex={1} paddingBottom={bottom + 100}>
        <YStack flex={1} justifyContent='center' alignItems='center'>
          <StyledText color='$white1' fontSize={20} fontWeight='600'>
            {track.title}
          </StyledText>
          <I18nText marginTop='$4' color='$grey' fontWeight='600' fontSize={10}>
            Offered by
          </I18nText>
          <Image
            source={require('@/assets/logos/house-of-wellbeing.png')}
            width={24}
            height={24}
            borderRadius={12}
            borderColor='#FFFFFF40'
            marginTop='$2'
          />
        </YStack>
        <YStack paddingHorizontal='$5'>
          <XStack
            height={64}
            marginBottom={32}
            justifyContent='space-between'
            paddingHorizontal={48}>
            <YStack
              height={64}
              justifyContent='center'
              alignItems='center'
              onPress={seekBackward}>
              <MaterialIcons name='replay-10' color='#FFFFFF' size={32} />
            </YStack>
            <YStack
              onPress={togglePlayPause}
              width={64}
              height={64}
              backgroundColor='#FFFFFF'
              borderRadius={32}
              justifyContent='center'
              alignItems='center'>
              {isPlaying ? (
                <FontAwesome6 name='pause' size={24} />
              ) : (
                <FontAwesome6 name='play' size={24} />
              )}
            </YStack>
            <YStack
              height={64}
              justifyContent='center'
              alignItems='center'
              onPress={seekForward}>
              <MaterialIcons name='forward-10' color='#FFFFFF' size={32} />
            </YStack>
          </XStack>
          <Slider
            value={[Math.round(position)]}
            min={0}
            max={Math.round(duration)}
            height={2}
            onSlideEnd={onSlideEnd}
            onSlideMove={onSlideMove}>
            <Slider.Track backgroundColor='#D9D9D9'>
              <Slider.TrackActive />
            </Slider.Track>
            <Slider.Thumb
              size={8}
              index={0}
              circular
              backgroundColor='#FFF'
              borderWidth={0}
            />
          </Slider>
          <XStack justifyContent='space-between' marginTop='$2'>
            <StyledText color='$white1' fontSize={12}>
              {formatTime(position)}
            </StyledText>
            {!!duration && (
              <StyledText color='$white1' fontSize={12}>
                {formatTime(duration)}
              </StyledText>
            )}
          </XStack>
        </YStack>
      </YStack>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  image: {
    flex: 1,
  },
});
export default HouseOfWellbeingTrackId;
