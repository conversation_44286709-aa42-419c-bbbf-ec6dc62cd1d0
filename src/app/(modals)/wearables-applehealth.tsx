import React, {useEffect, useState} from 'react';
import {Alert, Linking, NativeModules} from 'react-native';
import {Stack, Image, Text, XStack, Spinner} from 'tamagui';
import {Pressable} from 'react-native';
import I18nText from '@/src/components/I18nText';
import StyledButton from '@/src/components/UI/Button';
import {useRouter} from 'expo-router';
import {useMutationConnectAppleHealth} from '@/src/hooks/api/useMutationConnectAppleHealth';
import {useMutationDisconnectWearable} from '@/src/hooks/api/useMutationDisconnectWearable';
import logger from '@/src/utils/logger';
import appleHealthService from '@/src/services/appleHealthService';
import {useTranslation} from 'react-i18next';

interface AppleHealthInfoProps {
  onConnect: () => void;
}

const {GJRNHealthKit, GJRNWorkouts} = NativeModules;

export default function WearablesAppleHealth({
  onConnect,
}: AppleHealthInfoProps) {
  const router = useRouter();
  const {connect, isPending: isConnecting} = useMutationConnectAppleHealth();
  const {disconnect, isPending: isDisconnecting} =
    useMutationDisconnectWearable('appleHealth');
  const [status, setStatus] = useState<string>('disconnected');
  const {t} = useTranslation();

  useEffect(() => {
    const fetchStatus = async () => {
      try {
        const currentStatus = appleHealthService.getStatus();
        setStatus(currentStatus);
      } catch (e) {
        console.error('Failed to get Apple Health status:', e);
      }
    };

    fetchStatus();
  }, []);
  const connected = status === 'connected';
  const wearable = 'Apple Health';

  const composeEmail = async () => {
    const email = '<EMAIL>';
    const subject = 'I have an issue with Apple Health';
    const body = 'Hi, can you help me with...';
    const url = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

    const canOpen = await Linking.canOpenURL(url);
    if (canOpen) {
      await Linking.openURL(url);
    } else {
      Alert.alert('Oops!', 'We could not open any "Mail" App.');
    }
  };
  const handleSyncNow = () => {
    try {
      // Use promise-based approach instead of await
      GJRNWorkouts.syncNow()
        .then((result: string) => {
          logger.info('✅ Sync Now completed, result:', result);
          Alert.alert('Success', t('✅ Sync successfully initiated'), [
            {text: 'OK', onPress: () => router.back()},
          ]);
        })
        .catch((error: unknown) => {
          logger.error('❌ Sync Now error:', error);
          Alert.alert('Error', t('❌ Error syncing data'));
        });
    } catch (error: unknown) {
      // This catch block handles any synchronous errors that might occur
      logger.error('❌ Sync Now synchronous error:', error);
      Alert.alert('Error', t('❌ Error syncing data'));
    }
  };
  const handleOnConnect = () => {
    GJRNHealthKit.authorize(async (err: Error) => {
      if (err) {
        logger.error('❌ Apple Health authorization failed:', err);
        router.back();
        return;
      }

      connect(undefined, {
        onSuccess: (result: any) => {
          logger.info('✅ Apple Health connected successfully');
          setStatus('connected');
          appleHealthService.setStatus('connected');
          if (onConnect) onConnect();
        },
        onError: (error) => {
          logger.error('❌ Failed to connect Apple Health:', error);
          Alert.alert('Error', 'Failed to connect to Apple Health');
          setStatus('disconnected');
          appleHealthService.setStatus('disconnected');
        },
      });
    });
  };

  const handleDisconnect = () => {
    Alert.alert(
      'Disconnect wearable',
      `Are you sure you want to disconnect from ${wearable}?`,
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Disconnect',
          style: 'destructive',
          onPress: () => {
            disconnect(undefined, {
              onSuccess: () => {
                logger.info('✅ Apple Health disconnected');
                setStatus('disconnected');
                appleHealthService.setStatus('disconnected');
                router.back();
              },
              onError: (error) => {
                logger.error(
                  '❌ Failed to disconnect from Apple Health:',
                  error,
                );
                Alert.alert('Error', `Could not disconnect from ${wearable}`);
                setStatus('connected');
                appleHealthService.setStatus('connected');
              },
            });
          },
        },
      ],
    );
  };
  const onDismiss = () => {
    router.back();
  };

  return (
    <Stack bg='$background' p='$4' br='$4' gap='$4'>
      <Stack ai='center' mt='$4' mb='$6'>
        <Image
          source={require('@/assets/devices/logo_apple.png')}
          style={{width: 205, height: 30}}
        />
      </Stack>

      <Text textAlign='center' px='$3'>
        GoJoe syncs the following Apple Watch (only) activities via Apple
        Health:
      </Text>

      <XStack jc='space-around' mt='$6' mb='$4'>
        <Stack ai='center'>
          <Image
            source={require('@/assets/images/apple_running_workouts.png')}
            style={{
              width: 58,
              height: 58,
              marginBottom: 12,
            }}
          />
          <Text textAlign='center'>{'Apple watch\nworkouts'}</Text>
        </Stack>
        <Stack ai='center'>
          <Image
            source={require('@/assets/images/apple_nrc_workouts.png')}
            style={{
              width: 58,
              height: 58,
              marginBottom: 12,
            }}
          />
          <Text textAlign='center'>{'Nike RC\nworkouts'}</Text>
        </Stack>
      </XStack>

      <Text textAlign='center' px='$3'>
        If you do have issues, do let us know at
      </Text>

      <Pressable onPress={composeEmail}>
        <Text textAlign='center' color='$blue10' px='$3'>
          <EMAIL>
        </Text>
      </Pressable>

      <Stack mt='$6' gap='$3'>
        {connected ? (
          <>
            <StyledButton onPress={handleSyncNow}>
              <XStack gap='$2' ai='center'>
                <I18nText color='$white1'>Sync Now</I18nText>
              </XStack>
            </StyledButton>
            <StyledButton onPress={handleDisconnect}>
              {isDisconnecting ? (
                <Spinner color='$white' />
              ) : (
                <I18nText color='$white1'>Disconnect</I18nText>
              )}
            </StyledButton>
          </>
        ) : (
          <StyledButton onPress={handleOnConnect}>
            {isConnecting ? (
              <Spinner color='$white' />
            ) : (
              <I18nText color='$white1'>Connect</I18nText>
            )}
          </StyledButton>
        )}

        <StyledButton variant='secondary' onPress={onDismiss}>
          <I18nText>Dismiss</I18nText>
        </StyledButton>
      </Stack>
    </Stack>
  );
}
