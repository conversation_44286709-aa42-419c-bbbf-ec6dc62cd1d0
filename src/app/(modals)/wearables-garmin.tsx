import React from 'react';
import WebViewWearables from '@/src/components/WebViewWearable';
import {Spinner} from 'tamagui';
import {useWearableConnectUrl} from '@/src/hooks/useWearableConnectUrl';

export default function WearablesGarmin() {
  const url = useWearableConnectUrl(
    'garmin',
    'garminConnectControllerConnectWithGarmin',
  );

  if (!url) {
    return <Spinner />;
  }

  return <WebViewWearables uri={url} />;
}
