import React, {useState, useEffect, useCallback} from 'react';
import {useTranslation} from 'react-i18next';
import {<PERSON><PERSON>, YStack} from 'tamagui';
import {useRouter, useLocalSearchParams} from 'expo-router';
import {StyledInput} from '@/src/components/UI/StyledInput';
import StyledButton from '@/src/components/UI/Button';
import I18nText from '@/src/components/I18nText';
import {useMutationPasscode} from '@/src/hooks/api/useMutationPasscode';
import {triggerHaptics} from '@/src/utils/haptics';
import ChallengeList from '@/src/components/ChallengeList';
import logger from '@/src/utils/logger';
import PasscodeBusiness from '@/src/components/PasscodeBusiness';

const MIN_CODE_LENGTH = 6;

const PasscodeScreen: React.FC = () => {
  const {t} = useTranslation();
  const params = useLocalSearchParams<{code: string}>();
  const [code, setCode] = useState(params.code || '');
  const router = useRouter();
  const {business, challenge, mutate, isPending, isError} =
    useMutationPasscode();

  const handleSubmit = async () => {
    await triggerHaptics();
    mutate(code);
  };

  // If code is provided via params, submit it automatically
  useEffect(() => {
    if (params.code && params.code.length >= MIN_CODE_LENGTH) {
      mutate(params.code);
    }
  }, [params.code, mutate]);

  // Handle API response
  useEffect(() => {
    if (business || challenge) {
      // We have a valid response, show the appropriate message
      // In a real implementation, you might want to navigate to the challenge or business page
      logger.debug('Valid code:', {business, challenge});
    }
  }, [business, challenge]);

  const handleOnPressChallenge = useCallback(async () => {
    if (!challenge) {
      return;
    }
    await triggerHaptics();
    router.back();
    setTimeout(() => {
      router.push({
        pathname: '/challenge/[challengeId]',
        params: {
          challengeId: challenge.id,
          passcode: code,
        },
      });
    }, 100); // try 100m
  }, [challenge, router, code]);

  const handleGoBack = async () => {
    await triggerHaptics();
    router.back();
  };

  return (
    <YStack paddingHorizontal='$5' flex={1}>
      {isPending ? (
        <YStack flex={1} justifyContent='center' alignItems='center'>
          <Spinner size='large' />
          <I18nText marginTop='$4' fontWeight='600'>
            Checking code...
          </I18nText>
        </YStack>
      ) : business || challenge ? (
        <YStack flex={1} gap='$4'>
          {business && <PasscodeBusiness business={business} passcode={code} />}
          {challenge && (
            <>
              <I18nText fontSize={20} fontWeight='600' textAlign='center'>
                The code belongs to a Challenge
              </I18nText>
              <ChallengeList
                challenge={challenge}
                onPress={handleOnPressChallenge}
              />
            </>
          )}
          <StyledButton
            marginTop='$4'
            onPress={handleGoBack}
            variant='secondary'>
            <I18nText color='$color'>Go Back</I18nText>
          </StyledButton>
        </YStack>
      ) : (
        <YStack>
          <YStack paddingLeft='$2' height='$2'>
            {isError && (
              <I18nText color='$primary' fontWeight='600'>
                The code is not valid
              </I18nText>
            )}
          </YStack>

          <StyledInput
            fontSize={18}
            fontWeight='600'
            letterSpacing={4}
            placeholder={t('Enter the code')}
            clearButtonMode='always'
            autoFocus
            onChangeText={setCode}
            autoCapitalize='none'
          />
          <StyledButton
            marginTop='$4'
            disabled={code.length < MIN_CODE_LENGTH || isPending}
            onPress={handleSubmit}
            iconAfter={isPending ? <Spinner /> : undefined}>
            <I18nText color='$white'>Submit code</I18nText>
          </StyledButton>
        </YStack>
      )}
    </YStack>
  );
};

export default PasscodeScreen;
