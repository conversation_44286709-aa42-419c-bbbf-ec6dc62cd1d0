import React from 'react';
import {Stack} from 'expo-router';
import BackButton from '@/src/components/BackButton';
import Ionicons from '@expo/vector-icons/Ionicons';
import {useTranslation} from 'react-i18next';
import {NativeStackNavigationOptions} from '@react-navigation/native-stack';

const defaultOptions: NativeStackNavigationOptions = {
  headerShown: true,
  headerBackground: () => null, // Ensures there's no background
  headerTitle: '',
  headerTintColor: '#FFFFFF',
  presentation: 'transparentModal',
  animation: 'fade',
  headerRight: () => <BackButton icon={<Ionicons name='close' size={24} />} />,
};

export default function ModalsStackLayout() {
  const {t} = useTranslation();
  return (
    <Stack screenOptions={{}}>
      <Stack.Screen
        name='house-of-wellbeing/[trackId]'
        options={{
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='gift-card'
        options={{
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='passcode'
        options={{
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='faq'
        options={{
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='privacy-policy'
        options={{
          ...defaultOptions,
          headerTitle: t('Privacy Policy'),
        }}
      />
      <Stack.Screen
        name='terms-of-use'
        options={{
          ...defaultOptions,
          headerTitle: t('Terms of Use'),
        }}
      />
      <Stack.Screen
        name='wearables-applehealth'
        options={{
          headerShown: true,
          headerBackground: () => null,
          headerTitle: t('Connect with Apple Health'),
          presentation: 'transparentModal',
          animation: 'fade',
          headerRight: () => (
            <BackButton icon={<Ionicons name='close' size={24} />} />
          ),
        }}
      />
      <Stack.Screen
        name='wearables-healthconnect'
        options={{
          headerShown: true,
          headerBackground: () => null,
          headerTitle: t('Connect with HealthConnect'),
          presentation: 'transparentModal',
          animation: 'fade',
          headerRight: () => (
            <BackButton icon={<Ionicons name='close' size={24} />} />
          ),
        }}
      />
      <Stack.Screen
        name='wearables-coros'
        options={{
          ...defaultOptions,
          headerTitle: t('Connect with Coros'),
          headerTintColor: '#000000',
        }}
      />
      <Stack.Screen
        name='wearables-fitbit'
        options={{
          ...defaultOptions,
          headerTitle: t('Connect with Fitbit'),
          headerTintColor: '#000000',
        }}
      />
      <Stack.Screen
        name='wearables-garmin'
        options={{
          ...defaultOptions,
          headerTitle: t('Connect with Garmin'),
          headerTintColor: '#000000',
        }}
      />
      <Stack.Screen
        name='wearables-oura'
        options={{
          ...defaultOptions,
          headerTitle: t('Connect with Oura'),
          headerTintColor: '#000000',
        }}
      />
      <Stack.Screen
        name='wearables-polar'
        options={{
          headerShown: true,
          headerBackground: () => null, // Ensures there's no background
          headerTitle: t('Connect with Polar'),
          presentation: 'transparentModal',
          animation: 'fade',
          headerRight: () => (
            <BackButton icon={<Ionicons name='close' size={24} />} />
          ),
        }}
      />
      <Stack.Screen
        name='wearables-suunto'
        options={{
          headerTitle: t('Connect with Suunto'),
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='wearables-whoop'
        options={{
          headerTitle: t('Connect with Whoop'),
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='wearables-wahoo'
        options={{
          headerTitle: t('Connect with Wahoo'),
          ...defaultOptions,
        }}
      />
      <Stack.Screen
        name='wearables-withings'
        options={{
          headerTitle: t('Connect with Withings'),
          ...defaultOptions,
        }}
      />
    </Stack>
  );
}
