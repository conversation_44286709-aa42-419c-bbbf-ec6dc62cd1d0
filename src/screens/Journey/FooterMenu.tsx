import React from 'react';
import {XStack} from 'tamagui';
import {JourneyDto} from '@gojoe/typescript-sdk';
import {SheetManager} from 'react-native-actions-sheet';
import {Ionicons} from '@expo/vector-icons';

import {triggerHaptics} from '@/src/utils/haptics';
import I18nText from '@/src/components/I18nText';

interface Props {
  journey: JourneyDto;
}

const FooterMenu: React.FC<Props> = ({journey}) => {
  const handlePressExitJourney = async () => {
    await triggerHaptics();
    await SheetManager.show('exitJourney', {
      payload: {journeyId: journey.id},
    });
  };

  return (
    <XStack
      justifyContent='space-between'
      marginHorizontal='$5'
      marginTop='$3.5'
      backgroundColor='$windowBackground'>
      <XStack
        height={48}
        width={48}
        borderWidth={1}
        borderColor='#888'
        borderRadius={8}
        justifyContent='center'
        alignItems='center'
        marginRight='$3.5'
        onPress={handlePressExitJourney}>
        <Ionicons name='ellipsis-horizontal' size={18} color='#888888' />
      </XStack>

      <XStack
        flex={1}
        height={48}
        justifyContent='center'
        alignItems='center'
        borderWidth={1}
        borderColor='#CA3D2A'
        paddingHorizontal='$3'
        borderRadius={8}
        position='relative'>
        <I18nText fontSize={14} fontWeight='$7' color='#CA3D2A'>
          Invite a Friend
        </I18nText>

        <XStack position='absolute' right={12}>
          <Ionicons name='person-add-outline' size={14} color='#CA3D2A' />
        </XStack>
      </XStack>
    </XStack>
  );
};

export default FooterMenu;
