import React from 'react';
import {ActivityIndicator, Pressable} from 'react-native';
import {YStack} from 'tamagui';

import {triggerHaptics} from '@/src/utils/haptics';
import useMutationJoinJourney from '@/src/hooks/api/useMutationJoinJourney';
import I18nText from '@/src/components/I18nText';

interface Props {
  id: string;
}

const JoinJourney: React.FC<Props> = ({id}) => {
  const {mutate, isPending} = useMutationJoinJourney(id);

  const handlePressJoin = async () => {
    await triggerHaptics();
    mutate();
  };

  return (
    <Pressable onPress={handlePressJoin} disabled={isPending}>
      <YStack
        height={48}
        alignItems='center'
        justifyContent='center'
        backgroundColor='#CA3D2A'
        borderRadius={8}
        marginHorizontal='$5'
        marginTop='$3.5'>
        {isPending ? (
          <ActivityIndicator color='#FFF' />
        ) : (
          <I18nText fontSize={14} fontWeight='$7' color='#FFF'>
            Start this Journey
          </I18nText>
        )}
      </YStack>
    </Pressable>
  );
};

export default JoinJourney;
