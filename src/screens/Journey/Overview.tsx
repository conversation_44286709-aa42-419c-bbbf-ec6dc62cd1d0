import React from 'react';
import {useWindowDimensions, XStack, YStack} from 'tamagui';
import {FontAwesome} from '@expo/vector-icons';
import RenderHTML from 'react-native-render-html';
import {CalendarDays, ChartNoAxesColumnIncreasing} from '@tamagui/lucide-icons';
import {JourneyDto, UserDto} from '@gojoe/typescript-sdk';
import {useRouter} from 'expo-router';

import {journeyLevel} from '@/src/utils/helper';
import StyledText from '@/src/components/UI/StyledText';
import I18nText from '@/src/components/I18nText';
import Avatar from '@/src/components/UI/Avatar/Avatar';
import {triggerHaptics} from '@/src/utils/haptics';
import useJourneyChatChannel from '@/src/hooks/api/useJourneyChatChannel';

interface Props {
  data: JourneyDto;
}

const Overview: React.FC<Props> = ({data}) => {
  const layout = useWindowDimensions();
  const router = useRouter();
  const {data: channelId} = useJourneyChatChannel(data.id);

  const handlePressChat = async () => {
    await triggerHaptics();

    if (channelId) {
      router.push({
        pathname: '/chat/[channelId]',
        params: {channelId},
      });
    }
  };
  return (
    <YStack flex={1} marginBottom='$5'>
      <StyledText fontSize={24} fontWeight='$7'>
        {data.name}
      </StyledText>

      <XStack alignItems='center' marginTop='$3.5'>
        <CalendarDays size={18} color='#888' />
        <I18nText
          fontSize={12}
          fontWeight='$6'
          color='#888'
          marginLeft='$2'
          i18nParams={{count: data.length}}>
          {data.length === 1 ? '{{count}} day' : '{{count}} days'}
        </I18nText>
      </XStack>

      <XStack alignItems='center' marginTop='$3.5'>
        <ChartNoAxesColumnIncreasing size={18} color='#888' />
        <I18nText fontSize={12} fontWeight='$6' color='#888' marginLeft='$2'>
          {journeyLevel(data.level)}
        </I18nText>
      </XStack>

      <XStack
        justifyContent='space-between'
        alignItems='center'
        marginTop='$3.5'>
        <XStack alignItems='center'>
          <FontAwesome name='check-square' size={18} color='#888' />
          <I18nText
            fontSize={12}
            fontWeight='$5'
            color='#888'
            marginLeft='$2'
            i18nParams={{count: data.totalUsers}}>
            {`{{count}} started`}
          </I18nText>
        </XStack>

        <XStack>
          {data.users.slice(0, 3).map((user: UserDto, index: number) => {
            const marginLeft = index === 0 ? 0 : -10;
            return (
              <XStack
                key={user.id}
                marginLeft={marginLeft}
                borderRadius={12}
                backgroundColor='$background'
                padding='$0.75'
                overflow='hidden'>
                <Avatar
                  id={user.id}
                  name={user.name}
                  avatar={user.avatar}
                  size={22}
                  circular
                />
              </XStack>
            );
          })}
        </XStack>
      </XStack>

      <XStack marginTop={28} marginBottom={16}>
        <XStack
          backgroundColor='#000'
          borderRadius='$11'
          paddingVertical='$2'
          paddingHorizontal='$3'>
          <I18nText fontSize={16} fontWeight='$7' color='#FFF'>
            About
          </I18nText>
        </XStack>
        <XStack
          borderRadius='$11'
          paddingVertical='$2'
          paddingHorizontal='$3'
          onPress={handlePressChat}>
          <I18nText fontSize={16} fontWeight='$7' color='#000'>
            Chat
          </I18nText>
        </XStack>
      </XStack>

      <RenderHTML
        contentWidth={layout.width - 6 * 4}
        source={{html: data.description ?? ''}}
        tagsStyles={{
          p: {
            fontFamily: 'Inter',
            display: 'flex',
            marginTop: 10,
            fontSize: 14,
            lineHeight: 20,
          },
        }}
      />
    </YStack>
  );
};

export default Overview;
