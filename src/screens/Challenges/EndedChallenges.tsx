import React, {useCallback} from 'react';
import {ListRenderItemInfo} from '@shopify/flash-list';
import useChallengesFinished from '@/src/hooks/api/useChallengesFinished';
import {Spinner, YStack} from 'tamagui';
import {SharedValue, useAnimatedScrollHandler} from 'react-native-reanimated';
import {EndedChallengeListDto, FeaturedChallengeListItemDto} from '@gojoe/typescript-sdk';
import ListItemSeparator from '@/src/components/ListItemSeparator';
import {createAnimatedFlashList} from '@/src/components/AnimatedFlashList';
import {useSafeListPadding} from '@/src/hooks/useSafeListPadding';
import ApiFetchError from '@/src/components/ApiFetchError';
import ChallengeEndedList from '@/src/components/ChallengeEndedList';

const AnimatedFlashList =
  createAnimatedFlashList<EndedChallengeListDto>();

type Props = {
  scrollY: SharedValue<number>;
};

const EndedChallenges = ({scrollY}: Props) => {
  const {
    isLoading,
    data,
    fetchNextPage,
    isFetchingNextPage,
    isRefetching,
    refetch,
    error,
  } = useChallengesFinished();
  const {paddingBottom} = useSafeListPadding();

  const onScroll = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  const listHeaderComponent = useCallback(() => {
    if (isLoading) {
      return <Spinner marginTop='$3' />;
    }
    return <YStack height={24} />;
  }, [isLoading]);

  const listFooterComponent = useCallback(() => {
    if (isFetchingNextPage) {
      return <Spinner marginVertical='$3' />;
    }
    return null;
  }, [isFetchingNextPage]);

  const keyExtractor = useCallback(
    (item: EndedChallengeListDto) => item.id,
    [],
  );

  const renderItem = useCallback(
    ({item}: ListRenderItemInfo<EndedChallengeListDto>) => (
      <ChallengeEndedList challenge={item} />
    ),
    [],
  );

  const onEndReached = () => fetchNextPage();

  if (error) {
    return <ApiFetchError error={error} />;
  }

  return (
    <YStack flex={1} paddingHorizontal='$5'>
      <AnimatedFlashList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        onRefresh={refetch}
        refreshing={isRefetching}
        onEndReached={onEndReached}
        ListHeaderComponent={listHeaderComponent}
        ListFooterComponent={listFooterComponent}
        estimatedItemSize={414}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={ListItemSeparator}
        onScroll={onScroll}
        scrollEventThrottle={16}
        contentContainerStyle={{
          paddingBottom: paddingBottom,
        }}
      />
    </YStack>
  );
};

export default EndedChallenges;
