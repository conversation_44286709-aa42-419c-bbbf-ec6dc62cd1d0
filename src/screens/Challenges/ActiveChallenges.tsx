import React, {useCallback} from 'react';
import {ListRenderItemInfo} from '@shopify/flash-list';
import useChallengesFeatured from '@/src/hooks/api/useChallengesFeatured';
import {Spinner, YStack} from 'tamagui';
import {SharedValue, useAnimatedScrollHandler} from 'react-native-reanimated';
import FeaturedChallengeList from '@/src/components/ChallengeFeaturedList';
import {FeaturedChallengeListItemDto} from '@gojoe/typescript-sdk';
import ListItemSeparator from '@/src/components/ListItemSeparator';
import {createAnimatedFlashList} from '@/src/components/AnimatedFlashList';
import {useSafeListPadding} from '@/src/hooks/useSafeListPadding';
const AnimatedFlashList =
  createAnimatedFlashList<FeaturedChallengeListItemDto>();

type Props = {
  scrollY: SharedValue<number>;
};
const ActiveChallenges = ({scrollY}: Props) => {
  const {
    isLoading,
    data,
    fetchNextPage,
    isFetchingNextPage,
    isRefetching,
    refetch,
  } = useChallengesFeatured();
  const {paddingBottom} = useSafeListPadding();

  const onScroll = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  const listHeaderComponent = useCallback(() => {
    if (isLoading) {
      return <Spinner marginTop='$3' />;
    }
    return <YStack height={24} />;
  }, [isLoading]);

  const listFooterComponent = useCallback(() => {
    if (isFetchingNextPage) {
      return <Spinner marginVertical='$3' />;
    }
    return <YStack height={96} />;
  }, [isFetchingNextPage]);

  const keyExtractor = useCallback(
    (item: FeaturedChallengeListItemDto) => item.id,
    [],
  );

  const renderItem = useCallback(
    ({item}: ListRenderItemInfo<FeaturedChallengeListItemDto>) => (
      <FeaturedChallengeList challenge={item} />
    ),
    [],
  );

  const onEndReached = () => fetchNextPage();
  return (
    <YStack flex={1} paddingHorizontal='$5'>
      <AnimatedFlashList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        onRefresh={refetch}
        refreshing={isRefetching}
        onEndReached={onEndReached}
        ListHeaderComponent={listHeaderComponent}
        ListFooterComponent={listFooterComponent}
        estimatedItemSize={414}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={ListItemSeparator}
        onScroll={onScroll}
        scrollEventThrottle={16}
        contentContainerStyle={{
          paddingBottom: paddingBottom,
        }}
      />
    </YStack>
  );
};

export default ActiveChallenges;
