import React, {useEffect} from 'react';
import {StyleSheet} from 'react-native';
import {ImageBackground} from 'expo-image';
import {SheetManager} from 'react-native-actions-sheet';
import {CountryDto} from '@gojoe/typescript-sdk';
import I18nText from '@/src/components/I18nText';
import {XStack, YStack} from 'tamagui';
import {ChevronDown} from '@tamagui/lucide-icons';
import useCountries from '@/src/hooks/api/useCountries';
import {useSession} from '@/src/contexts/SessionContext';
import useMutationUserProfile from '@/src/hooks/api/useMutationUserProfile';
import logger from '@/src/utils/logger';

const SelectCountry: React.FC = () => {
  const {user, setUser} = useSession();
  const [country, setCountry] = React.useState<CountryDto | undefined>();
  const {data} = useCountries();
  const {mutateAsync} = useMutationUserProfile();

  useEffect(() => {
    if (data && user) {
      const selectedCountry = data.find((c) => c.id === user.countryId);
      if (selectedCountry) {
        setCountry(selectedCountry);
      }
    }
  }, [data, user]);

  const handlePressInput = async () => {
    if (!user) {
      return;
    }
    const handleSelectCountry = async (selectedCountry: CountryDto) => {
      try {
        await mutateAsync({countryId: selectedCountry.id});
        setCountry(selectedCountry);
        setUser({
          ...user,
          countryId: selectedCountry.id,
        });
      } catch (e) {
        await logger.error(e);
      }
    };
    await SheetManager.show('country_list', {
      payload: {countryId: country?.id, onSelect: handleSelectCountry},
    });
  };

  return (
    <ImageBackground
      source={require('@/assets/images/dots_map.png')}
      style={styles.bg}>
      <I18nText fontWeight='700' fontSize={24}>
        Choose your country
      </I18nText>
      <I18nText fontWeight='500' fontSize={12} marginTop='$2'>
        In order to show you the rewards that you can redeem, please choose your
        country.
      </I18nText>
      <XStack
        onPress={handlePressInput}
        backgroundColor='$background'
        height={56}
        width='100%'
        borderColor='$accentGrey'
        borderRadius='$5'
        marginTop={24}
        alignItems='center'
        justifyContent='space-between'
        paddingHorizontal='$5'>
        <YStack gap='$1'>
          <I18nText
            textTransform='uppercase'
            fontSize={10}
            fontWeight='500'
            color='$accentGrey'>
            Choose country
          </I18nText>
          <I18nText fontWeight='500'>{country?.name}</I18nText>
        </YStack>
        <ChevronDown color='$color' />
      </XStack>
    </ImageBackground>
  );
};
const styles = StyleSheet.create({
  bg: {
    height: 406,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
});

export default SelectCountry;
