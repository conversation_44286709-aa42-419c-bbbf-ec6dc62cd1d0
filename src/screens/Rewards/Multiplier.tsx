import React from 'react';
import {XStack} from 'tamagui';
import {UserRewardBalanceDto} from '@gojoe/typescript-sdk';
import {useTranslation} from 'react-i18next';
import {SheetManager} from 'react-native-actions-sheet';
import I18nText from '@/src/components/I18nText';
import StyledText from '@/src/components/UI/StyledText';
import {Info, Rocket} from '@tamagui/lucide-icons';

interface Props {
  rewards: UserRewardBalanceDto;
}

const Multiplier: React.FC<Props> = ({rewards}) => {
  const {t} = useTranslation();
  const multiplierValue = rewards.multiplier / 100;
  const isInteger = Number.isInteger(multiplierValue);
  const multiplier = Number(multiplierValue.toFixed(isInteger ? 0 : 2));

  const handlePressInfo = async () => {
    if (!rewards.business) {
      return SheetManager.show('multiplier_off', {});
    }
    if (multiplier === 1) {
      return SheetManager.show('multiplier_no_bust', {
        payload: {
          organisation: rewards.business?.name,
        },
      });
    }
    return SheetManager.show('multiplier', {
      payload: {
        multiplier: multiplier.toString(),
        capping: rewards.cap.toString(),
        organisation: rewards.business?.name,
      },
    });
  };

  return (
    <XStack
      height={48}
      backgroundColor='#FFF5D2'
      alignItems='center'
      paddingHorizontal={24}
      gap='$2'>
      <XStack
        flexDirection='row'
        alignItems='center'
        backgroundColor='#FFCE1F'
        paddingHorizontal={8}
        paddingVertical={4}
        borderRadius={10}
        gap='$1'>
        <Rocket color={multiplier === 1 ? '#00000060' : '#000000'} />
        {multiplier === 1 ? (
          <I18nText fontWeight='600' fontSize={12} color='#00000060'>
            n/a
          </I18nText>
        ) : (
          <I18nText fontWeight='600' fontSize={11} color='#000000'>
            x{multiplier}
          </I18nText>
        )}
      </XStack>

      {multiplier === 1 ? (
        <StyledText fontWeight='600' fontSize={12} flex={1}>
          {rewards.business
            ? t('No earning boost provided by {{name}}.', {
                name: rewards.business.name,
              })
            : t('Unlock Rewards with Premium')}
        </StyledText>
      ) : (
        <I18nText fontWeight='600' fontSize={12} flex={1} color='#000000'>
          Your Reward Boost is Active!
        </I18nText>
      )}

      <Info color='#000000' size={16} onPress={handlePressInfo} />
    </XStack>
  );
};

export default Multiplier;
