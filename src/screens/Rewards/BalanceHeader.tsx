import React from 'react';
import {Image, Dimensions} from 'react-native';
import {YStack, XStack} from 'tamagui';
import {UserRewardBalanceDto} from '@gojoe/typescript-sdk';
import StyledText from '@/src/components/UI/StyledText';
import I18nText from '@/src/components/I18nText';

const windowsWidth = Dimensions.get('window').width;

interface BalanceHeaderProps {
  rewards: UserRewardBalanceDto;
}

const BalanceHeader: React.FC<BalanceHeaderProps> = ({rewards}) => {
  const progressWidth =
    (rewards.currentMonth / rewards.cap) * (windowsWidth - 48);

  return (
    <YStack
      height={68}
      backgroundColor='#FFCE1F'
      justifyContent='center'
      paddingHorizontal={24}>
      <XStack gap='$2'>
        <XStack flex={1} flexDirection='row' gap='$2'>
          <StyledText fontWeight='700'>{rewards.balance}</StyledText>
          <Image
            source={require('@/assets/images/icons/icon-circles-three.png')}
            style={{width: 18, height: 18}}
            tintColor='#FFFFFF'
          />
        </XStack>

        <XStack flexDirection='row' gap='$2'>
          <StyledText fontWeight='500' fontSize={12} color='#5C4A0B'>
            {rewards.currentMonth} / {rewards.cap}
          </StyledText>
          <I18nText fontWeight='500' fontSize={12} color='#5C4A0B'>
            added this month
          </I18nText>
        </XStack>
      </XStack>

      <YStack
        width={windowsWidth - 48}
        height={8}
        borderRadius={4}
        backgroundColor='#00000010'
        marginTop={9}>
        <YStack
          height={8}
          borderRadius={4}
          backgroundColor='#FFFFFF'
          width={progressWidth}
        />
      </YStack>
    </YStack>
  );
};

export default BalanceHeader;
