import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Alert, StyleSheet, Pressable} from 'react-native';
import {Spacer, XStack, YStack} from 'tamagui';
import {ChevronDown, Plus, Minus, Map} from '@tamagui/lucide-icons';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import BackgroundGeolocation, {
  Subscription,
} from 'react-native-background-geolocation';
import {ActivityTypeDto} from '@gojoe/typescript-sdk';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {LineLayer, ShapeSource, UserLocation} from '@rnmapbox/maps';
import {SheetManager} from 'react-native-actions-sheet';
import {useRouter} from 'expo-router';
import {storage} from '@/src/utils/localStorage';
import {HISTORY_KEY} from '@/src/hooks/api/useActivityTypes';

import {
  BackgroundLocation,
  GPSSignal,
  TrackerState,
} from '@/src/screens/Tracker/types';
import {
  calculateCalories,
  formatDuration,
  formatPace,
  formattedDistance,
  formattedTime,
  getDistance,
  gpsSignal,
  TrackerRunningStatus,
} from '@/src/utils/tracker';
import BackButton from '@/src/components/BackButton';
import {triggerHaptics} from '@/src/utils/haptics';
import I18nText from '@/src/components/I18nText';
import Mapbox from '@/src/services/mapbox';
import StyledText from '@/src/components/UI/StyledText';
import TrackerActions from '@/src/screens/Tracker/Actions';
import {LocalesProps} from '@/src/contexts/LocaleContext';
import ActivityIcon from '@/src/components/ActivityIcon';
import MediaPicker from '@/src/components/MediaPicker';
import SaveButton from './SaveButton';
import logger from '@/src/utils/logger';
import {AvgPaceIcon} from '@/src/components/GoJoeIcon/icons/avg-pace';
import {ClockIcon} from '@/src/components/GoJoeIcon/icons/clock';
import {CalendarIcon} from '@/src/components/GoJoeIcon/icons/calendar';

// Storage key for persisting activity type during tracking
const TRACKER_ACTIVITY_TYPE_KEY = 'tracker_activity_type';

interface Props {
  activityType: ActivityTypeDto;
  locales: LocalesProps;
}

const MAP_STYLES = {
  outdoors: 'mapbox://styles/mapbox/outdoors-v12',
  streets: 'mapbox://styles/mapbox/streets-v12',
  satellite: 'mapbox://styles/mapbox/satellite-v9',
  hybrid: 'mapbox://styles/mapbox/satellite-streets-v12',
};

const TrackerScreen: React.FC<Props> = ({
  activityType,
  locales: {distanceUnit: unit, timezone},
}) => {
  const {top, bottom} = useSafeAreaInsets();
  const [status, setStatus] = useState(TrackerRunningStatus.Idle);
  const [uiVisible, setUiVisible] = useState(true);
  const hideTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const opacity = useSharedValue(1);
  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));
  const router = useRouter();
  const [state, setState] = useState<TrackerState>({
    signalAcquired: false,
    signal: GPSSignal.None,
    activityType: activityType,
    startTime: null,
    signalQuality: null,
    coordinate: null,
    routeCoords: [],
    totalDistance: 0,
    avgPace: '00:00',
    elapsedTime: 0,
    pausedDuration: 0,
    pauseStart: null,
    calories: 0,
  });
  const [media, setMedia] = useState<{id?: string; url: string}[]>([]);
  const cameraRef = useRef<Mapbox.Camera>(null);
  const [zoomLevel, setZoomLevel] = useState(13);
  const [mapStyle, setMapStyle] = useState('outdoors');

  const scheduleUiHide = useCallback(() => {
    if (status !== TrackerRunningStatus.Running) return;

    hideTimer.current && clearTimeout(hideTimer.current);
    hideTimer.current = setTimeout(() => {
      opacity.value = withTiming(0, {duration: 500}, () =>
        runOnJS(setUiVisible)(false),
      );
    }, 10000);
  }, [status, opacity, setUiVisible]);

  useEffect(() => {
    if (status === TrackerRunningStatus.Running && uiVisible) {
      scheduleUiHide();
    }
  }, [scheduleUiHide, status, uiVisible]);

  useEffect(() => {
    BackgroundGeolocation.getState().then((bgState) => {
      if (bgState.enabled) {
        Alert.alert(
          'Resume Activity?',
          'You were tracking before the app was closed. Would you like to resume?',
          [
            {
              text: 'No',
              style: 'destructive',
              onPress: async () => {
                try {
                  await BackgroundGeolocation.stop(); // Stop tracking
                  await BackgroundGeolocation.destroyLocations(); // Clear stored locations
                  await BackgroundGeolocation.setOdometer(0); // Reset odometer
                  storage.remove(TRACKER_ACTIVITY_TYPE_KEY); // Clear stored activity type
                  setStatus(TrackerRunningStatus.Idle); // Optional: reset your UI state
                } catch (error) {
                  logger.error('Cleanup error:', error);
                }
              },
            },
            {
              text: 'Resume',
              onPress: async () => {
                const locations =
                  (await BackgroundGeolocation.getLocations()) as BackgroundLocation[];

                if (locations.length > 1) {
                  const coords: [number, number][] = locations.map((l) => [
                    l.coords.longitude,
                    l.coords.latitude,
                  ]);

                  // Get elapsed time (based on first and last timestamp)
                  const startTimestamp = new Date(
                    locations[0].timestamp,
                  ).getTime();
                  const endTimestamp = new Date(
                    locations[locations.length - 1].timestamp,
                  ).getTime();
                  const elapsedTime = Math.floor(
                    (endTimestamp - startTimestamp) / 1000,
                  );

                  // Calculate total distance
                  let totalDistance = 0;
                  for (let i = 1; i < coords.length; i++) {
                    totalDistance += getDistance(
                      coords[i - 1][1],
                      coords[i - 1][0],
                      coords[i][1],
                      coords[i][0],
                    );
                  }

                  // Calculate pace
                  const avgPace = formatPace(elapsedTime, totalDistance, unit);

                  // Restore the saved activity type if available
                  const savedActivityType = storage.getObject<ActivityTypeDto>(
                    TRACKER_ACTIVITY_TYPE_KEY,
                  );

                  setState((prev) => ({
                    ...prev,
                    routeCoords: coords,
                    coordinate: coords[coords.length - 1],
                    startTime: startTimestamp,
                    elapsedTime,
                    totalDistance,
                    avgPace,
                    activityType: savedActivityType || prev.activityType, // Use saved activity type or fallback to current
                    status: TrackerRunningStatus.Running,
                  }));

                  setStatus(TrackerRunningStatus.Running);
                }
              },
            },
          ],
        );
      }
    });
  }, [unit]);

  useEffect(() => {
    /// 1.  Subscribe to events.
    const onLocation: Subscription = BackgroundGeolocation.onLocation(
      (location) => {
        if (!location || !location.coords) {
          return;
        }
        const signalQuality = location.coords.accuracy;
        const signal = gpsSignal(signalQuality);
        const [lon, lat] = [
          location.coords.longitude,
          location.coords.latitude,
        ];
        const newCoordinates: [number, number] = [lon, lat];

        if (status === TrackerRunningStatus.Running) {
          setState((prevState) => {
            const [prevLon, prevLat] = prevState.routeCoords[
              prevState.routeCoords.length - 1
            ] || [lon, lat];
            const segmentDistance = getDistance(prevLat, prevLon, lat, lon);
            const totalDistance = prevState.totalDistance + segmentDistance;
            const elapsedTime = Math.floor(
              (Date.now() - (prevState.startTime ? prevState.startTime : 0)) /
                1000,
            );
            const avgPace = formatPace(elapsedTime, totalDistance, unit);

            return {
              ...prevState,
              signalQuality,
              signal,
              coordinate: newCoordinates,
              routeCoords: [...prevState.routeCoords, newCoordinates],
              totalDistance,
              avgPace,
              elapsedTime,
            };
          });
        } else {
          setState((prevState) => ({
            ...prevState,
            coordinate: newCoordinates,
            signalQuality,
            signal,
            signalAcquired: !!prevState.coordinate,
          }));
        }
      },
    );

    /// 2. ready the plugin.
    BackgroundGeolocation.ready({
      activityType: 1,
      desiredAccuracy: BackgroundGeolocation.DESIRED_ACCURACY_HIGH,
      distanceFilter: 5,
      stopOnTerminate: false,
      startOnBoot: true,
      autoSync: true,
      debug: false,
      enableHeadless: true, // for Android resume
    }).then(async (state) => {
      if (!state.enabled) {
        await BackgroundGeolocation.start();
        await BackgroundGeolocation.configure({activityType: 1});
      }
      await BackgroundGeolocation.getCurrentPosition({timeout: 30000}).then(
        (location) => {
          if (location && location.coords) {
            setState((prevState) => ({
              ...prevState,
              coordinate: [location.coords.longitude, location.coords.latitude],
            }));
          }
        },
      );
    });

    return () => {
      // Remove BackgroundGeolocation event-subscribers when the View is removed or refreshed
      // during development live-reload.  Without this, event-listeners will accumulate with
      // each refresh during live-reload.
      onLocation.remove();
    };
  }, [status, unit]);

  useEffect(() => {
    if (status !== TrackerRunningStatus.Running || !state.startTime) return;

    const interval = setInterval(() => {
      setState((prevState) => {
        const now = Date.now();
        const isPaused = prevState.pauseStart !== null;
        const pausedDuration = isPaused
          ? prevState.pausedDuration + (now - (prevState.pauseStart ?? 0))
          : prevState.pausedDuration;

        const elapsedTime = Math.floor(
          (now - (prevState.startTime ?? now) - pausedDuration) / 1000,
        );

        const avgPace = formatPace(elapsedTime, prevState.totalDistance, unit);
        const calories = calculateCalories(elapsedTime);

        return {
          ...prevState,
          elapsedTime,
          avgPace,
          calories,
        };
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [status, state.startTime, unit]);

  const showUI = () => {
    hideTimer.current && clearTimeout(hideTimer.current);

    if (!uiVisible) setUiVisible(true); // 👈 Mount if not already visible

    opacity.value = withTiming(1, {duration: 300});

    if (status === TrackerRunningStatus.Running) {
      scheduleUiHide();
    }
  };

  const handlePressActivityType = async () => {
    if (status !== TrackerRunningStatus.Idle) {
      return;
    }
    await triggerHaptics();
    const history = storage.getObject<string[]>(HISTORY_KEY) ?? [];
    await SheetManager.show('activity_types', {
      payload: {
        activityTypeId: state.activityType.id,
        showHistory: history.length > 0,
        onSelect: (activity: ActivityTypeDto) => {
          setState((prevState) => ({
            ...prevState,
            activityType: activity,
          }));
        },
      },
    });
  };

  const handleOnStart = useCallback(async () => {
    await triggerHaptics();
    setStatus(TrackerRunningStatus.Running);
    setState((prevState) => {
      // Save the current activity type to storage when starting
      storage.setObject(TRACKER_ACTIVITY_TYPE_KEY, prevState.activityType);
      return {
        ...prevState,
        startTime: Date.now(),
      };
    });
  }, []);

  const handleOnPause = useCallback(async () => {
    await triggerHaptics();
    setState((prevState) => ({
      ...prevState,
      pauseStart: Date.now(),
    }));
    setStatus(TrackerRunningStatus.Paused);
  }, []);

  const handleOnResume = useCallback(async () => {
    await triggerHaptics();
    setState((prevState) => ({
      ...prevState,
      pausedDuration:
        prevState.pausedDuration +
        (Date.now() - (prevState.pauseStart ? prevState.pauseStart : 0)),
      pauseStart: null,
    }));
    setStatus(TrackerRunningStatus.Running);
  }, []);

  const handleOnFinish = useCallback(async () => {
    await triggerHaptics();
    setState((prevState) => ({
      ...prevState,
      pauseStart: Date.now(),
    }));
    setStatus(TrackerRunningStatus.Finished);
  }, []);

  const handleDiscard = useCallback(async () => {
    await triggerHaptics();
    BackgroundGeolocation.stop(); // stops tracking
    BackgroundGeolocation.destroyLocations(); // wipes saved locations
    BackgroundGeolocation.setOdometer(0); // resets odometer
    storage.remove(TRACKER_ACTIVITY_TYPE_KEY); // Clear stored activity type
    router.back();
  }, [router]);

  const handleZoomIn = () => {
    const newZoom = zoomLevel + 1;
    setZoomLevel(newZoom);
    cameraRef.current?.setCamera({
      zoomLevel: newZoom,
      animationDuration: 200,
    });
  };

  const handleZoomOut = () => {
    const newZoom = zoomLevel - 1;
    setZoomLevel(newZoom);
    cameraRef.current?.setCamera({
      zoomLevel: newZoom,
      animationDuration: 200,
    });
  };

  const cycleMapStyle = () => {
    const styles = Object.keys(MAP_STYLES);
    const currentIndex = styles.indexOf(mapStyle);
    const nextIndex = (currentIndex + 1) % styles.length;
    setMapStyle(styles[nextIndex]);
  };

  if (status === TrackerRunningStatus.Finished) {
    return (
      <YStack flex={1} paddingTop={top} paddingHorizontal='$5'>
        <XStack>
          <I18nText
            flex={1}
            color='$primary'
            onPress={handleOnResume}
            fontWeight='500'>
            Resume
          </I18nText>
          <I18nText flex={1} textAlign='center' fontWeight='700'>
            Save Activity
          </I18nText>
          <Spacer flex={1} />
        </XStack>
        <YStack marginTop={60} gap={16}>
          <XStack alignItems='center' gap={8}>
            <ActivityIcon
              name={state.activityType.name}
              color='$color'
              size={16}
            />
            <I18nText fontWeight='500'>{state.activityType.title}</I18nText>
          </XStack>

          <XStack alignItems='center' gap={8}>
            <CalendarIcon color='$color' />
            <StyledText fontWeight='500'>
              {formattedTime(state.startTime)}
            </StyledText>
          </XStack>

          <XStack alignItems='center'>
            <XStack alignItems='center' gap={8}>
              <ClockIcon color='$color' />
              <StyledText fontWeight='500'>
                {formatDuration(state.elapsedTime)}
              </StyledText>
            </XStack>

            <Spacer width={102} />

            {state.activityType.hasDistance && (
              <XStack alignItems='center' gap={8}>
                <AvgPaceIcon color='$color' />
                <I18nText
                  fontWeight='500'
                  i18nParams={{
                    avgPace: state.avgPace,
                    unit,
                  }}>{`{{avgPace}} min/{{unit}}`}</I18nText>
              </XStack>
            )}
          </XStack>
        </YStack>

        {state.activityType.hasDistance && (
          <YStack
            marginTop='$5'
            height={216}
            borderRadius={16}
            overflow='hidden'>
            <Mapbox.MapView
              style={StyleSheet.absoluteFill}
              styleURL={MAP_STYLES[mapStyle as keyof typeof MAP_STYLES]}
              attributionEnabled={false}
              logoEnabled={false}
              zoomEnabled={false}
              pitchEnabled={false}
              scrollEnabled={true}>
              <Mapbox.Camera
                ref={cameraRef}
                bounds={{
                  ne: [
                    Math.max(...state.routeCoords.map((coord) => coord[0])),
                    Math.max(...state.routeCoords.map((coord) => coord[1])),
                  ],
                  sw: [
                    Math.min(...state.routeCoords.map((coord) => coord[0])),
                    Math.min(...state.routeCoords.map((coord) => coord[1])),
                  ],
                }}
                padding={{
                  paddingTop: 30,
                  paddingBottom: 30,
                  paddingLeft: 30,
                  paddingRight: 30,
                }}
                animationDuration={0}
              />
              <ShapeSource
                id='finishedRouteSource'
                shape={{
                  type: 'Feature',
                  geometry: {
                    type: 'LineString',
                    coordinates: state.routeCoords,
                  },
                  properties: {},
                }}>
                <LineLayer
                  id='finishedRouteLine'
                  style={{
                    lineColor: '#3b82f6',
                    lineWidth: 4,
                    lineCap: 'round',
                    lineJoin: 'round',
                  }}
                />
              </ShapeSource>
            </Mapbox.MapView>
            <XStack
              position='absolute'
              right={10}
              top={10}
              gap={8}
              backgroundColor='$background'
              padding='$2'
              borderRadius='$4'
              borderWidth={1}
              borderColor='$grey3'>
              <Pressable onPress={handleZoomIn}>
                <Plus color='$color' size={18} />
              </Pressable>
              <Pressable onPress={handleZoomOut}>
                <Minus color='$color' size={18} />
              </Pressable>
            </XStack>
            <XStack
              position='absolute'
              right={10}
              bottom={10}
              gap={8}
              alignItems='center'
              paddingHorizontal={12}
              paddingVertical={6}
              borderRadius={40}
              borderWidth={1}
              borderColor='$grey1'
              backgroundColor='rgba(255, 255, 255, 0.9)'
              onPress={cycleMapStyle}>
              <Map color='$grey1' size={14} />
              <I18nText fontSize={11} fontWeight='600' color='$grey1'>
                Map Type
              </I18nText>
            </XStack>
          </YStack>
        )}

        <YStack marginTop='$5'>
          <I18nText fontSize={16} fontWeight='700'>
            Activity Photos
          </I18nText>
          <YStack marginTop='$3'>
            <MediaPicker
              media={media}
              onMediasChange={setMedia}
              label='Add photos'
              maxImages={5}
            />
          </YStack>
        </YStack>

        <YStack flex={1} justifyContent='flex-end' paddingBottom={bottom}>
          <SaveButton
            activityType={state.activityType}
            state={state}
            timezone={timezone}
            media={media}
          />
          <YStack
            onPress={handleDiscard}
            height={48}
            alignItems='center'
            justifyContent='center'
            marginTop={8}>
            <I18nText
              color='$primary'
              textDecorationLine='underline'
              fontWeight='600'>
              Discard
            </I18nText>
          </YStack>
        </YStack>
      </YStack>
    );
  }
  return (
    <YStack flex={1} onPress={showUI}>
      <XStack
        paddingTop={top}
        zIndex={1}
        paddingHorizontal='$5'
        alignItems='center'>
        {status === TrackerRunningStatus.Idle && <BackButton rounded />}
      </XStack>
      <Mapbox.MapView style={StyleSheet.absoluteFill}>
        {state.coordinate && (
          <Mapbox.Camera
            centerCoordinate={state.coordinate}
            zoomLevel={15}
            animationMode='none'
            animationDuration={1000}
          />
        )}
        <UserLocation visible animated />
        <ShapeSource
          id='routeSource'
          shape={{
            type: 'Feature',
            geometry: {
              type: 'LineString',
              coordinates: state.routeCoords,
            },
            properties: {},
          }}>
          <LineLayer
            id='routeLine'
            style={{
              lineColor: '#3b82f6',
              lineWidth: 4,
              lineCap: 'round',
              lineJoin: 'round',
            }}
          />
        </ShapeSource>
      </Mapbox.MapView>
      <YStack
        paddingBottom={bottom}
        backgroundColor='$windowBackground'
        bottom={0}
        zIndex={100}
        width='100%'
        paddingHorizontal='$5'
        position='absolute'>
        <YStack alignItems='center' marginTop='$3.5'>
          {status === TrackerRunningStatus.Idle ? (
            <XStack
              onPress={handlePressActivityType}
              backgroundColor='$background'
              height={40}
              paddingHorizontal={16}
              borderWidth={1}
              borderColor='$grey3'
              borderRadius={20}
              gap={8}
              alignItems='center'>
              <I18nText fontWeight='700' fontSize={16}>
                {state.activityType.title}
              </I18nText>
              <ChevronDown color='$color' />
            </XStack>
          ) : (
            <XStack
              backgroundColor='$background'
              height={40}
              paddingHorizontal={16}
              borderWidth={1}
              borderColor='$grey3'
              borderRadius={20}
              gap={8}
              alignItems='center'>
              <I18nText fontWeight='700' fontSize={16}>
                {state.activityType.title}
              </I18nText>
            </XStack>
          )}
        </YStack>

        <YStack alignItems='center' marginTop='$3'>
          <StyledText fontSize={76} fontWeight='700' letterSpacing={-3}>
            {formatDuration(state.elapsedTime)}
          </StyledText>
        </YStack>
        {uiVisible && (
          <Animated.View style={[animatedStyle]}>
            <XStack marginTop='$4'>
              {state.activityType.hasDistance && (
                <YStack alignItems='center' flex={1} gap={4}>
                  <I18nText fontSize={10} fontWeight='600' color='$grey2'>
                    DISTANCE ({unit})
                  </I18nText>
                  <StyledText fontSize={28} fontWeight='700'>
                    {formattedDistance(state.totalDistance, unit)}
                  </StyledText>
                </YStack>
              )}
              <YStack alignItems='center' flex={1} gap={4}>
                <I18nText fontSize={10} fontWeight='600' color='$grey2'>
                  CALORIES (kcal)
                </I18nText>
                <StyledText fontSize={28} fontWeight='700'>
                  {state.calories}
                </StyledText>
              </YStack>
              {state.activityType.hasDistance && (
                <YStack alignItems='center' flex={1} gap={4}>
                  <I18nText fontSize='$1' color='$gray10' i18nParams={{unit}}>
                    {`AVG. PACE (min/{{unit}})`}
                  </I18nText>
                  <StyledText fontSize={28} fontWeight='700'>
                    {state.avgPace}{' '}
                    <I18nText
                      fontSize={12}
                      color='$grey2'>{`min/${unit}`}</I18nText>
                  </StyledText>
                </YStack>
              )}
            </XStack>
            <TrackerActions
              status={status}
              signal={state.signal}
              signalAcquired={state.signalAcquired}
              onStart={handleOnStart}
              onPause={handleOnPause}
              onResume={handleOnResume}
              onFinish={handleOnFinish}
            />
          </Animated.View>
        )}
      </YStack>
    </YStack>
  );
};

export default TrackerScreen;
