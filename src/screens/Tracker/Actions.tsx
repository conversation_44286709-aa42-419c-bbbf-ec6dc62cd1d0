import React, {memo} from 'react';
import {XStack, YStack} from 'tamagui';
import SwipeButton from 'rn-swipe-button';
import {useTranslation} from 'react-i18next';
import {TrackerRunningStatus} from '@/src/utils/tracker';
import I18nText from '@/src/components/I18nText';
import SwipeLeftButton from './SwipeLeft';
import SwipeRightButton from './SwipeRight';
import {GPSSignal} from '@/src/screens/Tracker/types';
import GpsSignal from '@/src/components/GpsSignal';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';

interface Props {
  status: TrackerRunningStatus;
  signal: GPSSignal;
  signalAcquired: boolean;
  onStart: () => void;
  onPause: () => void;
  onResume: () => void;
  onFinish: () => void;
}
const TrackerActions: React.FC<Props> = ({
  status,
  signal,
  signalAcquired,
  onStart,
  onPause,
  onResume,
  onFinish,
}) => {
  const {t} = useTranslation();

  if (status === TrackerRunningStatus.Running) {
    return (
      <YStack marginTop='$5'>
        <SwipeButton
          enableReverseSwipe
          containerStyles={{borderRadius: 16}}
          thumbIconStyles={{width: 48, height: 48, borderRadius: 16}}
          railStyles={{borderRadius: 16}}
          title={t('Swipe to Stop')}
          titleStyles={{
            fontFamily: 'Inter',
            fontWeight: 'bold',
            fontSize: 14,
          }}
          railBorderColor='#ACAFBA'
          railBackgroundColor='#C43F2D24'
          thumbIconBorderColor='#FFF'
          thumbIconBackgroundColor='#C43F2D'
          railFillBorderColor='#FFF'
          railFillBackgroundColor='#FFF'
          thumbIconComponent={SwipeLeftButton}
          titleColor='#000'
          swipeSuccessThreshold={70}
          onSwipeSuccess={onPause}
        />
      </YStack>
    );
  }
  if (status === TrackerRunningStatus.Paused) {
    return (
      <XStack
        justifyContent='space-around'
        paddingHorizontal='$4'
        gap='$4'
        py='$3'>
        <YStack
          flex={1}
          ml='$2'
          borderWidth={1}
          borderColor='$color'
          backgroundColor='$background'
          borderRadius={10}>
          <I18nText
            fontWeight='500'
            color='$color'
            py='$3'
            px='$5'
            textAlign='center'
            onPress={onResume}>
            Resume
          </I18nText>
        </YStack>
        <YStack
          flex={1}
          ml='$2'
          backgroundColor='$primary'
          borderRadius={10}
          mr='$2'>
          <I18nText
            color='$white1'
            py='$3'
            px='$5'
            fontWeight='500'
            textAlign='center'
            borderRadius={10}
            onPress={onFinish}>
            Finish
          </I18nText>
        </YStack>
      </XStack>
    );
  }

  return (
    <YStack marginTop='$5'>
      {signalAcquired ? (
        <>
          <XStack
            marginTop='$5'
            h='$2.5'
            marginBottom={8}
            borderRadius={16}
            paddingHorizontal={16}
            backgroundColor='$green'
            alignItems='center'
            justifyContent='space-between'>
            <I18nText fontWeight='700' fontSize={12} color='$white1'>
              GPS Signal Acquired
            </I18nText>
            <GpsSignal quality={signal} />
          </XStack>
          <SwipeButton
            containerStyles={{borderRadius: 16}}
            thumbIconStyles={{width: 48, height: 48, borderRadius: 16}}
            railStyles={{borderRadius: 16}}
            title={t('Swipe to Start')}
            titleStyles={{
              fontFamily: 'Inter',
              fontWeight: 'bold',
              fontSize: 14,
            }}
            railBorderColor='#ACAFBA'
            railBackgroundColor='#FFF'
            thumbIconBorderColor='#FFF'
            thumbIconBackgroundColor='#C43F2D'
            railFillBorderColor='#FFF'
            railFillBackgroundColor='#C43F2D24'
            thumbIconComponent={SwipeRightButton}
            titleColor='#000'
            swipeSuccessThreshold={70}
            onSwipeSuccess={onStart}
          />
        </>
      ) : (
        <XStack
          marginTop='$5'
          h='$2.5'
          marginBottom={8}
          borderRadius={16}
          paddingHorizontal={16}
          backgroundColor='$background'
          borderWidth={1}
          borderColor='$grey2'
          alignItems='center'
          justifyContent='space-between'>
          <I18nText fontWeight='700' fontSize={12} color='$primary'>
            Searching for GPS signal...
          </I18nText>
          <MaterialCommunityIcons
            name='signal-cellular-3'
            size={18}
            color='#E76049'
          />
        </XStack>
      )}
    </YStack>
  );
};

export default memo(
  TrackerActions,
  (prev, next) =>
    prev.status === next.status &&
    prev.signal === next.signal &&
    prev.signalAcquired === next.signalAcquired,
);
