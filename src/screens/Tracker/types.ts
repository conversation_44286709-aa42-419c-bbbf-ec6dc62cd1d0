import {ActivityTypeDto} from '@gojoe/typescript-sdk';

export enum GPSSignal {
  None = 'NONE',
  Poor = 'POOR',
  Fair = 'FAIR',
  Good = 'GOOD',
  Excellent = 'EXCELLENT',
}

export type TrackerState = {
  activityType: ActivityTypeDto;
  startTime: number | null;
  signal: GPSSignal;
  signalQuality: number | null;
  signalAcquired: boolean;
  coordinate: [number, number] | null;
  routeCoords: [number, number][];
  totalDistance: number;
  avgPace: string;
  elapsedTime: number;
  pausedDuration: number;
  pauseStart: number | null;
  calories: number;
};

type BackgroundLocationActivity = {
  confidence: number;
  type: string;
};
type BackgroundLocationBattery = {
  is_charging: boolean;
  level: number;
};
type BackgroundLocationCoords = {
  accuracy: number;
  altitude: number;
  altitude_accuracy: number;
  ellipsoidal_altitude: number;
  floor: number;
  heading: number;
  heading_accuracy: number;
  latitude: number;
  longitude: number;
  speed: number;
  speed_accuracy: number;
};

export type BackgroundLocation = {
  activity: BackgroundLocationActivity;
  age: number;
  battery: BackgroundLocationBattery;
  coords: BackgroundLocationCoords;
  extras: Record<string, any>;
  is_moving: boolean;
  mock: boolean;
  odometer: number;
  timestamp: string; // ISO string
  uuid: string;
};
