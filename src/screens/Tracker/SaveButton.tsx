import React, {useRef} from 'react';
import * as Crypto from 'expo-crypto';
import {ActivityTypeDto, CreateActivityDto} from '@gojoe/typescript-sdk';
import BackgroundGeolocation from 'react-native-background-geolocation';
import {useRouter} from 'expo-router';
import {DateTime} from 'luxon';

import StyledButton from '@/src/components/UI/Button';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';
import {TrackerState} from './types';
import useMutationActivityCreate from '@/src/hooks/api/useMutationActivityCreate';
import logger from '@/src/utils/logger';
import {storage} from '@/src/utils/localStorage';

// Storage key for persisting activity type during tracking
const TRACKER_ACTIVITY_TYPE_KEY = 'tracker_activity_type';

interface Props {
  activityType: ActivityTypeDto;
  state: TrackerState;
  timezone: string;
  media?: {id?: string; url: string}[];
}

const SaveButton: React.FC<Props> = ({
  activityType,
  timezone,
  state,
  media,
}) => {
  const router = useRouter();
  const {mutateAsync, isPending} = useMutationActivityCreate();
  const hasSubmittedRef = useRef(false);

  const handleSave = async () => {
    if (hasSubmittedRef.current || isPending) {
      return;
    }
    hasSubmittedRef.current = true;

    try {
      await triggerHaptics();

      const startTime = new Date(state.startTime ?? 'now');
      const endTime = new Date(state.pauseStart ?? 'now');
      const time = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);

      const input: CreateActivityDto = {
        wearableProviderActivityId: Crypto.randomUUID(),
        sportId: activityType.id,
        title: 'GoJoe Tracker',
        time,
        distance: state.totalDistance,
        points: Math.round(state.totalDistance / activityType.indexPoints),
        startTime: DateTime.fromJSDate(startTime).toUTC().toISO() || '',
        endTime: DateTime.fromJSDate(endTime).toUTC().toISO() || undefined,
        date: DateTime.fromJSDate(endTime).toUTC().toISODate() || '',
        calories: state.calories,
        heartRate: 0,
        timezone,
        attachments: media?.filter((m) => m.id).map((m) => m.id) as string[],
      };

      await mutateAsync(input);
      void Promise.all([
        BackgroundGeolocation.stop(),
        BackgroundGeolocation.destroyLocations(),
        BackgroundGeolocation.setOdometer(0),
      ]);

      // Clear the stored activity type since the activity is now saved
      storage.remove(TRACKER_ACTIVITY_TYPE_KEY);

      router.back();
    } catch (err) {
      logger.error('Error saving activity:', err);
      hasSubmittedRef.current = false; // Allow retry on failure
    }
  };

  return (
    <StyledButton onPress={handleSave} loading={isPending} disabled={isPending}>
      <I18nText color='$white1' fontWeight='500'>
        Save Activity
      </I18nText>
    </StyledButton>
  );
};

export default SaveButton;
