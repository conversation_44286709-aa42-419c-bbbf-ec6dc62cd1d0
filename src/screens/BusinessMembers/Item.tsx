import React, {useCallback} from 'react';
import {useRouter} from 'expo-router';
import {XStack} from 'tamagui';
import {UserListDto} from '@gojoe/typescript-sdk';
import {useQueryClient} from '@tanstack/react-query';

import {triggerHaptics} from '@/src/utils/haptics';
import {checkIfAccountIsPrivate} from '@/src/utils/users';
import FollowerButton from '@/src/components/FollowerButton';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';
import StyledText from '@/src/components/UI/StyledText';

interface Props {
  authUserId: string;
  user: UserListDto;
  businessId?: string;
}

const BusinessUserItem: React.FC<Props> = ({authUserId, user, businessId}) => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const updated = () => {
    queryClient.invalidateQueries({
      queryKey: ['business', 'users', businessId],
    });
  };

  const goToProfilePage = useCallback(
    async (userId: string) => {
      await triggerHaptics();
      router.push({
        pathname: '/user/[userId]',
        params: {
          userId: userId,
        },
      });
    },
    [router],
  );

  if (user.id === authUserId) {
    return null;
  }

  const followerButton = () => {
    if (user.id === authUserId) {
      return null;
    }

    const isPrivate = checkIfAccountIsPrivate({
      currentUserId: authUserId,
      privacy: user?.privacy || false,
      isFollowed: user.iFollow || false,
      userFollowId: user.id,
    });

    return (
      <FollowerButton
        isPrivate={isPrivate}
        userId={user.id}
        isFollowed={user.iFollow || false}
        updated={updated}
      />
    );
  };

  return (
    <XStack flex={1} height={56} alignItems='center'>
      <XStack
        flex={1}
        alignItems='center'
        gap='$2'
        onPress={() => goToProfilePage(user.id)}>
        <UserAvatar user={user} size={48} circular />
        <StyledText fontSize={14} fontWeight={700} numberOfLines={1}>
          {user.name}
        </StyledText>
      </XStack>
      {followerButton()}
    </XStack>
  );
};

export default BusinessUserItem;
