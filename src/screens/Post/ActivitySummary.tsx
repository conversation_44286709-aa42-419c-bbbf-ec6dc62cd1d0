import React from 'react';
import {useRouter} from 'expo-router';
import {useWindowDimensions} from 'react-native';
import {XStack, YStack, Image} from 'tamagui';
import convert from 'convert';
import RenderHtml from 'react-native-render-html';
import {PostSingleDto} from '@gojoe/typescript-sdk';

import {triggerHaptics} from '@/src/utils/haptics';
import {useLocales} from '@/src/contexts/LocaleContext';
import {useSession} from '@/src/contexts/SessionContext';
import {formatDuration} from '@/src/utils/tracker';
import WearableImage from '@/src/components/WearableImage';
import I18nText from '@/src/components/I18nText';
import StyledText from '@/src/components/UI/StyledText';
import ActivityIcon from '@/src/components/ActivityIcon';
import PostFooter from '@/src/components/PostFooter';
import ActivityMedia from './ActivityMedia';
import AddPhotos from './AddPhotos';

type Props = {
  post: PostSingleDto;
};

const ActivitySummary = ({post}: Props) => {
  const router = useRouter();
  const {locales} = useLocales();
  const {width} = useWindowDimensions();
  const {activity} = post;
  const {user} = useSession();

  if (!activity) {
    return null;
  }

  const {
    activityType,
    distance,
    time,
    calories,
    points,
    wearableProvider,
    details,
  } = activity;

  const distanceUnit = locales.distanceUnit;
  const formattedDistance = distance
    ? `${convert(distance, 'm').to(distanceUnit).toFixed(2)} ${distanceUnit}`
    : null;
  const formattedCalories = calories ? calories.toLocaleString() : '0';

  const formattedTime = formatDuration(time ?? 0);
  const itemWorkouts = details ? details : [];

  return (
    <YStack marginTop='$3.5'>
      <XStack paddingHorizontal='$3.5' alignItems='center'>
        <XStack flex={1} gap={8}>
          <XStack
            borderRadius={8}
            backgroundColor='$grey3'
            height={32}
            paddingHorizontal={8}
            gap={4}
            alignItems='center'>
            <ActivityIcon name={activityType.name} color='$grey1' size={16} />
            <I18nText
              color='$grey1'
              i18nParams={{
                activityType: activity.activityType.name,
              }}>{`{{activityType}} activity`}</I18nText>
          </XStack>
          <YStack
            backgroundColor='$grey1'
            borderRadius={8}
            height={32}
            justifyContent='center'
            paddingHorizontal={8}>
            <I18nText
              color='$white'
              fontSize={12}
              fontWeight='700'
              i18nParams={{
                count: points,
              }}>{`{{count}} pts`}</I18nText>
          </YStack>
        </XStack>
        {wearableProvider && (
          <WearableImage name={wearableProvider.name} size={32} />
        )}
      </XStack>
      <YStack
        backgroundColor='$background'
        borderRadius='$4'
        padding='$3.5'
        marginTop={-16}
        zIndex={-1}>
        <XStack marginTop='$3.5'>
          {formattedDistance && (
            <ValueItem label='Distance' value={formattedDistance} />
          )}
          <ValueItem label='Time' value={formattedTime} />
          <ValueItem label='Calories' value={formattedCalories} unit='Kcal' />
          {activity?.heartRate ? (
            <ValueItem
              label='Heart rate'
              value={`${activity?.heartRate}`}
              unit=''
            />
          ) : null}
        </XStack>
        <PostFooter post={post} />
      </YStack>
      <YStack alignItems='center' marginTop={40}>
        <StyledText fontSize={34}>🧐</StyledText>
        <I18nText fontWeight='700' fontSize={18}>
          Workout analysis
        </I18nText>
        <YStack marginTop='$3.5' alignItems='center' gap='$3.5'>
          {itemWorkouts.map((item, index) => {
            const handlePressSameAs = async () => {
              if (!item.link || !item.link.id) {
                return;
              }
              await triggerHaptics();
              router.push({
                pathname: '/user/[userId]',
                params: {
                  userId: item.link.id,
                },
              });
            };
            const type = (item.source.type ?? '') as any as string;

            return (
              <XStack
                gap={8}
                onPress={handlePressSameAs}
                alignItems='center'
                borderRadius={8}
                paddingHorizontal='$5'
                paddingVertical='$3'
                backgroundColor='$background'
                key={index.toString()}>
                {type === 'IMAGE' && item.source.content && (
                  <Image
                    source={{uri: item.source.content}}
                    width={36}
                    height={36}
                  />
                )}
                {type === 'TEXT' && item.source.content && (
                  <StyledText fontSize={32}>{item.source.content}</StyledText>
                )}
                <RenderHtml
                  contentWidth={width - (60 + index * 2)}
                  source={{html: item.description}}
                />
              </XStack>
            );
          })}
        </YStack>

        <ActivityMedia post={post} />

        {user?.id === post.user.id && <AddPhotos post={post} />}
      </YStack>
    </YStack>
  );
};

const ValueItem = ({
  label,
  value,
  unit,
}: {
  label: string;
  value: string;
  unit?: string;
}) => (
  <YStack flex={1}>
    <I18nText fontWeight='500' fontSize={12} color='$grey1'>
      {label}
    </I18nText>
    <XStack alignItems='center' marginTop='$2'>
      <StyledText fontSize={18} fontWeight='700'>
        {value}
      </StyledText>
      {unit && (
        <StyledText fontSize={18} fontWeight='700'>
          {unit}
        </StyledText>
      )}
    </XStack>
  </YStack>
);

export default ActivitySummary;
