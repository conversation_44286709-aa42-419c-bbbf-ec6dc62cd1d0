import React, {useLayoutEffect, useCallback} from 'react';
import {useLocalSearchParams, useNavigation} from 'expo-router';
import {<PERSON><PERSON>Vie<PERSON>, Spinner, YStack, XStack} from 'tamagui';
import StyledText from '@/src/components/UI/StyledText';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import usePost from '@/src/hooks/api/usePost';
import useReadPostByActivity from '@/src/hooks/api/useReadPostByActivity';
import {ImageBackground} from 'expo-image';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';
import {getRelativeTimeFromNow} from '@/src/utils/date';
import {useLocales} from '@/src/contexts/LocaleContext';
import ActivitySummary from './ActivitySummary';
import PostReported from '@/src/components/PostReported';
import {useSession} from '@/src/contexts/SessionContext';
import {MenuIcon} from '@/src/components/GoJoeIcon/icons/menu';
import {triggerHaptics} from '@/src/utils/haptics';
import {SheetManager} from 'react-native-actions-sheet';
import {useQueryClient} from '@tanstack/react-query';
import {updateCollectionItem, updateSingleItem} from '@/src/utils/query';

const PostScreen: React.FC = () => {
  const {postId, activityId} = useLocalSearchParams<{
    postId: string;
    activityId?: string;
  }>();

  const navigation = useNavigation();
  const queryClient = useQueryClient();

  const {top} = useSafeAreaInsets();
  const {locales} = useLocales();
  const {user} = useSession();

  // Use different hooks based on whether we're looking for an activity post or regular post
  const isActivityPost = postId === 'activity' && activityId;

  // Only call the appropriate hook based on the post type
  const {data: regularPost, isLoading: isLoadingRegular} = usePost(
    isActivityPost ? '' : postId,
  );
  const {data: activityPost, isLoading: isLoadingActivity} =
    useReadPostByActivity(isActivityPost ? activityId! : '');

  const post = isActivityPost ? activityPost : regularPost;
  const isLoading = isActivityPost ? isLoadingActivity : isLoadingRegular;

  // Menu functionality
  const onSuccessReportMutate = useCallback(() => {
    return Promise.all([
      updateCollectionItem({
        queryClient,
        id: post?.id,
        queryKey: ['feed'],
        update: {
          data: {
            data: {
              reported: true,
            },
          },
        },
        fields: ['reported'],
      }),
      updateSingleItem({
        queryClient,
        queryKey: ['post', post?.id],
        update: {
          data: {
            data: {
              reported: true,
            },
          },
        },
        fields: ['reported'],
      }),
      updateSingleItem({
        queryClient,
        queryKey: ['post', 'activities', post?.user?.id],
        update: {
          data: {
            data: {
              reported: true,
            },
          },
        },
        fields: ['reported'],
      }),
    ]);
  }, [queryClient, post?.id, post?.user?.id]);

  const handlePressOnMenu = useCallback(async () => {
    if (!post) return;

    await triggerHaptics();
    await SheetManager.show('post_menu', {
      payload: {
        post: post as any, // Type assertion since PostSingleDto and PostListDto are compatible for menu purposes
        onSuccessReportMutate,
      },
    });
  }, [post, onSuccessReportMutate]);

  // Set up header with menu icon
  useLayoutEffect(() => {
    if (!post) return;

    navigation.setOptions({
      headerRight: () => (
        <XStack
          alignItems='center'
          onPress={handlePressOnMenu}
          paddingRight='$3'>
          <MenuIcon size={24} color='#000' />
        </XStack>
      ),
    });
  }, [navigation, post, handlePressOnMenu]);

  if (isLoading) {
    return (
      <YStack paddingTop={top} marginTop={'$8'}>
        <Spinner />
      </YStack>
    );
  }

  if (!post) {
    return null;
  }

  return (
    <ImageBackground
      source={require('@/assets/images/tracks.png')}
      style={{width: '100%', height: '100%'}}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        flex={1}
        paddingTop={top}>
        <YStack paddingHorizontal='$5' marginTop={'$8'} flex={1}>
          <YStack alignItems='center'>
            <UserAvatar user={post.user} size='$11' borderRadius={8} />
            <StyledText fontWeight='700' marginTop='$2.5'>
              {post.user.name}
            </StyledText>
            <StyledText fontWeight='500' color='$grey1' marginTop='$1.5'>
              {getRelativeTimeFromNow(
                post.publishedAt,
                locales.timezone,
                locales.languageCode,
              )}
            </StyledText>
          </YStack>
          <YStack>
            {post.reported && (
              <PostReported
                isInternal={post.user?.id === user?.id}
                isActivity={!!post.activity}
              />
            )}
            <ActivitySummary post={post} />
          </YStack>
        </YStack>
      </ScrollView>
    </ImageBackground>
  );
};

export default PostScreen;
