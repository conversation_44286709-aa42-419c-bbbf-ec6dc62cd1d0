import React, {useState} from 'react';
import * as ImagePicker from 'expo-image-picker';
import {Spin<PERSON>, XStack} from 'tamagui';
import {Camera} from '@tamagui/lucide-icons';
import {PostSingleDto} from '@gojoe/typescript-sdk';

import {triggerHaptics} from '@/src/utils/haptics';
import {bulkUploadMedia} from '@/src/utils/upload';
import I18nText from '@/src/components/I18nText';
import useMutationUpdatePost from '@/src/hooks/api/useMutationUpdatePost';

interface Props {
  post: PostSingleDto;
}

const AddPhotos: React.FC<Props> = ({post}) => {
  const {mutateAsync} = useMutationUpdatePost(post.id);
  const [isLoading, setIsLoading] = useState(false);
  const [media, setMedia] = useState(
    post.attachments.map((attachment) => ({
      id: attachment.id,
      url: attachment.uri,
    })),
  );

  const maxImages = 10;
  const length = post.attachments.length;
  const canAddMore = length < maxImages;

  const pickImageFromLibrary = async () => {
    setIsLoading(true);
    await triggerHaptics();

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      quality: 0.8,
      allowsMultipleSelection: canAddMore && maxImages - media.length > 1,
      selectionLimit: maxImages - media.length,
    });

    if (!result.canceled) {
      const imagesToAdd = result.assets.slice(0, maxImages - media.length);
      const newMedia = await bulkUploadMedia(imagesToAdd, 'post');

      const updatedMedia = [...media, ...newMedia];
      setMedia(updatedMedia);

      // ✅ Extract IDs and merge with existing attachment IDs
      const newAttachmentIds = newMedia.map((m) => m.id);
      const existingAttachmentIds = post.attachments.map((a) => a.id);

      const attachments = [...existingAttachmentIds, ...newAttachmentIds];

      await mutateAsync({
        attachments,
        id: post.id,
      });
    }

    setIsLoading(false);
  };

  return (
    <>
      {canAddMore && !isLoading && (
        <XStack
          justifyContent='center'
          alignItems='center'
          backgroundColor='$background'
          borderWidth={1}
          borderColor='$primary'
          borderStyle='dashed'
          paddingHorizontal='$3.5'
          paddingVertical='$2'
          marginTop='$7'
          marginBottom='$11'
          gap='$2'
          onPress={pickImageFromLibrary}>
          <Camera size={24} color='$primary' />
          <I18nText fontSize={14} fontWeight={700} color='$primary'>
            Add Photos
          </I18nText>
        </XStack>
      )}
      {isLoading && (
        <XStack marginTop='$7' marginBottom='$11'>
          <Spinner size='small' color='$primary' />
        </XStack>
      )}
    </>
  );
};
export default AddPhotos;
