import React, {useCallback} from 'react';
import {Image} from 'expo-image';
import {<PERSON>ner, XStack, YStack} from 'tamagui';
import {FlashList, ListRenderItem} from '@shopify/flash-list';
import {
  MediaListDto,
  MediaListDtoTypeEnum,
  PostSingleDto,
} from '@gojoe/typescript-sdk';
import {XCircle} from '@tamagui/lucide-icons';

import useMutationUpdatePost from '@/src/hooks/api/useMutationUpdatePost';
import {useImage} from '@/src/contexts/ImageContext';

interface Props {
  post: PostSingleDto;
}

const ActivityMedia: React.FC<Props> = ({post}) => {
  const {showImage} = useImage();
  const {mutateAsync, isPending} = useMutationUpdatePost(post.id);

  const handleImagePress = useCallback(
    (uri: string) => {
      showImage(uri);
    },
    [showImage],
  );

  const removePhotoFromActivityDetail = useCallback(
    (media: MediaListDto) => {
      const attachments = post.attachments
        .filter((att) => att.id !== media.id)
        .map((at) => at.id);

      mutateAsync({
        attachments,
        id: post.id,
      });
    },
    [mutateAsync, post.attachments, post.id],
  );

  const keyExtractor = useCallback((item: MediaListDto) => item.id, []);

  const renderItem: ListRenderItem<MediaListDto> = useCallback(
    ({item, index}) => {
      if (item.type !== MediaListDtoTypeEnum.Image) return null;

      const width = 188;
      const height = 120;

      return (
        <YStack
          marginTop={40}
          marginLeft={index === 0 ? 0 : '$1.5'}
          overflow='hidden'
          onPress={() => handleImagePress(item.uri)}>
          <Image
            source={{uri: item.uri}}
            style={{
              width: width,
              height: height,
              borderRadius: 8,
            }}
            contentFit='cover'
          />
          <XStack
            position='absolute'
            top={0}
            right={0}
            padding={8}
            onPress={() => removePhotoFromActivityDetail(item)}>
            {isPending ? (
              <Spinner size='small' color='$background' />
            ) : (
              <XCircle size={16} color='$background' />
            )}
          </XStack>
        </YStack>
      );
    },
    [isPending, removePhotoFromActivityDetail, handleImagePress],
  );

  if (!post.attachments || post.attachments.length === 0) {
    return null;
  }

  return (
    <XStack alignSelf='flex-start'>
      <FlashList
        data={post.attachments}
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        horizontal
        estimatedItemSize={188}
        showsHorizontalScrollIndicator={false}
      />
    </XStack>
  );
};

export default ActivityMedia;
