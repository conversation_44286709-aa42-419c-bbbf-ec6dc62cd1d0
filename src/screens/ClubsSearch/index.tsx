import React, {useCallback} from 'react';
import {Spinner, YStack} from 'tamagui';
import {ListRenderItem} from '@shopify/flash-list';
import useClubs from '@/src/hooks/api/useClubs';
import {ClubDto} from '@gojoe/typescript-sdk';
import ClubItemHorizontal from '@/src/components/ClubItemHorizontal';
import {triggerHaptics} from '@/src/utils/haptics';
import {useRouter} from 'expo-router';
import {SharedValue, useAnimatedScrollHandler} from 'react-native-reanimated';
import {createAnimatedFlashList} from '@/src/components/AnimatedFlashList';

const AnimatedFlashList = createAnimatedFlashList<ClubDto>();

interface Props {
  scrollY: SharedValue<number>;
}

const FindClubs: React.FC<Props> = ({scrollY}) => {
  const router = useRouter();
  const {data, isLoading, isRefetching, refetch} = useClubs();

  const onScroll = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  const keyExtractor = useCallback((item: ClubDto) => item.id, []);

  const listHeaderComponent = useCallback(() => {
    if (isLoading) {
      return <Spinner marginTop='$3' />;
    }
    return <YStack height={24} />;
  }, [isLoading]);

  const renderItem = useCallback<ListRenderItem<ClubDto>>(
    ({item}) => {
      const onPress = async () => {
        await triggerHaptics();
        router.push({
          pathname: '/club/[clubId]',
          params: {clubId: item.id},
        });
      };

      return (
        <YStack marginBottom={8} marginHorizontal='$5' onPress={onPress}>
          <ClubItemHorizontal club={item} />
        </YStack>
      );
    },
    [router],
  );

  if (isLoading) {
    return <Spinner marginTop='$5' />;
  }
  return (
    <YStack flex={1}>
      <AnimatedFlashList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        showsVerticalScrollIndicator={false}
        estimatedItemSize={80}
        refreshing={isRefetching}
        onRefresh={refetch}
        onScroll={onScroll}
        scrollEventThrottle={16}
        ListHeaderComponent={listHeaderComponent}
      />
    </YStack>
  );
};

export default FindClubs;
