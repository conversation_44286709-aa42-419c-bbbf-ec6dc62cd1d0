import React, {useCallback} from 'react';
import {Dimensions, FlatList, StyleSheet} from 'react-native';
import {useRouter} from 'expo-router';
import {Image} from 'expo-image';
import * as Linking from 'expo-linking';
import {YStack} from 'tamagui';
import {BenefitDto} from '@gojoe/typescript-sdk';

import {useBenefits} from '@/src/hooks/api/useBenefits';
import StyledText from '@/src/components/UI/StyledText';
import {triggerHaptics} from '@/src/utils/haptics';

const itemWidth = 98;
const PADDING = 12;
const windowWidth = Dimensions.get('window').width;
let numColumns = Math.floor(windowWidth / itemWidth);
let spaceRemaining = windowWidth - PADDING * 2 - numColumns * itemWidth;
let gap = spaceRemaining / (numColumns - 1);

const BenefitsScreen: React.FC = () => {
  const {data, refetch, isRefetching} = useBenefits();
  const router = useRouter();

  const handleOnPress = useCallback(
    async (benefit: BenefitDto) => {
      await triggerHaptics();
      switch (benefit.name) {
        case 'Les Mills':
          router.push('/les-mills');
          return;
        case 'House Of Wellbeing':
          router.push('/house-of-wellbeing');
          return;
      }
      if (benefit.description) {
        router.push({
          pathname: '/benefits/[benefitId]',
          params: {
            benefitId: benefit.id,
          },
        });
        return;
      }
      if (benefit.url) {
        Linking.canOpenURL(benefit.url).then(async (supported) => {
          if (supported) {
            await Linking.openURL(benefit.url);
          }
        });
        return;
      }

      return undefined;
    },
    [router],
  );
  const renderItem = ({item}: {item: BenefitDto}) => {
    const onPress = () => handleOnPress(item);
    return (
      <YStack
        onPress={onPress}
        alignItems='center'
        justifyContent='space-between'
        width={itemWidth}
        height={92}
        marginBottom={24}
        overflow='hidden'>
        <Image source={{uri: item.image}} style={styles.icon} />
        <StyledText fontWeight='600' fontSize={12} numberOfLines={1}>
          {item.name}
        </StyledText>
      </YStack>
    );
  };

  return (
    <YStack flex={1} paddingVertical='$5' paddingStart={PADDING / 2}>
      <FlatList
        renderItem={renderItem}
        data={data}
        numColumns={numColumns}
        columnWrapperStyle={styles.grid}
        refreshing={isRefetching}
        onRefresh={refetch}
      />
    </YStack>
  );
};

const styles = StyleSheet.create({
  icon: {
    width: 72,
    height: 72,
    borderRadius: 16,
    overflow: 'hidden',
  },
  grid: {
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    gap: gap,
  },
});
export default BenefitsScreen;
