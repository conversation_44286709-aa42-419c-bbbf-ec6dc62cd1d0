import React from 'react';
import {ImageBackground} from 'expo-image';
import {XStack, YStack} from 'tamagui';
import ParallaxScrollView from '@/src/components/ParallaxScrollView';
import BusinessAvatar from '@/src/components/UI/Avatar/BusinessAvatar';
import StyledText from '@/src/components/UI/StyledText';
import {BusinessDetailsDto} from '@gojoe/typescript-sdk';
import LatestPost from './LatestPost';
import I18nText from '@/src/components/I18nText';
import {ChevronRight, File, Users2} from '@tamagui/lucide-icons';
import {useRouter} from 'expo-router';
import {triggerHaptics} from '@/src/utils/haptics';
import BusinessActions from '@/src/components/BusinessActions';
import {useSession} from '@/src/contexts/SessionContext';

interface Props {
  business: BusinessDetailsDto;
}
const BusinessScreen: React.FC<Props> = ({business}) => {
  const router = useRouter();
  const {user} = useSession();

  const cover = business.cover
    ? {uri: business.cover}
    : require('@/assets/images/business_cover.jpg');

  const handleOnPressMembers = async () => {
    await triggerHaptics();
    router.push({
      pathname: '/business/[businessId]/members',
      params: {
        businessId: business.id,
      },
    });
  };

  const handleOnPressPosts = async () => {
    await triggerHaptics();
    router.push({
      pathname: '/business/[businessId]/posts',
      params: {
        businessId: business.id,
      },
    });
  };

  return (
    <YStack flex={1}>
      <BusinessActions business={business} userId={user?.id ?? ''} />
      <ParallaxScrollView
        backgroundColor={business.color ?? '$windowBackground'}
        headerImage={
          <YStack height={220} backgroundColor={business.color}>
            <ImageBackground
              source={cover}
              style={{width: '100%', height: 220, justifyContent: 'flex-end'}}>
              <XStack padding='$5' gap='$2.5'>
                <BusinessAvatar
                  business={business}
                  size={65}
                  borderRadius={8}
                />
                <YStack justifyContent='center'>
                  <StyledText color='$white1' fontSize={24} fontWeight='700'>
                    {business.name}
                  </StyledText>
                  <StyledText
                    color='$white1'
                    fontSize={24}
                    fontWeight='700'></StyledText>
                </YStack>
              </XStack>
            </ImageBackground>
          </YStack>
        }>
        <YStack paddingHorizontal='$5' gap='$5' paddingTop='$5'>
          {/* <BusinessTrends businessId={business.id} /> */}
          <LatestPost businessId={business.id} />
          <YStack
            backgroundColor='$background'
            gap={16}
            borderRadius={16}
            borderWidth={1}
            borderColor='$grey3'
            padding='$3.5'>
            <XStack alignItems='center' gap={8} onPress={handleOnPressPosts}>
              <File size={18} color='$grey1' />
              <I18nText color='$grey1' fontWeight='500' flex={1}>
                Posts
              </I18nText>
              <ChevronRight size={24} color='$grey1' />
            </XStack>
            <XStack alignItems='center' gap={8} onPress={handleOnPressMembers}>
              <Users2 size={18} color='$grey1' />
              <I18nText color='$grey1' fontWeight='500' flex={1}>
                Members
              </I18nText>
              <ChevronRight size={24} color='$grey1' />
            </XStack>
          </YStack>
        </YStack>
        <YStack height={48} />
      </ParallaxScrollView>
    </YStack>
  );
};

export default BusinessScreen;
