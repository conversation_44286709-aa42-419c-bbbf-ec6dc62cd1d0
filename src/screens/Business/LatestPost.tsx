import React from 'react';
import {useLocales} from '@/src/contexts/LocaleContext';
import useBusinessPosts from '@/src/hooks/api/useBusinessPosts';
import PostCard from '@/src/components/PostCard';

interface Props {
  businessId: string;
}

const LatestPost: React.FC<Props> = ({businessId}) => {
  const {locales} = useLocales();
  const {data} = useBusinessPosts(businessId);

  if (data.length === 0) {
    return null;
  }

  return <PostCard post={data[0]} unit={locales.distanceUnit} />;
};

export default LatestPost;
