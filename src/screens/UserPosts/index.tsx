import React, {useCallback} from 'react';
import {FlashList} from '@shopify/flash-list';
import {Spinner, YStack} from 'tamagui';
import {PostListDto} from '@gojoe/typescript-sdk';

import {useLocales} from '@/src/contexts/LocaleContext';
import useUserPosts from '@/src/hooks/api/useUserPosts';
import PostCard from '@/src/components/PostCard';
import {ListSpacer16} from '@/src/components/ListSpacer';

interface Props {
  userId: string;
}

const UserPosts: React.FC<Props> = ({userId}) => {
  const {locales} = useLocales();
  const {
    data,
    isRefetching,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
  } = useUserPosts(userId, 'STATUS,VIDEO,PHOTO');

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const renderItem = useCallback(
    ({item}: {item: PostListDto}) => {
      return (
        <PostCard
          post={item}
          languageTag={locales.languageTag}
          unit={locales.distanceUnit}
        />
      );
    },
    [locales.languageTag, locales.distanceUnit],
  );

  const renderFooter = () => {
    if (isFetchingNextPage) {
      return (
        <YStack paddingVertical='$4' alignItems='center'>
          <Spinner size='small' color='$color' />
        </YStack>
      );
    }
    return <YStack height={48} />;
  };

  return (
    <YStack paddingHorizontal='$5' paddingTop='$5' flex={1}>
      <FlashList
        data={data}
        renderItem={renderItem}
        estimatedItemSize={280}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        onEndReached={onEndReached}
        ListFooterComponent={renderFooter}
        refreshing={isRefetching}
        onRefresh={refetch}
        ItemSeparatorComponent={ListSpacer16}
      />
    </YStack>
  );
};

export default UserPosts;
