import React, {useCallback, useMemo} from 'react';
import {<PERSON><PERSON>, XStack, YStack} from 'tamagui';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';
import StyledText from '@/src/components/UI/StyledText';
import I18nText from '@/src/components/I18nText';
import {UserProfileDto} from '@gojoe/typescript-sdk';
import {useChallengeUserActivities} from '@/src/hooks/api/useChallengeUserActivities';
import useChallengeUserActivitySummary from '@/src/hooks/api/useChallengeUserActivitySummary';
import {useLocales} from '@/src/contexts/LocaleContext';
import Animated, {
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import {createAnimatedFlashList} from '@/src/components/AnimatedFlashList';
import ActivityItem from './ActivityItem';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {HEADER_HEIGHT, styles} from './styles';
import {groupActivitiesByDate} from './utils';
import ListHeader from './ListHeader';
import {SectionList} from '@/src/screens/challenge/UserActivities/types';
import ActivitySummary from './ActivitySummary';
import {ActivityTypeMetricsDto} from '@gojoe/typescript-sdk/api';
import {triggerHaptics} from '@/src/utils/haptics';
import {useRouter} from 'expo-router';

interface Props {
  challengeId: string;
  user: UserProfileDto;
}

// Create an animated version of FlashList
const AnimatedFlashList = createAnimatedFlashList<SectionList>();

const UserActivities: React.FC<Props> = ({challengeId, user}) => {
  const router = useRouter();
  const {data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage} =
    useChallengeUserActivities(challengeId, user.id);
  const {data: summaryData} = useChallengeUserActivitySummary(
    challengeId,
    user.id,
  );
  const {locales} = useLocales();
  const {bottom} = useSafeAreaInsets();

  // Create a shared value for tracking scroll position
  const scrollY = useSharedValue(0);

  // Create an animated style for the header
  const headerAnimatedStyle = useAnimatedStyle(() => {
    // Calculate how much of the header should be hidden based on scroll position
    const translateY = Math.min(Math.max(-scrollY.value, -HEADER_HEIGHT), 0);

    return {
      transform: [{translateY}],
      zIndex: 10,
    };
  });

  // Handle scroll events to update the scrollY value
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  // Group activities by date and flatten for FlashList
  const flattenedData = useMemo(() => {
    const groups = groupActivitiesByDate(data || []);
    const flattened: SectionList[] = [];

    groups.forEach((group) => {
      // Add section with all activities
      const sectionItems = group.data.map((activity, index) => {
        // Add dateLabel to the first item in each group
        if (index === 0) {
          return {
            ...activity,
            dateLabel: group.title,
          };
        }
        return activity;
      });

      flattened.push({
        type: 'section',
        data: sectionItems,
        id: `section-${group.dateString}`,
        title: group.title,
      });
    });

    return flattened;
  }, [data]);

  const renderItem = useCallback(
    ({
      item,
    }: {
      item: {type: 'section'; data: any[]; id: string; title: string};
    }) => {
      return (
        <YStack
          backgroundColor='$background'
          borderRadius={8}
          paddingHorizontal='$3.5'
          paddingVertical='$3'
          marginBottom='$2'>
          {item.data.map((activity, index) => (
            <ActivityItem
              key={activity.id}
              item={activity}
              distanceUnit={locales.distanceUnit}
              showDate={index === 0} // Only show date for first item in group
            />
          ))}
        </YStack>
      );
    },
    [locales.distanceUnit],
  );

  const keyExtractor = useCallback((item: {id: string}) => item.id, []);

  const loadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  const goToProfilePage = async () => {
    await triggerHaptics();
    router.push({
      pathname: '/user/[userId]',
      params: {
        userId: user?.id,
      },
    });
  };

  return (
    <YStack flex={1}>
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <XStack gap={12}>
          <UserAvatar
            user={user}
            size={52}
            circular
            onPress={goToProfilePage}
          />
          <YStack justifyContent='center'>
            <StyledText
              fontSize={20}
              fontWeight='700'
              onPress={goToProfilePage}>
              {user.firstName} {user.lastName}
            </StyledText>
            <I18nText
              fontSize={12}
              fontWeight='500'
              color='$grey2'
              marginTop='$1'>
              Activities in Challenge
            </I18nText>
          </YStack>
        </XStack>
      </Animated.View>

      {isLoading ? (
        <Spinner marginTop='$5' />
      ) : (
        <AnimatedFlashList
          data={flattenedData}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          estimatedItemSize={80}
          contentContainerStyle={{
            ...styles.listContent,
            paddingBottom: Math.max(bottom + 40, 80), // Use safe area insets plus extra padding
          }}
          // No separator needed as we're rendering complete sections
          ListEmptyComponent={
            <YStack alignItems='center' marginTop='$8'>
              <I18nText>No activities found</I18nText>
            </YStack>
          }
          ListHeaderComponent={
            <YStack>
              <YStack height={HEADER_HEIGHT} />
              {summaryData && (
                <YStack marginBottom='$6'>
                  <ActivitySummary
                    summaryData={
                      summaryData as unknown as Record<
                        string,
                        ActivityTypeMetricsDto
                      >
                    }
                    distanceUnit={locales.distanceUnit}
                    challengeId={challengeId}
                    userId={user.id}
                  />
                </YStack>
              )}
              <ListHeader distanceUnit={locales.distanceUnit} />
              <YStack height={8} />
            </YStack>
          }
          ListFooterComponent={
            isFetchingNextPage ? (
              <Spinner marginVertical='$4' />
            ) : (
              <YStack style={styles.footerSpace} />
            )
          }
          onScroll={scrollHandler}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          onEndReached={loadMore}
          onEndReachedThreshold={0.5}
        />
      )}
    </YStack>
  );
};

export default UserActivities;
