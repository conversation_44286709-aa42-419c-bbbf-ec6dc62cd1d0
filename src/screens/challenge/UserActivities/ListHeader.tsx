import React from 'react';
import {XStack, YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';
import {DistanceUnit} from '@/src/contexts/LocaleContext';

interface ListHeaderProps {
  distanceUnit: DistanceUnit;
}

const ListHeader: React.FC<ListHeaderProps> = ({distanceUnit}) => {
  return (
    <XStack
      height={12}
      alignItems='center'
      justifyContent='space-between'
      paddingHorizontal='$3.5'>
      <XStack alignItems='center' gap='$3' flex={1}>
        <I18nText fontSize={10} fontWeight='500' color='$grey1' width={90}>
          Date
        </I18nText>
        <YStack width={16} />
      </XStack>
      <XStack alignItems='center'>
        <XStack width={60} justifyContent='flex-end'>
          <I18nText fontSize={10} fontWeight='500' color='$grey1'>
            {distanceUnit.toUpperCase()}
          </I18nText>
        </XStack>
        <XStack width={50} justifyContent='flex-end'>
          <I18nText fontSize={10} fontWeight='500' color='$grey1'>
            Pts
          </I18nText>
        </XStack>
      </XStack>
    </XStack>
  );
};

export default ListHeader;
