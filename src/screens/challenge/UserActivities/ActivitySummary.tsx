import React, {useMemo} from 'react';
import {XStack, YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';
import StyledText from '@/src/components/UI/StyledText';
import {DistanceUnit} from '@/src/contexts/LocaleContext';
import ActivityIcon from '@/src/components/ActivityIcon';
import convert from 'convert';
import {SheetManager} from 'react-native-actions-sheet';
import {ChevronRight} from '@tamagui/lucide-icons';
import {ActivityTypeMetricsDto} from '@gojoe/typescript-sdk/api';

interface ActivitySummaryProps {
  summaryData: Record<string, ActivityTypeMetricsDto>;
  distanceUnit: DistanceUnit;
  challengeId: string;
  userId: string;
}

const ActivitySummary: React.FC<ActivitySummaryProps> = ({
  summaryData,
  distanceUnit,
  challengeId,
  userId,
}) => {
  // Calculate total points and distance
  const totals = useMemo(() => {
    let totalPoints = 0;
    let totalDistance = 0;

    Object.values(summaryData).forEach((activity) => {
      totalPoints += activity.totalPoints;
      totalDistance += activity.totalDistance;
    });

    return {
      totalPoints: totalPoints.toFixed(2),
      totalDistance: convert(totalDistance, 'm').to(distanceUnit).toFixed(2),
    };
  }, [summaryData, distanceUnit]);

  // Sort activities by points (highest first) and take top 3
  const topActivities = useMemo(() => {
    return Object.entries(summaryData)
      .sort(([, a], [, b]) => b.totalPoints - a.totalPoints)
      .slice(0, 3)
      .map(([type, data]) => ({
        type,
        ...data,
      }));
  }, [summaryData]);

  // Check if there are more than 3 activities
  const hasMoreActivities = useMemo(() => {
    return Object.keys(summaryData).length > 3;
  }, [summaryData]);

  const handleViewAllActivities = async () => {
    // Open sheet with all activities
    await SheetManager.show('activity_summary_all', {
      payload: {
        summaryData,
        distanceUnit,
        challengeId,
        userId,
      },
    });
  };

  return (
    <YStack
      backgroundColor='$background'
      borderRadius={16}
      padding='$5'
      borderWidth={1}
      borderColor='$grey3'>
      {/* Header with Summary title and totals */}
      <XStack justifyContent='space-between' alignItems='flex-start'>
        <I18nText fontSize={18} fontWeight='700'>
          Summary
        </I18nText>
        <YStack alignItems='flex-end'>
          <XStack gap='$2'>
            <YStack>
              <StyledText fontSize={18} fontWeight='700' textAlign='right'>
                {totals.totalPoints}
              </StyledText>
              <I18nText
                fontSize={12}
                fontWeight='500'
                color='$grey2'
                textAlign='right'
                width={50}>
                Pts
              </I18nText>
            </YStack>
            <YStack>
              <StyledText fontSize={18} fontWeight='700' textAlign='right'>
                {totals.totalDistance}
              </StyledText>
              <I18nText
                fontSize={12}
                fontWeight='500'
                color='$grey2'
                textAlign='right'
                width={50}>
                {distanceUnit.toUpperCase()}
              </I18nText>
            </YStack>
          </XStack>
        </YStack>
      </XStack>

      {/* Activity list */}
      <YStack marginTop='$3' gap='$2'>
        {topActivities.map((activity) => {
          // Calculate percentage for the progress bar
          const maxPoints = topActivities[0].totalPoints;
          const percentage = (activity.totalPoints / maxPoints) * 100;

          const distanceConverted = convert(activity.totalDistance, 'm').to(
            distanceUnit,
          ) as number;

          const distanceFormatted = distanceConverted
            ? distanceConverted.toFixed(2)
            : 0;

          return (
            <XStack
              key={activity.type}
              justifyContent='space-between'
              alignItems='center'
              paddingVertical='$2'>
              <XStack alignItems='center' gap='$3.5' flex={1}>
                <ActivityIcon name={activity.type} size={18} color='$primary' />
                <YStack
                  flex={1}
                  height={8}
                  backgroundColor='$grey3'
                  borderRadius={4}
                  overflow='hidden'>
                  <YStack
                    height='100%'
                    width={`${Math.min(percentage, 100)}%`}
                    backgroundColor='$red10'
                    borderRadius={4}
                  />
                </YStack>
              </XStack>
              <XStack gap='$2' alignItems='center'>
                <StyledText
                  fontSize={12}
                  fontWeight='500'
                  color='$grey2'
                  textAlign='right'
                  width={50}>
                  {activity.totalPoints.toFixed(2)}
                </StyledText>
                <StyledText
                  fontSize={12}
                  fontWeight='500'
                  color='$grey2'
                  textAlign='right'
                  width={50}>
                  {distanceFormatted}
                </StyledText>
              </XStack>
            </XStack>
          );
        })}
      </YStack>

      {/* View all link */}
      {hasMoreActivities && (
        <XStack
          marginTop='$3'
          justifyContent='flex-end'
          alignItems='center'
          gap='$1'
          onPress={handleViewAllActivities}>
          <I18nText fontSize={14} fontWeight='500' color='$primary'>
            View all activities
          </I18nText>
          <ChevronRight size={16} color='$primary' />
        </XStack>
      )}
    </YStack>
  );
};

export default ActivitySummary;
