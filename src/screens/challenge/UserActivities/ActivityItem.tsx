import React from 'react';
import {XStack, YStack} from 'tamagui';
import StyledText from '@/src/components/UI/StyledText';
import ActivityIcon from '@/src/components/ActivityIcon';
import convert from 'convert';
import {ChallengeUserActivitiesItemDto} from '@gojoe/typescript-sdk';
import {DateTime} from 'luxon';
import ActivityPressable from '@/src/components/ActivityPressable';

interface ActivityItemProps {
  item: ChallengeUserActivitiesItemDto;
  distanceUnit: 'km' | 'mi';
  showDate?: boolean;
}

const ActivityItem: React.FC<ActivityItemProps> = ({
  item,
  distanceUnit,
  showDate = false,
}) => {
  // Format the date for the first item, time for others
  const dateOrTime = showDate
    ? DateTime.fromISO(item.startTime).toFormat('EEE, MMM d')
    : DateTime.fromISO(item.startTime).toFormat('h:mm a');

  // Format the distance without unit
  const formattedDistance = item.distance
    ? `${convert(item.distance, 'm').to(distanceUnit).toFixed(2)}`
    : '-';

  return (
    <ActivityPressable activityId={item.id}>
      <XStack paddingVertical={12} justifyContent='space-between'>
        <XStack alignItems='center' gap='$3' flex={1}>
          {showDate && (
            <StyledText
              fontSize={12}
              fontWeight='500'
              color='$color'
              width={90}>
              {dateOrTime}
            </StyledText>
          )}
          {!showDate && <YStack width={90} />}
          <ActivityIcon
            name={item.activityType.name}
            size={16}
            color='$primary'
          />
        </XStack>
        <XStack alignItems='center'>
          <XStack width={60} justifyContent='flex-end'>
            <StyledText
              fontSize={12}
              fontWeight='500'
              color='$color'
              textAlign='right'>
              {formattedDistance}
            </StyledText>
          </XStack>
          <XStack width={50} justifyContent='flex-end'>
            <StyledText
              fontSize={12}
              fontWeight='500'
              color='$color'
              textAlign='right'>
              {item.points}
            </StyledText>
          </XStack>
        </XStack>
      </XStack>
    </ActivityPressable>
  );
};

export default ActivityItem;
