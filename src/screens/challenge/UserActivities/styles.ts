import {StyleSheet} from 'react-native';

// Header height constant
export const HEADER_HEIGHT = 80;

export const styles = StyleSheet.create({
  // Non-Tamagui element styles
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingTop: 16,
    backgroundColor: '$background',
    paddingBottom: 16,
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 3,
  },
  listContent: {
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  footerSpace: {
    height: 20,
  },
});

// No need for ActivityItem styles as we'll use inline Tamagui props
