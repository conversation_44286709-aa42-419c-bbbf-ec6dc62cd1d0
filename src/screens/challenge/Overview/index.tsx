import React, {memo} from 'react';
import {ScrollView, XStack, YStack} from 'tamagui';
import {ChallengeDetailsDto} from '@gojoe/typescript-sdk';
import ChallengeCover from './ChallengeCover';
import I18nText from '@/src/components/I18nText';
import {
  Calendar,
  Check,
  Flame,
  Flag,
  Users2,
  Watch,
  Handshake,
  ChevronRight,
} from '@tamagui/lucide-icons';
import {Trans} from 'react-i18next';
import ActivityIcon from '@/src/components/ActivityIcon';
import StyledText from '@/src/components/UI/StyledText';
import {triggerHaptics} from '@/src/utils/haptics';
import {challengeDate} from '@/src/utils/challenge';
import {useLocales} from '@/src/contexts/LocaleContext';
import TimeStatus from '@/src/components/ChallengeFeaturedList/TimeStatus';
import {CodeIcon} from '@/src/components/GoJoeIcon';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {SPACE_BOTTOM} from '@/src/constants/Constants';
import NeedHelp from '@/src/components/NeedHelp';
import {SheetManager} from 'react-native-actions-sheet';
import {useRouter} from 'expo-router';

interface Props {
  challenge: ChallengeDetailsDto;
}

const DISPLAYED_ACTIVITY_TYPES = 4;
const ChallengeOverview: React.FC<Props> = ({challenge}) => {
  const {bottom} = useSafeAreaInsets();
  const {locales} = useLocales();
  const router = useRouter();

  const handlePressActivities = async () => {
    await triggerHaptics();
    await SheetManager.show('challenge_activity_type_list', {
      payload: {
        activities: challenge.activityTypes,
      },
    });
  };

  const handlePressDescription = async () => {
    if (!challenge.description) {
      return;
    }
    await triggerHaptics();
    await SheetManager.show('challenge_description', {
      payload: {
        description: challenge.description,
      },
    });
  };

  const handlePressFairPlay = async () => {
    await triggerHaptics();
    router.push({
      pathname: '/fair-play',
    });
  };

  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <ChallengeCover challenge={challenge} />
      <YStack
        paddingHorizontal='$5'
        marginTop={-48}
        gap={16}
        paddingBottom={bottom + SPACE_BOTTOM}>
        <YStack
          borderWidth={1}
          borderColor='$grey3'
          backgroundColor='$background'
          padding={8}
          paddingVertical={16}
          borderRadius={16}>
          <I18nText color='$color' fontWeight='700' paddingHorizontal={8}>
            About the challenge
          </I18nText>
          <YStack marginVertical='$5' gap={16}>
            <XStack
              justifyContent='space-between'
              alignItems='center'
              paddingHorizontal={8}>
              <XStack alignItems='center' gap='$2'>
                <Flame color='$grey2' />
                <I18nText color='$grey1'>Activity Types</I18nText>
              </XStack>
              <XStack
                gap={8}
                alignItems='center'
                onPress={handlePressActivities}>
                {challenge.activityTypes
                  .slice(0, DISPLAYED_ACTIVITY_TYPES)
                  .map((item) => {
                    return (
                      <YStack
                        width={24}
                        height={24}
                        alignItems='center'
                        justifyContent='center'
                        key={item.id}>
                        <ActivityIcon
                          name={item.name}
                          size={18}
                          color='$grey1'
                        />
                      </YStack>
                    );
                  })}
                {challenge.activityTypes.length > DISPLAYED_ACTIVITY_TYPES && (
                  <StyledText fontWeight='500' color='$primary'>
                    {`+${
                      challenge.activityTypes.length - DISPLAYED_ACTIVITY_TYPES
                    }`}
                  </StyledText>
                )}
              </XStack>
            </XStack>
            <XStack
              backgroundColor='$backgroundHighlight'
              padding={8}
              gap={8}
              borderRadius={8}>
              <Calendar color='$grey2' />
              <YStack>
                <I18nText color='$grey1'>Dates</I18nText>
                <StyledText fontSize={12} fontWeight='700' marginTop={8}>
                  {challengeDate(
                    challenge.startDate,
                    challenge.endDate,
                    locales.timezone,
                  )}
                </StyledText>
                <TimeStatus
                  secondsToStart={challenge.secondsToStart}
                  secondsRemaining={challenge.secondsRemaining}
                />
              </YStack>
            </XStack>
            <XStack
              justifyContent='space-between'
              alignItems='center'
              paddingHorizontal={8}>
              <XStack alignItems='center' gap='$2'>
                <Flag color='$grey2' />
                <I18nText color='$grey1'>Challenge Type</I18nText>
              </XStack>
              <I18nText color='$color' fontSize={12} fontWeight='600'>
                {challenge.event.name}
              </I18nText>
            </XStack>
            <XStack
              justifyContent='space-between'
              alignItems='center'
              paddingHorizontal={8}>
              <XStack alignItems='center' gap='$2'>
                <Watch color='$grey2' />
                <I18nText color='$grey1'>Wearables only?</I18nText>
              </XStack>
              <I18nText color='$color' fontSize={12} fontWeight='600'>
                {challenge.wearablesOnly ? 'Yes' : 'No'}
              </I18nText>
            </XStack>
            <XStack
              justifyContent='space-between'
              alignItems='center'
              paddingHorizontal={8}>
              <XStack alignItems='center' gap='$2'>
                <Users2 color='$grey2' />
                <I18nText color='$grey1'>Team Size</I18nText>
              </XStack>
              {challenge.maxTeamSize ? (
                <StyledText color='$color' fontSize={12} fontWeight='600'>
                  1-{challenge.maxTeamSize}
                </StyledText>
              ) : (
                <I18nText color='$color' fontSize={12} fontWeight='600'>
                  Unlimited
                </I18nText>
              )}
            </XStack>
          </YStack>
          {!!challenge.description && (
            <YStack onPress={handlePressDescription} paddingHorizontal={8}>
              <StyledText
                numberOfLines={3}
                ellipsizeMode='tail'
                fontSize={12}
                lineHeight={16.8}
                fontWeight='500'
                color='$grey1'>
                {challenge.description}
              </StyledText>
              <XStack
                position='absolute'
                right={8}
                bottom={0}
                backgroundColor='$background'
                gap={2}>
                <StyledText
                  numberOfLines={3}
                  ellipsizeMode='tail'
                  fontSize={12}
                  fontWeight='500'
                  color='$grey1'>
                  ...
                </StyledText>
                <I18nText color='$primary' fontSize={12} fontWeight='500'>
                  Read more
                </I18nText>
              </XStack>
            </YStack>
          )}
        </YStack>

        <YStack
          backgroundColor='$backgroundHighlight'
          padding='$3.5'
          borderRadius={16}
          borderWidth={1}
          borderColor='$borderHighlight'>
          <CodeIcon
            color='$warning'
            size={56}
            position='absolute'
            top={8}
            right={8}
          />
          <I18nText fontSize={16} fontWeight='700' color='$grey1'>
            The Joe Code
          </I18nText>
          <I18nText fontWeight='500' color='$grey1'>
            (Code of Ethics)
          </I18nText>
          <YStack marginVertical={16} gap={12}>
            <XStack gap={8}>
              <Check color='$green' />
              <StyledText flex={1} color='$grey1'>
                <Trans
                  i18nKey="<b>GoJoe Spirit:</b> Stay positive and never give in. It's the GoJoe way!"
                  components={{
                    b: <StyledText fontWeight='700' color='$grey1' />,
                  }}
                />
              </StyledText>
            </XStack>
            <XStack gap={8}>
              <Check color='$green' />
              <StyledText flex={1} color='$grey1'>
                <Trans
                  i18nKey="<b>No Shortcuts:</b> Cheating isn’t cool. Joes really compete with yesterday's us, not each other."
                  components={{
                    b: <StyledText fontWeight='700' color='$grey1' />,
                  }}
                />
              </StyledText>
            </XStack>
            <XStack gap={8}>
              <Check color='$green' />
              <StyledText flex={1} color='$grey1'>
                <Trans
                  i18nKey='<b>Respect:</b> Competition is fun, respect is fundamental. We thrive together!'
                  components={{
                    b: <StyledText fontWeight='700' color='$grey1' />,
                  }}
                />
              </StyledText>
            </XStack>
            <XStack gap={8}>
              <Check color='$green' />
              <StyledText flex={1} color='$grey1'>
                <Trans
                  i18nKey='<b>Never Leave a Joe Behind:</b> We’re a team. We lift weights and each other!'
                  components={{
                    b: <StyledText fontWeight='700' color='$grey1' />,
                  }}
                />
              </StyledText>
            </XStack>
            <XStack gap={8}>
              <Check color='$green' />
              <StyledText flex={1} color='$grey1'>
                <Trans
                  i18nKey="<b>Fun:</b> If it's not fun, it's not GoJoe. Keep things light."
                  components={{
                    b: <StyledText fontWeight='700' color='$grey1' />,
                  }}
                />
              </StyledText>
            </XStack>
          </YStack>
          <I18nText fontSize={16} fontWeight='700' color='$grey1'>
            Don't be an ordinary joe, be GoJoe!
          </I18nText>
        </YStack>
        <XStack
          onPress={handlePressFairPlay}
          alignItems='center'
          backgroundColor='$backgroundHighlight'
          borderWidth={1}
          height={44}
          paddingHorizontal='$3.5'
          gap={8}
          borderRadius={16}
          borderColor='$borderHighlight'>
          <Handshake color='$grey1' />
          <I18nText flex={1} color='$grey1' fontWeight='600'>
            The Fair Play Book of Rules
          </I18nText>
          <ChevronRight color='$grey1' />
        </XStack>
        <NeedHelp />
      </YStack>
    </ScrollView>
  );
};

export default memo(ChallengeOverview);
