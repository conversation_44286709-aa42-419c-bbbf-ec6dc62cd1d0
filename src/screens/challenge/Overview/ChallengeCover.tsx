import React, {memo} from 'react';
import {StyleSheet} from 'react-native';
import {Image} from 'expo-image';
import {XStack, YStack} from 'tamagui';
import {useRouter} from 'expo-router';
import {ChallengeDetailsDto} from '@gojoe/typescript-sdk';
import {FontAwesome} from '@expo/vector-icons';
import ChallengeMap from '@/src/components/ChallengeMap';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';
import useChallengeInsights from '@/src/hooks/api/useChallengeInsights';

interface Props {
  challenge: ChallengeDetailsDto;
  mapId?: string;
  cover?: string;
}
const ChallengeCover: React.FC<Props> = ({challenge}) => {
  const router = useRouter();
  const {data} = useChallengeInsights(challenge.id);

  const {id, mapId, cover} = challenge;

  if (mapId) {
    const handleOnPress = async () => {
      await triggerHaptics();
      router.push({
        pathname: '/map/[mapId]',
        params: {
          mapId: mapId,
          challengeId: id,
          overallPoints: data ? `${data.totalPoints}` : '0',
        },
      });
    };
    return (
      <YStack height={420} width='100%'>
        <ChallengeMap
          mapId={mapId}
          challengeId={id}
          overallPoints={parseInt(data ? `${data.totalPoints}` : '0')}
          style={{width: '100%', height: 420}}
        />
        <XStack
          onPress={handleOnPress}
          backgroundColor='#00000050'
          borderColor='$white1'
          borderWidth={1}
          borderRadius={16}
          height={32}
          position='absolute'
          bottom={64}
          right={24}
          justifyContent='center'
          alignItems='center'
          paddingHorizontal={12}
          gap={8}
          zIndex={2}>
          <FontAwesome name='arrows-alt' color='#FFFFFF' />
          <I18nText color='#FFFFFF' fontWeight='700'>
            Show Map
          </I18nText>
        </XStack>
      </YStack>
    );
  }
  const imageSource = cover
    ? {uri: cover}
    : require('@/assets/images/challenge_cover.jpg');

  return <Image source={imageSource} style={styles.cover} />;
};

const styles = StyleSheet.create({
  cover: {
    width: '100%',
    height: 240,
    justifyContent: 'center',
  },
});
export default memo(
  ChallengeCover,
  (prev, next) => prev.challenge.id === next.challenge.id,
);
