import React from 'react';
import {ChallengeTeamsStandingsItemDto} from '@gojoe/typescript-sdk';
import {XStack, YStack} from 'tamagui';
import StyledText from '@/src/components/UI/StyledText';
import TeamAvatar from '@/src/components/UI/Avatar/TeamAvatar';
import I18nText from '@/src/components/I18nText';
import {ChallengeEventType} from '@/src/utils/challenge';
import {DistanceUnit} from '@/src/contexts/LocaleContext';
import convert from 'convert';
import {triggerHaptics} from '@/src/utils/haptics';
import {useRouter} from 'expo-router';

interface Props {
  item: ChallengeTeamsStandingsItemDto;
  columnType: ChallengeEventType;
  unit: DistanceUnit;
  challengeId: string;
  highlighted?: boolean;
}

const TeamItem: React.FC<Props> = ({
  item,
  challengeId,
  columnType,
  unit,
  highlighted = false,
}) => {
  const router = useRouter();

  const onPress = async () => {
    await triggerHaptics();
    router.push({
      pathname: '/team/[teamId]',
      params: {
        teamId: item.team.id,
        challengeId: challengeId,
      },
    });
  };

  return (
    <XStack
      onPress={onPress}
      borderRadius={8}
      alignItems='center'
      height={56}
      gap={8}
      borderWidth={highlighted ? 1 : 0}
      borderColor='$borderHighlight'
      backgroundColor={highlighted ? '$backgroundHighlight' : '$background'}
      paddingHorizontal='$3.5'
      marginBottom={4}>
      <StyledText
        fontWeight='700'
        fontSize={12}
        color={highlighted ? '$black1' : '$color'}>
        {item.rank}
      </StyledText>
      <TeamAvatar team={item.team} size={36} circular />
      <YStack justifyContent='center' gap={2} flex={1}>
        <StyledText
          fontSize={12}
          fontWeight='500'
          color={highlighted ? '$black1' : '$color'}
          numberOfLines={1}>
          {item.team.name}
        </StyledText>
        <I18nText
          fontSize={12}
          fontWeight='500'
          color='$grey2'
          i18nParams={{count: item.team.usersCount}}>
          {`{{count}} joe`}
        </I18nText>
      </YStack>
      <StyledText
        fontWeight='700'
        fontSize={12}
        color={highlighted ? '$black1' : '$color'}>
        {columnType === ChallengeEventType.Distance
          ? Intl.NumberFormat('en-US', {
              notation: 'compact',
              compactDisplay: 'short',
              maximumFractionDigits: 2,
            }).format(convert(item.distance ?? 0, 'm').to(unit))
          : Intl.NumberFormat('en-US', {
              notation: 'compact',
              compactDisplay: 'short',
              maximumFractionDigits: 2,
            }).format(item.points ?? 0)}
      </StyledText>
    </XStack>
  );
};

export default TeamItem;
