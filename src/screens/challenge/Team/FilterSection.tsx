import React from 'react';
import {useTranslation} from 'react-i18next';
import {Switch, XStack, YStack} from 'tamagui';
import {ChallengeFilters} from '@/src/constants/challenge';
import I18nText from '@/src/components/I18nText';
import {ClearCircleIcon, QuestionCircleIcon} from '@/src/components/GoJoeIcon';
import {StyledInput} from '@/src/components/UI/StyledInput';
import {ChevronDown} from '@tamagui/lucide-icons';
import {triggerHaptics} from '@/src/utils/haptics';
import {SheetManager} from 'react-native-actions-sheet';
import {ActivityTypeListDto} from '@gojoe/typescript-sdk';

interface Props {
  filters: ChallengeFilters;
  setFilters: React.Dispatch<React.SetStateAction<ChallengeFilters>>;
  activityTypes: ActivityTypeListDto[];
}

const FilterSection: React.FC<Props> = ({
  filters,
  setFilters,
  activityTypes,
}) => {
  const {t} = useTranslation();

  const onCheckedChange = () => {
    setFilters((prev) => ({
      ...prev,
      withFlaggedUsers: !prev.withFlaggedUsers,
    }));
  };

  const onChangeText = (text: string) => {
    setFilters((prev) => ({
      ...prev,
      name: text,
    }));
  };

  const onClearActivities = () => {
    setFilters((prev) => ({
      ...prev,
      activityTypes: [],
    }));
  };

  const handlePressUnfilteredLeaderboardHelp = async () => {
    await triggerHaptics();
    await SheetManager.show('info_unfiltered');
  };

  const handlePressActivityType = async () => {
    await triggerHaptics();
    await SheetManager.show('challenge_activity_type_list', {
      payload: {
        activities: activityTypes,
        selectedActivities: filters.activityTypes,
        onSelect: (activity: ActivityTypeListDto) => {
          setFilters((prev) => ({
            ...prev,
            activityTypes: [activity],
          }));
        },
      },
    });
  };

  return (
    <YStack height={150} gap='$3.5'>
      <XStack alignItems='center' gap={8}>
        <Switch
          size='$2'
          id='PRIVATE_ACCOUNT'
          onCheckedChange={onCheckedChange}
          backgroundColor={filters.withFlaggedUsers ? '$green' : '$grey3'}
          checked={filters.withFlaggedUsers}>
          <Switch.Thumb animation='bouncy' backgroundColor='$white1' />
        </Switch>
        <I18nText fontWeight='500' color='$grey1'>
          Unfiltered leaderboard
        </I18nText>
        <QuestionCircleIcon
          size={18}
          color='$grey1'
          onPress={handlePressUnfilteredLeaderboardHelp}
        />
      </XStack>
      <XStack
        backgroundColor='$background'
        height={48}
        borderRadius={16}
        borderWidth={1}
        alignItems='center'
        paddingHorizontal={16}
        borderColor='$grey3'>
        <I18nText
          fontWeight='500'
          color='$grey1'
          flex={1}
          onPress={handlePressActivityType}>
          {filters.activityTypes.length
            ? filters.activityTypes[0].name
            : 'All activities'}
        </I18nText>
        <XStack alignItems='center' gap={8}>
          {filters.activityTypes.length > 0 && (
            <ClearCircleIcon
              color='$grey2'
              size={18}
              onPress={onClearActivities}
            />
          )}
          <ChevronDown color='$grey2' onPress={handlePressActivityType} />
        </XStack>
      </XStack>
      <StyledInput
        onChangeText={onChangeText}
        placeholder={t('Search by team')}
        clearButtonMode='always'
        value={filters.name}
      />
    </YStack>
  );
};

export default FilterSection;
