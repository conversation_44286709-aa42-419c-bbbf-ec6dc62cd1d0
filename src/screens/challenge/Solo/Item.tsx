import React from 'react';
import {ChallengeUserStandingsItemDto} from '@gojoe/typescript-sdk';
import {XStack, YStack} from 'tamagui';
import StyledText from '@/src/components/UI/StyledText';
import {ChallengeEventType} from '@/src/utils/challenge';
import {DistanceUnit} from '@/src/contexts/LocaleContext';
import convert from 'convert';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';
import {useRouter} from 'expo-router';
import {triggerHaptics} from '@/src/utils/haptics';

interface Props {
  item: ChallengeUserStandingsItemDto;
  challengeId: string;
  columnType: ChallengeEventType;
  unit: DistanceUnit;
  highlighted?: boolean;
}

const SoloItem: React.FC<Props> = ({
  item,
  challengeId,
  columnType,
  unit,
  highlighted = false,
}) => {
  const router = useRouter();
  const onPress = async () => {
    await triggerHaptics();
    router.push({
      pathname: '/challenge/[challengeId]/[userId]',
      params: {
        challengeId: challengeId,
        userId: item.user.id,
      },
    });
  };
  return (
    <XStack
      onPress={onPress}
      borderRadius={8}
      alignItems='center'
      height={56}
      gap={8}
      borderWidth={highlighted ? 1 : 0}
      borderColor='$borderHighlight'
      backgroundColor={highlighted ? '$backgroundHighlight' : '$background'}
      paddingHorizontal='$3.5'
      marginBottom={4}>
      <StyledText
        fontWeight='700'
        fontSize={12}
        color={highlighted ? '$black1' : '$color'}>
        {item.rank}
      </StyledText>
      <UserAvatar user={item.user} size={36} circular />
      <YStack justifyContent='center' gap={2} flex={1}>
        <StyledText
          fontSize={12}
          fontWeight='500'
          color={highlighted ? '$black1' : '$color'}
          numberOfLines={1}>
          {item.user.name}
        </StyledText>
        {item.team && (
          <StyledText fontSize={12} fontWeight='500' color='$grey2'>
            {item.team.name}
          </StyledText>
        )}
      </YStack>
      <StyledText
        fontWeight='700'
        fontSize={12}
        color={highlighted ? '$black1' : '$color'}>
        {columnType === ChallengeEventType.Distance
          ? Intl.NumberFormat('en-US', {
              notation: 'compact',
              compactDisplay: 'short',
              maximumFractionDigits: 2,
            }).format(convert(item.distance ?? 0, 'm').to(unit))
          : Intl.NumberFormat('en-US', {
              notation: 'compact',
              compactDisplay: 'short',
              maximumFractionDigits: 2,
            }).format(item.points ?? 0)}
      </StyledText>
    </XStack>
  );
};

export default SoloItem;
