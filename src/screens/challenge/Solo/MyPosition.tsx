import React from 'react';
import {useSession} from '@/src/contexts/SessionContext';
import {useChallengeLeaderboardUsers} from '@/src/hooks/api/useChallengeLeaderboardUsers';
import Item from '@/src/screens/challenge/Solo/Item';
import {ChallengeEventType} from '@/src/utils/challenge';
import {DistanceUnit} from '@/src/contexts/LocaleContext';
import {ChallengeFilters} from '@/src/constants/challenge';
import I18nText from '@/src/components/I18nText';
import {YStack} from 'tamagui';

interface Props {
  challengeId: string;
  columnType: ChallengeEventType;
  unit: DistanceUnit;
  filters: ChallengeFilters;
}

const MyPosition: React.FC<Props> = ({...props}) => {
  const {user} = useSession();
  if (!user) {
    return null;
  }
  return <Position {...props} userId={user.id} />;
};

const Position: React.FC<Props & {userId: string}> = ({
  challengeId,
  filters,
  columnType,
  unit,
  userId,
}) => {
  const {data} = useChallengeLeaderboardUsers({
    challengeId: challengeId,
    sports: filters.activityTypes.map((s) => s.name),
    userId: userId,
    withFlaggedUsers: filters.withFlaggedUsers,
  });

  if (!data || data.length === 0) {
    return null;
  }

  return (
    <YStack gap={8} marginBottom='$5'>
      <I18nText fontWeight='700' fontSize={12}>
        Your position
      </I18nText>
      <Item
        item={data[0]}
        challengeId={challengeId}
        columnType={columnType}
        unit={unit}
        highlighted
      />
    </YStack>
  );
};
export default MyPosition;
