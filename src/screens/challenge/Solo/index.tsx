import React, {memo, useCallback, useEffect, useState} from 'react';
import {<PERSON><PERSON>, XStack, YStack} from 'tamagui';
import convert from 'convert';

import {
  ActivityTypeListDto,
  ChallengeUserStandingsItemDto,
} from '@gojoe/typescript-sdk';
import {ChallengeEventType} from '@/src/utils/challenge';
import {DistanceUnit} from '@/src/contexts/LocaleContext';
import AnimatedLeaderBoard, {
  Leaderboard,
} from '@/src/components/AnimatedLeaderboard';
import {shuffleArray} from '@/src/utils/array';
import {useChallengeLeaderboardUsers} from '@/src/hooks/api/useChallengeLeaderboardUsers';

import Item from './Item';
import MyPosition from '@/src/screens/challenge/Solo/MyPosition';
import {useSession} from '@/src/contexts/SessionContext';
import I18nText from '@/src/components/I18nText';
import StyledButton from '@/src/components/UI/Button';
import {Search, X as CloseIcon} from '@tamagui/lucide-icons';
import {triggerHaptics} from '@/src/utils/haptics';
import {ChallengeFilters} from '@/src/constants/challenge';
import FilterSection from './FilterSection';
import Animated, {
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import {createAnimatedFlashList} from '@/src/components/AnimatedFlashList';

const AnimatedFlashList =
  createAnimatedFlashList<ChallengeUserStandingsItemDto>();

interface Props {
  challengeId: string;
  inChallenge: boolean;
  columnType: ChallengeEventType;
  unit: DistanceUnit;
  activityTypes: ActivityTypeListDto[];
}

const ChallengeSoloStandings: React.FC<Props> = ({
  challengeId,
  inChallenge,
  columnType,
  unit,
  activityTypes,
}) => {
  const {user} = useSession();
  const [filters, setFilters] = useState<ChallengeFilters>({
    name: '',
    activityTypes: [],
    withFlaggedUsers: false,
    showFilterUI: false,
  });
  const [topLeaderboard, setTopLeaderboard] = useState<Leaderboard[]>([]);
  const scrollY = useSharedValue(0);
  const HEADER_CONTENT_OFFSET = inChallenge ? 385 : 280;

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  const headerAnimatedStyle = useAnimatedStyle(() => {
    // Calculate how much of the header should be hidden based on scroll position
    // This creates a direct movement effect as you scroll up
    const translateY = Math.min(
      Math.max(-scrollY.value, -HEADER_CONTENT_OFFSET),
      0,
    );

    return {
      transform: [
        {
          // Use the calculated translateY directly without timing
          // for a natural scroll-linked animation
          translateY: translateY,
        },
      ],
      // No opacity animation - header stays fully visible until it moves off screen
    };
  });

  const {
    data,
    isLoading,
    isRefetching,
    refetch,
    fetchNextPage,
    isFetchingNextPage,
  } = useChallengeLeaderboardUsers({
    challengeId: challengeId,
    sports: filters.activityTypes.map((s) => s.name),
    name: filters.name,
    withFlaggedUsers: filters.withFlaggedUsers,
  });

  useEffect(() => {
    if (data.length === 0 || topLeaderboard.length > 0) return;

    const sliced = data.slice(0, 7);
    const shuffled = shuffleArray(sliced);

    const items = shuffled.map((item) => ({
      id: item.team.id,
      name: item.user.name,
      avatar: item.user.profilePicture,
      score:
        columnType === ChallengeEventType.Distance
          ? convert(item.distance ?? 0, 'm').to(unit)
          : (item.points ?? 0),
    }));

    setTopLeaderboard(items);
  }, [data, topLeaderboard.length, columnType, unit]);

  const toggleFilters = useCallback(async () => {
    await triggerHaptics();
    setFilters((prev) => ({...prev, showFilterUI: !prev.showFilterUI}));
  }, []);

  const onEndReached = useCallback(() => fetchNextPage(), [fetchNextPage]);

  const renderItem = useCallback(
    ({item}: {item: ChallengeUserStandingsItemDto}) => {
      return (
        <Item
          item={item}
          challengeId={challengeId}
          columnType={columnType}
          unit={unit}
          highlighted={item.user.id === user?.id}
        />
      );
    },
    [challengeId, columnType, unit, user?.id],
  );

  const listHeaderComponent = useCallback(() => {
    if (isLoading) {
      return (
        <Spinner position='absolute' zIndex={1} top={35} alignSelf='center' />
      );
    }
    return null;
  }, [isLoading]);

  const listFooterComponent = useCallback(() => {
    if (isFetchingNextPage) {
      return <Spinner marginVertical='$3' />;
    }
    return <YStack height={48} />;
  }, [isFetchingNextPage]);

  return (
    <YStack flex={1} paddingHorizontal='$5'>
      <Animated.View
        style={[
          {
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            zIndex: 10,
          },
          headerAnimatedStyle,
        ]}>
        <YStack padding='$5'>
          {filters.showFilterUI ? (
            <FilterSection
              filters={filters}
              setFilters={setFilters}
              activityTypes={activityTypes}
            />
          ) : (
            <AnimatedLeaderBoard items={topLeaderboard} />
          )}
          <YStack marginTop='$5'>
            {inChallenge && (
              <MyPosition
                filters={filters}
                challengeId={challengeId}
                columnType={columnType}
                unit={unit}
              />
            )}
          </YStack>

          <XStack alignItems='center' justifyContent='space-between'>
            <XStack alignItems='center' gap={4}>
              <I18nText fontWeight='bold'>Leaderboard</I18nText>
            </XStack>
            <StyledButton
              onPress={toggleFilters}
              height={32}
              borderRadius={8}
              paddingHorizontal={8}
              icon={
                filters.showFilterUI ? (
                  <CloseIcon color='$white1' size={16} />
                ) : (
                  <Search color='$white1' size={16} />
                )
              }>
              <I18nText
                color='$white1'
                fontWeight='600'
                fontSize={12}
                paddingStart={4}>
                {filters.showFilterUI ? 'Hide filters' : 'Search'}
              </I18nText>
            </StyledButton>
          </XStack>
          <XStack height={12} paddingHorizontal='$3.5' gap={42} marginTop='$5'>
            <I18nText fontWeight='500' fontSize={10} color='$grey1'>
              Pos
            </I18nText>
            <I18nText fontWeight='500' fontSize={10} color='$grey1' flex={1}>
              Name
            </I18nText>
            <I18nText fontWeight='500' fontSize={10} color='$grey1'>
              {columnType === ChallengeEventType.Points ? 'Pts' : `${unit}`}
            </I18nText>
          </XStack>
        </YStack>
      </Animated.View>
      <AnimatedFlashList
        contentContainerStyle={{paddingTop: HEADER_CONTENT_OFFSET}}
        data={data}
        renderItem={renderItem}
        estimatedItemSize={56}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={listHeaderComponent}
        ListFooterComponent={listFooterComponent}
        refreshing={isRefetching}
        onRefresh={refetch}
        onEndReached={onEndReached}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
      />
    </YStack>
  );
};

export default memo(ChallengeSoloStandings);
