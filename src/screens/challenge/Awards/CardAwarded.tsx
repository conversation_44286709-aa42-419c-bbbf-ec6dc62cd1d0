import React from 'react';
import {StyleSheet} from 'react-native';
import {ImageBackground} from 'expo-image';
import I18nText from '@/src/components/I18nText';
import {TWO_BOX_WIDTH} from '@/src/constants/Constants';
import BoxTwo from '@/src/components/UI/BoxTwo';
import AwardIcon from '@/src/components/AwardIcon';

interface Props {
  name: string;
  description: string;
  onPress: () => void;
}

const CardAwarded: React.FC<Props> = ({name, description, onPress}) => {
  return (
    <BoxTwo height={200} onPress={onPress}>
      <ImageBackground
        source={require('@/assets/images/award_bg.png')}
        style={styles.cover}>
        <BoxTwo
          height={200}
          borderWidth={1}
          borderColor='$borderHighlight'
          backgroundColor={undefined}>
          <AwardIcon name={name} size={64} color='$yellow' />
          <I18nText
            color='$color'
            fontWeight='700'
            textAlign='center'
            marginTop={24}>
            {name}
          </I18nText>
          <I18nText
            marginTop={4}
            fontWeight='500'
            fontSize={12}
            color='$grey2'
            textAlign='center'>
            {description}
          </I18nText>
        </BoxTwo>
      </ImageBackground>
    </BoxTwo>
  );
};

const styles = StyleSheet.create({
  cover: {
    width: TWO_BOX_WIDTH,
    height: 200,
    overflow: 'hidden',
    borderRadius: 16,
  },
});

export default CardAwarded;
