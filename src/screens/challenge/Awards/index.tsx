import React, {memo, useState} from 'react';
import {XStack, YStack} from 'tamagui';
import MenuButton from '@/src/components/UI/MenuButton';
import TeamOverview from './TeamOverview';
import SoloOverview from './SoloOverview';

interface Props {
  challengeId: string;
  challengeStartDate: string;
  challengeEndDate: string;
}

const ChallengeAwards: React.FC<Props> = ({
  challengeId,
  challengeStartDate,
  challengeEndDate,
}) => {
  const [index, setIndex] = useState(0);
  const handlePressTeamAwards = () => {
    setIndex(0);
  };
  const handlePressSoloAwards = () => {
    setIndex(1);
  };
  return (
    <YStack flex={1}>
      <XStack gap={8} padding='$5'>
        <MenuButton
          label='Team Awards'
          active={index === 0}
          onPress={handlePressTeamAwards}
        />
        <MenuButton
          label='Solo Awards'
          active={index === 1}
          onPress={handlePressSoloAwards}
        />
      </XStack>
      {index === 0 ? (
        <TeamOverview
          challengeId={challengeId}
          challengeStartDate={challengeStartDate}
          challengeEndDate={challengeEndDate}
        />
      ) : (
        <SoloOverview
          challengeId={challengeId}
          challengeStartDate={challengeStartDate}
          challengeEndDate={challengeEndDate}
        />
      )}
    </YStack>
  );
};

export default memo(ChallengeAwards);
