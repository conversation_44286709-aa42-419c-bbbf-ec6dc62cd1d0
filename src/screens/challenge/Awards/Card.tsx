import React, {memo} from 'react';
import I18nText from '@/src/components/I18nText';
import BoxTwo from '@/src/components/UI/BoxTwo';
import AwardIcon from '@/src/components/AwardIcon';

interface Props {
  name: string;
  description: string;
  onPress: () => void;
}

const Card: React.FC<Props> = ({name, description, onPress}) => {
  return (
    <BoxTwo height={200} onPress={onPress}>
      <AwardIcon name={name} size={64} color='$grey3' />
      <I18nText
        color='$color'
        fontWeight='700'
        textAlign='center'
        marginTop={24}>
        {name}
      </I18nText>
      <I18nText
        marginTop={4}
        fontWeight='500'
        fontSize={12}
        color='$grey2'
        textAlign='center'>
        {description}
      </I18nText>
    </BoxTwo>
  );
};

export default memo(Card);
