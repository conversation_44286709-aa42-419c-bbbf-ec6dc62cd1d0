import React, {memo} from 'react';
import {<PERSON><PERSON>V<PERSON><PERSON>, Spin<PERSON>, <PERSON>Stack, YStack} from 'tamagui';
import StyledText from '@/src/components/UI/StyledText';
import useChallengeInsights from '@/src/hooks/api/useChallengeInsights';
import I18nText from '@/src/components/I18nText';
import {Dimensions} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Users, Shirt} from '@tamagui/lucide-icons';
import {MetricRing} from '@/src/components/MetricRing';
import TimeInsightCard from '@/src/components/TimeInsightCard';
import MostActiveTimeOfDay from '@/src/components/MostActiveTimeOfDay';
import DistanceInsightCard from '@/src/components/DistanceInsightCard';
import {DistanceUnit} from '@/src/contexts/LocaleContext';
import TopActivitiesChart from '@/src/components/TopActivitiesChart';

const width = Dimensions.get('window').width;
const boxSize = (width - 64) / 2;

interface Props {
  challengeId: string;
  unit: DistanceUnit;
}

const ChallengeInsights: React.FC<Props> = ({challengeId, unit}) => {
  const {bottom} = useSafeAreaInsets();
  const {data, isLoading} = useChallengeInsights(challengeId);

  if (isLoading) {
    return <Spinner marginTop='$5' />;
  }

  if (!data) {
    return null;
  }

  return (
    <ScrollView padding='$5' showsVerticalScrollIndicator={false}>
      <YStack flex={1} gap={16} paddingBottom={bottom}>
        <XStack gap='$3.5'>
          <YStack
            flex={1}
            padding='$5'
            backgroundColor='$background'
            borderRadius='$3.5'
            borderWidth={1}
            borderColor='$grey3'
            justifyContent='space-between'
            alignItems='flex-start'
            height={boxSize}>
            <XStack gap='$3'>
              <Users size={24} />
              <YStack>
                <I18nText
                  marginTop={4}
                  color='$grey1'
                  fontSize={12}
                  lineHeight={12}
                  fontWeight='500'>
                  Users
                </I18nText>
                <StyledText
                  color='$color'
                  fontWeight='700'
                  fontSize={18}
                  marginTop={4}
                  lineHeight={24}>
                  {Intl.NumberFormat('en-US', {
                    notation: 'compact',
                    compactDisplay: 'short',
                    maximumFractionDigits: 2,
                  }).format(data.totalUsers)}
                </StyledText>
              </YStack>
            </XStack>
            <XStack gap='$3'>
              <Shirt size={24} />
              <YStack>
                <I18nText
                  marginTop={4}
                  color='$grey1'
                  fontSize={12}
                  lineHeight={12}
                  fontWeight='500'>
                  Teams
                </I18nText>
                <StyledText
                  color='$color'
                  fontWeight='700'
                  fontSize={18}
                  marginTop={4}
                  lineHeight={24}>
                  {Intl.NumberFormat('en-US', {
                    notation: 'compact',
                    compactDisplay: 'short',
                    maximumFractionDigits: 2,
                  }).format(data.totalTeams)}
                </StyledText>
              </YStack>
            </XStack>
          </YStack>
          <MetricRing
            label='Engagement'
            value={data.engagement}
            size={boxSize}
          />
        </XStack>
        <TimeInsightCard totalSeconds={data.totalTime} />
        <MostActiveTimeOfDay activity={data.mostActive} />
        <DistanceInsightCard distanceMeters={data.totalDistance} unit={unit} />
        <TopActivitiesChart
          data={data.activitiesCount}
          total={data.totalActivities}
        />
      </YStack>
    </ScrollView>
  );
};

export default memo(ChallengeInsights);
