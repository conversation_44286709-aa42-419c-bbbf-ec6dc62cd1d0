import React from 'react';
import {ActivityIndicator} from 'react-native';
import {XStack} from 'tamagui';
import {Check} from '@tamagui/lucide-icons';

import {triggerHaptics} from '@/src/utils/haptics';
import useMutationCompleteJourneyEvent from '@/src/hooks/api/useMutationCompleteJourneyEvent';
import I18nText from '@/src/components/I18nText';
import StyledButton from '@/src/components/UI/Button';

interface Props {
  journeyId: string;
  eventId: string;
}

const CompleteEvent: React.FC<Props> = ({journeyId, eventId}) => {
  const {mutate, isPending} = useMutationCompleteJourneyEvent(
    journeyId,
    eventId,
  );

  const handlePressJoin = async () => {
    await triggerHaptics();
    mutate();
  };

  return (
    <StyledButton
      marginHorizontal='$5'
      marginTop='$3.5'
      onPress={handlePressJoin}>
      <I18nText fontSize={14} fontWeight='$7' color='#FFF'>
        Mark Completed
      </I18nText>
      <XStack position='absolute' right={12}>
        {isPending ? (
          <ActivityIndicator color='#FFF' />
        ) : (
          <Check size={18} color='#FFF' />
        )}
      </XStack>
    </StyledButton>
  );
};

export default CompleteEvent;
