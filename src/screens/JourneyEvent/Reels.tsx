import React, {useCallback} from 'react';
import {useRouter} from 'expo-router';
import {Image} from 'expo-image';
import {YStack} from 'tamagui';
import {FlashList, ListRenderItem} from '@shopify/flash-list';
import {ReelDto} from '@gojoe/typescript-sdk';

import StyledText from '@/src/components/UI/StyledText';
import {getResizeImage} from '@/src/utils/image';
import {triggerHaptics} from '@/src/utils/haptics';
import I18nText from '@/src/components/I18nText';

interface Props {
  reels?: ReelDto[];
  title?: string;
}

const Reels: React.FC<Props> = ({reels, title = 'Exercises Preview'}) => {
  const router = useRouter();

  const handlePressOnVideo = useCallback(
    async (videoUrl: string) => {
      if (!videoUrl) {
        return;
      }

      await triggerHaptics();
      router.push({
        pathname: '/video',
        params: {
          uri: videoUrl,
        },
      });
    },
    [router],
  );

  const renderItem: ListRenderItem<ReelDto> = useCallback(
    ({item}) => (
      <YStack
        width={96}
        marginHorizontal={8}
        borderRadius={8}
        overflow='hidden'
        onPress={() => handlePressOnVideo(item.video)}>
        <Image
          source={{uri: getResizeImage(item.image, 'auto', 244)}}
          style={{width: 96, height: 64, borderRadius: 8}}
        />
        <StyledText fontSize={12} fontWeight='$5' color='#888' marginTop='$1.5'>
          {item.name}
        </StyledText>
      </YStack>
    ),
    [handlePressOnVideo],
  );

  if (!reels || reels.length === 0) {
    return null;
  }

  return (
    <YStack flex={1} marginTop={40} marginBottom='$3.5'>
      <I18nText fontSize='$6' fontWeight='$7' marginBottom='$3.5'>
        {title}
      </I18nText>

      <FlashList
        data={reels}
        renderItem={renderItem}
        estimatedItemSize={80}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
      />
    </YStack>
  );
};

export default Reels;
