import React from 'react';
import {useRouter} from 'expo-router';
import {Image} from 'expo-image';
import {XStack, YStack} from 'tamagui';
import {Ionicons} from '@expo/vector-icons';
import {JourneyStepsDto} from '@gojoe/typescript-sdk';

import {colorHash} from '@/src/utils/image';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';

interface Props {
  event: JourneyStepsDto;
}

const Preview: React.FC<Props> = ({event}) => {
  const router = useRouter();

  const handlePressOnVideo = async () => {
    if (!event.videoUrl) {
      return;
    }

    await triggerHaptics();
    router.push({
      pathname: '/video',
      params: {
        uri: event.videoUrl,
      },
    });
  };

  return (
    <YStack
      height={220}
      width='100%'
      position='relative'
      onPress={handlePressOnVideo}>
      {event.cover ? (
        <Image
          source={{uri: event.cover}}
          style={{width: '100%', height: 220}}
        />
      ) : (
        <XStack height={220} backgroundColor={colorHash.hex(event.id)} />
      )}

      {event.videoUrl && (
        <XStack
          alignItems='center'
          position='absolute'
          top='50%'
          left='50%'
          transform={[{translateX: -50}, {translateY: -10}]}
          backgroundColor='$background'
          borderRadius={16}
          paddingHorizontal={8}
          paddingVertical={6}
          gap='$2'
          zIndex={1}>
          <Ionicons name='play-circle' color='#2F3542' size={14} />
          <I18nText fontSize={11} fontWeight='$7' color='#2F3542'>
            PLAY
          </I18nText>
        </XStack>
      )}
    </YStack>
  );
};

export default Preview;
