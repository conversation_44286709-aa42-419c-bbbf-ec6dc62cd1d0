import React from 'react';
import {XStack} from 'tamagui';
import {FontAwesome, Ionicons} from '@expo/vector-icons';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';
import {SheetManager} from 'react-native-actions-sheet';

interface Props {
  journeyId: string;
  eventId: string;
}

const FooterMenu: React.FC<Props> = ({journeyId, eventId}) => {
  const handlePressOptions = async () => {
    await triggerHaptics();
    await SheetManager.show('journey_event_options', {
      payload: {journeyId: journeyId, eventId: eventId},
    });
  };

  return (
    <XStack
      justifyContent='space-between'
      marginHorizontal='$5'
      marginTop='$3.5'
      backgroundColor='$background'>
      <XStack
        height={48}
        width={48}
        borderWidth={1}
        borderColor='#888'
        borderRadius={8}
        justifyContent='center'
        alignItems='center'
        marginRight='$3.5'
        onPress={handlePressOptions}>
        <Ionicons name='ellipsis-horizontal' size={18} color='#888888' />
      </XStack>

      <XStack alignItems='center'>
        <I18nText fontSize={14} fontWeight='$7' color='#0BB87A' marginRight={8}>
          Completed
        </I18nText>

        <FontAwesome name='check-circle' size={24} color='#0BB87A' />
      </XStack>
    </XStack>
  );
};

export default FooterMenu;
