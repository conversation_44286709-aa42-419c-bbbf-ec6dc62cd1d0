import React, {useCallback} from 'react';
import {Spinner, YStack} from 'tamagui';
import useClubsMyClubs from '@/src/hooks/api/useClubsMyClubs';
import ApiFetchError from '@/src/components/ApiFetchError';
import {ListRenderItem} from '@shopify/flash-list';
import {ClubDto} from '@gojoe/typescript-sdk';
import ListEmptyComponent from '@/src/screens/ClubsMy/ListEmptyComponent';
import RecommendedClubs from '@/src/components/RecommededClubs';
import ClubItemHorizontal from '@/src/components/ClubItemHorizontal';
import {useRouter} from 'expo-router';
import {triggerHaptics} from '@/src/utils/haptics';
import {SharedValue, useAnimatedScrollHandler} from 'react-native-reanimated';
import {createAnimatedFlashList} from '@/src/components/AnimatedFlashList';

const AnimatedFlashList = createAnimatedFlashList<ClubDto>();

interface Props {
  scrollY: SharedValue<number>;
}
const MyClubs: React.FC<Props> = ({scrollY}) => {
  const router = useRouter();
  const {data, isLoading, error, isRefetching, refetch} = useClubsMyClubs();

  const onScroll = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  const keyExtractor = useCallback((item: ClubDto) => item.id, []);

  const renderItem = useCallback<ListRenderItem<ClubDto>>(
    ({item}) => {
      const onPress = async () => {
        await triggerHaptics();
        router.push({
          pathname: '/club/[clubId]',
          params: {clubId: item.id},
        });
      };
      return (
        <YStack marginBottom={8} marginHorizontal='$5' onPress={onPress}>
          <ClubItemHorizontal club={item} />
        </YStack>
      );
    },
    [router],
  );

  const listHeaderComponent = useCallback(() => {
    if (isLoading) {
      return <Spinner marginTop='$3' />;
    }
    return <YStack height={24} />;
  }, [isLoading]);

  if (isLoading) {
    return (
      <YStack flex={1} justifyContent='center'>
        <Spinner size='large' />
      </YStack>
    );
  }

  if (error) {
    return <ApiFetchError error={error} />;
  }

  return (
    <YStack flex={1}>
      <AnimatedFlashList
        ListEmptyComponent={ListEmptyComponent}
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        showsVerticalScrollIndicator={false}
        estimatedItemSize={80}
        ListFooterComponent={<RecommendedClubs />}
        refreshing={isRefetching}
        onRefresh={refetch}
        onScroll={onScroll}
        scrollEventThrottle={16}
        ListHeaderComponent={listHeaderComponent}
      />
    </YStack>
  );
};

export default MyClubs;
