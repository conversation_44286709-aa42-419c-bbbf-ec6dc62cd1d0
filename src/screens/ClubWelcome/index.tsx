import React from 'react';
import {useWindowDimensions, StyleSheet} from 'react-native';
import RenderHtml from 'react-native-render-html';
import {ScrollView, YStack} from 'tamagui';
import {ClubDto, ClubDtoCategoryEnum} from '@gojoe/typescript-sdk';
import StyledText from '@/src/components/UI/StyledText';
import I18nText from '@/src/components/I18nText';
import ClubActivities from '@/src/components/ClubActivities';
import ClubMembersAvatars from '@/src/components/ClubMembersAvatars';
import ClubStats from '@/src/components/ClubStats';
import StyledButton from '@/src/components/UI/Button';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import useMutationJoinClub from '@/src/hooks/api/useMutationJoinClub';
import {triggerHaptics} from '@/src/utils/haptics';
import Mapbox from '@/src/services/mapbox';

interface Props {
  club: ClubDto;
}

const ClubWelcome: React.FC<Props> = ({club}) => {
  const {bottom} = useSafeAreaInsets();
  const {width} = useWindowDimensions();
  const {mutateAsync, isPending} = useMutationJoinClub(club.id);

  const handlePressJoinClub = async () => {
    await triggerHaptics();
    await mutateAsync();
  };
  return (
    <YStack flex={1}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        flex={1}
        flexGrow={1}
        padding={'$5'}>
        <StyledText fontSize={24} fontWeight='700'>
          {club.name}
        </StyledText>
        {!!club.description && (
          <YStack marginTop={16}>
            <RenderHtml
              contentWidth={width - 48}
              source={{html: club.description}}
            />
          </YStack>
        )}
        {!!club.location && (
          <YStack flex={1} height={180} marginTop={40}>
            <I18nText
              fontSize={10}
              lineHeight={14}
              fontWeight='700'
              textTransform='uppercase'
              marginBottom={8}>
              Location
            </I18nText>
            <Mapbox.MapView style={styles.map} logoEnabled={false}>
              <Mapbox.Camera
                zoomLevel={12}
                animationMode={'flyTo'}
                animationDuration={0}
                centerCoordinate={club.location as [number, number]}
              />
            </Mapbox.MapView>
          </YStack>
        )}
        {club.category === ClubDtoCategoryEnum.Activities &&
          club.activities.length > 0 && (
            <YStack marginTop='$5'>
              <I18nText
                fontSize={10}
                lineHeight={14}
                fontWeight='700'
                textTransform='uppercase'
                marginBottom={8}>
                Activities
              </I18nText>
              <ClubActivities club={club} />
            </YStack>
          )}
        {club.memberCount > 0 && (
          <>
            <YStack marginTop='$5'>
              <I18nText
                fontSize={10}
                lineHeight={14}
                fontWeight='700'
                textTransform='uppercase'
                marginBottom={8}>
                Members
              </I18nText>
              <ClubMembersAvatars clubId={club.id} limit={10} />
            </YStack>
            <YStack marginTop='$5'>
              <I18nText
                fontSize={10}
                lineHeight={14}
                fontWeight='700'
                textTransform='uppercase'
                marginBottom={8}>
                Group Activity
              </I18nText>
              <ClubStats club={club} />
            </YStack>
          </>
        )}
      </ScrollView>
      <YStack paddingHorizontal={'$5'}>
        <StyledButton
          marginBottom={bottom}
          onPress={handlePressJoinClub}
          loading={isPending}>
          <I18nText color='$white1'>Join Club</I18nText>
        </StyledButton>
      </YStack>
    </YStack>
  );
};

const styles = StyleSheet.create({
  map: {
    flex: 1,
    zIndex: 0,
  },
});
export default ClubWelcome;
