import React, {useCallback, useRef, useEffect, useMemo} from 'react';
import {FlashList} from '@shopify/flash-list';
import {PostListDto} from '@gojoe/typescript-sdk';
import {Spin<PERSON>, YStack} from 'tamagui';

import useFeed from '@/src/hooks/api/useFeed';
import PostCard from '@/src/components/PostCard';
import {DistanceUnit} from '@/src/contexts/LocaleContext';
import {useSession} from '@/src/contexts/SessionContext';
import {WeekInNumbers} from '@/src/components/WeekInNumbers';
import ForYouFeedSection from '@/src/components/ForYouFeedSection';
import FeedEmptyState from '@/src/components/FeedEmptyState';
import FeedInjector from '@/src/components/FeedInjector';

interface Props {
  unit: DistanceUnit;
  languageTag?: string;
}

// Create a global ref to access the home screen list from anywhere
export const homeListRef = {
  current: null as FlashList<PostListDto> | null,
  scrollToTop: () => {
    homeListRef.current?.scrollToOffset({offset: 0, animated: true});
  },
};

const HomeScreen: React.FC<Props> = ({languageTag, unit}) => {
  const {user, business} = useSession();
  const {
    isLoading,
    isRefetching,
    fullData,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
  } = useFeed();

  // Flatten the feed data with memoization
  const flatData = useMemo(() => {
    return fullData ? fullData.pages.flatMap((page) => page.data) : [];
  }, [fullData]);

  // Create a local ref for the FlashList and sync it with the global ref
  const listRef = useRef<FlashList<PostListDto>>(null);
  useEffect(() => {
    homeListRef.current = listRef.current;
  }, []);

  const renderItem = useCallback(
    ({item, index}: {item: PostListDto; index: number}) => (
      <YStack>
        <YStack paddingHorizontal='$5' marginTop={8}>
          <PostCard post={item} languageTag={languageTag} unit={unit} />
        </YStack>
        <FeedInjector
          index={index}
          total={flatData.length}
          businessId={business?.id}
        />
      </YStack>
    ),
    [flatData.length, languageTag, unit, business?.id],
  );

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const renderFooter = () => {
    if (!isFetchingNextPage) return null;
    return (
      <YStack paddingVertical='$4' alignItems='center'>
        <Spinner size='small' color='$color' />
      </YStack>
    );
  };

  const renderHeader = () => (
    <YStack>
      <YStack
        padding='$4'
        paddingBottom={100}
        backgroundColor='$background'
        borderBottomWidth={1}
        borderColor='$grey3'>
        {isLoading && (
          <YStack alignItems='center' paddingVertical='$4'>
            <Spinner size='large' color='$color' />
          </YStack>
        )}
        <WeekInNumbers />
      </YStack>
      <YStack marginTop={-80} marginBottom={16}>
        {user && <ForYouFeedSection user={user} />}
      </YStack>
    </YStack>
  );

  return (
    <YStack flex={1}>
      <FlashList
        ref={listRef}
        data={flatData}
        extraData={flatData}
        renderItem={renderItem}
        estimatedItemSize={280}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        onEndReached={onEndReached}
        ListFooterComponent={renderFooter}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={!isLoading ? <FeedEmptyState /> : null}
        refreshing={isRefetching}
        onRefresh={refetch}
      />
    </YStack>
  );
};

export default HomeScreen;
