import React, {memo} from 'react';
import {Spinner} from 'tamagui';
import useClubChatChannel from '@/src/hooks/api/useClubChatChannel';
import ChatChannel from '@/src/components/ChatChannel';
import {ImageBackground} from 'expo-image';
import I18nText from '@/src/components/I18nText';

interface Props {
  clubId: string;
}

const ClubChat: React.FC<Props> = ({clubId}) => {
  const {data, isLoading} = useClubChatChannel(clubId);

  if (isLoading) {
    return <Spinner marginTop='$5' />;
  }

  if (!data) {
    return (
      <ImageBackground
        source={require('@/assets/images/chat_skeleton_background.jpg')}
        style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <I18nText fontSize={24} fontWeight='700'>
          Whoops
        </I18nText>
        <I18nText color='$grey1' marginTop='$4' textAlign='center'>
          Join the club to access the club chat
        </I18nText>
      </ImageBackground>
    );
  }

  return <ChatChannel channelId={data} />;
};

export default memo(ClubChat);
