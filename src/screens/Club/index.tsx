import React, {useCallback} from 'react';
import {YStack} from 'tamagui';
import {ClubDto} from '@gojoe/typescript-sdk';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import StyledTabNavigator from '@/src/components/StyledTopTab';
import ClubOverview from '../ClubOverview';
import {useTranslation} from 'react-i18next';
import ClubWelcome from '../ClubWelcome';
import ClubActivities from '../ClubActivities';
import ClubMembersList from '@/src/components/ClubMembers';
import ClubChat from './Chat';

const Tab = createMaterialTopTabNavigator();
interface Props {
  club: ClubDto;
}
const ClubScreen: React.FC<Props> = ({club}) => {
  const {t} = useTranslation();

  const Overview = useCallback(() => {
    return <ClubOverview club={club} />;
  }, [club]);

  const Activities = useCallback(() => {
    return <ClubActivities clubId={club.id} />;
  }, [club.id]);

  const Members = useCallback(() => {
    return <ClubMembersList clubId={club.id} />;
  }, [club.id]);

  const Chat = useCallback(() => {
    return <ClubChat clubId={club.id} />;
  }, [club.id]);

  return (
    <YStack flex={1}>
      {club.memberType ? (
        <StyledTabNavigator>
          <Tab.Screen
            name={t('Overview')}
            component={Overview}
            options={{lazy: true}}
          />
          <Tab.Screen
            name={t('Activities')}
            component={Activities}
            options={{lazy: true}}
          />
          <Tab.Screen
            name={t('Members')}
            component={Members}
            options={{lazy: true}}
          />
          <Tab.Screen
            name={t('Chat')}
            component={Chat}
            options={{lazy: true}}
          />
        </StyledTabNavigator>
      ) : (
        <ClubWelcome club={club} />
      )}
    </YStack>
  );
};
export default ClubScreen;
