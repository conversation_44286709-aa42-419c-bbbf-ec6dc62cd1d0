import React, {useCallback} from 'react';
import {Spinner, YStack} from 'tamagui';
import {FlashList, ListRenderItem} from '@shopify/flash-list';
import {PostListDto} from '@gojoe/typescript-sdk';
import useClubActivities from '@/src/hooks/api/useClubActivities';
import EmptyComponentFeed from '@/src/components/EmptyComponentFeed';
import {useLocales} from '@/src/contexts/LocaleContext';
import PostCard from '@/src/components/PostCard';

interface Props {
  clubId: string;
}

const ClubActivities: React.FC<Props> = ({clubId}) => {
  const {locales} = useLocales();
  const {
    data,
    hasNextPage,
    isFetchingNextPage,
    isRefetching,
    isLoading,
    refetch,
    fetchNextPage,
  } = useClubActivities(clubId);

  const keyExtractor = useCallback((item: PostListDto) => item.id, []);

  const renderItem = useCallback<ListRenderItem<PostListDto>>(
    ({item}) => {
      return (
        <YStack paddingHorizontal='$5' marginBottom={8}>
          <PostCard
            post={item}
            languageTag={locales.languageTag}
            unit={locales.distanceUnit}
          />
        </YStack>
      );
    },
    [locales.languageTag, locales.distanceUnit],
  );

  const loadMore = useCallback(() => {
    if (hasNextPage && !isRefetching) {
      return fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isRefetching]);

  const renderFooter = useCallback(() => {
    if (isFetchingNextPage) {
      return <Spinner marginVertical={8} />;
    }
    return null;
  }, [isFetchingNextPage]);

  const renderHeader = useCallback(() => {
    if (isLoading) {
      return <Spinner marginVertical={8} />;
    }
    return <YStack height={24} />;
  }, [isLoading]);

  return (
    <FlashList
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      estimatedItemSize={280}
      scrollEventThrottle={16}
      data={data}
      ListEmptyComponent={isLoading ? null : EmptyComponentFeed}
      ListFooterComponent={renderFooter}
      ListHeaderComponent={renderHeader}
      showsVerticalScrollIndicator={false}
      onRefresh={refetch}
      refreshing={isLoading}
      onEndReached={loadMore}
      onEndReachedThreshold={0.5}
    />
  );
};

export default ClubActivities;
