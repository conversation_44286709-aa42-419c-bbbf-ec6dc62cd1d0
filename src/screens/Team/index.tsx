import React, {useCallback, useState} from 'react';
import {
  ChallengeDetailsDto,
  ChallengeListTeamInfoDto,
  ChallengeUserStandingsItemDto,
  SearchUserListDto,
} from '@gojoe/typescript-sdk';
import {Dimensions} from 'react-native';
import {Spinner, XStack, YStack} from 'tamagui';
import TeamAvatar from '@/src/components/UI/Avatar/TeamAvatar';
import {Path, Svg} from 'react-native-svg';
import StyledText from '@/src/components/UI/StyledText';
import I18nText from '@/src/components/I18nText';
import {formattedDistance} from '@/src/utils/tracker';
import {useLocales} from '@/src/contexts/LocaleContext';
import {useTranslation} from 'react-i18next';
import {
  Users2,
  StarFull,
  Pencil,
  ChevronsUp,
  Search,
  X,
  UserPlus,
} from '@tamagui/lucide-icons';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';
import StyledButton from '@/src/components/UI/Button';
import {useSession} from '@/src/contexts/SessionContext';
import {DateTime} from 'luxon';
import {ChallengeFilters} from '@/src/constants/challenge';
import FilterSection from './FilterSection';
import {triggerHaptics} from '@/src/utils/haptics';
import {ChallengeEventType, getColumnType} from '@/src/utils/challenge';
import {useTeamStandings} from '@/src/hooks/api/useTeamStandings';
import {createAnimatedFlashList} from '@/src/components/AnimatedFlashList';
import SoloItem from '@/src/screens/challenge/Solo/Item';
import {useToastController} from '@tamagui/toast';
import useCreateBusinessUser from '@/src/hooks/api/useCreateBusinessUser';
import useJoinTeamChallenge from '@/src/hooks/api/useJoinTeamChallenge';
import {useRouter} from 'expo-router';
import {SheetManager} from 'react-native-actions-sheet';
import useAddUserToTeamMutation from '@/src/hooks/api/useAddUserToTeamMutation';
import logger from '@/src/utils/logger';

const AnimatedFlashList =
  createAnimatedFlashList<ChallengeUserStandingsItemDto>();

const screenWidth = Dimensions.get('window').width;

interface Props {
  team: ChallengeListTeamInfoDto;
  challenge: ChallengeDetailsDto;
}

const Team: React.FC<Props> = ({team, challenge}) => {
  const {locales} = useLocales();
  const {user} = useSession();
  const {t} = useTranslation();
  const router = useRouter();
  const [filters, setFilters] = useState<ChallengeFilters>({
    name: '',
    activityTypes: [],
    withFlaggedUsers: false,
    showFilterUI: false,
  });
  const {
    data,
    isLoading,
    fetchNextPage,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useTeamStandings({
    challengeId: challenge.id,
    teamId: team.id,
    sports: filters.activityTypes.map((s) => s.name),
    searchName: filters.name,
    withFlaggedUsers: filters.withFlaggedUsers,
  });
  const toast = useToastController();
  console.log('data', data);
  const isCaptain = user?.id === team.captain?.id;

  // Function to determine if the user can add members to the team
  const canAddMembers = useCallback(() => {
    return (
      team.joined &&
      challenge.inChallenge &&
      (!challenge.maxTeamSize || team.usersCount < challenge.maxTeamSize)
    );
  }, [
    team.joined,
    challenge.inChallenge,
    challenge.maxTeamSize,
    team.usersCount,
  ]);

  const columnType = getColumnType(challenge.event.name);

  const {mutate: createBusinessUser} = useCreateBusinessUser();
  const {mutate: joinTeamChallenge, isPending: isJoining} =
    useJoinTeamChallenge();
  const {mutate: addUserToTeam, isPending: isAddingUser} =
    useAddUserToTeamMutation();

  const handleJoinTeam = useCallback(() => {
    if (isJoining) return;

    const joinTeam = () => {
      joinTeamChallenge(
        {
          challengeCode: challenge.code,
          challengeId: challenge.id,
          teamId: team.id,
        },
        {
          onSuccess: () => {
            toast.show(t('You have successfully joined the team.'), {
              type: 'success',
            });
          },
          onError: () => {
            toast.show(t('Failed to join the team. Please try again.'), {
              type: 'error',
            });
          },
        },
      );
    };

    if (challenge.autoJoinBusiness && challenge.business) {
      createBusinessUser(
        {
          businessId: challenge.business.id,
          passcode: challenge.business.passcode,
        },
        {
          onSuccess: joinTeam,
          onError: () => {
            toast.show(t('Failed to join the business. Please try again.'), {
              type: 'error',
            });
          },
        },
      );
    } else {
      joinTeam();
    }
  }, [
    challenge,
    team,
    createBusinessUser,
    joinTeamChallenge,
    isJoining,
    t,
    toast,
  ]);

  const handleManageTeam = useCallback(async () => {
    await triggerHaptics();

    router.push({
      pathname: '/challenge/[challengeId]/create-team',
      params: {
        challengeId: challenge.id,
        teamId: team.id,
        isEdit: 'true',
      },
    });
  }, [challenge.id, team.id, router]);

  const handleAddMember = useCallback(async () => {
    if (isAddingUser || !canAddMembers()) return;

    await triggerHaptics();

    await SheetManager.show('user_search', {
      payload: {
        challengeId: challenge.id,
        // Add team data for invite functionality
        teamId: team.id,
        teamName: team.name,
        challengeName: challenge.name,
        passcode: challenge.code,
        onSelect: (user: SearchUserListDto) => {
          // Add user to team
          addUserToTeam(
            {
              userId: user.id,
              teamId: team.id,
              challengeId: challenge.id,
            },
            {
              onSuccess: () => {
                toast.show(t('User added to team successfully'), {
                  type: 'success',
                });
              },
              onError: (error) => {
                logger.error('Error adding user to team:', error);
                toast.show(t('Failed to add user to team'), {
                  type: 'error',
                });
              },
            },
          );
        },
      },
    });
  }, [
    challenge.id,
    challenge.name,
    challenge.code,
    team.id,
    team.name,
    addUserToTeam,
    toast,
    t,
    isAddingUser,
    canAddMembers,
  ]);

  const toggleFilters = useCallback(async () => {
    await triggerHaptics();
    setFilters((prev) => ({...prev, showFilterUI: !prev.showFilterUI}));
  }, []);

  // Simple scroll handler for the list
  const scrollHandler = useCallback((_event: any) => {
    // We can add scroll handling logic here if needed
  }, []);

  const onEndReached = useCallback(() => fetchNextPage(), [fetchNextPage]);

  const HeaderComponent = useCallback(() => {
    return (
      <YStack>
        <Svg
          width={screenWidth}
          height={309}
          viewBox={`0 0 392 309`}
          fill='none'>
          <Path
            d='M195.991 -1.97994C134.767 -1.97994 85.0127 47.7743 85.0127 108.999C85.0127 170.223 134.767 219.977 195.991 219.977C257.216 219.977 306.97 170.223 306.97 108.999C306.97 47.7743 257.216 -1.97994 195.991 -1.97994ZM195.991 179.838C156.943 179.838 125.152 148.047 125.152 108.999C125.152 69.9502 156.943 38.159 195.991 38.159C235.04 38.159 266.831 69.9502 266.831 108.999C266.831 148.047 235.04 179.838 195.991 179.838Z'
            fill='white'
          />
          <Path
            opacity='0.4'
            d='M195.999 -47.4316C109.774 -47.4316 39.5655 22.7771 39.5655 109.001C39.5655 195.226 109.774 265.435 195.999 265.435C282.223 265.435 352.432 195.226 352.432 109.001C352.432 22.7771 282.223 -47.4316 195.999 -47.4316ZM195.999 225.296C131.859 225.296 79.7044 173.141 79.7044 109.001C79.7044 44.8622 131.859 -7.29266 195.999 -7.29266C260.138 -7.29266 312.293 44.8622 312.293 109.001C312.293 173.141 260.138 225.296 195.999 225.296Z'
            fill='white'
          />
          <Path
            opacity='0.2'
            d='M196 -92.8877C84.6847 -92.8877 -5.88766 -2.31525 -5.88766 109C-5.88766 220.315 84.6847 310.888 196 310.888C307.315 310.888 397.888 220.315 397.888 109C397.888 -2.31525 307.315 -92.8877 196 -92.8877ZM196 270.749C106.861 270.749 34.2513 198.139 34.2513 109C34.2513 19.7705 106.86 -7.29266 196 -7.29266C285.14 -7.29266 357.749 19.7705 357.749 109C357.749 198.139 285.139 270.749 196 270.749Z'
            fill='white'
          />
          <YStack
            marginTop={44}
            justifyContent='center'
            alignItems='center'
            width={screenWidth}>
            <TeamAvatar circular size={130} team={team} />
            <StyledText fontSize={18} fontWeight='700' marginTop='$3.5'>
              {team.name}
            </StyledText>
            <StyledText fontWeight='700' marginTop='$2'>
              {`${team.rank.rank} / ${team.totalTeams}`}
            </StyledText>
            <XStack paddingHorizontal='$5' marginTop='$5'>
              <YStack flex={1} justifyContent='center' alignItems='center'>
                <I18nText fontSize={12} fontWeight='500' color='$grey2'>
                  Activities
                </I18nText>
                <StyledText fontWeight='700' fontSize={12} marginTop='$1.5'>
                  {team.rank.countActivities ?? 0}
                </StyledText>
              </YStack>
              <YStack flex={1} justifyContent='center' alignItems='center'>
                <I18nText fontSize={12} fontWeight='500' color='$grey2'>
                  Points
                </I18nText>
                <StyledText fontWeight='700' fontSize={12} marginTop='$1.5'>
                  {t('{{count}} pts', {
                    count: team.rank.points ? Math.round(team.rank.points) : 0,
                  })}
                </StyledText>
              </YStack>
              <YStack flex={1} justifyContent='center' alignItems='center'>
                <I18nText fontSize={12} fontWeight='500' color='$grey2'>
                  Distance
                </I18nText>
                <StyledText fontWeight='700' fontSize={12} marginTop='$1.5'>
                  {formattedDistance(
                    team.rank.distance ?? 0,
                    locales.distanceUnit,
                  )}
                  {locales.distanceUnit}
                </StyledText>
              </YStack>
            </XStack>
          </YStack>
        </Svg>
        <YStack marginTop='$4' marginHorizontal='$5'>
          {filters.showFilterUI ? (
            <FilterSection
              filters={filters}
              setFilters={setFilters}
              activityTypes={challenge.activityTypes || []}
            />
          ) : (
            <YStack
              gap={12}
              padding='$3.5'
              backgroundColor='$background'
              borderRadius={16}
              borderWidth={1}
              borderColor='$grey3'>
              {!!team.captain && (
                <XStack
                  height={32}
                  alignItems='center'
                  justifyContent='space-between'>
                  <XStack alignItems='center' gap={8}>
                    <StarFull size={18} color='$grey1' />
                    <I18nText fontSize={12} fontWeight='500' color='$grey1'>
                      Team Captain
                    </I18nText>
                  </XStack>
                  <XStack alignItems='center' gap={8}>
                    <StyledText
                      numberOfLines={1}
                      fontWeight='500'
                      fontSize={12}>
                      {team.captain.name}
                    </StyledText>
                    <UserAvatar circular size={32} user={team.captain} />
                  </XStack>
                </XStack>
              )}
              <XStack
                height={32}
                alignItems='center'
                justifyContent='space-between'>
                <XStack alignItems='center' gap={8}>
                  <Users2 size={18} color='$grey1' />
                  <I18nText fontSize={12} fontWeight='500' color='$grey1'>
                    Team Size
                  </I18nText>
                </XStack>
                <XStack alignItems='center' gap={8}>
                  <StyledText numberOfLines={1} fontWeight='500' fontSize={12}>
                    {` ${team.usersCount} / ${challenge.maxTeamSize ? challenge.maxTeamSize : t('Unlimited')}`}
                  </StyledText>
                </XStack>
              </XStack>
              {team.joined && (
                <XStack
                  height={32}
                  alignItems='center'
                  justifyContent='space-between'>
                  <XStack alignItems='center' gap={8}>
                    <ChevronsUp color='$grey1' />
                    <I18nText fontSize={12} fontWeight='500' color='$grey1'>
                      Joined
                    </I18nText>
                  </XStack>
                  <XStack alignItems='center' gap={8}>
                    <StyledText
                      numberOfLines={1}
                      fontWeight='500'
                      fontSize={12}>
                      {DateTime.fromISO(team.joined, {zone: 'utc'})
                        .toLocal()
                        .toLocaleString(DateTime.DATE_FULL, {
                          locale: locales.languageTag,
                        })}
                    </StyledText>
                  </XStack>
                </XStack>
              )}

              {/* Add Member Button - Only show if user is in the team and team is not full */}
              {canAddMembers() && (
                <StyledButton
                  variant='secondary'
                  onPress={handleAddMember}
                  disabled={isAddingUser}
                  iconAfter={() => (
                    <UserPlus
                      color='$color'
                      position='absolute'
                      right={16}
                      size={18}
                    />
                  )}>
                  <I18nText color='$color' fontWeight='500'>
                    {isAddingUser ? 'Adding...' : 'Add Members to Team'}
                  </I18nText>
                </StyledButton>
              )}
              {!challenge.inChallenge && (
                <StyledButton
                  onPress={handleJoinTeam}
                  disabled={isJoining}
                  variant='primary'>
                  <I18nText color='$white1' fontWeight='500'>
                    {isJoining ? 'Joining...' : 'Join Team'}
                  </I18nText>
                </StyledButton>
              )}
              {isCaptain && (
                <StyledButton
                  variant='secondary'
                  onPress={handleManageTeam}
                  iconAfter={() => (
                    <Pencil
                      color='$color'
                      position='absolute'
                      right={16}
                      size={18}
                    />
                  )}>
                  <I18nText color='$color' fontWeight='500'>
                    Manage Team
                  </I18nText>
                </StyledButton>
              )}
            </YStack>
          )}
        </YStack>
        <XStack
          alignItems='center'
          justifyContent='space-between'
          paddingHorizontal='$5'
          marginTop='$5'>
          <XStack alignItems='center' gap={4}>
            <I18nText fontWeight='bold'>Leaderboard</I18nText>
          </XStack>
          <StyledButton
            onPress={toggleFilters}
            height={32}
            borderRadius={8}
            paddingHorizontal={8}
            icon={
              filters.showFilterUI ? (
                <X color='$white1' size={16} />
              ) : (
                <Search color='$white1' size={16} />
              )
            }>
            <I18nText
              color='$white1'
              fontWeight='600'
              fontSize={12}
              paddingStart={4}>
              {filters.showFilterUI ? 'Hide filters' : 'Search'}
            </I18nText>
          </StyledButton>
        </XStack>
        <XStack
          height={12}
          paddingHorizontal='$6'
          gap={42}
          marginTop='$5'
          marginBottom='$2'>
          <I18nText fontWeight='500' fontSize={10} color='$grey1'>
            Pos
          </I18nText>
          <I18nText fontWeight='500' fontSize={10} color='$grey1' flex={1}>
            Name
          </I18nText>
          <I18nText fontWeight='500' fontSize={10} color='$grey1'>
            {columnType === ChallengeEventType.Points
              ? 'Pts'
              : `${locales.distanceUnit}`}
          </I18nText>
        </XStack>
        {isLoading && (
          <Spinner position='absolute' zIndex={1} top={35} alignSelf='center' />
        )}
      </YStack>
    );
  }, [
    team,
    t,
    locales.distanceUnit,
    locales.languageTag,
    filters,
    challenge,
    isCaptain,
    canAddMembers,
    handleAddMember,
    isAddingUser,
    handleJoinTeam,
    isJoining,
    handleManageTeam,
    toggleFilters,
    columnType,
    isLoading,
  ]);

  const listFooterComponent = useCallback(() => {
    if (isFetchingNextPage) {
      return <Spinner marginVertical='$3' />;
    }
    return <YStack height={48} />;
  }, [isFetchingNextPage]);

  const renderItem = useCallback(
    ({item}: {item: ChallengeUserStandingsItemDto}) => {
      return (
        <YStack paddingHorizontal='$5'>
          <SoloItem
            item={item}
            columnType={columnType}
            unit={locales.distanceUnit}
            highlighted={item.user.id === user?.id}
            challengeId={challenge.id}
          />
        </YStack>
      );
    },
    [challenge.id, columnType, locales.distanceUnit, user?.id],
  );

  return (
    <YStack flex={1}>
      <AnimatedFlashList
        contentContainerStyle={{paddingBottom: 48}}
        data={data}
        renderItem={renderItem}
        estimatedItemSize={56}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={HeaderComponent}
        ListFooterComponent={listFooterComponent}
        refreshing={isRefetching}
        onRefresh={refetch}
        onEndReached={onEndReached}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
      />
    </YStack>
  );
};

export default Team;
