import PostCard from '@/src/components/PostCard';
import {useLocales} from '@/src/contexts/LocaleContext';
import useBusinessPosts from '@/src/hooks/api/useBusinessPosts';
import {PostListDto} from '@gojoe/typescript-sdk';
import {FlashList} from '@shopify/flash-list';
import {useCallback} from 'react';
import {Spinner, YStack} from 'tamagui';

import {ListSpacer16} from '@/src/components/ListSpacer';

interface Props {
  businessId: string;
}
const BusinessPosts: React.FC<Props> = ({businessId}) => {
  const {locales} = useLocales();
  const {
    isLoading,
    isRefetching,
    data,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
  } = useBusinessPosts(businessId);

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const renderItem = useCallback(
    ({item}: {item: PostListDto}) => {
      return (
        <PostCard
          post={item}
          languageTag={locales.languageTag}
          unit={locales.distanceUnit}
        />
      );
    },
    [locales.languageTag, locales.distanceUnit],
  );

  const renderFooter = () => {
    if (isFetchingNextPage) {
      return (
        <YStack paddingVertical='$4' alignItems='center'>
          <Spinner size='small' color='$color' />
        </YStack>
      );
    }
    return <YStack height={48} />;
  };

  const renderHeader = () => {
    return (
      <YStack padding='$4'>
        {isLoading && (
          <YStack alignItems='center' paddingVertical='$4'>
            <Spinner size='large' color='$color' />
          </YStack>
        )}
        {/* Other header content like filters */}
      </YStack>
    );
  };

  return (
    <YStack paddingHorizontal='$5' flex={1}>
      <FlashList
        data={data}
        renderItem={renderItem}
        estimatedItemSize={280}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        onEndReached={onEndReached}
        ListFooterComponent={renderFooter}
        ListHeaderComponent={renderHeader}
        refreshing={isRefetching}
        onRefresh={refetch}
        ItemSeparatorComponent={ListSpacer16}
      />
    </YStack>
  );
};

export default BusinessPosts;
