import {useLocalSearchParams} from 'expo-router';
import {useMemo} from 'react';

export const useParsedParams = (): Record<string, string> => {
  const rawParams = useLocalSearchParams();

  // Serialize rawParams once to use as a stable dependency
  const serializedParams = useMemo(
    () => JSON.stringify(rawParams),
    [rawParams],
  );

  const fixedParams = useMemo(() => {
    const parsed: Record<string, string> = {};
    const raw = JSON.parse(serializedParams) as Record<string, string>;

    Object.entries(raw).forEach(([key, value]) => {
      if (typeof value !== 'string') return;

      if (value.includes('?')) {
        const [main, query] = value.split('?');
        parsed[key] = main;

        const searchParams = new URLSearchParams(query);
        searchParams.forEach((val, qkey) => {
          parsed[qkey] = val;
        });
      } else {
        parsed[key] = value;
      }
    });

    return parsed;
  }, [serializedParams]);

  return fixedParams;
};
