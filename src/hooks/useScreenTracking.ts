import {useEffect} from 'react';
import {usePathname} from 'expo-router';
import segmentClient from '../services/segment';

export function useScreenTracking() {
  const pathname = usePathname();

  useEffect(() => {
    const decodedPath = decodeURIComponent(pathname);
    const segments = decodedPath.split('/').filter(Boolean);

    const screenNameSegments: string[] = [];
    const properties: Record<string, string> = {};

    for (let i = 0; i < segments.length; i++) {
      const prev = segments[i - 1];
      const current = segments[i];

      if (isId(current) && isNamedParam(prev)) {
        const paramName = `${prev}Id`;
        screenNameSegments.push(`[${paramName}]`);
        properties[paramName] = current;
      } else {
        screenNameSegments.push(slugify(current));
      }
    }

    const screenName = screenNameSegments.join('/') || 'home';

    segmentClient.screen(screenName, properties);
  }, [pathname]);
}

function isId(segment: string): boolean {
  return /^[a-zA-Z0-9_-]{6,}$/.test(segment);
}

function isNamedParam(segment: string): boolean {
  // List all route prefixes that denote dynamic IDs
  return [
    'benefit',
    'business',
    'challenge',
    'club',
    'user',
    'team',
    'post',
    'event',
    'program',
  ].includes(segment);
}

function slugify(value: string): string {
  return value
    .toLowerCase()
    .trim()
    .replace(/[\s_]+/g, '-') // spaces/underscores → dash
    .replace(/[^\w/-]+/g, ''); // remove non-word characters
}
