import {useEffect, useState} from 'react';
import {getCalendars} from 'expo-localization';
import {useSession} from '@/src/contexts/SessionContext';
import * as Device from 'expo-device';
import {Platform} from 'react-native';
import * as Application from 'expo-application';
import {useDeviceUUID} from '@/src/hooks/useDeviceUUID';
import {getUserAccessToken} from '@/src/utils/auth';

type Header = {
  'x-gj-device-timezone': string | null;
  'x-gj-app-version': string | null;
  'x-gj-app-bundleid': string | null;
  'x-gj-device-id': string | null;
  'x-gj-device-os': string;
  'x-gj-user-id'?: string;
  authorization?: string;
};
const initialState = {
  'x-gj-device-timezone': getCalendars()[0].timeZone,
  'x-gj-app-version': Application.nativeApplicationVersion,
  'x-gj-app-bundleid': Device.osBuildId,
  'x-gj-device-id': '',
  'x-gj-device-os': Platform.OS,
  'x-gj-user-id': undefined,
  authorization: undefined,
};
const useHeaders = () => {
  const {user} = useSession();
  const deviceUUID = useDeviceUUID();
  const [headers, setHeaders] = useState<Header>({
    ...initialState,
    'x-gj-user-id': user?.id,
    'x-gj-device-id': deviceUUID,
  });

  useEffect(() => {
    if (deviceUUID) {
      setHeaders((prev) => ({...prev, 'x-gj-device-id': deviceUUID}));
    }
  }, [deviceUUID]);

  useEffect(() => {
    getUserAccessToken().then((token) => {
      if (token) {
        setHeaders((prev) => ({...prev, authorization: `Bearer ${token}`}));
      }
    });
  }, []);

  return headers;
};

export default useHeaders;
