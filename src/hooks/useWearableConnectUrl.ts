import {useEffect, useState} from 'react';
import {useDeviceUUID} from '@/src/hooks/useDeviceUUID';
import {useSession} from '@/src/contexts/SessionContext';
import {useWearableUrlScheme} from '@/src/hooks/useWearableUrlScheme';
import * as Crypto from 'expo-crypto';
import {api} from '@/src/services/api';
import logger from '@/src/utils/logger';

export function useWearableConnectUrl(
  wearableName: string,
  connectMethodName?: string,
) {
  if (!connectMethodName) {
    connectMethodName = `${wearableName}ControllerConnect${capitalize(wearableName)}`;
  }
  const [url, setUrl] = useState<string | undefined>();
  const deviceId = useDeviceUUID();
  const {user} = useSession();
  const scheme = useWearableUrlScheme(wearableName.toLowerCase());

  useEffect(() => {
    if (!deviceId || !user?.id || !scheme) return;

    const fetchUrl = async () => {
      try {
        const uuid = Crypto.randomUUID();
        const customState = `${deviceId}__${user.id}__${scheme}__${uuid}`;
        const result = await api.getConnectUrl(
          customState,
          `${connectMethodName}`,
        );
        setUrl(result);
      } catch (error) {
        logger.error(`Failed to fetch ${wearableName} connect URL:`, error);
      }
    };

    fetchUrl();
  }, [deviceId, user?.id, scheme, connectMethodName, wearableName]);

  return url;
}

// Optional helper
function capitalize(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
