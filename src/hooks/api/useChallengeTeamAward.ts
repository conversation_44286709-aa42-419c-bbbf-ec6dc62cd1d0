import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

export const useChallengeTeamAward = (challengeId: string, awardId: string) => {
  const {data, isLoading, refetch} = useQuery({
    queryKey: ['awards', 'team', challengeId, awardId],
    initialData: [],
    queryFn: async () =>
      api
        .getApiClient()
        .challengeControllerTeamAwards({challengeId, awardId})
        .then((res) => res.data.data),
  });

  return {
    data,
    isLoading,
    refetch,
  };
};
