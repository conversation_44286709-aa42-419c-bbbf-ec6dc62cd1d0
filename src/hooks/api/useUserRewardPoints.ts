import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {
  DefaultApiRewardsControllerGetPointsRequest,
  UserRewardPointsDtoTypeEnum,
} from '@gojoe/typescript-sdk';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (params: DefaultApiRewardsControllerGetPointsRequest) => {
  return api
    .getApiClient()
    .rewardsControllerGetPoints(params)
    .then(({data}) => data.data);
};

const useUserRewardPoints = (
  type?: UserRewardPointsDtoTypeEnum,
  limit = 20,
) => {
  const queryKey = type ? ['rewards', 'points', type] : ['rewards', 'points'];

  const {
    data,
    error,
    isLoading,
    fetchNextPage,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: queryKey,
    queryFn: ({pageParam}) => fetch({type, limit, after: pageParam}),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    error,
    isLoading,
    fetchNextPage,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data
      ? data.pages[data.pages.length - 1].paging.cursors.hasNextPage
      : false,
  };
};

export default useUserRewardPoints;
