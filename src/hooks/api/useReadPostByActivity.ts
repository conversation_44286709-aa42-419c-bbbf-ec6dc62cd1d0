import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (activityId: string) => {
  return api
    .getApiClient()
    .postControllerReadPostByActivity({activityId: activityId})
    .then(({data}) => data.data);
};
const useReadPostByActivity = (activityId: string) => {
  const {data, isLoading} = useQuery({
    queryKey: ['activity-post', activityId],
    queryFn: () => fetch(activityId),
    staleTime: Infinity,
    enabled: !!activityId,
  });

  return {data, isLoading};
};

export default useReadPostByActivity;
