import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const getBusiness = async (businessId: string) => {
  const {data} = await api
    .getApiClient()
    .businessControllerGetBusiness({businessId});
  return data.data;
};
export default function useBusiness(businessId: string) {
  const {data, refetch, isRefetching} = useQuery({
    queryKey: ['business', businessId],
    queryFn: () => getBusiness(businessId),
  });
  return {
    data,
    refetch,
    isRefetching,
  };
}
