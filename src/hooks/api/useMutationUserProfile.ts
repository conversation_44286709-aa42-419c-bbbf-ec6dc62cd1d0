import {useMutation} from '@tanstack/react-query';
import {UserInputDto} from '@gojoe/typescript-sdk';

import {api} from '@/src/services/api';

const apiCall = async (input: UserInputDto) => {
  return api
    .getApiClient()
    .userControllerUserEdit({userInputDto: input, userId: 'me'});
};

const useMutationUserProfile = () => {
  const {mutateAsync, mutate, isPending} = useMutation({
    mutationFn: (input: UserInputDto) => apiCall(input),
  });

  return {
    mutate,
    mutateAsync,
    isPending,
  };
};

export default useMutationUserProfile;
