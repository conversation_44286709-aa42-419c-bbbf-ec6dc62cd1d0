import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (businessId: string) => {
  return api
    .getApiClient()
    .businessDepartmentsControllerList({businessId})
    .then(({data}) => data.data);
};
const useBusinessDepartments = (businessId: string) => {
  const {data, isLoading, refetch, error} = useQuery({
    queryKey: ['departments', businessId],
    queryFn: () => fetch(businessId),
    initialData: [],
  });

  return {departments: data, isLoading, refetch, error};
};

export default useBusinessDepartments;
