import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (businessId: string) => {
  return api
    .getApiClient()
    .businessControllerBusinessStats({businessId})
    .then((result) => result.data.data);
};

const useBusinessStats = (businessId: string) => {
  const {data, isLoading} = useQuery({
    queryKey: ['business', businessId, 'stats'],
    queryFn: () => fetch(businessId),
  });

  return {
    data,
    isLoading,
  };
};

export default useBusinessStats;
