import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async () => {
  return api
    .getApiClient()
    .clubsControllerRecommendedClubs()
    .then((result) => result.data.data);
};

export const useRecommendedClubs = () => {
  const {data, isLoading} = useQuery({
    queryKey: ['clubs', 'recommendations'],
    queryFn: () => fetch(),
  });

  return {
    data: data ?? [],
    isLoading,
  };
};
