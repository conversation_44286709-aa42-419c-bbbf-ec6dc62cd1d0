import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (challengeId: string) => {
  return api
    .getApiClient()
    .challengeControllerChallengeStats({challengeId})
    .then((res) => res.data.data);
};

const useChallengeInsights = (challengeId: string) => {
  const {data, isLoading, refetch, isRefetching} = useQuery({
    queryKey: ['challenge', 'insights', challengeId],
    queryFn: () => fetch(challengeId),
    initialData: {
      totalUsers: 0,
      totalTeams: 0,
      totalDistance: 0,
      totalPoints: 0,
      totalActivities: 0,
      engagement: 0,
    },
  });

  return {
    data,
    isLoading,
    refetch,
    isRefetching,
  };
};

export default useChallengeInsights;
