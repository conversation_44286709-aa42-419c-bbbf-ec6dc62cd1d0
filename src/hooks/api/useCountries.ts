import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async () => {
  return api.getApiClient().countryControlerGetCountries();
};
const useCountries = () => {
  const {data, isLoading} = useQuery({
    queryKey: ['countries'],
    queryFn: () => fetch(),
    staleTime: Infinity,
  });

  return {
    data: data ? data.data.data : [],
    isLoading,
  };
};

export default useCountries;
