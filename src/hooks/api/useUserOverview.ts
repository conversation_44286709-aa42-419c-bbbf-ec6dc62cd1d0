import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

export const fetch = async (userId: string) => {
  return api
    .getApiClient()
    .userControllerOverview({userId: userId})
    .then((result) => result.data.data);
};

const useUserOverview = (userId: string) => {
  const {data, isLoading, refetch, error} = useQuery({
    queryKey: ['overview', userId],
    queryFn: () => fetch(userId),
    staleTime: Infinity,
  });

  return {
    data,
    isLoading,
    refetch,
    error,
  };
};

export default useUserOverview;
