import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (after?: string, limit = 20) => {
  return api
    .getApiClient()
    .challengesControllerGetFinishedChallenges({after: after, limit: limit})
    .then((result) => result.data.data);
};

const useChallengesFinished = (limit = 20) => {
  const {
    data,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: ['challenges', 'finished'],
    queryFn: ({pageParam}) => fetch(pageParam, limit),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data
      ? data.pages[data.pages.length - 1].paging.cursors.hasNextPage
      : false,
  };
};

export default useChallengesFinished;
