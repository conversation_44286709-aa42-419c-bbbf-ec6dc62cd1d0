import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (postId: string, limit = 20, pageParam?: string) => {
  return api
    .getApiClient()
    .postControllerReactionUserList({
      postId,
      limit,
      after: pageParam,
    })
    .then((result) => result.data.data);
};

const usePostReactionUserList = (postId: string, limit = 20) => {
  const {
    data,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: ['post', 'reactions', 'members', postId],
    queryFn: ({pageParam}) => fetch(postId, limit, pageParam),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data
      ? data.pages[data.pages.length - 1].paging.cursors.hasNextPage
      : false,
  };
};

export default usePostReactionUserList;
