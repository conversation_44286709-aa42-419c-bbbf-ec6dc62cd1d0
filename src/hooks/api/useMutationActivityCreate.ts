import {api} from '@/src/services/api';
import {CreateActivityDto} from '@gojoe/typescript-sdk';
import {useMutation} from '@tanstack/react-query';
import logger from '@/src/utils/logger';

const apiCall = async (createActivityDto: CreateActivityDto) => {
  return api
    .getApiClient()
    .activitiesControllerCreateActivity({createActivityDto: createActivityDto});
};

const useMutationActivityCreate = () => {
  const {mutateAsync, isPending} = useMutation({
    mutationFn: (createActivityDto: CreateActivityDto) =>
      apiCall(createActivityDto),
    onSuccess: (result) => {
      // Handle success
      // clear cache or show a success message if needed
    },
    onError: (error) => {
      logger.error('Error creating activity:', error);
      // Handle error if needed
    },
  });

  return {
    mutateAsync,
    isPending,
  };
};

export default useMutationActivityCreate;
