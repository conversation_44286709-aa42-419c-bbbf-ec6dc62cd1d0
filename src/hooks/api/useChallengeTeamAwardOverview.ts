import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

export const useChallengeTeamAwardsOverview = (challengeId: string) => {
  const {data, isLoading, isRefetching, refetch} = useQuery({
    queryKey: ['teamAwards', challengeId],
    initialData: [],
    queryFn: () =>
      api
        .getApiClient()
        .challengeControllerTeamAwardsOverview({challengeId})
        .then((res) => res.data.data),
  });

  return {
    data,
    isLoading,
    refetch,
    isRefetching,
  };
};
