import {useInfiniteQuery} from '@tanstack/react-query';
import {DefaultApiGiftCardsControllerGetGiftCardsRequest} from '@gojoe/typescript-sdk';

import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (
  requestParameters: DefaultApiGiftCardsControllerGetGiftCardsRequest,
) => {
  return api
    .getApiClient()
    .giftCardsControllerGetGiftCards(requestParameters)
    .then((r) => r.data.data);
};
export function useGiftCards(limit = 20) {
  const {
    data,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: ['gift-cards'],
    queryFn: ({pageParam}) => fetch({after: pageParam, limit}),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  const giftCards = data?.pages.flatMap((page) => page.data) ?? [];
  return {
    data: giftCards,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  };
}
