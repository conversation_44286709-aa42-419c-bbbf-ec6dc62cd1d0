import {ImageSourcePropType} from 'react-native';

export type Goal = {
  id: string;
  name: string;
  icon: ImageSourcePropType;
};
export const useGoals = () => {
  const data: Goal[] = [
    {
      id: 'uuid-1',
      name: 'Manage weight',
      icon: require('@/assets/images/icons/icon-scale.png'),
    },
    {
      id: 'uuid-2',
      name: 'Improve mental health',
      icon: require('@/assets/images/icons/icon-mental-health.png'),
    },
    {
      id: 'uuid-3',
      name: 'Get stronger',
      icon: require('@/assets/images/icons/icon-biceps.png'),
    },
    {
      id: 'uuid-4',
      name: 'Improve cardio',
      icon: require('@/assets/images/icons/icon-heart.png'),
    },
    {
      id: 'uuid-5',
      name: 'Improve sleep',
      icon: require('@/assets/images/icons/icon-sleepy.png'),
    },
    {
      id: 'uuid-6',
      name: 'Something else',
      icon: require('@/assets/images/icons/icon-other.png'),
    },
  ];
  return {
    goals: data,
  };
};
