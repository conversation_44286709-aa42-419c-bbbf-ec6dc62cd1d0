import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (businessId: string) => {
  return api
    .getApiClient()
    .businessRegionsControllerList({businessId})
    .then(({data}) => data.data);
};
const useBusinessRegions = (businessId: string) => {
  const {data, isLoading, refetch, error} = useQuery({
    queryKey: ['regions', businessId],
    queryFn: () => fetch(businessId),
    initialData: [],
  });

  return {regions: data, isLoading, refetch, error};
};

export default useBusinessRegions;
