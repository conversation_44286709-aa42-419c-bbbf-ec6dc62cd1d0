import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (businessId: string, userId: string) => {
  return api
    .getApiClient()
    .businessUsersControllerGet({businessId, userId})
    .then(({data}) => data.data);
};
const useBusinessUser = (businessId: string, userId: string) => {
  const {data} = useQuery({
    queryKey: ['businessUser', {businessId, userId}],
    queryFn: () => fetch(businessId, userId),
  });

  return {data};
};

export default useBusinessUser;
