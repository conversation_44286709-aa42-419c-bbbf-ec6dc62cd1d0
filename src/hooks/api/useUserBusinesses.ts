import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const getBusinesses = async (after?: string, limit = 20, name?: string) => {
  const {data} = await api
    .getApiClient()
    .userControllerUserBusinesses({after, limit, q: name});
  return data.data;
};

interface Params {
  limit?: number;
  name?: string;
}
export default function useUserBusinesses(param?: Params) {
  const {limit, name} = param ?? {};
  const queryKey = ['user', 'businesses'];
  if (name) {
    queryKey.push(name);
  }
  const {
    data,
    isLoading,
    fetchNextPage,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: queryKey,
    queryFn: ({pageParam}) => getBusinesses(pageParam, limit, name),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  const businesses = data?.pages.flatMap((page) => page.data) ?? [];
  return {
    data: businesses,
    isLoading,
    fetchNextPage,
    refetch,
    isRefetching,
    isFetchingNextPage,
  };
}
