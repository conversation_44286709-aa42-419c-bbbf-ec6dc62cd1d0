import {useInfiniteQuery} from '@tanstack/react-query';
import {DefaultApiChallengesControllerChallengeTeamUsersRequest} from '@gojoe/typescript-sdk';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (
  requestParameters: DefaultApiChallengesControllerChallengeTeamUsersRequest,
) => {
  return api
    .getApiClient()
    .challengesControllerChallengeTeamUsers(requestParameters)
    .then((res) => res.data.data);
};
export function useTeamStandings({
  challengeId,
  teamId,
  userId,
  sports,
  searchName,
  withFlaggedUsers,
}: {
  challengeId: string;
  teamId: string;
  userId?: string;
  sports?: string[];
  searchName?: string;
  withFlaggedUsers?: boolean;
}) {
  const queryKey = ['challenge', challengeId, 'team', teamId, withFlaggedUsers];
  if (sports && sports.length) {
    queryKey.push(sports.join(','));
  }
  if (searchName) {
    queryKey.push(searchName);
  }
  if (userId) {
    queryKey.push(userId);
  }
  const {
    data,
    isLoading,
    isRefetching,
    refetch,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey: queryKey,
    queryFn: ({pageParam}) =>
      fetch({
        challengeId,
        teamId,
        sports,
        q: searchName,
        userId,
        after: pageParam,
        withFlaggedUsers,
      }),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
    initialPageParam: '',
  });
  const items = data?.pages.flatMap((page) => page.data) ?? [];
  return {
    data: items,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data?.pages[data.pages.length - 1].paging.cursors.hasNextPage,
  };
}
