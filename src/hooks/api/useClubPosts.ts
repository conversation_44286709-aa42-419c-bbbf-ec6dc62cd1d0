import {useInfiniteQuery} from '@tanstack/react-query';
import {DefaultApiClubsControllerGetClubPostsRequest} from '@gojoe/typescript-sdk';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (
  requestParameters: DefaultApiClubsControllerGetClubPostsRequest,
) => {
  return api
    .getApiClient()
    .clubsControllerGetClubPosts(requestParameters)
    .then((result) => result.data.data);
};
const useClubPosts = (clubId: string, limit = 20) => {
  const {
    data,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: ['clubs', 'posts', clubId],
    queryFn: ({pageParam}) => fetch({clubId, limit, after: pageParam}),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data
      ? data.pages[data.pages.length - 1].paging.cursors.hasNextPage
      : false,
  };
};

export default useClubPosts;
