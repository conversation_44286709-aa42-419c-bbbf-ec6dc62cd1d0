import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (clubId: string) => {
  return api
    .getApiClient()
    .clubsControllerGetClubStats({clubId})
    .then((result) => result.data.data);
};
const useClubStats = (clubId: string) => {
  const {data, isLoading} = useQuery({
    queryKey: ['club', clubId, 'stats'],
    queryFn: () => fetch(clubId),
  });

  return {
    data,
    isLoading,
  };
};

export default useClubStats;
