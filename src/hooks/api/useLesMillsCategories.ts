import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async () => {
  return api
    .getApiClient()
    .lesMillsControllerCategories({
      withPrograms: true,
    })
    .then((resp) => resp.data.data);
};

export function useLesMillsCategories() {
  const {data, error, isLoading, refetch, isRefetching} = useQuery({
    queryKey: ['les-mills', 'categories'],
    queryFn: () => fetch(),
  });

  return {
    data,
    error,
    isLoading,
    refetch,
    isRefetching,
  };
}
