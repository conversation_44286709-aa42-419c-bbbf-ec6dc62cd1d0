import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (postId: string, pageParam: string) => {
  return api
    .getApiClient()
    .postControllerPostCommentsList({
      postId,
      after: pageParam,
    })
    .then((result) => result.data.data);
};

const usePostComments = (postId: string) => {
  const {
    isLoading,
    isRefetching,
    data,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    isSuccess,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: ['comments', 'post', postId],
    queryFn: ({pageParam}) => fetch(postId, pageParam),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    isLoading,
    isRefetching,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    isSuccess,
    hasNextPage: data?.pages[data.pages.length - 1].paging.cursors.hasNextPage,
  };
};

export default usePostComments;
