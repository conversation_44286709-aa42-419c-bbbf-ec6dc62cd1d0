import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (challengeId: string, teamId: string) => {
  return api
    .getApiClient()
    .challengesControllerGetTeamChallenge({
      challengeId,
      teamId,
    })
    .then((response) => response.data.data);
};
const useTeam = (challengeId: string, teamId = '') => {
  const {data, isLoading} = useQuery({
    queryKey: ['challenge', challengeId, 'team', teamId, 'details'],
    queryFn: () => fetch(challengeId, teamId),
    staleTime: Infinity,
    enabled: teamId !== '',
  });

  return {data, isLoading};
};

export default useTeam;
