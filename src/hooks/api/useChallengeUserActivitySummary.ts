import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

export const fetchUserProfile = async (challengeId: string, userId: string) => {
  return api
    .getApiClient()
    .challengeControllerGetUserStatsInChallenge({challengeId, userId})
    .then((result) => result.data.data);
};

const useChallengeUserActivitySummary = (
  challengeId: string,
  userId: string,
) => {
  const {data, isLoading, error} = useQuery({
    queryKey: [
      'challenge',
      challengeId,
      'user',
      userId,
      'activities',
      'overview',
    ],
    queryFn: () => fetchUserProfile(challengeId, userId),
    staleTime: Infinity,
  });

  return {
    data,
    isLoading,
    error,
  };
};

export default useChallengeUserActivitySummary;
