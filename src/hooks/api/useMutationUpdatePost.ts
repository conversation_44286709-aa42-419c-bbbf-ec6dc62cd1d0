import {useMutation, useQueryClient} from '@tanstack/react-query';
import {UpdateUserPostInputDto} from '@gojoe/typescript-sdk';

import {api} from '@/src/services/api';

const updatePost = async (input: UpdateUserPostInputDto) => {
  return api
    .getApiClient()
    .postControllerUpdatePost({updateUserPostInputDto: input});
};

const useMutationUpdatePost = (postId: string) => {
  const queryClient = useQueryClient();

  const {mutateAsync, isPending} = useMutation({
    mutationFn: (input: UpdateUserPostInputDto) => updatePost(input),
    onSuccess: () => {
      return Promise.all([
        queryClient.invalidateQueries({
          queryKey: ['post', postId],
        }),
        queryClient.invalidateQueries({
          queryKey: ['posts'],
        }),
      ]);
    },
  });

  return {
    mutateAsync,
    isPending,
  };
};

export default useMutationUpdatePost;
