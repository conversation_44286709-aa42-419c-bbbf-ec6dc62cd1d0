import {api} from '@/src/services/api';
import {useMutation} from '@tanstack/react-query';

const disconnectMap: Record<string, () => Promise<any>> = {
  appleHealth: () =>
    api.getApiClient().appleHealthControllerDisconnectFromAppleHealth(),
  coros: () => api.getApiClient().corosControllerDisconnectCoros(),
  fitbit: () => api.getApiClient().fitbitControllerDisconnectFitbit(),
  garmin: () =>
    api
      .getApiClient()
      .garminConnectControllerDisconnectFromGarmin({deviceId: 'updateSDK'}),
  healthConnect: () =>
    api.getApiClient().healthConnectControllerDisconnectHealthConnect(),
  oura: () => api.getApiClient().ouraControllerDisconnectOura(),
  polar: () => api.getApiClient().polarControllerDisconnectPolar(),
  suunto: () => api.getApiClient().suuntoControllerDisconnectSuunto(),
  wahoo: () => api.getApiClient().wahooControllerDisconnectWahoo(),
  whoop: () => api.getApiClient().whoopControllerDisconnectWhoop(),
  withings: () => api.getApiClient().withingsControllerDisconnectWithings(),
};

export const useMutationDisconnectWearable = (wearable: string) => {
  const {mutate, isPending, isError} = useMutation({
    mutationFn: async () => {
      const fn = disconnectMap[wearable];
      if (!fn) {
        throw new Error(`No disconnect method found for ${wearable}`);
      }
      return fn();
    },
  });

  return {
    disconnect: mutate,
    isPending,
    isError,
  };
};
