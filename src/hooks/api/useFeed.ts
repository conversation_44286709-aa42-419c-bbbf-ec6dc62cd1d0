import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (after?: string, limit = 20) => {
  return api
    .getApiClient()
    .postControllerGetFeed({after, limit})
    .then((result) => result.data.data);
};

const useFeed = (limit = 20) => {
  const query = useInfiniteQuery({
    initialPageParam: '',
    queryKey: ['feed', `${limit}`],
    queryFn: ({pageParam}) => fetch(pageParam, limit),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  const flatData = query.data
    ? query.data.pages.flatMap((page) => page.data)
    : [];

  return {
    ...query,
    data: flatData,
    fullData: query.data, // keep original InfiniteData in case you need to update it
    hasNextPage: query.data
      ? query.data.pages[query.data.pages.length - 1].paging.cursors.hasNextPage
      : false,
  };
};

export default useFeed;
