import {useMutation, useQueryClient} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const leaveJourneyCall = async (journeyId: string) => {
  return api.getApiClient().journeyControllerLeaveJourney({id: journeyId});
};

const useMutationLeaveJourney = (journeyId: string) => {
  const queryClient = useQueryClient();

  const {mutate, isPending} = useMutation({
    mutationFn: () => leaveJourneyCall(journeyId),
    onSuccess: () => {
      return Promise.all([
        queryClient.invalidateQueries({
          queryKey: ['journeys'],
        }),
        queryClient.invalidateQueries({
          queryKey: ['journey', journeyId],
        }),
      ]);
    },
  });

  return {
    mutate,
    isPending,
  };
};

export default useMutationLeaveJourney;
