import {useMutation, useQueryClient} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const joinJourneyCall = async (journeyId: string) => {
  return api.getApiClient().journeyControllerJoinA<PERSON>ourney({id: journeyId});
};

const useMutationJoinJourney = (journeyId: string) => {
  const queryClient = useQueryClient();

  const {mutate, isPending} = useMutation({
    mutationFn: () => joinJourneyCall(journeyId),
    onSuccess: () => {
      return Promise.all([
        queryClient.invalidateQueries({
          queryKey: ['journeys'],
        }),
        queryClient.invalidateQueries({
          queryKey: ['journey', journeyId],
        }),
      ]);
    },
  });

  return {
    mutate,
    isPending,
  };
};

export default useMutationJoinJourney;
