import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

export const fetchUserSearch = async (
  search: string,
  challengeId: string,
  recommendedOnly?: boolean,
  pageParam?: string,
) => {
  return api
    .getApiClient()
    .userControllerSearch({
      after: pageParam,
      limit: 20,
      name: search,
      challengeId,
      recommendedOnly,
    })
    .then((result) => result.data.data);
};

const useUserSearch = (
  search: string,
  challengeId?: string,
  recommendedOnly?: boolean,
) => {
  const queryKey = ['userSearch', search];

  if (challengeId) {
    queryKey.push(challengeId);
  }

  const {
    data: users,
    refetch,
    isLoading,
    isRefetching,
    isFetchingNextPage,
    fetchNextPage,
    error,
  } = useInfiniteQuery({
    queryKey,
    queryFn: ({pageParam}) =>
      fetchUserSearch(
        search,
        challengeId || '',
        recommendedOnly ?? true,
        pageParam,
      ),
    staleTime: Infinity,
    initialPageParam: '',
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  return {
    data: users?.pages.flatMap((page) => page.data) ?? [],
    refetch,
    isLoading,
    isRefetching,
    isFetchingNextPage,
    fetchNextPage,
    error,
  };
};

export default useUserSearch;
