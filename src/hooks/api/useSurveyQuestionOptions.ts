import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (questionId: string) => {
  return api
    .getApiClient()
    .surveyControllerGetSurveyQuestionOptions({questionId})
    .then(({data}) => data.data);
};

const useSurveyQuestionOptions = (questionId: string) => {
  const {data, isLoading} = useQuery({
    queryKey: ['surveys', 'challenges', 'question', 'options', questionId],
    queryFn: () => fetch(questionId),
    initialData: [],
  });

  return {options: data, isLoading};
};

export default useSurveyQuestionOptions;
