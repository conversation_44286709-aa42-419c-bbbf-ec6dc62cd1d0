import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

export const useChallengeSoloAwardsOverview = (challengeId: string) => {
  const {data, isLoading, refetch, isRefetching} = useQuery({
    queryKey: ['awards', 'solo', challengeId],
    queryFn: async () =>
      api
        .getApiClient()
        .challengeControllerUserAwardsOverview({challengeId})
        .then((res) => res.data.data),
  });

  return {
    data,
    isLoading,
    refetch,
    isRefetching,
  };
};
