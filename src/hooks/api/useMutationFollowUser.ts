import {useMutation} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const followUserCall = async (
  userId: string,
  updated?: (userId: string) => void,
) => {
  api
    .getApiClient()
    .userControllerFollowUser({userId})
    .then(async ({data}) => {
      updated && updated(userId);

      return data;
    });
};

const useMutationFollowUser = (
  userId: string,
  handleFollow: () => void,
  updated?: (userId: string) => void,
) => {
  const {mutateAsync, isPending} = useMutation({
    mutationFn: () => followUserCall(userId, updated),
    onSuccess: () => {
      return handleFollow();
    },
  });

  return {
    mutateAsync,
    isPending,
  };
};

export default useMutationFollowUser;
