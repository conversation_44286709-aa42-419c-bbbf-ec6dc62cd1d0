import {useMutation, useQueryClient} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const leaveClubCall = async (clubId: string) => {
  return api.getApiClient().clubsControllerLeaveClub({clubId});
};

const useMutationLeaveClub = (clubId: string) => {
  const queryClient = useQueryClient();
  const {mutateAsync, isPending} = useMutation({
    mutationFn: () => leaveClubCall(clubId),
    onSuccess: () => {
      return Promise.all([
        queryClient.invalidateQueries({
          queryKey: ['club', clubId],
        }),
        queryClient.invalidateQueries({
          queryKey: ['clubs', 'personal'],
        }),
        queryClient.invalidateQueries({
          queryKey: ['clubs', 'recommendations'],
        }),
        queryClient.invalidateQueries({
          queryKey: ['club', clubId, 'stats'],
        }),
      ]);
    },
  });

  return {
    mutateAsync,
    isPending,
  };
};

export default useMutationLeaveClub;
