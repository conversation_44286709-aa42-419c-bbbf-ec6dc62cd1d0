import {useMutation} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {CreateClubEventDto} from '@gojoe/typescript-sdk';

const createClubEventCall = async (
  clubId: string,
  createClubEventDto: CreateClubEventDto,
) => {
  return api.getApiClient().clubsControllerCreateClubEvent({
    clubId,
    createClubEventDto,
  });
};

const useMutationCreateClubEvent = (clubId: string) => {
  const {mutateAsync, isPending} = useMutation({
    mutationFn: (createClubEventDto: CreateClubEventDto) =>
      createClubEventCall(clubId, createClubEventDto),
  });

  return {
    mutateAsync,
    isPending,
  };
};

export default useMutationCreateClubEvent;
