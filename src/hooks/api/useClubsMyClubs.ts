import {useInfiniteQuery} from '@tanstack/react-query';
import {DefaultApiClubsControllerMyClubsRequest} from '@gojoe/typescript-sdk';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (
  requestParameters: DefaultApiClubsControllerMyClubsRequest,
) => {
  return api
    .getApiClient()
    .clubsControllerMyClubs(requestParameters)
    .then((result) => result.data.data);
};
const useClubsMyClubs = (limit = 20) => {
  const {
    data,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: ['clubs', 'personal'],
    queryFn: ({pageParam}) => fetch({limit, after: pageParam}),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
    staleTime: Infinity,
  });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data
      ? data.pages[data.pages.length - 1].paging.cursors.hasNextPage
      : false,
  };
};

export default useClubsMyClubs;
