import {useMutation} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const joinBusinessCall = async (businessId: string, passcode: string) => {
  return api.getApiClient().businessUsersControllerCreate({
    createBusinessUserInputDto: {
      businessId,
      passcode,
    },
  });
};

const useMutationJoinBusiness = (
  businessId: string,
  passcode: string,
  onSuccessMutate: Promise<void>,
) => {
  const {mutateAsync, isPending} = useMutation({
    mutationFn: () => joinBusinessCall(businessId, passcode),
    onSuccess: () => onSuccessMutate,
  });

  return {
    mutateAsync,
    isPending,
  };
};

export default useMutationJoinBusiness;
