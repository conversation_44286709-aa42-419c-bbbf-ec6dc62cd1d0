import {useMutation, useQueryClient} from '@tanstack/react-query';
import {CreateActivityDto} from '@gojoe/typescript-sdk';
import {api} from '@/src/services/api';

const apiCall = async (createActivityDto: CreateActivityDto) => {
  return api
    .getApiClient()
    .activitiesControllerCreateActivity({createActivityDto});
};

export const useMutationManualActivity = () => {
  const queryClient = useQueryClient();

  const {mutateAsync, isPending, isError, error} = useMutation({
    mutationFn: (createActivityDto: CreateActivityDto) =>
      apiCall(createActivityDto),
    onSuccess: () => {
      // Invalidate relevant queries to refresh data
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return (
            queryKey.includes('activities') ||
            queryKey.includes('challenge') ||
            queryKey.includes('challenges') ||
            queryKey.includes('feed') ||
            (queryKey[0] === 'rewards' && queryKey[1] === 'balance')
          );
        },
      });
    },
  });

  return {
    mutateAsync,
    isPending,
    isError,
    error,
  };
};

export default useMutationManualActivity;
