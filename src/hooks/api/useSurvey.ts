import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (surveyId: string) => {
  return api
    .getApiClient()
    .surveyControllerGetSurvey({surveyId})
    .then(({data}) => data.data);
};

const useSurvey = (surveyId: string) => {
  const {data, isLoading} = useQuery({
    queryKey: ['surveys', surveyId],
    queryFn: () => fetch(surveyId),
    initialData: null,
  });

  return {data, isLoading};
};

export default useSurvey;
