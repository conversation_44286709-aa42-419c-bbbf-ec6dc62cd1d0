import {useMutation} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const leaveBusinessCall = async (businessId: string) => {
  return api.getApiClient().businessUsersControllerDelete({
    businessId,
  });
};

const useMutationLeaveBusiness = (
  businessId: string,
  onSuccessMutate: Promise<void>,
) => {
  const {mutateAsync, isPending} = useMutation({
    mutationFn: () => leaveBusinessCall(businessId),
    onSuccess: () => onSuccessMutate,
  });

  return {
    mutateAsync,
    isPending,
  };
};

export default useMutationLeaveBusiness;
