import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (userId: string, type: string, after?: string) => {
  return api
    .getApiClient()
    .userControllerGetUserPosts({
      userId,
      type,
      after,
    })
    .then((result) => result.data.data);
};

const useUserPosts = (userId: string, type: string) => {
  const queryKey = ['posts', userId];

  if (type === 'ACTIVITY') {
    queryKey.push('activities');
  }

  const {
    data,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: queryKey,
    queryFn: ({pageParam}) => fetch(userId, type, pageParam),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data
      ? data.pages[data.pages.length - 1].paging.cursors.hasNextPage
      : false,
  };
};

export default useUserPosts;
