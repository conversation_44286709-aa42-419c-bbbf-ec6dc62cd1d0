import {api} from '@/src/services/api';
import {useQuery} from '@tanstack/react-query';

const fetch = async (postId: string) => {
  return api
    .getApiClient()
    .postControllerReadPost({postId})
    .then((response) => response.data.data);
};

const usePost = (postId: string) => {
  const {data, isLoading} = useQuery({
    queryKey: ['post', postId],
    queryFn: () => fetch(postId),
    enabled: !!postId,
  });

  return {data, isLoading};
};

export default usePost;
