import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (businessId: string) => {
  return api
    .getApiClient()
    .businessControllerSurveys({businessId})
    .then(({data}) => data.data);
};
const useBusinessSurveys = (businessId: string) => {
  const {data, isLoading, refetch, error} = useQuery({
    queryKey: ['surveys', 'business', businessId],
    queryFn: () => fetch(businessId),
    initialData: [],
  });
  return {surveys: data, isLoading, refetch, error};
};

export default useBusinessSurveys;
