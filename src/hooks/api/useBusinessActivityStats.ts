import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {BusinessControllerGetActivityStatsPeriodEnum} from '@gojoe/typescript-sdk';

export const fetchBusinessActivityStats = async (
  businessId: string,
  period = 'week' as BusinessControllerGetActivityStatsPeriodEnum,
) => {
  return api
    .getApiClient()
    .businessControllerGetActivityStats({
      businessId,
      period,
    })
    .then((result) => result.data.data);
};

export default function useBusinessActivityStats(
  businessId: string,
  period: BusinessControllerGetActivityStatsPeriodEnum,
) {
  const {data, refetch, isRefetching} = useQuery({
    queryKey: ['business', businessId, period],
    queryFn: () => fetchBusinessActivityStats(businessId, period),
    initialData: {
      summary: {
        points: 0,
        time: 0,
        distance: 0,
        calories: 0,
      },
      trend: [],
    },
  });
  return {
    data,
    refetch,
    isRefetching,
  };
}
