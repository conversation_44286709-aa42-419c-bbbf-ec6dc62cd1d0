import {useMutation, useQueryClient} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const joinTeamChallenge = async ({
  challengeCode,
  challengeId,
  teamId,
}: {
  challengeCode: string;
  challengeId: string;
  teamId: string;
}) => {
  return api
    .getApiClient()
    .challengesControllerJoinTeamChallenge({
      joinTeamChallengeDto: {challengeCode},
      challengeId,
      teamId,
    })
    .then((response) => response.data);
};

const useJoinTeamChallenge = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: joinTeamChallenge,
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return (
            queryKey.includes('challenge') ||
            queryKey.includes('challenges') ||
            queryKey.includes('team')
          );
        },
      });
    },
  });
};

export default useJoinTeamChallenge;
