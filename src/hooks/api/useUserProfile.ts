import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

export const fetchUserProfile = async (userId: string) => {
  return api
    .getApiClient()
    .userControllerProfile({userId: userId})
    .then((result) => result.data.data);
};

const useUserProfile = (userId: string) => {
  const {data, isLoading, refetch, error} = useQuery({
    queryKey: ['user', userId],
    queryFn: () => fetchUserProfile(userId),
    staleTime: Infinity,
  });

  return {
    data,
    isLoading,
    refetch,
    error,
  };
};

export default useUserProfile;
