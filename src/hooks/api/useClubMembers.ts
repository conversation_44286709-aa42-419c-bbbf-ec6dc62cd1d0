import {useInfiniteQuery} from '@tanstack/react-query';
import {DefaultApiClubsControllerGetClubMembersRequest} from '@gojoe/typescript-sdk';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (
  requestParameters: DefaultApiClubsControllerGetClubMembersRequest,
) => {
  return api
    .getApiClient()
    .clubsControllerGetClubMembers(requestParameters)
    .then((result) => result.data.data);
};
const useClubMembers = (clubId: string, searchName?: string, limit = 20) => {
  const {
    data,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: ['clubs', 'members', clubId, searchName],
    queryFn: ({pageParam}) =>
      fetch({clubId, searchName, limit, after: pageParam}),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
    staleTime: Infinity,
  });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data
      ? data.pages[data.pages.length - 1].paging.cursors.hasNextPage
      : false,
  };
};

export default useClubMembers;
