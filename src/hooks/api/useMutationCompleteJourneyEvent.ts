import {useMutation, useQueryClient} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const completeEventCall = async (journeyId: string, eventId: string) => {
  return api.getApiClient().journeyControllerMarkStepCompleted({
    journeyId,
    stepCompletedDto: {stepId: eventId},
  });
};

const useMutationCompleteJourneyEvent = (
  journeyId: string,
  eventId: string,
) => {
  const queryClient = useQueryClient();

  const {mutate, isPending} = useMutation({
    mutationFn: () => completeEventCall(journeyId, eventId),
    onSuccess: () => {
      return Promise.all([
        queryClient.invalidateQueries({
          queryKey: ['journeys'],
        }),
        queryClient.invalidateQueries({
          queryKey: ['journey', journeyId],
        }),
        queryClient.invalidateQueries({
          queryKey: ['journey-events', journeyId],
        }),
        queryClient.invalidateQueries({
          queryKey: ['journey-step', eventId],
        }),
      ]);
    },
  });

  return {
    mutate,
    isPending,
  };
};

export default useMutationCompleteJourneyEvent;
