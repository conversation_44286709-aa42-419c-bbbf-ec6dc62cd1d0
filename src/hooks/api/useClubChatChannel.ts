import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (clubId: string) => {
  return api
    .getApiClient()
    .clubsControllerGetClubChatChannel({clubId})
    .then(({data}) => data.data);
};

const useClubChatChannel = (clubId: string) => {
  const {data, isLoading} = useQuery({
    queryKey: ['club', clubId, 'chatChannel'],
    queryFn: () => fetch(clubId),
  });

  return {
    data,
    isLoading,
  };
};

export default useClubChatChannel;
