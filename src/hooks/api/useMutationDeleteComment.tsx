import {useMutation} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const deleteCommentCall = async (postId: string, commentId: string) => {
  return api.getApiClient().postControllerDeleteComment({
    postId,
    commentId,
  });
};

const useMutationDeleteComment = (
  postId: string,
  commentId: string,
  onSuccessMutate: (data: any) => Promise<any>,
  invalidateQueries?: () => void,
) => {
  const {mutateAsync, isPending} = useMutation({
    mutationFn: () => deleteCommentCall(postId, commentId),
    onSettled: () => (invalidateQueries ? invalidateQueries() : null),
    onSuccess: (update) => onSuccessMutate(update),
  });

  return {
    mutateAsync,
    isPending,
  };
};

export default useMutationDeleteComment;
