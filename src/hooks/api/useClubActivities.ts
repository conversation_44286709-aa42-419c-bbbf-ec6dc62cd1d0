import {useInfiniteQuery} from '@tanstack/react-query';
import {DefaultApiClubsControllerGetClubActivitiesRequest} from '@gojoe/typescript-sdk';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (
  requestParameters: DefaultApiClubsControllerGetClubActivitiesRequest,
) => {
  return api
    .getApiClient()
    .clubsControllerGetClubActivities(requestParameters)
    .then((result) => result.data.data);
};
const useClubActivities = (clubId: string, limit = 20) => {
  const {
    data,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: ['clubs', 'activities', clubId],
    queryFn: ({pageParam}) => fetch({clubId, limit, after: pageParam}),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data
      ? data.pages[data.pages.length - 1].paging.cursors.hasNextPage
      : false,
  };
};

export default useClubActivities;
