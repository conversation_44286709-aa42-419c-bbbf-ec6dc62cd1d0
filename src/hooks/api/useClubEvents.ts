import {useQuery} from '@tanstack/react-query';
import {DefaultApiClubsControllerGetClubEventsRequest} from '@gojoe/typescript-sdk';
import {api} from '@/src/services/api';

const fetch = async (
  requestParameters: DefaultApiClubsControllerGetClubEventsRequest,
) => {
  return api
    .getApiClient()
    .clubsControllerGetClubEvents(requestParameters)
    .then((result) => result.data.data);
};
const useClubEvents = (clubId: string) => {
  const {data, isLoading} = useQuery({
    queryKey: ['clubs', 'events', clubId],
    queryFn: () =>
      fetch({
        clubId,
      }),
  });

  return {
    data,
    isLoading,
  };
};

export default useClubEvents;
