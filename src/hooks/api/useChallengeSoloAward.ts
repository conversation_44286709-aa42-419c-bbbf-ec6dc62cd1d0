import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

export const useChallengeSoloAward = (challengeId: string, awardId: string) => {
  const {data, isLoading, refetch} = useQuery({
    queryKey: ['awards', 'solo', challengeId, awardId],
    queryFn: async () =>
      api
        .getApiClient()
        .challengeControllerUserAwards({challengeId, awardId})
        .then((res) => res.data.data),
  });

  return {
    data,
    isLoading,
    refetch,
  };
};
