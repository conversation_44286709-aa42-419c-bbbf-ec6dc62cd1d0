import {useMutation} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {
  LoginBodyDto,
  LoginRequestPinInputDto,
  RegisterBodyDto,
} from '@gojoe/typescript-sdk';

const emailRequestPinApi = async (
  loginRequestPinInputDto: LoginRequestPinInputDto,
) => {
  const response = await api.getApiClient().userAuthControllerRequestPin({
    loginRequestPinInputDto,
  });
  return response.data.data;
};
const emailLoginApi = async (loginBodyDto: LoginBodyDto) => {
  const response = await api.getApiClient().userAuthControllerLoginEmail({
    loginBodyDto,
  });
  return response.data.data;
};

const registerApiCall = async (registerBodyDto: RegisterBodyDto) => {
  const response = await api
    .getApiClient()
    .userAuthControllerRegisterWithEmail({
      registerBodyDto,
    });
  return response.data.data;
};

export function useAuthWithEmail() {
  const {isPending: requestPinLoading, mutateAsync: requestPin} = useMutation({
    mutationFn: (variables: LoginRequestPinInputDto) =>
      emailRequestPinApi(variables),
  });
  const {isPending: emailLoading, mutateAsync: emailLogin} = useMutation({
    mutationFn: (variables: LoginBodyDto) => emailLoginApi(variables),
  });
  const {isPending: registerLoading, mutateAsync: register} = useMutation({
    mutationFn: (variables: RegisterBodyDto) => registerApiCall(variables),
  });

  return {
    isPending: requestPinLoading || emailLoading || registerLoading,
    requestPin,
    emailLogin,
    register,
  };
}
