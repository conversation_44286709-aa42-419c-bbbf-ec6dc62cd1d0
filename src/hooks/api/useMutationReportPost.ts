import {useMutation} from '@tanstack/react-query';
import {api} from '@/src/services/api';

type OnSuccessMutate = () => void | Promise<[void, void, void]>;

const reportCall = async (postId: string, reason: string) => {
  return api.getApiClient().postControllerReportPost({
    postId,
    reportPostInputDto: {reason},
  });
};

const useMutationReportPost = (
  postId: string,
  reason: string,
  onSuccessMutate: OnSuccessMutate,
) => {
  const {mutate, isPending} = useMutation({
    mutationKey: ['post', postId],
    mutationFn: () => reportCall(postId, reason),
    onSuccess: () => onSuccessMutate(),
  });

  return {
    mutate,
    isPending,
  };
};

export default useMutationReportPost;
