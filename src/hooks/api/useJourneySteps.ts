import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {DefaultApiJourneyControllerGetStepsRequest} from '@gojoe/typescript-sdk';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (
  requestParameters: DefaultApiJourneyControllerGetStepsRequest,
) => {
  return api
    .getApiClient()
    .journeyControllerGetSteps(requestParameters)
    .then((result) => result.data.data);
};

const useJourneySteps = (journeyId: string) => {
  const {data, fetchNextPage, isFetchingNextPage, isRefetching, isLoading} =
    useInfiniteQuery({
      queryKey: ['journey-events', journeyId],
      queryFn: ({pageParam}) => fetch({id: journeyId, after: pageParam}),
      getNextPageParam: (lastPage) => getNextPageParam(lastPage),
      initialPageParam: '',
    });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    hasNextPage: data
      ? data.pages[data.pages.length - 1].paging.cursors.hasNextPage
      : false,
    fetchNextPage,
    isFetchingNextPage,
    isRefetching,
    isLoading,
  };
};

export default useJourneySteps;
