import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (journeyId: string) => {
  return api
    .getApiClient()
    .getStreamControllerGetChatId({journeyId})
    .then((res) => res.data.data.id);
};

const useJourneyChatChannel = (journeyId: string) => {
  const {data, isLoading} = useQuery({
    queryKey: ['journey', journeyId, 'chatChannel'],
    queryFn: () => fetch(journeyId),
  });

  return {
    data,
    isLoading,
  };
};

export default useJourneyChatChannel;
