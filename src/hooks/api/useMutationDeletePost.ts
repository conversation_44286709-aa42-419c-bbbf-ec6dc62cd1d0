import {useMutation} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {handleDeletePost} from '@/src/services/queryClient';

const deletePostCall = async (postId: string) => {
  return api.getApiClient().postControllerDelete({
    postId,
  });
};

const useMutationDeletePost = (postId: string) => {
  const {mutate, isPending} = useMutation({
    mutationFn: () => deletePostCall(postId),
    onSuccess: () => handleDeletePost(postId),
  });

  return {
    mutate,
    isPending,
  };
};

export default useMutationDeletePost;
