import {useMutation} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {handleDeletePost} from '@/src/services/queryClient';

const deletePostCall = async (postId: string) => {
  return api.getApiClient().postControllerDelete({
    postId,
  });
};

const useMutationDeletePost = (
  postId: string,
  onSuccessCallback?: () => Promise<void>,
) => {
  const {mutate, isPending} = useMutation({
    mutationFn: () => deletePostCall(postId),
    onSuccess: async () => {
      // Call the custom callback first (e.g., router.back())
      if (onSuccessCallback) {
        await onSuccessCallback();
      }
      // Then handle the cache updates
      await handleDeletePost(postId);
    },
  });

  return {
    mutate,
    isPending,
  };
};

export default useMutationDeletePost;
