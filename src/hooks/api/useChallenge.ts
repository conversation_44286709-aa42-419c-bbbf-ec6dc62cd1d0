import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (challengeId: string, passcode?: string) => {
  return api
    .getApiClient()
    .challengesControllerGetChallengeDetails({challengeId, code: passcode})
    .then(({data}) => data.data);
};
const useChallenge = (challengeId: string, passcode?: string) => {
  const {data, isLoading} = useQuery({
    queryKey: ['challenge', challengeId, passcode],
    queryFn: () => fetch(challengeId, passcode),
    staleTime: Infinity,
  });

  return {data, isLoading};
};

export default useChallenge;
