import {api} from '@/src/services/api';
import {useQuery} from '@tanstack/react-query';

const getInsights = async (userId = 'me') => {
  const {data} = await api
    .getApiClient()
    .userControllerThisWeekActivitiesOverview({userId});
  return data.data.reverse();
};

export default function useUserInsightsThisWeek(userId = 'me') {
  const {data, isLoading} = useQuery({
    queryKey: ['user', 'insights', 'this-week', userId],
    queryFn: () => getInsights(userId),
  });

  return {
    data: data ?? [],
    isLoading: isLoading,
  };
}
