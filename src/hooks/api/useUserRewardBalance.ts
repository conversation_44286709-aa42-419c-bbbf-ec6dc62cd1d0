import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async () => {
  return api
    .getApiClient()
    .rewardsControllerGetBalance()
    .then((res) => res.data.data);
};

export const useUserRewardBalance = () => {
  const {data, isLoading} = useQuery({
    queryKey: ['rewards', 'balance'],
    queryFn: fetch,
  });
  return {
    data,
    isLoading,
  };
};
