import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (pageParam?: string, limit = 100, businessId?: string) => {
  return api
    .getApiClient()
    .journeyControllerList({after: pageParam, limit: `${limit}`, businessId})
    .then((resp) => resp.data.data);
};
export function useJourneys(businessId?: string, limit = 100) {
  const queryKey = businessId ? ['journeys', businessId] : ['journeys'];

  const {
    data,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: queryKey,
    queryFn: ({pageParam}) => fetch(pageParam, limit, businessId),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data
      ? data.pages[data.pages.length - 1].paging.cursors.hasNextPage
      : false,
  };
}
