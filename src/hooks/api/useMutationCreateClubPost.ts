import {useMutation} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {CreateClubPostDto} from '@gojoe/typescript-sdk';

const createClubPostCall = async (
  clubId: string,
  createClubPostDto: CreateClubPostDto,
) => {
  return api.getApiClient().clubsControllerCreateClubPost({
    clubId,
    createClubPostDto,
  });
};

const useMutationCreateClubPost = (clubId: string) => {
  const {mutateAsync, isPending} = useMutation({
    mutationFn: (createClubPostDto: CreateClubPostDto) =>
      createClubPostCall(clubId, createClubPostDto),
  });

  return {
    mutateAsync,
    isPending,
  };
};

export default useMutationCreateClubPost;
