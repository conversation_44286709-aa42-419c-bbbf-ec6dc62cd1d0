import {useMutation} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {SurveyAnswerInputDto} from '@gojoe/typescript-sdk';

const apiCall = async (input: SurveyAnswerInputDto) => {
  return api
    .getApiClient()
    .surveyAnswerControllerAddAnswer({surveyAnswerInputDto: input})
    .then(({data}) => data.data);
};

const useMutationAddQuestionAnswer = () => {
  const {mutateAsync, isPending} = useMutation({
    mutationFn: (input: SurveyAnswerInputDto) => apiCall(input),
  });

  return {
    addAnswer: mutateAsync,
    isPending,
  };
};

export default useMutationAddQuestionAnswer;
