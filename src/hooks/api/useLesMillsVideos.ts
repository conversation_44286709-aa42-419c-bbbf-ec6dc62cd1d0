import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (programId: string, language: string, after?: string) => {
  return api
    .getApiClient()
    .lesMillsControllerVideos({programId, language, after})
    .then((resp) => resp.data.data);
};

export function useLesMillsVideos(programId: string) {
  const language = 'EN';
  const {
    data,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: ['les-mills', 'videos', programId, language],
    queryFn: ({pageParam}) => fetch(programId, language, pageParam),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  const users = data?.pages.flatMap((page) => page.data) ?? [];

  return {
    data: users,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data?.pages[data.pages.length - 1].paging.cursors.hasNextPage,
  };
}
