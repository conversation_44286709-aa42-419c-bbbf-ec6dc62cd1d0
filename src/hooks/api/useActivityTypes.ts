import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

export const HISTORY_KEY = 'user_activity_type_history';

const fetch = async () => {
  return api
    .getApiClient()
    .sportControllerGetAllSports()
    .then((response) => response.data.data);
};

export const useActivityTypes = () => {
  const {data, refetch, isRefetching} = useQuery({
    queryKey: ['activity_types'],
    queryFn: () => fetch(),
    initialData: [],
  });
  return {
    data,
    refetch,
    isRefetching,
  };
};
