import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (challengeId: string) => {
  return api
    .getApiClient()
    .challengesControllerGetChallengeChatChannel({challengeId})
    .then(({data}) => data.data);
};
const useChallengeChatChannel = (challengeId: string) => {
  const {data, isLoading} = useQuery({
    queryKey: ['challenge', challengeId, 'chatChannel'],
    queryFn: () => fetch(challengeId),
  });

  return {
    data,
    isLoading,
  };
};

export default useChallengeChatChannel;
