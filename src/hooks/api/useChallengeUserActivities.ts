import {useInfiniteQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';
import {DefaultApiChallengesControllerChallengeUserActivitiesRequest} from '@gojoe/typescript-sdk/api';
import {getNextPageParam} from '@/src/utils/api';

const fetch = async (
  request: DefaultApiChallengesControllerChallengeUserActivitiesRequest,
) => {
  return api
    .getApiClient()
    .challengesControllerChallengeUserActivities(request)
    .then((res) => res.data.data);
};
export function useChallengeUserActivities(
  challengeId: string,
  userId: string,
) {
  const queryKey = ['challenge', challengeId, 'user', userId, 'activities'];

  const {
    data,
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: '',
    queryKey: queryKey,
    queryFn: ({pageParam}) => fetch({challengeId, userId, after: pageParam}),
    getNextPageParam: (lastPage) => getNextPageParam(lastPage),
  });

  return {
    data: data ? data.pages.flatMap((page) => page.data) : [],
    error,
    fetchNextPage,
    isLoading,
    refetch,
    isRefetching,
    isFetchingNextPage,
    hasNextPage: data
      ? data.pages[data.pages.length - 1].paging.cursors.hasNextPage
      : false,
  };
}
