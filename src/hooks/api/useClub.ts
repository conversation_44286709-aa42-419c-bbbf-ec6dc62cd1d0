import {useQuery} from '@tanstack/react-query';
import {api} from '@/src/services/api';

const fetch = async (clubId: string) => {
  return api
    .getApiClient()
    .clubsControllerGetClub({clubId})
    .then((result) => result.data.data);
};
const useClub = (clubId: string) => {
  const {data, isLoading, error} = useQuery({
    queryKey: ['club', clubId],
    queryFn: () => fetch(clubId),
    staleTime: Infinity,
  });

  return {
    data,
    isLoading,
    error,
  };
};

export default useClub;
