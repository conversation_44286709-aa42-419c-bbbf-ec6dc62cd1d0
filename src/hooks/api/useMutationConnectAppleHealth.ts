import {api} from '@/src/services/api';
import {useMutation} from '@tanstack/react-query';

const connectAppleHealthApiCall = async () => {
  return api.getApiClient().appleHealthControllerConnectToAppleHealth();
};
export const useMutationConnectAppleHealth = () => {
  const {mutate, isPending, isError} = useMutation({
    mutationFn: async () => connectAppleHealthApiCall(),
  });

  return {
    connect: mutate,
    isPending,
    isError,
  };
};
