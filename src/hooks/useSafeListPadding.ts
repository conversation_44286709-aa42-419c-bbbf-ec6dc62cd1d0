import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {useHeaderHeight} from '@react-navigation/elements';

export const useSafeListPadding = () => {
  const {top, bottom} = useSafeAreaInsets();
  const tabBarHeight = useBottomTabBarHeight();
  const headerHeight = useHeaderHeight();

  return {
    paddingBottom: top + bottom + headerHeight + tabBarHeight,
    paddingTop: top,
    headerHeight,
    tabBarHeight,
  };
};
