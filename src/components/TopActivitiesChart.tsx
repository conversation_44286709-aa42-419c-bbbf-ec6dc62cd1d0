import React, {useMemo, useState} from 'react';
import {Text, XStack, YStack} from 'tamagui';
import {ChevronDown, ChevronUp} from '@tamagui/lucide-icons';
import ActivityIcon from '@/src/components/ActivityIcon';

type Props = {
  data: Record<string, number>;
  total: number;
};

const TopActivitiesChart: React.FC<Props> = ({data = {}, total}) => {
  const [expanded, setExpanded] = useState(false);

  const sorted = useMemo(() => {
    return Object.entries(data)
      .sort((a, b) => b[1] - a[1])
      .map(([name, count]) => ({name, count}));
  }, [data]);

  const top = expanded ? sorted : sorted.slice(0, 5);
  const max = sorted[0]?.count ?? 1;

  return (
    <YStack
      backgroundColor='$background'
      borderRadius={16}
      padding='$5'
      gap='$3'>
      <XStack justifyContent='space-between' alignItems='center'>
        <Text fontWeight='700' fontSize={18}>
          Activities
        </Text>
        <Text fontWeight='600' fontSize={18}>
          {total}
        </Text>
      </XStack>

      <YStack gap='$3.5' marginTop='$5'>
        {top.map((item) => (
          <XStack key={item.name} alignItems='center' gap='$3.5'>
            <ActivityIcon name={item.name} color='$primary' size={18} />
            <YStack
              flex={1}
              height={8}
              backgroundColor='$grey3'
              borderRadius={4}>
              <YStack
                height='100%'
                width={`${(item.count / max) * 100}%`}
                backgroundColor='$red10'
                borderRadius={4}
              />
            </YStack>
            <Text
              color='$grey2'
              fontWeight='500'
              fontSize={12}
              width={50}
              textAlign='right'>
              {item.count.toLocaleString()}
            </Text>
          </XStack>
        ))}
      </YStack>

      {sorted.length > 5 && (
        <XStack
          onPress={() => setExpanded((prev) => !prev)}
          alignItems='center'
          justifyContent='flex-end'
          gap='$1'>
          <Text color='$primary' fontSize={12} fontWeight='500'>
            {expanded ? 'Show less' : 'Show more'}
          </Text>
          {expanded ? (
            <ChevronUp size={16} color='$primary' />
          ) : (
            <ChevronDown size={16} color='$primary' />
          )}
        </XStack>
      )}
    </YStack>
  );
};

export default TopActivitiesChart;
