import React from 'react';
import {Image, XStack} from 'tamagui';
import {useUserRewardBalance} from '@/src/hooks/api/useUserRewardBalance';
import StyledText from '@/src/components/UI/StyledText';
import {useRouter} from 'expo-router';

const TopBarRewards: React.FC = () => {
  const {data} = useUserRewardBalance();
  const router = useRouter();
  const handleOnPres = () => {
    router.push({
      pathname: '/(app)/(tabs)/locker',
    });
  };
  return (
    <XStack
      onPress={handleOnPres}
      height={32}
      paddingHorizontal={8}
      alignItems='center'
      backgroundColor='$yellow'
      gap='$2'
      borderRadius={16}>
      <Image
        source={require('@/assets/images/icons/icon-circles-three.png')}
        width={20}
        height={20}
      />
      <StyledText fontWeight='700' fontSize={10}>
        {data ? data.balance : 0}
      </StyledText>
    </XStack>
  );
};

export default TopBarRewards;
