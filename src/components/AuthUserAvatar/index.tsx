import React from 'react';
import {useSession} from '@/src/contexts/SessionContext';
import {AvatarProps} from '@tamagui/avatar';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';
import {useNavigation} from 'expo-router';
import {DrawerActions} from '@react-navigation/native';
import {YStack} from 'tamagui';
import {Menu} from '@tamagui/lucide-icons';
import {triggerHaptics} from '@/src/utils/haptics';

const AuthUserAvatar: React.FC<AvatarProps> = ({...props}) => {
  const navigation = useNavigation();
  const {user} = useSession();

  const handleOpenDrawer = async () => {
    await triggerHaptics();
    navigation.dispatch(DrawerActions.toggleDrawer());
  };
  if (!user) {
    return null;
  }
  return (
    <YStack>
      <UserAvatar
        user={user}
        circular
        size='$2.5'
        {...props}
        onPress={handleOpenDrawer}
      />
      <Menu
        position='absolute'
        bottom={0}
        left={0}
        backgroundColor='$background'
        size={12}
      />
    </YStack>
  );
};

export default AuthUserAvatar;
