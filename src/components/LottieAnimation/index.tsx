import React from 'react';
import {StyleSheet} from 'react-native';
import LottieView, {AnimationObject} from 'lottie-react-native';

type ColorFilter = {
  keypath: string;
  color: string;
};

interface Props {
  animationData: AnimationObject;
  loop?: boolean;
  autoPlay?: boolean;
  style?: any;
  colorFilters?: ColorFilter[];
}

const LottieAnimation: React.FC<Props> = ({
  animationData,
  loop = false,
  autoPlay = true,
  style = {},
}) => {
  return (
    <LottieView
      source={animationData}
      autoPlay={autoPlay}
      loop={loop}
      style={[styles.default, style]}
    />
  );
};

const styles = StyleSheet.create({
  default: {
    width: '100%',
    height: '100%',
  },
});
export default LottieAnimation;
