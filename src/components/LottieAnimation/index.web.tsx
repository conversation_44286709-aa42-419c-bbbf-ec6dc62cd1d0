import React from 'react';

import <PERSON><PERSON> from 'react-lottie';

interface Props {
  animationData: string;
  loop?: boolean;
  autoPlay?: boolean;
  style?: any;
}
const LottieAnimation: React.FC<Props> = ({
  animationData,
  loop = false,
  autoPlay = true,
  style = {},
}) => {
  const defaultOptions = {
    loop,
    autoplay: autoPlay,
    animationData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };

  return <Lottie options={defaultOptions} />;
};

export default LottieAnimation;
