import React from 'react';
import {useTranslation} from 'react-i18next';
import {Input, Spinner, XStack} from 'tamagui';
import {Search} from '@tamagui/lucide-icons';
import {FontAwesome} from '@expo/vector-icons';
import I18nText from '../I18nText';

interface Props {
  value: string;
  onChange: (value: string) => void;
  onPressCancel?: () => void;
  isLoading?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  onClear?: () => void;
  isFocused?: boolean;
}

const SearchBar: React.FC<Props> = ({
  value,
  onChange,
  isLoading = false,
  onFocus,
  onBlur,
  onClear,
  onPressCancel,
  isFocused = false,
}) => {
  const {t} = useTranslation();
  const rightIcon = isLoading ? (
    <Spinner />
  ) : (
    <XStack onPress={onClear}>
      <FontAwesome name='times-circle' color='#ACAFBB' size={24} />
    </XStack>
  );

  return (
    <XStack height={44} marginVertical='$3' alignItems='center'>
      <XStack
        flex={1}
        height={44}
        backgroundColor={isFocused ? '#FFF' : '#F7F7F7'}
        alignItems='center'
        paddingHorizontal='$3'
        borderWidth={1}
        borderColor='#E5E5E5'
        borderRadius='$4'
        overflow='hidden'>
        <Search size='$1' color='#ACAFBB' />
        <Input
          flex={1}
          placeholder={t('Search...')}
          backgroundColor={isFocused ? '#FFF' : '#F7F7F7'}
          borderWidth={0}
          value={value}
          onChangeText={onChange}
          onFocus={onFocus}
          onBlur={onBlur}
        />
        {rightIcon}
      </XStack>
      {onPressCancel && (
        <XStack marginLeft={16} onPress={onPressCancel}>
          <I18nText fontSize={16} fontWeight={500} color='#C43F2D'>
            Cancel
          </I18nText>
        </XStack>
      )}
    </XStack>
  );
};

export default SearchBar;
