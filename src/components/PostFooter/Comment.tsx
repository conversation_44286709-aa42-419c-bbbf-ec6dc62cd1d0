import React, {useEffect, useState} from 'react';
import {useRouter} from 'expo-router';
import {XStack} from 'tamagui';
import {PostListDto, PostSingleDto} from '@gojoe/typescript-sdk';

import {triggerHaptics} from '@/src/utils/haptics';
import {CommentIcon} from '@/src/components/GoJoeIcon';
import StyledText from '../UI/StyledText';

interface Props {
  post: PostListDto | PostSingleDto;
}

const Comment: React.FC<Props> = ({post}) => {
  const router = useRouter();
  const [info, setInfo] = useState({
    isMe: !!post.myComments?.length,
    count: post.commentsCountTotal,
  });

  useEffect(() => {
    if (post && post.commentsCountTotal !== info.count) {
      setInfo({
        isMe: !!post?.myComments?.length,
        count: post?.commentsCountTotal || 0,
      });
    }
  }, [post, info.count]);

  const onPress = async () => {
    await triggerHaptics();
    router.push({
      pathname: '/comments/[postId]',
      params: {
        postId: post.id,
        postUserId: post.user?.id,
        postBusinessId: post.business?.id,
      },
    });
  };

  const colorActive = info.isMe ? '#C43F2D' : '$grey1';

  return (
    <XStack
      h='$4.5'
      alignItems='center'
      justifyContent='center'
      borderRadius='$4'
      paddingHorizontal='$3'
      onPress={onPress}>
      <CommentIcon size={24} color={colorActive} />
      {info.count ? (
        <StyledText fontSize={12} fontWeight={500} color='#646875' padding={8}>
          {info.count}
        </StyledText>
      ) : null}
    </XStack>
  );
};

export default Comment;
