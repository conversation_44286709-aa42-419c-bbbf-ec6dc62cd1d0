import React from 'react';
import {StyleSheet} from 'react-native';
import {LinearGradient} from 'expo-linear-gradient';
import {ChallengeMapDto} from '@gojoe/typescript-sdk';
import ChallengeMap from './Map';

interface Props {
  map: ChallengeMapDto;
  currentZoomLevel: number;
  showGradient?: boolean;
  overallPoints?: number;
}

const ChallengeMapContainer: React.FC<Props> = ({
  map,
  showGradient,
  currentZoomLevel,
  overallPoints,
}) => {
  if (showGradient) {
    return (
      <LinearGradient
        colors={['#00000096', '#00000060', 'transparent']}
        style={styles.gradient}>
        <ChallengeMap
          map={map}
          currentZoomLevel={currentZoomLevel}
          overallPoints={overallPoints}
        />
      </LinearGradient>
    );
  }
  return (
    <ChallengeMap
      map={map}
      currentZoomLevel={currentZoomLevel}
      overallPoints={overallPoints}
    />
  );
};

const styles = StyleSheet.create({
  gradient: {
    position: 'absolute',
    height: 196,
    width: '100%',
    top: 0,
  },
});

export default ChallengeMapContainer;
