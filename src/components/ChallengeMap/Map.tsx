import React from 'react';
import {Pressable} from 'react-native';
import {SheetManager} from 'react-native-actions-sheet';
import * as turf from '@turf/turf';
import {ChallengeMapDto} from '@gojoe/typescript-sdk';
import Mapbox from '@/src/services/mapbox';
import Pin from './Pin';
import {MapPin} from '@tamagui/lucide-icons';
import type {LineString} from 'geojson';

interface Props {
  map: ChallengeMapDto;
  currentZoomLevel: number;
  overallPoints?: number;
}

const ChallengeMap: React.FC<Props> = ({
  map,
  currentZoomLevel,
  overallPoints = 0,
}) => {
  const routeShape = map.routeShape as LineString;
  if (!routeShape) {
    return null;
  }
  const line = turf.lineString(routeShape.coordinates);
  const totalDistance = turf.length(line, {units: 'kilometers'});
  /**
   * negative multipliers < -1 will multiply the distance covered
   * Eg. pointsDistanceMultiplier = -2 => 2 * overallPoints, etc.
   * positive multipliers > 1 will divide the distance covered
   * Eg. pointsDistanceMultiplier = 2 => overallPoints / 2, etc.
   */
  if (!map.pointsDistanceMultiplier) {
    map.pointsDistanceMultiplier = 1;
  }
  const distanceCovered =
    map.pointsDistanceMultiplier < 0
      ? Math.abs(map.pointsDistanceMultiplier) * overallPoints
      : overallPoints / (map.pointsDistanceMultiplier ?? 1);
  const distanceCoveredInKm =
    distanceCovered < totalDistance ? distanceCovered : totalDistance - 0.1;
  const pointAlongTheRout = turf.along(line, distanceCoveredInKm, {
    units: 'kilometers',
  });
  let cumulativeDistance = 0;
  let insertIndex = 0;
  for (let i = 0; i < routeShape.coordinates.length - 1; i++) {
    const segment = turf.lineString([
      routeShape.coordinates[i],
      routeShape.coordinates[i + 1],
    ]);
    const segmentLength = turf.length(segment, {units: 'kilometers'});
    cumulativeDistance += segmentLength;

    if (cumulativeDistance >= distanceCoveredInKm) {
      insertIndex = i + 1; // The point should be inserted after the current index
      break;
    }
  }
  const coveredCoordinates = [
    ...routeShape.coordinates.slice(0, insertIndex),
    pointAlongTheRout.geometry.coordinates,
  ];
  const remainCoordinates = [
    pointAlongTheRout.geometry.coordinates,
    ...routeShape.coordinates.slice(insertIndex, routeShape.coordinates.length),
  ];
  const coverRouteShape: LineString = {
    type: 'LineString',
    coordinates: coveredCoordinates,
  };

  const remainRouteShape: LineString = {
    type: 'LineString',
    coordinates: remainCoordinates,
  };
  const pointSize = currentZoomLevel > 8 ? 64 : 32;
  const circleLayer = {
    circleColor: map.lineColorCovered,
    circleRadius: 10,
  };

  return (
    <>
      <Mapbox.Camera
        zoomLevel={map.zoomLevel ?? 5}
        centerCoordinate={pointAlongTheRout.geometry.coordinates}
      />
      {map.points.map((point) => {
        const handleOnSelect = async () => {
          if (!point.showAfter || point.showAfter < distanceCoveredInKm) {
            await SheetManager.show('map_point', {
              payload: {
                item: point,
              },
            });
          }
        };

        return (
          <Mapbox.MarkerView
            id={point.id}
            key={point.id}
            coordinate={point.location as [number, number]}>
            <Pressable onPressIn={handleOnSelect}>
              {!point.showAfter || point.showAfter < distanceCoveredInKm ? (
                <Pin image={point.image} color={point.color} size={pointSize} />
              ) : (
                <MapPin size={pointSize} color={point.color} />
              )}
            </Pressable>
          </Mapbox.MarkerView>
        );
      })}
      <Mapbox.ShapeSource id='routeSourceCovered' shape={coverRouteShape}>
        <Mapbox.LineLayer
          id='routeLineCovered'
          style={{
            lineWidth: map.lineWidthCovered,
            lineColor: map.lineColorCovered,
          }}
        />
      </Mapbox.ShapeSource>
      <Mapbox.ShapeSource id='routeSourceRemain' shape={remainRouteShape}>
        <Mapbox.LineLayer
          id='routeLineRemain'
          style={{
            lineWidth: map.lineWidth,
            lineColor: map.lineColor,
            lineDasharray: map.lineDashed ? [4, 4] : undefined,
          }}
        />
      </Mapbox.ShapeSource>
      <Mapbox.ShapeSource
        id='circleSource'
        shape={{
          type: 'Point',
          coordinates: pointAlongTheRout.geometry.coordinates,
        }}>
        <Mapbox.CircleLayer id='circleLayer' style={circleLayer} />
      </Mapbox.ShapeSource>
    </>
  );
};

export default ChallengeMap;
