import React, {useCallback} from 'react';
import {Alert} from 'react-native';
import {Button, YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';
import {useTranslation} from 'react-i18next';
import useTeam from '../hooks/api/useTeam';
import useChallenge from '../hooks/api/useChallenge';
import {useRouter} from 'expo-router';
import useLeaveTeam from '../hooks/api/useLeaveTeam';
import {useToastController} from '@tamagui/toast';

interface LeaveTeamButtonProps {
  teamId: string;
  challengeId: string;
}

const LeaveTeamButton: React.FC<LeaveTeamButtonProps> = ({
  teamId,
  challengeId,
}) => {
  const {t} = useTranslation();
  const {data: team} = useTeam(challengeId, teamId);
  const {data: challenge} = useChallenge(challengeId);
  const router = useRouter();
  const toast = useToastController();

  const {mutate: leaveTeam, isPending: isLeaving} = useLeaveTeam(
    challengeId,
    teamId,
  );

  const handleLeaveTeam = useCallback(() => {
    if (isLeaving) return;

    leaveTeam(teamId, {
      onSuccess: () => {
        toast.show(t('You have successfully left the team.'), {
          type: 'success',
        });
        router.back();
        router.replace({
          pathname: '/challenge/[challengeId]',
          params: {challengeId, passcode: challenge?.code},
        });
      },
      onError: () => {
        toast.show(t('Failed to leave the team. Please try again.'), {
          type: 'error',
        });
      },
    });
  }, [
    isLeaving,
    leaveTeam,
    teamId,
    toast,
    t,
    router,
    challengeId,
    challenge?.code,
  ]);

  const handleLeaveTeamPress = useCallback(async () => {
    await triggerHaptics();

    Alert.alert(
      t('Leave Team'),
      t('Are you sure you want to leave this team?'),
      [
        {
          text: t('Cancel'),
          style: 'cancel',
        },
        {
          text: t('Leave'),
          style: 'destructive',
          onPress: handleLeaveTeam,
        },
      ],
    );
  }, [handleLeaveTeam, t]);

  if (!team || !challenge || !team.joined) {
    return null;
  }

  return (
    <Button
      themeReset
      onPressIn={handleLeaveTeamPress}
      paddingHorizontal={0}
      borderWidth={1}>
      <I18nText
        color={isLeaving ? '$grey3' : '$grey2'}
        fontSize={12}
        fontWeight='500'
        textDecorationLine='underline'>
        {isLeaving ? 'Leaving...' : 'Leave Team'}
      </I18nText>
    </Button>
  );
};

export default LeaveTeamButton;
