import React from 'react';
import {WebView} from 'react-native-webview';
import {Spinner, YStack} from 'tamagui';

interface Props {
  uri: string;
}

const WebViewGoJoePages: React.FC<Props> = ({uri}) => {
  return (
    <WebView
      source={{
        uri: uri,
      }}
      javaScriptEnabled={true}
      startInLoadingState
      injectedJavaScript={
        '(function() {document.getElementsByClassName("press-header")[0].style.display = "none";document.getElementsByClassName("heade")[0].style.display = "none";document.getElementsByClassName("cc-banner")[0].style.display = "none";document.getElementsByClassName("footer-section")[0].style.display = "none";document.getElementsById("PopupSignupForm_0").style.display = "none";})();'
      }
      onMessage={(_) => {}}
      ref={() => {}}
      renderLoading={() => (
        <YStack flex={1}>
          <Spinner marginTop='$5' />
        </YStack>
      )}
    />
  );
};

export default WebViewGoJoePages;
