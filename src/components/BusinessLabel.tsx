import React from 'react';
import {StyleSheet} from 'react-native';
import {XStack} from 'tamagui';
import {Image} from 'expo-image';
import {BusinessDto} from '@gojoe/typescript-sdk';
import StyledText from '@/src/components/UI/StyledText';

interface Props {
  business: BusinessDto;
  color?: string;
  fontSize?: number;
}

const BusinessLabel: React.FC<Props> = ({
  business,
  color = '$background',
  fontSize = 12,
}) => {
  return (
    <XStack
      alignSelf='flex-start'
      borderRadius={24}
      paddingVertical={4}
      alignItems='center'
      gap={4}
      marginTop={8}>
      {business.logo && (
        <Image source={{uri: business.logo}} style={styles.logo} />
      )}
      <StyledText
        numberOfLines={1}
        fontSize={fontSize}
        lineHeight={16}
        color={color}
        fontWeight='600'>
        {business.name}
      </StyledText>
    </XStack>
  );
};

const styles = StyleSheet.create({
  logo: {
    width: 16,
    height: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
});
export default BusinessLabel;
