import React from 'react';
import {XStack} from 'tamagui';
import {Flag} from '@tamagui/lucide-icons';
import {FontAwesome5} from '@expo/vector-icons';

import I18nText from '../I18nText';

interface Props {
  isInternal: boolean;
  isActivity?: boolean;
}

const PostReported: React.FC<Props> = ({isInternal, isActivity}) => {
  return (
    <XStack
      flex={1}
      alignItems='center'
      justifyContent='space-between'
      backgroundColor='#FFD6D6'
      borderRadius={8}
      padding='$3.5'
      marginTop='$5'>
      <XStack alignItems='center'>
        <Flag size={16} color='#FF0000' />
        <I18nText fontSize={11} color='#FF0000' marginLeft='$1.5'>
          {`This ${isActivity ? 'activity' : 'post'} is flagged and is under review`}
        </I18nText>
      </XStack>
      <FontAwesome5 name='info-circle' size={16} color='#FF0000' />
    </XStack>
  );
};

export default PostReported;
