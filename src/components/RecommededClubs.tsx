import React, {useCallback} from 'react';
import {YStack} from 'tamagui';
import {FlashList, ListRenderItem} from '@shopify/flash-list';
import {ClubDto} from '@gojoe/typescript-sdk';

import {useRecommendedClubs} from '../hooks/api/useRecommendedClubs';
import ClubItemVertical from './ClubItemVertical';
import I18nText from './I18nText';

const RecommendedClubs: React.FC = () => {
  const {data} = useRecommendedClubs();

  const keyExtractor = useCallback((item: ClubDto) => item.id, []);

  const renderItem = useCallback<ListRenderItem<ClubDto>>(
    ({item, index}) => {
      const marginRight = index === data.length - 1 ? 0 : 8;
      return <ClubItemVertical club={item} marginRight={marginRight} />;
    },
    [data.length],
  );

  if (data.length === 0) {
    return null;
  }
  return (
    <YStack flex={1}>
      <I18nText
        fontSize={10}
        fontWeight={700}
        textTransform='uppercase'
        marginVertical='$5'
        paddingHorizontal='$5'>
        Recommended clubs for you
      </I18nText>

      <FlashList
        data={data}
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        estimatedItemSize={283}
        horizontal={true}
        ListHeaderComponent={<YStack width={24} />}
        ListFooterComponent={<YStack width={24} />}
        showsHorizontalScrollIndicator={false}
      />
    </YStack>
  );
};

export default RecommendedClubs;
