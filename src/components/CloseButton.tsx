import React from 'react';
import {YStack} from 'tamagui';
import {useRouter} from 'expo-router';
import {CircleX} from '@tamagui/lucide-icons';

const CloseButton: React.FC = () => {
  const router = useRouter();
  const handleBack = () => {
    router.back();
  };
  return (
    <YStack
      borderRadius={20}
      width={40}
      height={40}
      justifyContent='center'
      alignItems='center'
      backgroundColor='$accentGrey'
      onPress={handleBack}>
      <CircleX />
    </YStack>
  );
};

export default CloseButton;
