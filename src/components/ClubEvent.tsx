import React from 'react';
import {CalendarEventDto} from '@gojoe/typescript-sdk';
import {SheetManager} from 'react-native-actions-sheet';
import {Spinner, Text, XStack, YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';
import useClubEvent from '@/src/hooks/api/useClubEvent';
import {stripTags} from '@/src/utils/text';
import CalendarEventDate from '@/src/components/EventCalendarDate';
import CalendarEventTime from '@/src/components/EventCalendarTime';
import StyledText from './UI/StyledText';
import UserAvatar from './UI/Avatar/UserAvatar';
import StyledButton from './UI/Button';

interface Props {
  clubId: string;
  event: CalendarEventDto;
}

const Event: React.FC<Props> = ({event, clubId}) => {
  const {join, leave, isPending} = useClubEvent(clubId, event.id);

  const handlePressEvent = () => {
    return SheetManager.show('calendar_event', {payload: {event}});
  };

  const handleOnPress = async () => {
    if (event.isAttending) {
      await leave();
    } else {
      await join();
    }
  };

  return (
    <XStack
      padding='$2'
      borderRadius='$3'
      overflow='hidden'
      marginBottom='$0.75'
      gap='$3.5'
      onPress={handlePressEvent}
      backgroundColor='$background'>
      <CalendarEventDate eventDate={event.startTime} />
      <YStack flex={1}>
        <I18nText
          fontSize={10}
          textTransform='uppercase'
          fontWeight='bold'
          color='$primary'>
          {event.type}
        </I18nText>

        <StyledText fontWeight='bold' marginTop='$1'>
          {event.title}
        </StyledText>

        <StyledText
          fontWeight='500'
          marginTop='$1'
          numberOfLines={2}
          color='$grey1'>
          {stripTags(event.description)}
        </StyledText>

        <YStack height={16} />
        <CalendarEventTime
          eventDate={event.startTime}
          duration={event.duration}
        />
        <YStack height={16} />

        <XStack justifyContent='space-between' alignItems='center'>
          <XStack alignItems='center' gap='$4'>
            <XStack flexWrap='wrap'>
              {event.shortListAttendees.map((user) => (
                <YStack
                  width={36}
                  height={36}
                  borderRadius={16}
                  overflow='hidden'
                  marginRight={-16}
                  borderWidth={2}
                  borderColor='$background'
                  key={user.id}>
                  <UserAvatar user={user} size={32} borderRadius={16} />
                </YStack>
              ))}
            </XStack>
            {event.attendeesCount > event.shortListAttendees.length && (
              <Text color='$windowBackground'>{`+${
                event.attendeesCount - event.shortListAttendees.length
              }`}</Text>
            )}
          </XStack>

          <StyledButton
            borderRadius={event.isAttending ? 40 : undefined}
            variant={event.isAttending ? 'outlined' : 'primary'}
            onPress={handleOnPress}
            disabled={isPending}
            iconAfter={isPending ? <Spinner /> : undefined}>
            <I18nText color='$white'>
              {event.isAttending ? 'Going' : "I'm going"}
            </I18nText>
          </StyledButton>
        </XStack>
      </YStack>
    </XStack>
  );
};

export default Event;
