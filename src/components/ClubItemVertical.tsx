import React, {memo} from 'react';
import {StyleSheet} from 'react-native';
import {Text, XStack, YStack} from 'tamagui';
import {router} from 'expo-router';
import {ClubDto} from '@gojoe/typescript-sdk';
import {YStackProps} from 'tamagui';
import Category from '@/src/components/ClubCategory';
import {useLocales} from '@/src/contexts/LocaleContext';
import {StyledFeather, StyledMaterialIcons} from '@/src/components/StyledIcons';
import StyledText from './UI/StyledText';
import {ImageBackground} from 'expo-image';

interface Props extends YStackProps {
  club: ClubDto;
  marginRight?: number;
}

const ClubItemVertical: React.FC<Props> = ({club, marginRight, ...props}) => {
  const {locales} = useLocales();
  const handleOnPress = () => {
    router.push({
      pathname: '/club/[clubId]',
      params: {clubId: club.id},
    });
  };
  const formatter = new Intl.NumberFormat(locales.languageCode, {
    notation: 'compact',
    compactDisplay: 'short',
    maximumFractionDigits: 1,
  });

  return (
    <YStack
      padding={8}
      borderRadius={16}
      overflow='hidden'
      backgroundColor='$background'
      width={159}
      height={283}
      marginRight={marginRight}
      onPress={handleOnPress}
      {...props}>
      <ImageBackground
        source={{uri: club.profilePicture}}
        style={styles.image}
      />
      <YStack marginTop='$3.5' paddingHorizontal='$2' flex={1}>
        <Text numberOfLines={1} fontWeight='bold'>
          {club.name}
        </Text>
        <Category club={club} />
      </YStack>
      <XStack
        height='$1.5'
        paddingHorizontal='$2'
        justifyContent='space-between'
        alignItems='center'>
        <XStack gap='$2' flex={1} alignItems='center'>
          <StyledMaterialIcons name='group' size={12} color='$grey1' />
          <StyledText
            color='$grey1'
            fontSize={12}
            fontWeight='500'
            numberOfLines={1}>
            {formatter.format(club.memberCount)}
          </StyledText>
        </XStack>
        <StyledFeather name='arrow-right' color='$grey1' />
      </XStack>
    </YStack>
  );
};

const styles = StyleSheet.create({
  image: {
    width: 143,
    height: 143,
    borderRadius: 12,
    overflow: 'hidden',
  },
});
export default memo(
  ClubItemVertical,
  (prev, next) => prev.club.id === next.club.id,
);
