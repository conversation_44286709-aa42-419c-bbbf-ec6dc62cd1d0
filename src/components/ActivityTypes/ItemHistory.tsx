import {ActivityTypeDto} from '@gojoe/typescript-sdk';
import {FontAwesome} from '@expo/vector-icons';
import {YStack} from 'tamagui';
import ActivityIcon from '../ActivityIcon';
import I18nText from '../I18nText';

interface Props {
  item: ActivityTypeDto;
  isSelected?: boolean;
  onSelect?: () => void;
}

const ActivityItemHistory: React.FC<Props> = ({item, onSelect, isSelected}) => {
  return (
    <YStack
      key={item.id}
      width={96}
      height={96}
      borderRadius={12}
      borderWidth={1}
      gap='$2'
      borderColor={isSelected ? '$primary' : '$grey3'}
      backgroundColor='$background'
      justifyContent='center'
      alignItems='center'
      onPress={onSelect}>
      <ActivityIcon
        name={item.name}
        color={isSelected ? '$primary' : '$grey1'}
        size={36}
      />
      <I18nText
        fontSize={13}
        fontWeight='500'
        color={isSelected ? '$primary' : '$grey1'}
        textAlign='center'
        numberOfLines={1}>
        {item.title}
      </I18nText>
      {isSelected && (
        <YStack position='absolute' top={8} right={8}>
          <FontAwesome name='check-circle' color='#C43F2D' size={20} />
        </YStack>
      )}
    </YStack>
  );
};

export default ActivityItemHistory;
