import {useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {XStack, YStack} from 'tamagui';
import {ActivityTypeDto} from '@gojoe/typescript-sdk';
import I18nText from '../I18nText';
import {HISTORY_KEY, useActivityTypes} from '@/src/hooks/api/useActivityTypes';
import {storage} from '@/src/utils/localStorage';
import {triggerHaptics} from '@/src/utils/haptics';
import {FlashList} from 'react-native-actions-sheet/dist/src/views/FlashList';
import {Search} from '@tamagui/lucide-icons';
import {StyledInput} from '../UI/StyledInput';
import ActivityItemItem from './Item';
import ActivityItemHistory from './ItemHistory';
import {KeyboardAvoidingView, Platform} from 'react-native';

interface Props {
  activityTypeId?: string;
  showHistory?: boolean;
  useCategories?: boolean;
  sorted?: boolean;
  onClose?: () => void;
  onSelect?: (activity: ActivityTypeDto) => void;
}

const ActivityTypes: React.FC<Props> = ({
  activityTypeId,
  showHistory = true,
  useCategories = true,
  onSelect,
  onClose,
  sorted = true,
}) => {
  const {t} = useTranslation();
  const {data} = useActivityTypes();
  const [recentActivities, setRecentActivities] = useState<ActivityTypeDto[]>(
    [],
  );
  const [q, setQ] = useState('');

  // Load recent activities from local storage
  useEffect(() => {
    if (showHistory && data && data.length > 0) {
      const history = storage.getObject<string[]>(HISTORY_KEY) ?? [];
      const recent = history
        .slice(0, 3)
        .map((id) => data.find((item) => item.id === id))
        .filter((item) => item !== undefined) as ActivityTypeDto[];

      // If we have less than 3 recent activities, fill with most common ones
      if (recent.length < 3 && data.length >= 3) {
        const defaultActivities = ['Running', 'Walking', 'Cycling'];
        const remaining = defaultActivities
          .map((name) => data.find((item) => item.name === name))
          .filter(
            (item): item is ActivityTypeDto =>
              item !== undefined && !recent.some((r) => r.id === item.id),
          );

        recent.push(...remaining.slice(0, 3 - recent.length));
      }

      setRecentActivities(recent);
    }
  }, [data, showHistory]);

  const handleSelect = async (item: ActivityTypeDto) => {
    // Add to history
    const history = storage.getObject<string[]>(HISTORY_KEY) ?? [];
    const newHistory = [item.id, ...history.filter((id) => id !== item.id)];
    storage.setObject(HISTORY_KEY, newHistory);

    await triggerHaptics();
    if (onSelect) {
      onSelect(item as unknown as ActivityTypeDto);
    }
    if (onClose) {
      onClose();
    }
  };

  const filteredData = useMemo(() => {
    let result = q
      ? data.filter((item) =>
          item.title.toLowerCase().includes(q.toLowerCase()),
        )
      : [...data];

    if (sorted) {
      result.sort((a, b) =>
        a.title.localeCompare(b.title, undefined, {sensitivity: 'base'}),
      );
    }

    return result;
  }, [data, q, sorted]);

  const listData = useMemo(() => {
    if (!useCategories) {
      return filteredData;
    }

    // Group activities by their category from the API
    const grouped = filteredData.reduce(
      (acc, item) => {
        if (!acc[item.category]) {
          acc[item.category] = [];
        }
        acc[item.category].push(item);
        return acc;
      },
      {} as Record<string, ActivityTypeDto[]>,
    );

    // Define priority categories order
    const priorityCategories = ['Movement', 'Biking', 'Water sports'];

    // Convert grouped object to array format with category headers
    const result: (string | ActivityTypeDto)[] = [];

    // First add priority categories in specified order
    priorityCategories.forEach((category) => {
      if (grouped[category]?.length > 0) {
        result.push(category);
        result.push(...grouped[category]);
        delete grouped[category]; // Remove from grouped to avoid duplication
      }
    });

    // Save Other category for last
    const otherActivities = grouped['Other'];
    delete grouped['Other'];

    // Then add remaining categories alphabetically
    Object.entries(grouped)
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([category, items]) => {
        if (items.length > 0) {
          result.push(category);
          result.push(
            ...items.sort((a, b) =>
              a.title.localeCompare(b.title, undefined, {sensitivity: 'base'}),
            ),
          );
        }
      });

    // Finally add Other category at the end
    if (otherActivities?.length > 0) {
      result.push('Other');
      result.push(
        ...otherActivities.sort((a, b) =>
          a.title.localeCompare(b.title, undefined, {sensitivity: 'base'}),
        ),
      );
    }

    return result;
  }, [filteredData, useCategories]);

  const renderItem = ({
    item,
    index,
  }: {
    item: ActivityTypeDto | string;
    index: number;
  }) => {
    if (typeof item === 'string') {
      // For category headers
      const isFirstCategory = index === 0;
      return (
        <YStack
          paddingHorizontal='$5'
          marginTop={isFirstCategory ? '$0' : '$6'}
          marginBottom='$3.5'>
          <I18nText fontSize={16} fontWeight='700' color='$grey1'>
            {item}
          </I18nText>
        </YStack>
      );
    }

    const handleOnPress = async () => {
      await handleSelect(item);
    };
    const isSelected = activityTypeId === item.id;
    return (
      <ActivityItemItem
        item={item}
        onSelect={handleOnPress}
        isSelected={isSelected}
      />
    );
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'android' ? 'height' : undefined}>
      <YStack width='100%' height='100%'>
        <XStack
          justifyContent='space-between'
          alignItems='center'
          paddingHorizontal='$5'
          borderBottomWidth={1}
          marginTop='$3.5'
          borderBottomColor='$accentGrey'
          height={64}>
          <I18nText fontWeight='700' fontSize={18}>
            Choose activity type
          </I18nText>
          <I18nText fontWeight='500' color='$primary' onPress={onClose}>
            Dismiss
          </I18nText>
        </XStack>

        {/* Recent Activities */}
        {showHistory && (
          <YStack padding='$5' backgroundColor='$background'>
            <I18nText fontWeight='700' fontSize={16} color='$color'>
              Your top activities:
            </I18nText>
            <XStack gap='$3' marginTop='$3.5'>
              {recentActivities.map((activity) => {
                const handleOnPress = async () => {
                  await handleSelect(activity);
                };
                const isSelected = activityTypeId === activity.id;
                return (
                  <ActivityItemHistory
                    key={activity.id}
                    item={activity}
                    isSelected={isSelected}
                    onSelect={handleOnPress}
                  />
                );
              })}
            </XStack>
          </YStack>
        )}

        {/* Search Input */}
        <XStack
          marginHorizontal='$5'
          marginVertical='$4.5'
          backgroundColor='$background'
          borderRadius={16}
          borderWidth={1}
          borderColor='$grey3'
          height={44}
          alignItems='center'
          paddingHorizontal='$4'>
          <Search size={20} color='$grey2' />
          <StyledInput
            flex={1}
            marginLeft='$2'
            placeholder={t('Search for activity type')}
            backgroundColor='transparent'
            borderWidth={0}
            value={q}
            onChangeText={setQ}
          />
        </XStack>

        <FlashList
          data={listData}
          renderItem={renderItem}
          estimatedItemSize={56}
          showsVerticalScrollIndicator={false}
          keyboardDismissMode='on-drag'
          keyboardShouldPersistTaps='handled'
        />
      </YStack>
    </KeyboardAvoidingView>
  );
};

export default ActivityTypes;
