import {ActivityTypeDto} from '@gojoe/typescript-sdk';
import {XStack} from 'tamagui';
import ActivityIcon from '../ActivityIcon';
import I18nText from '../I18nText';
import {FontAwesome} from '@expo/vector-icons';

interface Props {
  item: ActivityTypeDto;
  isSelected?: boolean;
  onSelect?: () => void;
}

const ActivityItemItem: React.FC<Props> = ({item, isSelected, onSelect}) => {
  return (
    <XStack
      paddingHorizontal='$5'
      gap={8}
      height={52}
      marginBottom={8}
      alignItems='center'
      onPress={onSelect}>
      <ActivityIcon
        name={item.name}
        size={36}
        color={isSelected ? '$primary' : '$grey1'}
      />
      <I18nText
        flex={1}
        fontSize={14}
        fontWeight='500'
        color={isSelected ? '$primary' : '$grey1'}>
        {item.title}
      </I18nText>
      {isSelected && (
        <FontAwesome name='check-circle' color='#C43F2D' size={20} />
      )}
    </XStack>
  );
};

export default ActivityItemItem;
