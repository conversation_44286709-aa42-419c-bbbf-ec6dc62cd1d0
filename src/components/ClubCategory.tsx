import React, {memo} from 'react';
import {ClubDto, ClubDtoCategoryEnum} from '@gojoe/typescript-sdk';
import I18nText from '@/src/components/I18nText';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import ActivityIcon from '@/src/components/ActivityIcon';
import {XStack, YStack} from 'tamagui';
import StyledText from './UI/StyledText';

interface Props {
  club: ClubDto;
}
const ClubCategory: React.FC<Props> = ({club}) => {
  const activityCount = club.activities.length;
  if (club.category === ClubDtoCategoryEnum.MentalHealth) {
    return (
      <YStack paddingVertical='$2' gap='$2'>
        <XStack gap='$1.5' alignItems='center'>
          <MaterialCommunityIcons
            name='head-snowflake-outline'
            color='#C43F2D'
            size={16}
          />
          <I18nText
            fontSize={12}
            fontWeight={700}
            numberOfLines={1}
            color='$primary'>
            Mental Health
          </I18nText>
        </XStack>
      </YStack>
    );
  }
  if (club.category === ClubDtoCategoryEnum.Lifestyle) {
    return (
      <YStack paddingVertical='$2' gap='$2'>
        <XStack gap='$1.5' alignItems='center'>
          <FontAwesome name='heartbeat' color='#C43F2D' size={16} />
          <I18nText
            fontSize={12}
            fontWeight={700}
            numberOfLines={1}
            color='$primary'>
            Lifestyle
          </I18nText>
        </XStack>
      </YStack>
    );
  }
  if (activityCount === 1) {
    const activity = club.activities[0];
    return (
      <YStack paddingVertical='$2' gap='$2'>
        <XStack gap='$1.5' alignItems='center'>
          <ActivityIcon name={activity.name} color='$primary' size={16} />
          <I18nText
            fontSize={12}
            fontWeight={700}
            numberOfLines={1}
            color='$primary'>
            {activity.name}
          </I18nText>
        </XStack>
      </YStack>
    );
  }
  return (
    <YStack paddingVertical='$2' gap='$2'>
      <XStack gap='$1.5'>
        {club.activities.slice(0, 3).map((activity) => (
          <ActivityIcon
            name={activity.name}
            color='$primary'
            size={16}
            key={activity.id}
          />
        ))}
        {activityCount > 3 && (
          <StyledText
            numberOfLines={1}
            color='$primary'>{`+${activityCount - 3}`}</StyledText>
        )}
      </XStack>
    </YStack>
  );
};

export default memo(
  ClubCategory,
  (prev, next) => prev.club.id === next.club.id,
);
