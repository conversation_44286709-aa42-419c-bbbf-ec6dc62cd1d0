import React from 'react';
import {Image, YStack} from 'tamagui';
import {Platform} from 'react-native';
import {useOnboarding} from '@/src/contexts/OnboardingContext';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import I18nText from '@/src/components/I18nText';

// import CompanyCode from './ScreenSteps/CompanyCode';
// import Goals from './ScreenSteps/Goals';
import ActivityLevel from './ScreenSteps/ActivityLevel';
// import AffectingThings from './ScreenSteps/AffectingThings';
import PersonalDetails from './ScreenSteps/PersonalDetails';
import Wearables from './ScreenSteps/Wearables';
import Welcome from './ScreenSteps/Welcome';
import Notifications from './ScreenSteps/Notifications';

const steps: React.JSX.Element[] = [
  <PersonalDetails />,
  // <CompanyCode />,
  // <Goals />,
  <ActivityLevel />,
  // <AffectingThings />,
  <Wearables />,
];
if (Platform.OS === 'ios') {
  steps.push(<Notifications />);
}
steps.push(<Welcome />);

const imageWidth = 1938;
const imageHeight = 349;

const OnboardingSteps: React.FC = () => {
  const {step} = useOnboarding();
  const {top} = useSafeAreaInsets();

  return (
    <YStack flex={1} paddingTop={top}>
      {step < steps.length - 1 ? (
        <YStack position='absolute' width='100%' height='100%' top={0}>
          <YStack paddingTop={top + 12} paddingHorizontal='$5'>
            <I18nText color='$primary' fontWeight='600'>
              Let us tailor GoJoe to your needs
            </I18nText>
          </YStack>
          <Image
            source={require('@/assets/images/mobile/onboarding/background.png')}
            width={imageWidth}
            height={imageHeight}
          />
        </YStack>
      ) : (
        <Image
          position='absolute'
          top={0}
          right={0}
          source={require('@/assets/images/tracks.png')}
          width='100%'
          height='100%'
          tintColor='#C43F2D'
        />
      )}
      <YStack marginTop='$7' flex={1}>
        {steps[step]}
      </YStack>
    </YStack>
  );
};

export default OnboardingSteps;
