import React from 'react';
import {useOnboarding} from '@/src/contexts/OnboardingContext';
import {ScrollView, YStack} from 'tamagui';
import {useTranslation} from 'react-i18next';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';
import {useActivityLevel} from '@/src/hooks/api/useActivityLevel';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import StyledListItem from '@/src/components/UI/StyledListItem';
import StyledButton from '@/src/components/UI/Button';
import {
  Signal,
  SignalHigh,
  SignalLow,
  SignalMedium,
  SignalZero,
} from '@tamagui/lucide-icons';
import {LevelListDto} from '@gojoe/typescript-sdk';
import {useSession} from '@/src/contexts/SessionContext';

const ActivityLevelScreen: React.FC = () => {
  const {next, data, setData, previous} = useOnboarding();
  const {setUser} = useSession();
  const {t} = useTranslation();
  const {data: activityLevel} = useActivityLevel();
  const {bottom} = useSafeAreaInsets();
  const onSelect = async (level: LevelListDto) => {
    setData({
      ...data,
      activityLevel: level,
    });
    setUser((prevUser) => ({
      ...(prevUser as any),
      level: level,
    }));
    await triggerHaptics();
  };

  return (
    <YStack
      flex={1}
      paddingHorizontal='$5'
      paddingTop='$1'
      paddingBottom={bottom}>
      <I18nText fontWeight='600' fontSize={32} marginTop='$1'>
        How active are you currently?
      </I18nText>
      <ScrollView flex={1}>
        <YStack marginTop='$5' gap='$2'>
          {activityLevel.map((level, index) => {
            const handleOnPress = () => onSelect(level);
            const isActive = data.activityLevel?.id === level.id;
            return (
              <StyledListItem
                key={level.id}
                active={isActive}
                icon={<LevelIcont index={index} />}
                title={t(level.name)}
                onPress={handleOnPress}
                variant={isActive ? 'active' : 'primary'}
                justifyContent='center'
                paddingVertical='$3'
              />
            );
          })}
        </YStack>
      </ScrollView>
      <YStack gap='$4'>
        <StyledButton variant='primary' onPress={next}>
          <I18nText color='$white1'>Continue</I18nText>
        </StyledButton>
        <StyledButton variant='secondary' onPress={previous}>
          <I18nText color='$color'>Back</I18nText>
        </StyledButton>
      </YStack>
    </YStack>
  );
};

const LevelIcont: React.FC<{index: number}> = ({index}) => {
  switch (index) {
    case 0:
      return <SignalZero />;
    case 1:
      return <SignalLow />;
    case 2:
      return <SignalMedium />;
    case 3:
      return <SignalHigh />;
    default:
      return <Signal />;
  }
};

export default ActivityLevelScreen;
