import React, {useEffect} from 'react';
import {ScrollView, XStack, YStack} from 'tamagui';
import {Controller, useForm} from 'react-hook-form';
import {Platform} from 'react-native';
import {DateTimePickerAndroid} from '@react-native-community/datetimepicker';

import {useOnboarding} from '@/src/contexts/OnboardingContext';
import I18nText from '@/src/components/I18nText';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {zodResolver} from '@hookform/resolvers/zod';
import {
  defaultValues,
  FormInterface,
  formSchema,
} from '@/src/forms/schema/personal-details-onboarding';
import {useTranslation} from 'react-i18next';
import {CalendarDays, ChevronDown, CircleX} from '@tamagui/lucide-icons';
import StyledButton from '@/src/components/UI/Button';
import {SheetManager} from 'react-native-actions-sheet';
import LocaleDate from '@/src/components/LocaleDate';
import Weight from '@/src/components/Weight';
import Height from '@/src/components/Height';
import FormControllerGender from '@/src/components/FormControllerGender';
import {triggerHaptics} from '@/src/utils/haptics';
import useMutationUserProfile from '@/src/hooks/api/useMutationUserProfile';
import {useSession} from '@/src/contexts/SessionContext';

const PersonalDetailsScreen: React.FC = () => {
  const {next, data, setData} = useOnboarding();
  const {bottom} = useSafeAreaInsets();
  const {mutateAsync} = useMutationUserProfile();
  const {t} = useTranslation();
  const {setUser} = useSession();
  const {control, handleSubmit, setValue, reset} = useForm<FormInterface>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      ...defaultValues,
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (data) {
      reset({
        dateOfBirth: data.dateOfBirth,
        gender: data.gender,
        weight: data.weight,
        height: data.height,
      });
    }
  }, [data, reset]); // Runs when `data` changes

  const onSubmit = async (formData: FormInterface) => {
    // Save all the data including dateOfBirth
    await triggerHaptics();
    setData({
      ...data,
      dateOfBirth: formData.dateOfBirth || undefined,
      gender: formData.gender || undefined,
      weight: formData.weight || undefined,
      height: formData.height || undefined,
    });

    await mutateAsync({
      gender: formData.gender || undefined,
      weight: formData.weight || undefined,
      height: formData.height || undefined,
      dateOfBirth: formData.dateOfBirth
        ? formData.dateOfBirth.toJSON().split('T')[0]
        : undefined,
    });
    setUser((prevUser) => ({
      ...(prevUser as any),
      gender: formData.gender || undefined,
      weight: formData.weight || undefined,
      height: formData.height || undefined,
      dateOfBirth: formData.dateOfBirth
        ? formData.dateOfBirth.toJSON().split('T')[0]
        : undefined,
    }));
    next();
  };

  const clearDateOfBirth = () => {
    setValue('dateOfBirth', undefined);
  };

  return (
    <YStack
      flex={1}
      paddingTop='$1'
      paddingHorizontal='$5'
      paddingBottom={bottom}>
      <I18nText fontWeight='600' fontSize={32} marginTop='$1'>
        Tell us who you are
      </I18nText>
      <ScrollView>
        <YStack marginTop='$5'>
          {/* Date of Birth */}
          <Controller
            control={control}
            name='dateOfBirth'
            render={({field: {value}}) => {
              const handleOnPress = async () => {
                if (Platform.OS === 'android') {
                  DateTimePickerAndroid.open({
                    value: value || new Date(),
                    mode: 'date',
                    is24Hour: true,
                    maximumDate: new Date(),
                    onChange: (_, selectedDate) => {
                      if (selectedDate) {
                        setValue('dateOfBirth', selectedDate);
                      }
                    },
                  });
                } else {
                  await SheetManager.show('dateTimePicker', {
                    payload: {
                      title: 'Date of Birth',
                      date: value || undefined,
                      onSelect: (selectedDate: Date) => {
                        setValue('dateOfBirth', selectedDate);
                      },
                    },
                  });
                }
              };
              return (
                <StyledButton
                  onPress={handleOnPress}
                  variant='secondary'
                  borderColor='$grey'
                  marginTop='$3.5'
                  justifyContent='flex-start'
                  gap='$1'
                  icon={<CalendarDays color='$grey' size={14} />}
                  iconAfter={
                    value ? (
                      <CircleX
                        color='$grey'
                        size={16}
                        onPress={clearDateOfBirth}
                      />
                    ) : (
                      <ChevronDown color='$grey' size='$1' />
                    )
                  }
                  paddingHorizontal='$3.5'>
                  {value ? (
                    <LocaleDate date={value} flex={1} color='$color' />
                  ) : (
                    <I18nText color='$grey' flex={1}>
                      Date of Birth
                    </I18nText>
                  )}
                </StyledButton>
              );
            }}
          />

          {/* Gender */}
          <FormControllerGender control={control} setValue={setValue} />
          <I18nText
            fontSize={10}
            fontWeight='700'
            textTransform='uppercase'
            marginTop='$7'>
            Body Metrics
          </I18nText>
          <XStack gap='$4'>
            {/* Weight */}
            <Controller
              control={control}
              name='weight'
              render={({field: {value}}) => {
                const handleOnPress = async () => {
                  await SheetManager.show('weightPicker', {
                    payload: {
                      title: 'Weight',
                      value: value || undefined,
                      onSelect: (selectedWeight: number) => {
                        setValue('weight', parseInt(selectedWeight.toString()));
                      },
                    },
                  });
                };
                const handleClear = () => {
                  setValue('weight', undefined);
                };

                return (
                  <StyledButton
                    onPress={handleOnPress}
                    variant='secondary'
                    borderColor='$grey'
                    marginTop='$3.5'
                    justifyContent='flex-start'
                    gap='$1'
                    flex={1}
                    iconAfter={
                      value ? (
                        <CircleX
                          color='$grey'
                          size={16}
                          onPress={handleClear}
                        />
                      ) : (
                        <ChevronDown color='$grey' size='$1' />
                      )
                    }
                    paddingHorizontal='$3.5'>
                    <Weight
                      value={value || undefined}
                      placeholder={t('Weight')}
                    />
                  </StyledButton>
                );
              }}
            />
            {/* Height */}
            <Controller
              control={control}
              name='height'
              render={({field: {value}}) => {
                const handleOnPress = async () => {
                  await SheetManager.show('heightPicker', {
                    payload: {
                      title: 'Height',
                      value: value || undefined,
                      onSelect: (selectedWeight: number) => {
                        setValue('height', parseInt(selectedWeight.toString()));
                      },
                    },
                  });
                };
                const handleClear = () => {
                  setValue('height', undefined);
                };

                return (
                  <StyledButton
                    onPress={handleOnPress}
                    variant='secondary'
                    borderColor='$grey'
                    marginTop='$3.5'
                    justifyContent='flex-start'
                    gap='$1'
                    flex={1}
                    iconAfter={
                      value ? (
                        <CircleX
                          color='$grey'
                          size={16}
                          onPress={handleClear}
                        />
                      ) : (
                        <ChevronDown color='$grey' size='$1' />
                      )
                    }
                    paddingHorizontal='$3.5'>
                    <Height
                      value={value || undefined}
                      placeholder={t('Height')}
                    />
                  </StyledButton>
                );
              }}
            />
          </XStack>
        </YStack>
      </ScrollView>
      <ScrollView flex={1}></ScrollView>
      <YStack gap='$4'>
        <StyledButton variant='primary' onPress={handleSubmit(onSubmit)}>
          <I18nText color='$white1'>Continue</I18nText>
        </StyledButton>
      </YStack>
    </YStack>
  );
};

export default PersonalDetailsScreen;
