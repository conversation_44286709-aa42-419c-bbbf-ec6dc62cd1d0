import React from 'react';
import {Button, Input, YStack} from 'tamagui';
import {useOnboarding} from '@/src/contexts/OnboardingContext';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';
import Tip from '@/src/components/UI/Tip';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import StyledButton from '../../UI/Button';

const CompanyCode: React.FC = () => {
  const {next, data, setData} = useOnboarding();
  const {bottom} = useSafeAreaInsets();

  const handlePressContinue = async () => {
    await triggerHaptics();
    next();
  };

  const handleChangeCode = (text: string) => {
    setData({
      ...data,
      companyCode: text,
    });
  };

  const isDisabled = !data || !data.companyCode || data.companyCode.length < 5;
  return (
    <YStack flex={1} paddingTop='$1'>
      <YStack flex={1} paddingHorizontal='$5'>
        <I18nText fontWeight='600' fontSize={32}>
          Enter invite code
        </I18nText>
        <I18nText marginTop='$5'>
          If you are joining a community or company, enter your invite code. You
          should find it in your company invite or email.
        </I18nText>
        <Input
          height='$4.5'
          marginTop='$3.5'
          borderRadius={16}
          borderWidth={1}
          borderColor='$grey'
          value={data.companyCode}
          onChangeText={handleChangeCode}
        />
        <StyledButton
          backgroundColor={isDisabled ? '$accentGrey' : '$primary'}
          height='$4.5'
          marginTop='$3.5'
          disabled={isDisabled}
          borderRadius={16}
          onPress={handlePressContinue}>
          <I18nText color={isDisabled ? '$accentGrey' : '$white1'}>
            Submit
          </I18nText>
        </StyledButton>
        <YStack marginTop='$6'>
          <Tip>
            <I18nText fontSize={12} fontWeight='500'>
              You can also join later, after you register, if you can’t find it.
            </I18nText>
          </Tip>
        </YStack>
      </YStack>
      <YStack
        gap='$4'
        backgroundColor='$background'
        paddingBottom={bottom}
        padding='$5'>
        <I18nText fontSize={20} fontWeight='600'>
          Or continue without a code
        </I18nText>
        <I18nText>
          You can join GoJoe solo and build your own community with your
          friends.
        </I18nText>
        <Button
          onPress={handlePressContinue}
          borderWidth={1}
          borderColor='$color'
          borderRadius={16}
          marginVertical='$5'>
          <I18nText fontWeight='600'>Continue without a code</I18nText>
        </Button>
      </YStack>
    </YStack>
  );
};

export default CompanyCode;
