import React, {useEffect, useState} from 'react';
import {Image, YStack} from 'tamagui';
import {CioPushPermissionStatus, CustomerIO} from 'customerio-reactnative';
import {useOnboarding} from '@/src/contexts/OnboardingContext';
import I18nText from '@/src/components/I18nText';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import StyledButton from '@/src/components/UI/Button';
import logger from '@/src/utils/logger';

const AffectingThingsScreen: React.FC = () => {
  const {previous, next} = useOnboarding();
  const {bottom} = useSafeAreaInsets();
  const [pusStatus, seStatus] = useState<CioPushPermissionStatus>(
    CioPushPermissionStatus.NotDetermined,
  );

  useEffect(() => {
    CustomerIO.pushMessaging.getPushPermissionStatus().then((status) => {
      seStatus(status);
    });
  }, []);

  const handlePressContinue = async () => {
    try {
      if (pusStatus === CioPushPermissionStatus.Granted) {
        const token = await CustomerIO.pushMessaging.getRegisteredDeviceToken();
        if (token) {
          await CustomerIO.registerDeviceToken(token);
          next();
        }
      }
    } catch (e) {
      logger.error(e);
    }
  };

  const handlePressRequest = async () => {
    const options = {ios: {sound: true, badge: true}};
    CustomerIO.pushMessaging
      .showPromptForPushNotifications(options)
      .then((status) => {
        seStatus(status);
      })
      .catch((error) => {
        // Failed to show push permission prompt
        logger.error(error);
      });
  };

  return (
    <YStack
      flex={1}
      paddingTop='$1'
      paddingHorizontal='$5'
      paddingBottom={bottom}>
      <I18nText fontWeight='600' fontSize={32} marginTop='$1'>
        Never miss an activity
      </I18nText>
      <YStack gap='$4' flex={1} paddingBottom={50}>
        <YStack flex={1} />
        <Image
          source={require('@/assets/images/mobile/onboarding/notification_bg.png')}
          width={390}
          height={410}
          objectFit={'cover'}
          bottom={-100}
        />
        {pusStatus === CioPushPermissionStatus.NotDetermined ? (
          <>
            <StyledButton variant='secondary' onPress={next}>
              <I18nText color='$color'>Not Now</I18nText>
            </StyledButton>
            <StyledButton variant='primary' onPress={handlePressRequest}>
              <I18nText color='$white1'>Turn on Notifications</I18nText>
            </StyledButton>
          </>
        ) : (
          <>
            <StyledButton variant='primary' onPress={handlePressContinue}>
              <I18nText color='$white1'>Continue</I18nText>
            </StyledButton>
            <StyledButton variant='secondary' onPress={previous}>
              <I18nText color='$color'>Back</I18nText>
            </StyledButton>
          </>
        )}
      </YStack>
    </YStack>
  );
};

export default AffectingThingsScreen;
