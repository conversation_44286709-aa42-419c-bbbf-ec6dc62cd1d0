import React from 'react';
import {ScrollView, YStack} from 'tamagui';
import {useOnboarding} from '@/src/contexts/OnboardingContext';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';
import Button from '@/src/components/UI/Button';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {
  AffectingThings,
  useAffectingThings,
} from '@/src/hooks/api/useAffectingThings';
import StyledListItem from '@/src/components/UI/StyledListItem';
import {useTranslation} from 'react-i18next';
import Feather from '@expo/vector-icons/Feather';
import StyledButton from '@/src/components/UI/Button';

const AffectingThingsScreen: React.FC = () => {
  const {next, data, setData, previous} = useOnboarding();
  const {affectingThings} = useAffectingThings();
  const {bottom} = useSafeAreaInsets();
  const {t} = useTranslation();

  const onSelect = async (item: AffectingThings, isSelected: boolean) => {
    setData({
      ...data,
      affectingThings: isSelected
        ? data.affectingThings.filter((a) => a.id !== item.id)
        : [...data.affectingThings, item],
    });
    await triggerHaptics();
  };

  return (
    <YStack
      flex={1}
      paddingTop='$1'
      paddingHorizontal='$5'
      paddingBottom={bottom}>
      <I18nText fontWeight='600' fontSize={32} marginTop='$1'>
        Things currently affecting you
      </I18nText>
      <ScrollView flex={1}>
        <YStack marginTop='$5' gap='$2'>
          {affectingThings.map((item) => {
            const isSelected = data.affectingThings.some(
              (a) => a.id === item.id,
            );
            const handleOnPress = () => onSelect(item, isSelected);
            return (
              <StyledListItem
                key={item.id}
                active={isSelected}
                title={t(item.name)}
                onPress={handleOnPress}
                variant={isSelected ? 'active' : 'primary'}
                iconAfter={
                  <Feather
                    name='check'
                    size={16}
                    color={isSelected ? 'green' : '#DFE4EA'}
                  />
                }
              />
            );
          })}
        </YStack>
      </ScrollView>
      <YStack gap='$4'>
        <StyledButton
          variant='primary'
          onPress={next}
          disabled={data.affectingThings.length === 0}>
          <I18nText color='$white1'>Continue</I18nText>
        </StyledButton>
        <StyledButton variant='secondary' onPress={previous}>
          <I18nText color="$color">Back</I18nText>
        </StyledButton>
      </YStack>
    </YStack>
  );
};

export default AffectingThingsScreen;
