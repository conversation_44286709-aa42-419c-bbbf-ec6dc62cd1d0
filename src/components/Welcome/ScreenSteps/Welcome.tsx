import React from 'react';
import {YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useSession} from '@/src/contexts/SessionContext';
import {useRouter} from 'expo-router';
import StyledButton from '@/src/components/UI/Button';
import {useOnboarding} from '@/src/contexts/OnboardingContext';
import useMutationUserProfile from '@/src/hooks/api/useMutationUserProfile';
import {Spinner} from 'tamagui';
import {triggerHaptics} from '@/src/utils/haptics';
import logger from '@/src/utils/logger';
import useMutationUserSettings from '@/src/hooks/api/useMutationUserSettings';

const WelcomeScreen: React.FC = () => {
  const {setOnboarded, setUser, user} = useSession();
  const {bottom} = useSafeAreaInsets();
  const router = useRouter();
  const {data} = useOnboarding();
  const {mutateAsync, isPending} = useMutationUserProfile();
  const {mutate: updateUserSettings} = useMutationUserSettings();

  const handleOnPress = async () => {
    try {
      await triggerHaptics();

      // Save user profile data if we have any
      if (data && user) {
        const input = {
          firstName: user.firstName,
          lastName: user.lastName,
          profilePicture: user.profilePicture,
          dateOfBirth: data.dateOfBirth
            ? data.dateOfBirth.toJSON().split('T')[0]
            : undefined,
          gender: data.gender ?? undefined,
          weight: data.weight ?? undefined,
          height: data.height ?? undefined,
          levelId: data.activityLevel?.id,
        };

        // Only make the API call if we have data to save
        if (
          data.dateOfBirth ||
          data.gender ||
          data.weight ||
          data.height ||
          data.activityLevel?.id
        ) {
          await mutateAsync(input);

          // Update local user state
          setUser({
            ...user,
            ...input,
          });
        }
        updateUserSettings({
          onboarded: true,
        });
      }

      // Mark onboarding as complete and navigate to main app
      setOnboarded(true);
      router.replace({
        pathname: '/(app)/(tabs)',
      });
    } catch (error) {
      logger.error('Error saving user profile data:', error);
      // Still mark onboarding as complete even if saving fails
      setOnboarded(true);
      router.replace({
        pathname: '/(app)/(tabs)',
      });
    }
  };

  return (
    <YStack
      flex={1}
      paddingHorizontal='$5'
      paddingBottom={bottom}
      justifyContent='center'>
      <YStack flex={1} justifyContent='center' alignItems='center'>
        <I18nText
          fontWeight='600'
          fontSize={48}
          marginTop='$3.5'
          color='$primary'
          textAlign='center'
          paddingHorizontal='$5'>
          Welcome to the party
        </I18nText>
        <I18nText
          marginTop='$5'
          lineHeight={22}
          textAlign='center'
          fontWeight='500'
          color='$grey1'>
          Welcome to GoJoe, your health and fitness sidekick helping you
          compete, train, connect and earn all year round.
        </I18nText>
      </YStack>
      <YStack gap='$4' justifyContent='flex-end'>
        <StyledButton variant='primary' onPress={handleOnPress}>
          {isPending ? (
            <Spinner color='$white' />
          ) : (
            <I18nText color='$white'>Continue</I18nText>
          )}
        </StyledButton>
      </YStack>
    </YStack>
  );
};

export default WelcomeScreen;
