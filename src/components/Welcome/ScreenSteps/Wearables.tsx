import React from 'react';
import {Sc<PERSON>View, YS<PERSON><PERSON>, Spin<PERSON>} from 'tamagui';

import {useWearableProviders} from '@/src/hooks/api/useWearableProviders';
import {useOnboarding} from '@/src/contexts/OnboardingContext';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import WearablesList from '../../Wearables/WearablesList';
import StyledButton from '@/src/components/UI/Button';
import I18nText from '@/src/components/I18nText';

const WearablesSlide: React.FC = () => {
  const {next, previous} = useOnboarding();
  const {bottom} = useSafeAreaInsets();
  const {data = [], isRefetching, refetch} = useWearableProviders();

  return (
    <YStack
      flex={1}
      paddingTop='$1'
      paddingHorizontal='$5'
      paddingBottom={bottom}>
      <I18nText fontWeight='600' fontSize={32} marginTop='$1'>
        Connect your wearable
      </I18nText>
      <I18nText marginTop='$2' marginBottom='$4'>
        Connect your wearable device to track your activities automatically
      </I18nText>
      <ScrollView flex={1}>
        {!data.length ? (
          <Spinner />
        ) : (
          <WearablesList
            data={data}
            refetch={refetch}
            isRefetching={isRefetching}
            isOnboarding={true}
          />
        )}
      </ScrollView>
      <YStack gap='$4' marginTop='$4'>
        <StyledButton variant='primary' onPress={next}>
          <I18nText color='$white1'>Continue</I18nText>
        </StyledButton>
        <StyledButton variant='secondary' onPress={previous}>
          <I18nText color='$color'>Back</I18nText>
        </StyledButton>
      </YStack>
    </YStack>
  );
};

export default WearablesSlide;
