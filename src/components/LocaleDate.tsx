import React from 'react';
import {DateTime} from 'luxon';
import StyledText, {StyledTextProps} from '@/src/components/UI/StyledText';
import {useLocales} from '@/src/contexts/LocaleContext';

interface Props extends Omit<StyledTextProps, 'children'> {
  date: Date;
}
const LocaleDate: React.FC<Props> = ({date, ...props}) => {
  const {locales} = useLocales();
  // Convert to Luxon DateTime using the timezone and format from locales
  const formattedDate = DateTime.fromJSDate(date, {zone: locales.timezone})
    .setLocale(locales.languageTag)
    .toFormat(locales.dateFormat || 'dd MMMM yyyy');
  return <StyledText {...props}>{formattedDate}</StyledText>;
};

export default LocaleDate;
