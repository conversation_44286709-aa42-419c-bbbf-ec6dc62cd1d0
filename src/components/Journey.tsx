import React from 'react';
import {Dimensions} from 'react-native';
import {XStack, YStack, Stack} from 'tamagui';
import {Image} from 'expo-image';
import {JourneyDto, UserDto} from '@gojoe/typescript-sdk';
import {ArrowRightCircle, CalendarDays} from '@tamagui/lucide-icons';

import StyledText from './UI/StyledText';
import I18nText from './I18nText';
import Avatar from './UI/Avatar/Avatar';

interface Props {
  journey: JourneyDto;
}

const Journey: React.FC<Props> = ({journey}) => {
  const width = Dimensions.get('window').width;
  const imageHeight = (240 / 390) * (width - 64);

  let percentageCompleted = 0;
  if (journey.myJourney) {
    percentageCompleted = journey.myJourney.percentageCompleted || 0;
  }

  return (
    <YStack
      width='100%'
      backgroundColor='$background'
      paddingHorizontal='$2'
      paddingTop='$2'
      paddingBottom='$5'
      marginTop='$5'
      borderRadius='$7'>
      <Stack height={122} overflow='hidden' borderRadius='$6'>
        <Image
          source={{uri: journey.cover}}
          style={{
            width: '100%',
            height: imageHeight,
            borderRadius: 16,
          }}
        />
      </Stack>

      <YStack marginTop='$3' paddingHorizontal='$2'>
        <XStack alignItems='center'>
          <CalendarDays size={16} color='#888888' />
          <I18nText
            fontSize={14}
            fontWeight='$6'
            marginLeft='$1.5'
            color='#888888'
            i18nParams={{count: journey.length}}>
            {journey.length === 1 ? `{{count}} day` : `{{count}} days`}
          </I18nText>
        </XStack>

        <StyledText marginTop='$2' fontSize={18} fontWeight='700'>
          {journey.name}
        </StyledText>

        <XStack marginTop='$3.5' jc='space-between' alignItems='center'>
          <XStack alignItems='center'>
            {journey.users.slice(0, 3).map((user: UserDto, index: number) => {
              const marginLeft = index === 0 ? 0 : -10;
              return (
                <XStack
                  key={user.id}
                  marginLeft={marginLeft}
                  borderRadius={12}
                  backgroundColor='$background'
                  padding='$0.75'
                  overflow='hidden'>
                  <Avatar
                    id={user.id}
                    name={user.name}
                    avatar={user.avatar}
                    size={22}
                    circular
                  />
                </XStack>
              );
            })}

            <I18nText
              fontSize={12}
              fontWeight='$5'
              color='#888888'
              marginLeft='$2'
              i18nParams={{count: journey.totalUsers}}>
              {`{{count}} started`}
            </I18nText>
          </XStack>

          {!journey.myJourney ? (
            <ArrowRightCircle size={24} color='#888888' />
          ) : (
            <I18nText fontSize={14} fontWeight='$5' color='#CA3D2A'>
              {percentageCompleted < 100 ? `${percentageCompleted}% ` : ''}
              Completed
            </I18nText>
          )}
        </XStack>
        {journey.myJourney && (
          <XStack
            w='100%'
            h='$0.75'
            marginTop='$3'
            backgroundColor='#EFEFEF'
            borderRadius={4}>
            <XStack
              h='$0.75'
              w={`${percentageCompleted}%`}
              backgroundColor='#CA3D2A'
              borderRadius={4}
            />
          </XStack>
        )}
      </YStack>
    </YStack>
  );
};

export default Journey;
