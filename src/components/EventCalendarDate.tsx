import React from 'react';
import {DateTime} from 'luxon';

import {Text, YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';

interface Props {
  eventDate: string;
}

const CalendarEventDate: React.FC<Props> = ({eventDate}) => {
  const date = DateTime.fromISO(eventDate).toLocal();
  const dayOfWeek = date.toFormat('ccc');
  const dayOfMonth = date.toFormat('dd');
  const month = date.toFormat('LLL');

  return (
    <YStack
      width='$6'
      backgroundColor='$windowBackground'
      borderRadius='$4'
      paddingVertical='$0.75'
      gap='$1.5'
      justifyContent='center'
      alignItems='center'>
      <I18nText fontSize={10} fontWeight='bold'>
        {dayOfWeek}
      </I18nText>
      <Text fontSize={24} fontWeight='bold'>
        {dayOfMonth}
      </Text>
      <I18nText fontSize={10} fontWeight='bold'>
        {month}
      </I18nText>
    </YStack>
  );
};

export default CalendarEventDate;
