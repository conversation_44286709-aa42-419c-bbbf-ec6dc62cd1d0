import React, {useMemo} from 'react';
import {YStack} from 'tamagui';
import useBusinessPosts from '@/src/hooks/api/useBusinessPosts';
import PostCard from '@/src/components/PostCard';
import {useLocales} from '@/src/contexts/LocaleContext';

interface Props {
  index: number;
  total: number;
  businessId: string;
}

const BusinessPostInjector: React.FC<Props> = ({index, total, businessId}) => {
  const {locales} = useLocales();

  const {data: businessPosts = []} = useBusinessPosts(businessId);

  // Filter only recent business posts (within 1 month)
  const recentBusinessPosts = useMemo(() => {
    if (!businessPosts.length) return [];

    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    return businessPosts
      .filter((post) => new Date(post.publishedAt) >= oneMonthAgo)
      .slice(0, 10);
  }, [businessPosts]);

  if (!recentBusinessPosts.length) return null;

  // If feed is short (less than 3 posts), show the first business post at the end
  if (total < 3 && index === total - 1) {
    const post = recentBusinessPosts[0];
    if (!post) return null;

    return (
      <YStack paddingHorizontal='$5' marginTop='$3.5'>
        <PostCard
          post={post}
          languageTag={locales.languageTag}
          unit={locales.distanceUnit}
        />
      </YStack>
    );
  }

  // Otherwise inject every 3rd position: index 2, 5, 8...
  const isInjectionPoint = index >= 2 && (index - 2) % 3 === 0;
  if (!isInjectionPoint) return null;

  const businessPostIndex = Math.floor((index - 2) / 3);
  const post = recentBusinessPosts[businessPostIndex];

  if (!post) return null;

  return (
    <YStack paddingHorizontal='$5' marginTop='$2.5'>
      <PostCard
        post={post}
        languageTag={locales.languageTag}
        unit={locales.distanceUnit}
      />
    </YStack>
  );
};

export default BusinessPostInjector;
