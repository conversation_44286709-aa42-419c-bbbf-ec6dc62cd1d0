import React from 'react';
import QuickLinksSection from '../QuickLinksSection';
import BusinessPostInjector from './BusinessPostInjector';

interface Props {
  index: number;
  total: number;
  businessId?: string;
}

const FeedInjector: React.FC<Props> = ({index, total, businessId}) => {
  // Determine if we should show QuickLinks
  let QUICK_LINKS_INDEX = 0;
  if (total > 1) {
    QUICK_LINKS_INDEX = 1;
  }

  // Show QuickLinks section
  if (index === QUICK_LINKS_INDEX) {
    return (
      <QuickLinksSection
        marginTop='$5'
        marginBottom={16}
        paddingVertical='$3.5'
        borderColor='$grey3'
        backgroundColor='#DADCE785'
        borderTopWidth={1}
        borderBottomWidth={1}
      />
    );
  }

  return (
    <>
      {businessId && (
        <BusinessPostInjector
          index={index}
          total={total}
          businessId={businessId}
        />
      )}
    </>
  );
};

export default FeedInjector;
