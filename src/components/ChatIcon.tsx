import React, {useEffect, useState} from 'react';
import {useRouter} from 'expo-router';
import {YStack} from 'tamagui';
import {useChat} from '@/src/contexts/ChatContext';
import {ChatIcon} from './GoJoeIcon';

const ChatTopBarIcon: React.FC = () => {
  const [unreadMessages, setUnreadMessages] = useState(0);
  const {connection} = useChat();
  const router = useRouter();

  useEffect(() => {
    if (connection && connection.me) {
      setUnreadMessages(connection.me.unread_count);
    }
  }, [connection]);

  const handleOnPres = () => {
    return router.push({
      pathname: '/chat',
    });
  };

  return (
    <YStack
      onPress={handleOnPres}
      width={32}
      height={32}
      justifyContent='center'
      alignItems='center'>
      <ChatIcon size={32} color={unreadMessages ? '$primary' : undefined} />
    </YStack>
  );
};

export default ChatTopBarIcon;
