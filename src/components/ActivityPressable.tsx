import React from 'react';
import {useRouter} from 'expo-router';
import {triggerHaptics} from '@/src/utils/haptics';

interface ActivityPressableProps {
  activityId: string;
  children: React.ReactNode;
  onPress?: () => void;
}

const ActivityPressable: React.FC<ActivityPressableProps> = ({
  activityId,
  children,
  onPress,
}) => {
  const router = useRouter();

  const handlePressActivity = async () => {
    await triggerHaptics();

    // Call custom onPress if provided
    if (onPress) {
      onPress();
    }

    // Navigate to the activity post
    router.push({
      pathname: '/post/[postId]',
      params: {
        postId: 'activity',
        activityId: activityId,
      },
    });
  };

  return React.cloneElement(children as React.ReactElement, {
    onPress: handlePressActivity,
    pressStyle: {opacity: 0.7},
  });
};

export default ActivityPressable;
