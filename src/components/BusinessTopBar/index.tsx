import React, {useEffect} from 'react';
import {AvatarProps} from '@tamagui/avatar';
import {useSession} from '@/src/contexts/SessionContext';
import BusinessAvatar from '@/src/components/UI/Avatar/BusinessAvatar';
import useUserBusinesses from '@/src/hooks/api/useUserBusinesses';
import {useRouter} from 'expo-router';
import {triggerHaptics} from '@/src/utils/haptics';

const BusinessTopBar: React.FC<AvatarProps> = ({...props}) => {
  const {business, selectBusiness} = useSession();
  const {data} = useUserBusinesses();
  const router = useRouter();

  useEffect(() => {
    if (business) {
      return;
    }
    if (data && data.length > 0) {
      const firstBusiness = data[0];
      const newBusiness = {
        id: firstBusiness.id,
        name: firstBusiness.name,
        logo: firstBusiness.avatar,
      };
      selectBusiness(newBusiness);
    }
  }, [business, data, selectBusiness]);

  if (!business) {
    return null;
  }
  const handleOnPress = async () => {
    await triggerHaptics();
    router.push({
      pathname: '/business/[businessId]',
      params: {
        businessId: business.id,
      },
    });
  };
  const handleLongPress = async () => {};

  return (
    <BusinessAvatar
      {...props}
      business={business}
      circular
      size='$2.5'
      onPress={handleOnPress}
      onLongPress={handleLongPress}
    />
  );
};

export default BusinessTopBar;
