import React, {useEffect, useState, useRef} from 'react';
import {SheetManager} from 'react-native-actions-sheet';
import {BusinessDetailsDto} from '@gojoe/typescript-sdk';

import useBusinessUser from '@/src/hooks/api/useBusinessUser';
import ChallengeActions from './ChallengeActions';

interface Props {
  showWelcomeMessage?: boolean;
  business: BusinessDetailsDto;
  userId: string;
}

const BusinessActions: React.FC<Props> = ({
  business,
  userId,
  showWelcomeMessage,
}) => {
  const [onboardingCompleted, setOnboardingCompleted] = useState(false);
  const {data: businessUser} = useBusinessUser(business.id, userId);
  const hasShownOnboardingRef = useRef(false);

  // Check if business has onboarding fields that need to be filled
  useEffect(() => {
    if (businessUser && !hasShownOnboardingRef.current) {
      const onboardingSettings = business.settings.onboarding ?? [];

      // Check if region is required and missing
      const isRegionRequired = onboardingSettings.some(
        (item) => item.name.toLowerCase() === 'region' && item.required,
      );
      const isMissingRegion = isRegionRequired && !businessUser.businessRegion;

      // Check if department is required and missing
      const isDepartmentRequired = onboardingSettings.some(
        (item) => item.name.toLowerCase() === 'department' && item.required,
      );
      const isMissingDepartment =
        isDepartmentRequired && !businessUser.businessDepartment;

      // Show onboarding sheet if any required field is missing
      const showOnboarding = isMissingRegion || isMissingDepartment;

      if (showOnboarding) {
        hasShownOnboardingRef.current = true;
        SheetManager.show('business_onboarding', {
          payload: {
            businessId: business.id,
            settings: business.settings,
            businessName: business.name,
            businessUser,
            isRegionRequired,
            isDepartmentRequired,
          },
        }).finally(() => {
          setOnboardingCompleted(true);
        });
      } else {
        setOnboardingCompleted(true);
      }
    } else if (!businessUser) {
      // Reset the ref if businessUser is not available yet
      hasShownOnboardingRef.current = false;
    } else {
      // If we already have businessUser data and we've already shown the onboarding,
      // just mark onboarding as completed
      setOnboardingCompleted(true);
    }
  }, [business.id, business.name, business.settings, businessUser]);

  // Show challenge sheet after onboarding is completed
  if (onboardingCompleted || !businessUser) {
    return (
      <ChallengeActions
        business={business}
        showWelcomeMessage={showWelcomeMessage}
      />
    );
  }

  return null;
};

export default BusinessActions;
