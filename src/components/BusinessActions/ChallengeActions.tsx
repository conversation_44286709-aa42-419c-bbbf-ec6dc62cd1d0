import React, {useEffect, useRef} from 'react';
import {SheetManager} from 'react-native-actions-sheet';
import useRecommendedChallenge from '@/src/hooks/api/useRecommendedChallenge';
import {storage} from '@/src/utils/localStorage';

interface Props {
  showWelcomeMessage?: boolean;
  business: {
    id: string;
    name: string;
  };
}

/**
 * Component to display a sheet with actions such as joining a challenge
 */
const ChallengeActions: React.FC<Props> = ({
  business,
  showWelcomeMessage = false,
}) => {
  const {isLoading, challenge} = useRecommendedChallenge(business.id);
  const hasShownSheetRef = useRef(false);

  // Check if the alert is paused for this business
  const getBusinessAlertPauseKey = (businessId: string) =>
    `business_alert_pause_${businessId}`;

  // Check if the alert is currently paused
  const isAlertPaused = React.useCallback((businessId: string): boolean => {
    const pauseUntilTimestamp = storage.getNumber(
      getBusinessAlertPauseKey(businessId),
    );
    if (!pauseUntilTimestamp) return false;

    const now = Date.now();
    return now < pauseUntilTimestamp;
  }, []);

  useEffect(() => {
    if (
      !isLoading &&
      !isAlertPaused(business.id) &&
      (showWelcomeMessage || challenge) &&
      !hasShownSheetRef.current
    ) {
      hasShownSheetRef.current = true;
      SheetManager.show('challenge_sheet', {
        payload: {
          business,
          challenge,
          showWelcomeMessage,
        },
      });
    }
  }, [isLoading, showWelcomeMessage, challenge, business, isAlertPaused]);

  return null;
};

export default ChallengeActions;
