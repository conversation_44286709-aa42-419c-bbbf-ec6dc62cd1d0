import React, {useState} from 'react';
import {XStack, YStack} from 'tamagui';
import {ChevronDown, Clock4} from '@tamagui/lucide-icons';
import StyledText from './UI/StyledText';
import {SheetManager} from 'react-native-actions-sheet';
import logger from '@/src/utils/logger';

interface TimeInputProps {
  value: number; // time in seconds
  onChange: (seconds: number) => void;
  label?: string;
}

const TimeInput: React.FC<TimeInputProps> = ({
  value,
  onChange,
  label = 'Time',
}) => {
  // Convert seconds to hours, minutes, seconds
  const hours = Math.floor(value / 3600);
  const minutes = Math.floor((value % 3600) / 60);
  const seconds = Math.floor(value % 60);

  const [hoursInput, setHoursInput] = useState(hours.toString());
  const [minutesInput, setMinutesInput] = useState(minutes.toString());
  const [secondsInput, setSecondsInput] = useState(seconds.toString());

  const updateTime = (h: string, m: string, s: string) => {
    const hoursVal = parseInt(h) || 0;
    const minutesVal = parseInt(m) || 0;
    const secondsVal = parseInt(s) || 0;
    setHoursInput(hoursVal.toString());
    setMinutesInput(minutesVal.toString());
    setSecondsInput(secondsVal.toString());

    const totalSeconds = hoursVal * 3600 + minutesVal * 60 + secondsVal;
    onChange(totalSeconds);
  };

  const handleOnPress = () => {
    SheetManager.show('activity_time_picker', {
      payload: {
        value: value,
        onSelect: (time: number) => {
          logger.debug('number', time);
          updateTime(
            Math.floor(time / 3600).toString(),
            Math.floor((time % 3600) / 60).toString(),
            (time % 60).toString(),
          );
        },
      },
    });
  };
  return (
    <XStack
      onPress={handleOnPress}
      backgroundColor='$background'
      borderRadius={8}
      flex={1}
      height='$4'
      borderWidth={1}
      borderColor='$grey3'
      paddingHorizontal='$3'
      gap='$2'
      alignItems='center'>
      <Clock4 size={16} color='$grey1' />
      <StyledText color='$grey1' flex={1}>
        {`${hoursInput.padStart(2, '0')}:${minutesInput.padStart(2, '0')}:${secondsInput.padStart(2, '0')}`}
      </StyledText>
      <ChevronDown color='$grey1' />
    </XStack>
  );
};

export default TimeInput;
