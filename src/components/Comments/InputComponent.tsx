import React, {useState} from 'react';
import {useMutation} from '@tanstack/react-query';
import {StyleSheet, TextInput} from 'react-native';
import {XStack} from 'tamagui';
import {CreateCommentInput, UserProfileDto} from '@gojoe/typescript-sdk';

import UserAvatar from '../UI/Avatar/UserAvatar';
import I18nText from '../I18nText';
import {api} from '@/src/services/api';
import {useTranslation} from 'react-i18next';

interface Props {
  user: UserProfileDto;
  postId: string;
  onSuccessMutate: (update: any) => Promise<any>;
  invalidateQueries?: () => void;
}

const InputComponent: React.FC<Props> = ({
  user,
  postId,
  onSuccessMutate,
  invalidateQueries,
}) => {
  const {t} = useTranslation();
  const [text, setText] = useState('');

  const commentMutate = useMutation({
    mutationFn: (input: CreateCommentInput) =>
      api.getApiClient().postControllerCreateComment({
        createCommentInput: input,
        postId: postId,
      }),
    onSuccess: (update) => onSuccessMutate(update).then(() => setText('')),
    onError: () => {},
    onSettled: () => (invalidateQueries ? invalidateQueries() : null),
  });

  const appendMessage = () => {
    if (commentMutate.isPending || !text) {
      return null;
    }

    commentMutate.mutate({comment: text});
  };

  return (
    <XStack
      backgroundColor='$background'
      alignItems='center'
      marginTop='auto'
      paddingTop='$3.5'
      paddingBottom='$5'
      paddingHorizontal='$5'
      gap='$3.5'>
      <UserAvatar user={user} size={48} circular />

      <XStack
        flex={1}
        backgroundColor='#FFF'
        borderWidth={1}
        borderColor='#ACAFBB'
        borderRadius={20}
        height={44}
        paddingHorizontal={0}
        alignItems='center'
        justifyContent='space-between'
        overflow='hidden'>
        <TextInput
          placeholder={t('Write a comment')}
          value={text}
          editable={!commentMutate.isPending}
          onChangeText={setText}
          onSubmitEditing={appendMessage}
          clearButtonMode='while-editing'
          style={[styles.textInput, {paddingRight: 40}]}
        />

        {text !== '' && (
          <XStack position='absolute' right={28} onPress={appendMessage}>
            <I18nText fontSize={14} fontWeight={500} color='#C43F2D'>
              Post
            </I18nText>
          </XStack>
        )}
      </XStack>
    </XStack>
  );
};

export default InputComponent;

const styles = StyleSheet.create({
  textInput: {
    flex: 1,
    borderWidth: 0,
    paddingHorizontal: 8,
  },
});
