import React from 'react';
import {Sheet<PERSON>anager} from 'react-native-actions-sheet';
import {XStack, YStack} from 'tamagui';
import {PostCommentsListDto} from '@gojoe/typescript-sdk';

import {triggerHaptics} from '@/src/utils/haptics';
import {timeAgo} from '@/src/utils/date';
import {MenuIcon} from '../GoJoeIcon/icons/menu';
import UserAvatar from '../UI/Avatar/UserAvatar';
import StyledText from '../UI/StyledText';

interface Props {
  item: PostCommentsListDto;
  authUserId?: string;
  businessId?: string;
  postId?: string;
  onSuccessMutate: (update: any) => Promise<any>;
  invalidateQueries?: () => void;
}

const Comment: React.FC<Props> = ({
  item,
  authUserId,
  businessId,
  postId,
  onSuccessMutate,
  invalidateQueries,
}) => {
  const handlePressOnMenu = async () => {
    await triggerHaptics();
    await SheetManager.show('comment_menu', {
      payload: {
        authUserId: authUserId ?? '',
        businessId: businessId ?? '',
        commentId: item.id,
        postId: postId ?? '',
        canDelete: item.user.id === authUserId,
        onSuccessMutate,
        invalidateQueries,
      },
    });
  };

  return (
    <XStack alignItems='center' gap={8}>
      <UserAvatar user={item.user} size={48} circular />

      <YStack flex={1}>
        <XStack flex={1} alignItems='flex-end' gap={8}>
          <StyledText fontWeight={700} numberOfLines={1}>
            {item.user.name}
          </StyledText>
          <StyledText fontSize={11} color='#ADAFB5'>
            {timeAgo(item.createdAt)}
          </StyledText>
        </XStack>

        <StyledText flex={1} fontSize={12}>
          {item.comment}
        </StyledText>
      </YStack>

      <XStack onPress={handlePressOnMenu}>
        <MenuIcon size={16} color='#ADAFB5' />
      </XStack>
    </XStack>
  );
};

export default Comment;
