import React from 'react';
import {XStack, YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';
import {useLocales} from '@/src/contexts/LocaleContext';
import {PinIcon} from './GoJoeIcon';
import StyledText from './UI/StyledText';
import {ChevronDown} from '@tamagui/lucide-icons';
import {SheetManager} from 'react-native-actions-sheet';
import {getDistanceUnit} from '../utils/helper';

interface DistanceInputProps {
  value: number;
  activityTypeName: string;
  onChange: (meters: number) => void;
  label?: string;
}

const DistanceInput: React.FC<DistanceInputProps> = ({
  value,
  activityTypeName,
  onChange,
  label = 'Distance',
}) => {
  const {locales} = useLocales();
  const unit = getDistanceUnit(locales.distanceUnit, activityTypeName);

  const handleOnPress = async () => {
    await SheetManager.show('distance_picker', {
      payload: {
        value: value,
        activityTypeName: activityTypeName,
        onSelect: (distance: number) => {
          onChange(distance);
        },
      },
    });
  };

  return (
    <XStack
      onPress={handleOnPress}
      backgroundColor='$background'
      borderRadius={8}
      flex={1}
      height='$4'
      borderWidth={1}
      borderColor='$grey3'
      paddingHorizontal='$3'
      gap='$1'
      alignItems='center'>
      <PinIcon size={16} color='$grey1' />
      <StyledText color='$grey1'>{value}</StyledText>
      <I18nText fontSize={14} color='$grey1' flex={1}>
        {unit}
      </I18nText>
      <ChevronDown color='$grey1' />
    </XStack>
  );
};

export default DistanceInput;
