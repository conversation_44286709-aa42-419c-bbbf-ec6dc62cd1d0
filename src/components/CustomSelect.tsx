import React from 'react';
import {XStack, YStack} from 'tamagui';
import {ChevronDown} from '@tamagui/lucide-icons';

import StyledText from './UI/StyledText';

interface Props {
  label: string;
  value: string;
  onPress: () => void;
  isRequired?: boolean;
}

const CustomSelect: React.FC<Props> = ({
  label,
  value,
  onPress,
  isRequired = false,
}) => {
  const showError = isRequired && !value;

  return (
    <YStack
      backgroundColor='$background'
      height={44}
      marginTop='$3.5'
      borderRadius={8}
      borderColor={showError ? '$primary' : '$grey1'}
      borderWidth={1}
      paddingVertical={8}
      paddingHorizontal={12}
      onPress={onPress}>
      <StyledText
        fontSize={8}
        fontWeight={500}
        lineHeight={10}
        color='$grey1'
        textTransform='uppercase'>
        {label}
      </StyledText>
      <XStack justifyContent='space-between'>
        <StyledText>{value}</StyledText>
        <ChevronDown color='$grey1' size='$1' />
      </XStack>
    </YStack>
  );
};

export default CustomSelect;
