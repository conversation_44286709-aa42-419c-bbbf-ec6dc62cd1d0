import React, {useEffect, useRef, useCallback} from 'react';
import {StyleSheet, Animated, Text, View, Dimensions} from 'react-native';

type ToastType = 'error' | 'success' | 'warning' | 'info';

interface LocalToastProps {
  visible: boolean;
  styles?: any;
  title?: string;
  message?: string;
  type?: ToastType;
  duration?: number;
  onHide?: () => void;
}

const LocalToast: React.FC<LocalToastProps> = ({
  visible,
  styles,
  title,
  message,
  type = 'error',
  duration = 4000,
  onHide,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(-50)).current;

  const hideToast = useCallback(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: -50,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      if (onHide) onHide();
    });
  }, [fadeAnim, translateY, onHide]);

  useEffect(() => {
    if (visible) {
      // Show toast with animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Auto hide after duration
      const timer = setTimeout(() => {
        hideToast();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [visible, duration, fadeAnim, translateY, hideToast]);

  const getBackgroundColor = () => {
    switch (type) {
      case 'error':
        return '#FF3B30';
      case 'success':
        return '#34C759';
      case 'warning':
        return '#FF9500';
      case 'info':
        return '#007AFF';
      default:
        return '#FF3B30';
    }
  };

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        ltStyles.container,
        {
          backgroundColor: getBackgroundColor(),
          opacity: fadeAnim,
          transform: [{translateY}],
        },
        styles,
      ]}>
      <View style={ltStyles.content}>
        <Text style={ltStyles.title}>{title}</Text>
        {message && <Text style={ltStyles.message}>{message}</Text>}
      </View>
    </Animated.View>
  );
};

const {width} = Dimensions.get('window');
const ltStyles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 16,
    right: 16,
    width: width - 32,
    zIndex: 9999999,
    elevation: 9999999,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  content: {
    padding: 16,
  },
  title: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 4,
  },
  message: {
    color: 'white',
    fontSize: 14,
  },
});

export default LocalToast;
