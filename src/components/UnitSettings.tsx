import React from 'react';
import {YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';

interface Props {
  value: string;
  isActive: boolean;
  onPress: () => void;
}

const UnitSettings: React.FC<Props> = ({value, isActive, onPress}) => {
  return (
    <YStack
      onPress={onPress}
      height='$1.5'
      borderRadius='$3'
      paddingHorizontal='$3'
      alignItems='center'
      justifyContent='center'
      boxShadow={isActive ? '0px 1px 4px 0px rgba(0, 0, 0, 0.12)' : undefined}
      backgroundColor={isActive ? '$background' : undefined}>
      <I18nText color={isActive ? '$primary' : '$grey1'} fontWeight='500'>
        {value}
      </I18nText>
    </YStack>
  );
};

export default UnitSettings;
