import React from 'react';
import {BasicUser} from '@gojoe/typescript-sdk';
import {Avatar, XStack} from 'tamagui';
import {colorHash} from '@/src/utils/image';

interface Props {
  members: BasicUser[];
}
const ClubMembers: React.FC<Props> = ({members}) => {
  return (
    <XStack alignItems='center' gap='$6' flexWrap='wrap' borderWidth={1}>
      {members.map((member) => (
        <Avatar circular size={32} key={member.id}>
          <Avatar.Image
            accessibilityLabel={member.name}
            source={{uri: member.profilePicture}}
          />
          <Avatar.Fallback backgroundColor={colorHash.hex(member.id)} />
        </Avatar>
      ))}
    </XStack>
  );
};

export default ClubMembers;
