import {useRouter} from 'expo-router';
import {Circle, ScrollView, XStack, YStack} from 'tamagui';

import I18nText from './I18nText';
import {useSession} from '../contexts/SessionContext';
import {ChevronRight, Users2} from '@tamagui/lucide-icons';
import {WearableIcon} from './GoJoeIcon';
import {triggerHaptics} from '../utils/haptics';
import {SheetManager} from 'react-native-actions-sheet';
import {ImageBackground} from 'expo-image';

const FeedEmptyState = () => {
  const {user} = useSession();
  const router = useRouter();

  const handlePressWearables = async () => {
    await triggerHaptics();
    router.push('/application-and-devices');
  };

  const handlePressFindFriends = async () => {
    await triggerHaptics();
    await SheetManager.show('find_friends');
  };

  const handlePressJourneys = async () => {
    await triggerHaptics();
    router.push('/journeys');
  };
  const handlePressLesMills = async () => {
    await triggerHaptics();
    router.push('/les-mills');
  };
  const handlePressHow = async () => {
    await triggerHaptics();
    router.push('/house-of-wellbeing');
  };

  return (
    <YStack flex={1} marginBottom='$5'>
      <YStack marginTop={40} paddingHorizontal='$5'>
        <I18nText
          fontWeight='600'
          fontSize={24}
          i18nParams={{name: user?.firstName}}>
          {`Hey {{name}}, welcome!`}
        </I18nText>
        <YStack marginTop='$3.5' gap='$2'>
          <XStack
            height={48}
            backgroundColor='$background'
            borderRadius={16}
            borderColor='$grey3'
            paddingHorizontal='$3.5'
            borderWidth={1}
            alignItems='center'
            gap='$2'
            onPress={handlePressWearables}>
            <WearableIcon size={20} color='$grey1' />
            <I18nText
              fontWeight='700'
              color='$grey1'
              flex={1}
              numberOfLines={1}>
              {`Connect wearable if you haven’t`}
            </I18nText>
            <ChevronRight color='$grey1' />
          </XStack>
          <XStack
            height={48}
            backgroundColor='$background'
            borderRadius={16}
            borderColor='$grey3'
            paddingHorizontal='$3.5'
            borderWidth={1}
            alignItems='center'
            gap='$2'
            onPress={handlePressFindFriends}>
            <Users2 size={20} color='$grey1' />
            <I18nText
              fontWeight='700'
              color='$grey1'
              flex={1}
              numberOfLines={1}>
              Find friends that use GoJoe
            </I18nText>
            <ChevronRight color='$grey1' />
          </XStack>
        </YStack>
      </YStack>
      <YStack marginTop={40} marginBottom={40}>
        <I18nText
          fontWeight='600'
          fontSize={24}
          width='90%'
          paddingHorizontal='$5'>
          Need help getting started?
        </I18nText>
        <I18nText
          paddingHorizontal='$5'
          fontWeight='500'
          fontSize={12}
          color='$grey1'
          marginTop='$2.5'>
          We’ve got you covered:
        </I18nText>
        <ScrollView
          paddingHorizontal='$5'
          horizontal
          showsHorizontalScrollIndicator={false}
          marginTop='$3.5'>
          <YStack
            borderRadius={16}
            overflow='hidden'
            onPress={handlePressLesMills}
            marginRight={8}>
            <ImageBackground
              source={require('@/assets/images/tile_background_les_mills.png')}
              style={{
                width: 160,
                height: 176,
                borderRadius: 16,
                padding: 16,
                justifyContent: 'space-between',
              }}>
              <I18nText
                color='$white'
                textTransform='uppercase'
                fontSize={11}
                fontWeight='600'>
                Les Mills
              </I18nText>
              <XStack alignItems='flex-end'>
                <I18nText color='$white' fontWeight='600' flex={1}>
                  Premium subscription to 350+ ready-to-stream workouts
                </I18nText>
                <Circle size={24} backgroundColor='$white1'>
                  <ChevronRight size={14} color='$primary' />
                </Circle>
              </XStack>
            </ImageBackground>
          </YStack>
          <YStack
            borderRadius={16}
            overflow='hidden'
            onPress={handlePressHow}
            marginRight={8}>
            <ImageBackground
              source={require('@/assets/images/tile_background_how.png')}
              style={{
                width: 160,
                height: 176,
                borderRadius: 16,
                padding: 16,
                justifyContent: 'space-between',
              }}>
              <I18nText
                color='$white'
                textTransform='uppercase'
                fontSize={11}
                fontWeight='600'>
                House of Wellbeing
              </I18nText>
              <XStack alignItems='flex-end'>
                <I18nText color='$white' fontWeight='600' flex={1}>
                  Unwind with in-app hypnotherapy meditation audios
                </I18nText>
                <Circle size={24} backgroundColor='$white1'>
                  <ChevronRight size={14} color='$primary' />
                </Circle>
              </XStack>
            </ImageBackground>
          </YStack>
          <YStack
            borderRadius={16}
            marginRight={8}
            overflow='hidden'
            onPress={handlePressJourneys}>
            <ImageBackground
              source={require('@/assets/images/tile_background_journeys.png')}
              style={{
                width: 160,
                height: 176,
                borderRadius: 16,
                padding: 16,
                justifyContent: 'space-between',
              }}>
              <I18nText
                color='$white'
                textTransform='uppercase'
                fontSize={11}
                fontWeight='600'>
                Journeys
              </I18nText>
              <XStack alignItems='flex-end'>
                <I18nText color='$white' fontWeight='600' flex={1}>
                  Take a solo health and fitness journey bespoke to your goal
                </I18nText>
                <Circle size={24} backgroundColor='$white1'>
                  <ChevronRight size={14} color='$primary' />
                </Circle>
              </XStack>
            </ImageBackground>
          </YStack>
        </ScrollView>
      </YStack>
    </YStack>
  );
};

export default FeedEmptyState;
