import React, {memo} from 'react';
import useClubMembers from '../hooks/api/useClubMembers';
import {XStack, YStack} from 'tamagui';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';

interface Props {
  clubId: string;
  limit?: number;
  onPress?: () => void;
}
const ClubMembersAvatars: React.FC<Props> = ({clubId, onPress, limit = 3}) => {
  const {data} = useClubMembers(clubId);
  if (!data || data.length === 0) {
    return null;
  }

  return (
    <XStack flexWrap='wrap' onPress={onPress}>
      {data.slice(0, limit).map(({user}) => (
        <YStack
          width={36}
          height={36}
          borderRadius={16}
          overflow='hidden'
          marginRight={-16}
          borderWidth={2}
          borderColor='$background'
          key={user.id}>
          <UserAvatar user={user} size={32} borderRadius={16} />
        </YStack>
      ))}
    </XStack>
  );
};

export default memo(ClubMembersAvatars);
