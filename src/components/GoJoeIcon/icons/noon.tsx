import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg
      width={size}
      height={size}
      viewBox='0 0 24 24'
      fill='none'
      {...otherProps}>
      <Path
        d='M11.8417 0.50477C11.8047 0.512978 11.7503 0.529413 11.7207 0.541298C11.575 0.599887 11.4201 0.743868 11.3453 0.890287C11.2614 1.05449 11.2657 0.983023 11.2657 2.20312C11.2657 3.06965 11.2682 3.28949 11.2786 3.33592C11.3366 3.59434 11.5244 3.80137 11.7777 3.88635C11.8441 3.90864 11.8658 3.91103 12.0001 3.91103C12.1344 3.91103 12.1561 3.90864 12.2225 3.88635C12.4131 3.82241 12.5674 3.68942 12.6572 3.51171C12.7386 3.35057 12.7345 3.41984 12.7345 2.20312C12.7345 0.982629 12.7389 1.05512 12.6546 0.889077C12.6188 0.818738 12.593 0.784332 12.5235 0.714146C12.3706 0.559826 12.2355 0.500162 12.0241 0.49349C11.9432 0.490936 11.8889 0.494306 11.8417 0.50477ZM4.22408 3.66217C3.98076 3.71072 3.76101 3.90893 3.67878 4.15405C3.65598 4.222 3.65371 4.24253 3.65353 4.38232C3.65335 4.52598 3.65508 4.54089 3.68014 4.61272C3.71087 4.70075 3.73978 4.75386 3.79769 4.82872C3.86939 4.92139 5.30819 6.34839 5.37076 6.38889C5.4984 6.4715 5.63203 6.50448 5.80724 6.49665C6.00238 6.48792 6.14275 6.42531 6.28412 6.28394C6.42542 6.14265 6.4881 6.00218 6.49682 5.80736C6.50467 5.63177 6.46576 5.48676 6.37412 5.35024C6.33537 5.29249 4.90296 3.85493 4.8241 3.79463C4.7474 3.73601 4.62976 3.68174 4.5389 3.66309C4.45146 3.64515 4.31144 3.64475 4.22408 3.66217ZM19.4792 3.65771C19.3572 3.68349 19.2591 3.73026 19.1605 3.80954C19.0525 3.89649 17.6637 5.29472 17.6238 5.35672C17.5421 5.48363 17.5113 5.57854 17.5037 5.72651C17.492 5.95435 17.5578 6.12434 17.7213 6.28871C17.8046 6.37245 17.9002 6.4326 18.007 6.46843C18.0751 6.49128 18.0952 6.49348 18.2353 6.49348C18.3754 6.49348 18.3955 6.49128 18.4636 6.46843C18.6282 6.41322 18.6091 6.43025 19.4461 5.59315C20.2833 4.756 20.2663 4.77507 20.3214 4.61058C20.3441 4.54274 20.3464 4.52187 20.3466 4.38232C20.3468 4.24045 20.3449 4.22285 20.3209 4.15192C20.2832 4.04032 20.2332 3.96139 20.1409 3.86787C20.0474 3.77317 19.9567 3.71527 19.846 3.67959C19.7859 3.66022 19.747 3.65487 19.6465 3.65213C19.5779 3.65027 19.5026 3.65277 19.4792 3.65771ZM11.6065 4.41683C9.89271 4.50485 8.29394 5.1561 6.98101 6.30102C6.82429 6.43768 6.43786 6.8241 6.3012 6.98082C5.98585 7.34246 5.70074 7.73409 5.47077 8.12152C4.60894 9.57349 4.25862 11.2517 4.46478 12.9407C4.63495 14.3349 5.20618 15.6772 6.09708 16.7764C6.41416 17.1676 6.83127 17.585 7.21986 17.8999C7.49325 18.1215 7.82653 18.354 8.1217 18.5292C10.0043 19.6467 12.2823 19.8981 14.3599 19.2178C15.3551 18.8919 16.2265 18.3917 17.0401 17.6795C17.1824 17.555 17.5551 17.1822 17.6797 17.0399C18.1426 16.5111 18.495 15.9905 18.7908 15.3983C19.5182 13.9422 19.7579 12.315 19.4824 10.7039C19.2455 9.31841 18.6201 8.0211 17.6752 6.95512C17.5276 6.78863 17.2018 6.46348 17.0347 6.31592C16.505 5.84804 15.9292 5.46408 15.3073 5.16405C14.5514 4.79935 13.755 4.56397 12.9409 4.4646C12.5069 4.41162 12.0361 4.39477 11.6065 4.41683ZM11.8513 5.88173C10.6514 5.9065 9.46235 6.29918 8.4721 6.99768C8.0041 7.3278 7.52996 7.78092 7.16557 8.24632C6.56147 9.01784 6.13921 9.96971 5.97228 10.9363C5.90799 11.3086 5.88341 11.6027 5.88341 11.9999C5.88341 12.3971 5.90799 12.6913 5.97228 13.0635C6.13911 14.0295 6.56185 14.9825 7.16557 15.7535C7.46741 16.139 7.861 16.5326 8.2465 16.8344C9.08961 17.4946 10.1248 17.9271 11.1985 18.0678C11.7003 18.1336 12.2999 18.1336 12.8017 18.0678C13.7817 17.9394 14.717 17.5738 15.5281 17.0021C15.9953 16.6728 16.4702 16.219 16.8346 15.7535C17.4946 14.9106 17.9273 13.875 18.068 12.8015C18.1337 12.2998 18.1337 11.7001 18.068 11.1983C17.9273 10.1246 17.4948 9.08943 16.8346 8.24632C16.5328 7.86082 16.1392 7.46723 15.7537 7.16539C15.0673 6.62793 14.2366 6.23322 13.3729 6.03415C12.8976 5.92459 12.364 5.87114 11.8513 5.88173ZM1.12982 11.2707C0.967078 11.2901 0.833139 11.3569 0.709519 11.4805C0.611321 11.5787 0.549007 11.6796 0.517126 11.7919C0.485177 11.9044 0.485177 12.0954 0.517126 12.208C0.567506 12.3854 0.72006 12.5684 0.88926 12.6544C1.0553 12.7387 0.982812 12.7343 2.2033 12.7343C3.42002 12.7343 3.35075 12.7384 3.51189 12.657C3.6896 12.5673 3.82259 12.4129 3.88653 12.2223C3.90883 12.1559 3.91122 12.1343 3.91122 11.9999C3.91122 11.8656 3.90883 11.844 3.88653 11.7775C3.80653 11.539 3.61368 11.355 3.37245 11.2871C3.29774 11.266 3.29713 11.266 2.2465 11.2644C1.66834 11.2636 1.16583 11.2664 1.12982 11.2707ZM20.7553 11.2669C20.4503 11.2985 20.2103 11.4895 20.1137 11.7775C20.0914 11.844 20.089 11.8656 20.089 11.9999C20.089 12.1343 20.0914 12.1559 20.1137 12.2223C20.1776 12.4129 20.3106 12.5673 20.4883 12.657C20.6494 12.7384 20.5802 12.7343 21.7969 12.7343C23.017 12.7343 22.9455 12.7386 23.1097 12.6547C23.2565 12.5797 23.3977 12.4279 23.4574 12.2809C23.5267 12.1104 23.5267 11.8894 23.4574 11.7189C23.3774 11.522 23.1782 11.3467 22.9667 11.2872L22.8913 11.266L21.8401 11.2647C21.2619 11.264 20.7738 11.265 20.7553 11.2669ZM5.6017 17.516C5.51562 17.5352 5.39677 17.5913 5.3233 17.6474C5.24496 17.7073 3.81214 19.1451 3.77373 19.2024C3.67954 19.3428 3.643 19.4773 3.65059 19.6559C3.65924 19.8595 3.71658 19.9884 3.8641 20.1359C3.96107 20.2329 4.0381 20.2823 4.1521 20.3206C4.2221 20.3442 4.24206 20.3465 4.3777 20.3468C4.50703 20.3471 4.53592 20.3442 4.5985 20.3248C4.68201 20.2988 4.75445 20.2604 4.8289 20.2025C4.9225 20.1298 6.3488 18.6915 6.38907 18.6293C6.47168 18.5016 6.50466 18.368 6.49683 18.1928C6.4881 17.9976 6.42549 17.8573 6.28412 17.7159C6.1948 17.6266 6.10066 17.5671 5.99317 17.5322C5.89655 17.5007 5.70582 17.4929 5.6017 17.516ZM18.0721 17.5148C17.8283 17.568 17.6155 17.7616 17.5338 18.0047C17.4992 18.1076 17.4911 18.2668 17.5145 18.3845C17.5332 18.4785 17.5797 18.5833 17.6396 18.6663C17.6628 18.6984 18.0137 19.0559 18.4194 19.4607C19.196 20.2356 19.2137 20.2516 19.3537 20.3063C19.447 20.3428 19.5374 20.3554 19.6624 20.3495C19.8556 20.3404 19.9965 20.2772 20.1369 20.1367C20.2796 19.994 20.3412 19.8548 20.3496 19.6559C20.3575 19.4689 20.3173 19.3304 20.2134 19.1864C20.1606 19.1132 18.7127 17.668 18.6504 17.6263C18.5764 17.5767 18.4709 17.5316 18.3917 17.5156C18.3032 17.4978 18.1518 17.4974 18.0721 17.5148ZM11.8417 20.0969C11.602 20.1529 11.4033 20.3219 11.3161 20.5439C11.2642 20.6758 11.2657 20.6399 11.2657 21.7967C11.2657 23.0168 11.2614 22.9453 11.3453 23.1095C11.4203 23.2563 11.5721 23.3975 11.7191 23.4572C11.8896 23.5265 12.1106 23.5265 12.2811 23.4572C12.4281 23.3975 12.5799 23.2563 12.6549 23.1095C12.7388 22.9453 12.7345 23.0168 12.7345 21.7967C12.7345 20.58 12.7386 20.6493 12.6572 20.4881C12.5672 20.3099 12.411 20.1755 12.2225 20.1142C12.1303 20.0842 11.9341 20.0753 11.8417 20.0969Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Noon';

export const NoonIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
