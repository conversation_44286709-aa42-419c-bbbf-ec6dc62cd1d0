import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg
      width={size}
      height={size}
      stroke={color}
      viewBox='0 0 24 24'
      fill='none'
      {...otherProps}>
      <Path
        d='M11.4096 2.45329C11.399 2.45572 11.3677 2.46232 11.3399 2.46795C11.0901 2.51848 10.8541 2.75296 10.7937 3.0107C10.777 3.0819 10.776 3.15001 10.776 4.17114V5.25594L10.7982 5.33274C10.8429 5.48738 10.9303 5.61876 11.0553 5.71937C11.184 5.82298 11.3364 5.87514 11.5104 5.87514C11.6359 5.87514 11.725 5.855 11.832 5.80242C12.0236 5.70827 12.1642 5.53838 12.2239 5.32907C12.2447 5.25598 12.2447 5.25533 12.2448 4.17114C12.2448 2.96838 12.2473 3.01866 12.1817 2.88011C12.0862 2.6782 11.913 2.53159 11.6976 2.47036C11.65 2.45683 11.4465 2.44476 11.4096 2.45329ZM3.7488 5.61669C3.61356 5.63954 3.48584 5.70925 3.37357 5.82151C3.28491 5.91018 3.22553 6.00383 3.18908 6.11247C3.16628 6.18042 3.16402 6.20095 3.16383 6.34074C3.16365 6.4844 3.16538 6.49931 3.19045 6.57114C3.22117 6.65917 3.25008 6.71228 3.30799 6.78714C3.37969 6.87982 4.81849 8.30681 4.88106 8.34731C5.0087 8.42992 5.14234 8.46291 5.31755 8.45507C5.50377 8.44674 5.64356 8.38736 5.78004 8.25862C5.93335 8.11399 6.0096 7.93571 6.0096 7.72189C6.0096 7.57482 5.96562 7.42965 5.88443 7.30866C5.84567 7.25092 4.41326 5.81335 4.3344 5.75306C4.25771 5.69443 4.14006 5.64016 4.0492 5.62152C3.9709 5.60546 3.82885 5.60317 3.7488 5.61669ZM18.9648 5.62225C18.8796 5.64044 18.7606 5.69634 18.6864 5.75306C18.6081 5.81295 17.1752 7.25073 17.1368 7.308C17.044 7.44632 17.0058 7.5886 17.0137 7.76578C17.0224 7.96061 17.0851 8.10107 17.2264 8.24236C17.3678 8.38373 17.5081 8.44635 17.7033 8.45507C17.8879 8.46333 18.0204 8.42765 18.157 8.33294C18.2491 8.26905 19.6895 6.82684 19.7462 6.74178C19.7961 6.66678 19.8377 6.56269 19.8532 6.47363C19.8711 6.37104 19.8608 6.2055 19.8308 6.11247C19.7677 5.91711 19.5987 5.73457 19.4097 5.65777C19.2851 5.60715 19.1035 5.59265 18.9648 5.62225ZM11.1648 6.35592C8.86715 6.45799 6.72201 7.61266 5.36994 9.47514C4.74997 10.3291 4.31969 11.2883 4.09086 12.3263C3.91519 13.1233 3.87209 14.019 3.9703 14.8319C4.14136 16.2479 4.68873 17.5592 5.57361 18.6729C5.8207 18.984 6.23834 19.423 6.34772 19.4867C6.59456 19.6306 6.88761 19.6281 7.12292 19.4801C7.19148 19.437 7.28901 19.3431 7.3342 19.2767C7.42721 19.1401 7.46281 19.0258 7.46275 18.8639C7.46271 18.7407 7.45037 18.6706 7.41224 18.577C7.37078 18.4751 7.31374 18.4017 7.1251 18.207C6.71542 17.7844 6.39156 17.3435 6.11958 16.8383C5.54555 15.7721 5.29762 14.5308 5.41976 13.3345C5.60174 11.5522 6.53371 9.95757 7.99285 8.93177C8.25638 8.7465 8.47421 8.61784 8.7936 8.4588C9.47294 8.12052 10.1851 7.9187 10.9666 7.84299C11.1389 7.82629 11.4847 7.81837 11.7267 7.82558C12.4162 7.84614 13.1196 7.99423 13.7904 8.26007C13.9952 8.34123 14.4252 8.55307 14.6201 8.66882C16.0313 9.50673 17.0355 10.8461 17.4417 12.4319C17.6251 13.1478 17.6774 13.9567 17.5868 14.6735C17.4826 15.4979 17.2309 16.2603 16.8307 16.9638C16.5624 17.4354 16.2756 17.8152 15.8778 18.2255C15.7856 18.3206 15.6938 18.4232 15.6738 18.4536C15.5903 18.58 15.5587 18.6908 15.558 18.8591C15.5575 18.9976 15.5724 19.0625 15.6337 19.1872C15.7073 19.3373 15.8631 19.4821 16.0147 19.5415C16.1989 19.6137 16.4215 19.6077 16.5974 19.5258C16.6977 19.4791 16.7674 19.4224 16.9344 19.2514C18.0989 18.059 18.8311 16.5451 19.0416 14.8943C19.0861 14.5455 19.0978 14.3526 19.0982 13.9583C19.0987 13.4299 19.066 13.0503 18.979 12.5759C18.5487 10.2283 17.0426 8.22007 14.9128 7.15394C13.7439 6.56884 12.4714 6.29788 11.1648 6.35592ZM0.6528 13.2287C0.478186 13.2464 0.328051 13.3219 0.200218 13.4562C0.06792 13.5953 0 13.7652 0 13.9571C0 14.1579 0.0652416 14.3196 0.204998 14.4651C0.307862 14.5723 0.403094 14.6301 0.543725 14.6709L0.6192 14.6927H1.7136H2.808L2.8848 14.6705C3.12915 14.5999 3.31629 14.4209 3.39684 14.1807C3.41913 14.1143 3.42152 14.0927 3.4215 13.9583C3.42148 13.8226 3.41923 13.8028 3.39579 13.7327C3.35715 13.6173 3.3085 13.542 3.2064 13.4399C3.1029 13.3364 3.01198 13.2818 2.88341 13.2457L2.808 13.2246L1.7664 13.223C1.19352 13.2221 0.6924 13.2246 0.6528 13.2287ZM20.2462 13.2285C20.0735 13.2457 19.9463 13.3077 19.8134 13.4396C19.6644 13.5875 19.6051 13.7187 19.5961 13.9205C19.5898 14.0608 19.6087 14.1627 19.6613 14.272C19.7726 14.5035 19.9871 14.658 20.2397 14.6888C20.2892 14.6948 20.7071 14.6972 21.3648 14.6953L22.4112 14.6922L22.4829 14.6702C22.6109 14.6311 22.6993 14.5779 22.7963 14.4818C22.8996 14.3795 22.9606 14.2818 22.9933 14.1664C23.0114 14.1028 23.0156 14.0643 23.0158 13.9631C23.016 13.8306 23.0058 13.7719 22.9658 13.6756C22.8803 13.4694 22.685 13.3012 22.4615 13.2412C22.4074 13.2267 22.3004 13.2249 21.36 13.2232C20.7871 13.2222 20.2859 13.2246 20.2462 13.2285ZM0.569568 20.0981C0.297504 20.1577 0.0700416 20.3913 0.0138336 20.6687C-0.0075072 20.774 -0.0008832 20.9319 0.0289344 21.0287C0.101328 21.2638 0.297638 21.4572 0.5328 21.525L0.6096 21.5471H12H23.3904L23.4672 21.525C23.6559 21.4706 23.8192 21.3373 23.9165 21.1583C23.9536 21.09 23.9904 20.9799 23.9904 20.9371C23.9904 20.9221 23.9947 20.9072 24 20.9039C24.0054 20.9006 24.0095 20.8552 24.0095 20.8002C24.0094 20.7335 24.0063 20.7069 24 20.7167C23.9928 20.7278 23.9906 20.7245 23.9904 20.7023C23.99 20.659 23.9668 20.5799 23.9335 20.5088C23.8352 20.2988 23.6427 20.1435 23.4233 20.0974C23.3636 20.0848 22.0082 20.0833 11.9922 20.0841C2.47673 20.0849 0.619171 20.0872 0.569568 20.0981ZM0.003552 20.8127C0.003552 20.8682 0.005136 20.8909 0.0070752 20.8631C0.0090144 20.8354 0.0090144 20.7901 0.0070752 20.7623C0.005136 20.7346 0.003552 20.7573 0.003552 20.8127Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Morning';

export const MorningIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
