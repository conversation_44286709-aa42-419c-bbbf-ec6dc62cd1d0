import type {NamedExoticComponent} from 'react';
import {memo} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 32, ...otherProps} = props;
  return (
    <Svg
      width={size}
      height={size}
      viewBox='0 0 56 56'
      fill='none'
      {...otherProps}>
      <Path
        d='M40.25 1.75H8.75C6.89348 1.75 5.11301 2.4875 3.80025 3.80025C2.4875 5.11301 1.75 6.89348 1.75 8.75V50.75C1.75 51.6783 2.11875 52.5685 2.77513 53.2249C3.4315 53.8812 4.32174 54.25 5.25 54.25H8.58667'
        stroke={color}
      />
      <Path d='M8.75 12.834H25.6667' stroke={color} />
      <Path d='M8.75 22.166H20.09' stroke={color} />
      <Path
        d='M35 22.75V7C35 5.60761 35.5531 4.27226 36.5377 3.28769C37.5223 2.30312 38.8576 1.75 40.25 1.75C41.6424 1.75 42.9777 2.30312 43.9623 3.28769C44.9469 4.27226 45.5 5.60761 45.5 7V12.8333H35'
        stroke={color}
      />
      <Path d='M50.7499 45.7576L39.5732 47.9976' stroke={color} />
      <Path
        d='M40.6936 35.3266L35.4203 37.5433C35.0078 37.7218 34.5631 37.8139 34.1136 37.8139C33.6642 37.8139 33.2195 37.7218 32.807 37.5433C32.1982 37.2673 31.6798 36.825 31.3115 36.2672C30.9433 35.7094 30.7401 35.0589 30.7255 34.3907C30.7108 33.7225 30.8853 33.0637 31.2288 32.4903C31.5723 31.9169 32.0709 31.4523 32.667 31.15L37.9403 28.5133C38.5905 28.1802 39.3098 28.0043 40.0403 28C40.5917 27.9944 41.1387 28.0975 41.6503 28.3033L52.4303 32.6666'
        stroke={color}
      />
      <Path
        d='M19.1338 48.0895H22.8438L29.9605 53.5029C30.1272 53.6918 30.3295 53.8459 30.5559 53.9564C30.7824 54.0669 31.0284 54.1316 31.2799 54.1468C31.5313 54.162 31.7834 54.1273 32.0214 54.0449C32.2595 53.9624 32.4789 53.8337 32.6671 53.6662L42.6071 45.4529C43.0038 45.1325 43.2582 44.6686 43.315 44.1619C43.3718 43.6551 43.2264 43.1464 42.9105 42.7462L37.3338 36.7029'
        stroke={color}
      />
      <Path
        d='M32.0604 31.5237L31.477 31.0337C30.744 30.5519 29.8805 30.3075 29.0037 30.3337C28.4701 30.3337 27.9408 30.4285 27.4404 30.6137L19.1104 34.067'
        stroke={color}
      />
      <Path
        d='M15.75 49.8396H16.45C16.7994 49.8585 17.1491 49.8083 17.4791 49.6918C17.8091 49.5752 18.1128 49.3948 18.3729 49.1607C18.633 48.9266 18.8444 48.6435 18.9949 48.3275C19.1454 48.0116 19.2321 47.6691 19.25 47.3196V34.8363C19.2079 34.1346 18.8897 33.4782 18.3651 33.0103C17.8404 32.5425 17.1519 32.3014 16.45 32.3396H15.75'
        stroke={color}
      />
      <Path
        d='M54.2495 49.8396H53.6662C53.3168 49.8585 52.9671 49.8083 52.6371 49.6918C52.3071 49.5752 52.0034 49.3948 51.7433 49.1607C51.4832 48.9266 51.2718 48.6435 51.1213 48.3275C50.9708 48.0116 50.8841 47.6691 50.8662 47.3196V34.8363C50.9083 34.1346 51.2265 33.4782 51.7511 33.0103C52.2758 32.5425 52.9643 32.3014 53.6662 32.3396H54.3662'
        stroke={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Code';

export const CodeIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
