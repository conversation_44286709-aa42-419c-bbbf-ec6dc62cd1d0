import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg
      width={size}
      height={size}
      stroke={color}
      viewBox='0 0 24 24'
      fill='none'
      {...otherProps}>
      <Path
        d='M15 7C13.3438 7 12 5.65625 12 4C12 2.34375 13.3438 1 15 1C16.6562 1 18 2.34375 18 4C18 5.65625 16.6562 7 15 7ZM15 2C13.875 2 13 2.90625 13 4C13 5.125 13.875 6 15 6C16.0938 6 17 5.125 17 4C17 2.90625 16.0938 2 15 2ZM6 7C4.0625 7 2.5 5.4375 2.5 3.5C2.5 1.59375 4.0625 0 6 0C7.90625 0 9.5 1.59375 9.5 3.5C9.5 5.4375 7.90625 7 6 7ZM6 1C4.59375 1 3.5 2.125 3.5 3.5C3.5 4.90625 4.59375 6 6 6C7.375 6 8.5 4.90625 8.5 3.5C8.5 2.125 7.375 1 6 1ZM8.5 7.625C9.625 7.625 10.7188 8.15625 11.375 9.09375C11.75 9.6875 12 10.375 12 11.125V12.5C12 13.3438 11.3125 14 10.5 14H1.5C0.65625 14 0 13.3438 0 12.5V11.125C0 10.375 0.21875 9.6875 0.59375 9.09375C1.25 8.15625 2.34375 7.625 3.46875 7.625C4.53125 7.625 4.78125 8 6 8C7.1875 8 7.4375 7.625 8.5 7.625ZM11 12.5H10.9688V11.125C10.9688 10.5938 10.8438 10.0938 10.5312 9.65625C10.125 9.03125 9.34375 8.625 8.5 8.625C7.625 8.625 7.34375 9 6 9C4.65625 9 4.34375 8.625 3.46875 8.625C2.625 8.625 1.84375 9.03125 1.4375 9.65625C1.125 10.0938 1 10.5938 1 11.125V12.5C1 12.7812 1.21875 13 1.5 13H10.5C10.75 13 11 12.7812 11 12.5ZM19.4688 8.9375C19.8125 9.40625 20 9.96875 20 10.5938V11.75C20 12.4688 19.4375 13 18.75 13H12.9375C13 12.7188 13 12.5625 13 12H18.75C18.875 12 19 11.9062 19 11.75V10.5938C19 10.1875 18.875 9.8125 18.6562 9.5C18.3125 9 17.7188 8.71875 17.0625 8.71875C16.4062 8.71875 16.125 9.03125 15 9.03125C13.8438 9.03125 13.5938 8.71875 12.9062 8.71875C12.6875 8.71875 12.5312 8.75 12.3438 8.78125C12.2812 8.6875 12.1875 8.4375 11.7188 7.96875C12.0938 7.78125 12.5 7.6875 12.9062 7.6875C13.7812 7.6875 14 8 15 8C16 8 16.2188 7.6875 17.0625 7.6875C18.0312 7.6875 18.9375 8.125 19.4688 8.9375Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Users';

export const UsersIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
