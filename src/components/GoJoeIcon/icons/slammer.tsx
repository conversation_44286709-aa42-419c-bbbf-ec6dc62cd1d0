import React, {memo, NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 64 64' {...otherProps}>
      <Path
        d='M36.7018 25.6889C36.9492 25.5221 37.137 25.2808 37.238 25C37.3389 24.7192 37.3478 24.4136 37.2632 24.1275L36.3509 20.794C36.3278 20.7126 36.3287 20.6261 36.3536 20.5452C36.3785 20.4642 36.4263 20.3922 36.4913 20.3379L39.0177 18.1975C39.2418 18.0096 39.4025 17.7571 39.4779 17.4746C39.5532 17.192 39.5396 16.893 39.4387 16.6185C39.3351 16.3443 39.1502 16.1084 38.9087 15.9422C38.6673 15.776 38.3809 15.6876 38.0878 15.6886H35.1579C35.0709 15.6853 34.9866 15.657 34.9151 15.6073C34.8436 15.5575 34.7878 15.4884 34.7544 15.4079L33.3333 11.8991C33.2357 11.623 33.0552 11.3839 32.8166 11.2143C32.5779 11.0447 32.2927 10.953 31.9999 10.9517C31.7144 10.9537 31.4358 11.0396 31.1988 11.1986C30.9617 11.3577 30.7766 11.583 30.6666 11.8464L29.2455 15.3553C29.2139 15.437 29.1586 15.5075 29.0867 15.5575C29.0148 15.6075 28.9296 15.6349 28.8419 15.636H25.8945C25.6014 15.6349 25.315 15.7234 25.0736 15.8896C24.8321 16.0558 24.6472 16.2917 24.5436 16.5658C24.4428 16.8404 24.4291 17.1393 24.5045 17.4219C24.5798 17.7045 24.7405 17.957 24.9647 18.1448L27.491 20.3554C27.5585 20.4037 27.6085 20.4725 27.6338 20.5515C27.6591 20.6305 27.6582 20.7155 27.6314 20.794L26.7366 24.0924C26.6593 24.3805 26.6735 24.6856 26.7772 24.9654C26.8809 25.2451 27.0691 25.4857 27.3156 25.6538C27.5683 25.8445 27.8762 25.9477 28.1928 25.9477C28.5094 25.9477 28.8173 25.8445 29.07 25.6538L31.8245 23.5134C31.8992 23.4536 31.992 23.421 32.0877 23.421C32.1833 23.421 32.2761 23.4536 32.3508 23.5134L35.1053 25.6714C35.339 25.8339 35.6162 25.9224 35.9008 25.9255C36.1855 25.9287 36.4646 25.8462 36.7018 25.6889ZM18.8066 34.3207L21.5611 36.4786C21.7965 36.6724 22.0881 36.7853 22.3927 36.8006C22.6972 36.8159 22.9986 36.7329 23.2524 36.5637C23.5061 36.3946 23.6987 36.1483 23.8017 35.8613C23.9047 35.5742 23.9126 35.2617 23.8243 34.9698L22.912 31.6364C22.8852 31.5579 22.8843 31.4728 22.9096 31.3938C22.9349 31.3148 22.9849 31.2461 23.0524 31.1978L25.5612 29.0574C25.7896 28.8727 25.9535 28.6203 26.0292 28.3365C26.1049 28.0527 26.0884 27.7522 25.9823 27.4784C25.8847 27.2023 25.7042 26.9632 25.4655 26.7936C25.2269 26.624 24.9417 26.5323 24.6489 26.531H21.7014C21.6153 26.5337 21.5305 26.5099 21.4583 26.4628C21.3862 26.4157 21.3302 26.3477 21.2979 26.2678L19.8768 22.7589C19.7668 22.4955 19.5817 22.2703 19.3446 22.1112C19.1076 21.9521 18.8289 21.8662 18.5435 21.8642C18.2574 21.8639 17.9778 21.9488 17.7403 22.1082C17.5028 22.2676 17.3182 22.4942 17.2101 22.7589L15.789 26.2678C15.7548 26.3463 15.6983 26.413 15.6266 26.4598C15.5549 26.5065 15.4711 26.5313 15.3855 26.531H12.4205C12.1283 26.5348 11.8442 26.6275 11.6061 26.7967C11.3679 26.966 11.1869 27.2037 11.0871 27.4784C10.9863 27.7529 10.9726 28.0519 11.048 28.3344C11.1233 28.617 11.284 28.8695 11.5082 29.0574L14.0346 31.1978C14.0978 31.2534 14.1444 31.3255 14.1692 31.406C14.1939 31.4865 14.1959 31.5723 14.1749 31.6539L13.2626 34.9347C13.1813 35.2254 13.1934 35.5342 13.2973 35.8176C13.4012 36.101 13.5916 36.3445 13.8416 36.5137C14.0943 36.7044 14.4022 36.8076 14.7188 36.8076C15.0354 36.8076 15.3433 36.7044 15.596 36.5137L18.3505 34.3733C18.4151 34.325 18.4913 34.2946 18.5714 34.2854C18.6516 34.2761 18.7327 34.2883 18.8066 34.3207ZM52.952 28.3345C52.8766 28.6171 52.7159 28.8695 52.4918 29.0574L50.0531 31.1803C49.9929 31.2343 49.9482 31.3035 49.9235 31.3806C49.8989 31.4576 49.8952 31.5399 49.9128 31.6189L50.8426 34.9523C50.9222 35.2448 50.9074 35.5549 50.8003 35.8385C50.6932 36.122 50.4992 36.3645 50.2461 36.5313C49.9959 36.7276 49.687 36.8343 49.3689 36.8343C49.0508 36.8343 48.7419 36.7276 48.4917 36.5313L45.7372 34.3733C45.6586 34.3149 45.5632 34.2834 45.4653 34.2834C45.3673 34.2834 45.272 34.3149 45.1934 34.3733L42.4389 36.5137C42.1945 36.6843 41.9036 36.7758 41.6055 36.7758C41.3075 36.7758 41.0166 36.6843 40.7722 36.5137C40.5181 36.3484 40.3244 36.1052 40.22 35.8206C40.1157 35.5359 40.1063 35.2252 40.1932 34.9347L41.1055 31.654C41.1212 31.5722 41.1166 31.4879 41.0922 31.4084C41.0677 31.3289 41.0241 31.2566 40.9652 31.1978L38.4212 29.0574C38.2002 28.8661 38.0426 28.6119 37.9696 28.3288C37.8966 28.0457 37.9116 27.7471 38.0126 27.4727C38.1136 27.1983 38.2958 26.9612 38.5349 26.793C38.774 26.6248 39.0587 26.5334 39.3511 26.531H42.3161C42.4017 26.5313 42.4855 26.5066 42.5572 26.4598C42.6289 26.413 42.6854 26.3463 42.7196 26.2678L44.1407 22.759C44.2444 22.492 44.4264 22.2626 44.6628 22.1009C44.8992 21.9392 45.1789 21.8526 45.4653 21.8526C45.7517 21.8526 46.0314 21.9392 46.2678 22.1009C46.5042 22.2626 46.6862 22.492 46.7899 22.759L48.211 26.2678C48.2482 26.3477 48.3078 26.415 48.3826 26.4617C48.4573 26.5084 48.5439 26.5325 48.632 26.531H51.5795C51.8717 26.5348 52.1557 26.6275 52.3939 26.7968C52.6321 26.966 52.8131 27.2038 52.9129 27.4784C53.0137 27.7529 53.0274 28.0519 52.952 28.3345ZM48.3338 42.8122H45.3863C45.2987 42.8111 45.2135 42.7837 45.1416 42.7337C45.0696 42.6836 45.0143 42.6132 44.9828 42.5315L43.5617 39.0226C43.458 38.7557 43.276 38.5263 43.0396 38.3645C42.8033 38.2028 42.5235 38.1162 42.2371 38.1162C41.9507 38.1162 41.671 38.2028 41.4346 38.3645C41.1982 38.5263 41.0163 38.7557 40.9125 39.0226L39.4914 42.5315C39.4588 42.616 39.4009 42.6884 39.3255 42.7387C39.2501 42.7889 39.161 42.8146 39.0704 42.8122H36.1229C35.8298 42.8111 35.5434 42.8996 35.302 43.0658C35.0606 43.2319 34.8757 43.4679 34.772 43.742C34.6748 44.0198 34.6629 44.3203 34.738 44.6048C34.813 44.8894 34.9715 45.1449 35.1931 45.3386L37.737 47.479C37.7989 47.5354 37.8433 47.6083 37.8651 47.6891C37.8868 47.7699 37.885 47.8553 37.8598 47.9351L36.9475 51.3036C36.8717 51.5933 36.8882 51.8994 36.9946 52.1792C37.1011 52.4591 37.2922 52.6987 37.5413 52.8648C37.7904 53.0309 38.0851 53.1151 38.3844 53.1057C38.6836 53.0963 38.9725 52.9938 39.2107 52.8125L41.9652 50.6721C42.0412 50.6077 42.1376 50.5725 42.2371 50.5725C42.3367 50.5725 42.433 50.6077 42.5091 50.6721L45.2635 52.83C45.5028 53.0142 45.7941 53.1184 46.0959 53.1278C46.3978 53.1373 46.695 53.0515 46.9453 52.8826C47.1957 52.7138 47.3866 52.4704 47.4909 52.187C47.5953 51.9036 47.6078 51.5946 47.5267 51.3036L46.5969 47.9702C46.5812 47.8885 46.5858 47.8042 46.6103 47.7247C46.6347 47.6451 46.6783 47.5728 46.7372 47.5141L49.2636 45.3912C49.4847 45.1999 49.6422 44.9457 49.7152 44.6626C49.7882 44.3795 49.7732 44.0809 49.6722 43.8065C49.5713 43.5321 49.3891 43.295 49.15 43.1268C48.9108 42.9586 48.6261 42.8672 48.3338 42.8648V42.8122ZM26.6314 42.8121H29.5788L29.6666 42.742C29.9589 42.7443 30.2436 42.8357 30.4827 43.0039C30.7219 43.1722 30.9041 43.4093 31.005 43.6836C31.106 43.958 31.121 44.2567 31.048 44.5398C30.975 44.8229 30.8175 45.077 30.5964 45.2684L28.07 47.3912C28.0068 47.4469 27.9602 47.519 27.9354 47.5995C27.9107 47.68 27.9087 47.7658 27.9297 47.8474L28.842 51.1808C28.9248 51.4688 28.9133 51.7757 28.8091 52.0567C28.7049 52.3376 28.5136 52.5779 28.263 52.7422C28.0103 52.9329 27.7024 53.0361 27.3858 53.0361C27.0692 53.0361 26.7613 52.9329 26.5086 52.7422L23.7717 50.5843C23.6957 50.52 23.5993 50.4847 23.4997 50.4847C23.4002 50.4847 23.3038 50.52 23.2278 50.5843L20.4558 52.8124C20.2176 52.9938 19.9287 53.0963 19.6294 53.1057C19.3302 53.1151 19.0355 53.0308 18.7864 52.8648C18.5372 52.6987 18.3461 52.459 18.2397 52.1792C18.1333 51.8993 18.1168 51.5933 18.1926 51.3036L19.1049 47.9351C19.133 47.854 19.1346 47.7661 19.1094 47.684C19.0841 47.602 19.0334 47.5302 18.9645 47.4789L16.4381 45.3385C16.2171 45.1472 16.0595 44.8931 15.9865 44.6099C15.9135 44.3268 15.9285 44.0282 16.0295 43.7538C16.1305 43.4795 16.3127 43.2424 16.5518 43.0741C16.7909 42.9059 17.0756 42.8145 17.368 42.8121H20.3154C20.4036 42.8135 20.49 42.7872 20.5624 42.7368C20.6348 42.6864 20.6896 42.6146 20.719 42.5314L22.1401 39.0226C22.2493 38.7572 22.4337 38.5295 22.6706 38.3674C22.9074 38.2054 23.1865 38.116 23.4734 38.1103C23.762 38.1089 24.0442 38.1955 24.2824 38.3585C24.5205 38.5215 24.7035 38.7531 24.8068 39.0226L26.2279 42.5314C26.2594 42.6132 26.3147 42.6836 26.3866 42.7336C26.4585 42.7837 26.5438 42.811 26.6314 42.8121Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Slammer';

export const SlammerIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
