import React, {memo, NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 64 64' {...otherProps}>
      <Path
        d='M28.675 21.0872L22.9908 15.7355C22.6843 15.4457 22.5054 15.0461 22.4935 14.6244C22.4816 14.2028 22.6377 13.7937 22.9275 13.4871C23.2172 13.1806 23.6169 13.0017 24.0385 12.9898C24.4602 12.978 24.8693 13.1341 25.1758 13.4238L31.1925 19.1238C31.2741 19.1963 31.3694 19.2518 31.4727 19.2872C31.5759 19.3225 31.6852 19.337 31.7942 19.3297H32.2058C32.4256 19.3426 32.6417 19.2687 32.8075 19.1238L38.8242 13.4238C38.976 13.2762 39.1553 13.1599 39.3521 13.0816C39.5488 13.0033 39.759 12.9645 39.9707 12.9674C40.1825 12.9704 40.3915 13.015 40.586 13.0987C40.7805 13.1825 40.9566 13.3037 41.1042 13.4555C41.2518 13.6073 41.3681 13.7866 41.4464 13.9834C41.5247 14.1801 41.5635 14.3903 41.5606 14.602C41.5576 14.8138 41.513 15.0228 41.4293 15.2173C41.3455 15.4118 41.2243 15.5879 41.0725 15.7355L35.3883 21.0872C35.2893 21.1808 35.2166 21.2989 35.1777 21.4295C35.1388 21.5602 35.135 21.6987 35.1667 21.8313C35.1838 22.0527 35.1838 22.275 35.1667 22.4963V25.2672C35.1667 25.4771 35.0833 25.6785 34.9348 25.827C34.7863 25.9754 34.585 26.0588 34.375 26.0588H29.625C29.415 26.0588 29.2137 25.9754 29.0652 25.827C28.9167 25.6785 28.8333 25.4771 28.8333 25.2672V22.4963C28.8388 22.2735 28.86 22.0512 28.8967 21.8313C28.9283 21.6987 28.9245 21.5602 28.8856 21.4295C28.8467 21.2989 28.7741 21.1808 28.675 21.0872ZM30.1159 45.1222C29.0448 44.735 28.1191 44.0272 27.4647 43.0951C26.8102 42.163 26.4589 41.0519 26.4584 39.913V23.5571C26.4588 23.4147 26.4207 23.2748 26.3483 23.1522C26.2758 23.0296 26.1716 22.9288 26.0467 22.8605L25.0334 22.2905C23.801 21.6028 22.4102 21.2494 20.999 21.2654C19.5879 21.2815 18.2055 21.6663 16.989 22.3818C15.7726 23.0972 14.7644 24.1184 14.0647 25.3439C13.3649 26.5695 12.9979 27.9567 13 29.368C13 34.3843 16.4395 36.1154 20.8828 38.3517L21.0592 38.4405C21.1245 38.4683 21.1803 38.5146 21.2197 38.5736C21.2591 38.6327 21.2803 38.702 21.2809 38.773C21.284 38.8469 21.2664 38.9201 21.23 38.9845C21.1936 39.0489 21.1399 39.1018 21.075 39.1372C20.1113 39.6823 19.3101 40.4742 18.7536 41.4314C18.1971 42.3886 17.9053 43.4766 17.9084 44.5839C17.8672 45.4168 17.997 46.2493 18.2897 47.0301C18.5824 47.811 19.0318 48.5237 19.6102 49.1244C20.1887 49.7251 20.8839 50.2011 21.6532 50.523C22.4225 50.8449 23.2495 51.006 24.0834 50.9964C25.0078 50.9986 25.9223 50.8066 26.7679 50.4329C27.6134 50.0593 28.371 49.5122 28.9917 48.8272C29.699 48.0484 30.238 47.1321 30.575 46.1355C30.6472 45.9402 30.6394 45.7243 30.5535 45.5346C30.4675 45.3449 30.3103 45.1968 30.1159 45.1222ZM20.5209 31.6005C19.9728 31.6005 19.4371 31.438 18.9815 31.1335C18.5258 30.8291 18.1707 30.3963 17.9609 29.89C17.7512 29.3837 17.6963 28.8266 17.8033 28.2891C17.9102 27.7516 18.1741 27.2579 18.5616 26.8704C18.9491 26.4829 19.4428 26.219 19.9803 26.1121C20.5178 26.0051 21.0749 26.06 21.5812 26.2697C22.0875 26.4795 22.5203 26.8346 22.8247 27.2903C23.1292 27.7459 23.2917 28.2816 23.2917 28.8297C23.2917 29.5645 22.9998 30.2693 22.4801 30.7889C21.9605 31.3086 21.2557 31.6005 20.5209 31.6005ZM42.9408 38.4405L43.0773 38.3718C47.5399 36.1254 51 34.3837 51 29.368C51.0022 27.9567 50.6351 26.5695 49.9354 25.3439C49.2356 24.1184 48.2275 23.0972 47.011 22.3818C45.7946 21.6663 44.4122 21.2815 43.001 21.2654C41.5898 21.2494 40.199 21.6028 38.9667 22.2905L37.9533 22.8605C37.8284 22.9288 37.7242 23.0296 37.6518 23.1522C37.5793 23.2748 37.5413 23.4147 37.5417 23.5571V39.913C37.5412 41.0519 37.1898 42.163 36.5354 43.0951C35.8809 44.0272 34.9552 44.735 33.8842 45.1222C33.6869 45.1936 33.526 45.3405 33.4369 45.5305C33.3478 45.7205 33.3379 45.9382 33.4092 46.1355C33.7462 47.1321 34.2852 48.0484 34.9925 48.8272C35.615 49.5142 36.3752 50.0624 37.2236 50.4362C38.072 50.8099 38.9896 51.0008 39.9167 50.9964C40.7476 51.012 41.5729 50.858 42.3422 50.5438C43.1116 50.2296 43.8087 49.7618 44.3912 49.1691C44.9736 48.5763 45.429 47.8709 45.7295 47.0962C46.0301 46.3214 46.1695 45.4935 46.1392 44.663C46.1422 43.5558 45.8505 42.4677 45.294 41.5105C44.7375 40.5533 43.9362 39.7615 42.9725 39.2163C42.9076 39.1809 42.8539 39.128 42.8176 39.0637C42.7812 38.9993 42.7636 38.926 42.7667 38.8522C42.7458 38.774 42.7516 38.691 42.7832 38.6165C42.8147 38.5419 42.8702 38.48 42.9408 38.4405ZM40.7083 28.8297C40.7083 28.2816 40.8709 27.7459 41.1753 27.2903C41.4798 26.8346 41.9125 26.4795 42.4188 26.2697C42.9251 26.06 43.4823 26.0051 44.0197 26.1121C44.5572 26.219 45.051 26.4829 45.4385 26.8704C45.826 27.2579 46.0899 27.7516 46.1968 28.2891C46.3037 28.8266 46.2488 29.3837 46.0391 29.89C45.8294 30.3963 45.4742 30.8291 45.0186 31.1335C44.5629 31.438 44.0272 31.6005 43.4792 31.6005C42.7443 31.6005 42.0395 31.3086 41.5199 30.7889C41.0003 30.2693 40.7083 29.5645 40.7083 28.8297ZM34.375 28.4338H29.625C29.1878 28.4338 28.8334 28.7882 28.8334 29.2255V33.1838C28.8334 33.621 29.1878 33.9755 29.625 33.9755H34.375C34.8123 33.9755 35.1667 33.621 35.1667 33.1838V29.2255C35.1667 28.7882 34.8123 28.4338 34.375 28.4338ZM35.1667 37.1422V39.913C35.1667 40.7528 34.8331 41.5583 34.2392 42.1522C33.6453 42.746 32.8399 43.0797 32 43.0797C31.1602 43.0797 30.3547 42.746 29.7609 42.1522C29.167 41.5583 28.8334 40.7528 28.8334 39.913V37.1422C28.8334 36.9322 28.9168 36.7308 29.0652 36.5824C29.2137 36.4339 29.4151 36.3505 29.625 36.3505H34.375C34.585 36.3505 34.7864 36.4339 34.9348 36.5824C35.0833 36.7308 35.1667 36.9322 35.1667 37.1422Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'SocialButterfly';

export const SocialButterflyIcon: NamedExoticComponent<IconProps> =
  memo<IconProps>(themed(Icon));
