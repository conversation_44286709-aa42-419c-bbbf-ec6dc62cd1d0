import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 32, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 52 52' {...otherProps}>
      <Path
        d='M29.0234 14.3125H33.3125V24.4375H18.6875V14.3125H22.9414L23.7852 12.0625H28.1797L29.0234 14.3125ZM28.3906 10.375H23.7852C23.082 10.375 22.4492 10.832 22.2031 11.5L21.7812 12.625H18.6875C17.7383 12.625 17 13.3984 17 14.3125V24.4375C17 25.3867 17.7383 26.125 18.6875 26.125H33.3125C34.2266 26.125 35 25.3867 35 24.4375V14.3125C35 13.3984 34.2266 12.625 33.3125 12.625H30.2188L29.6914 11.2891C29.4805 10.7617 28.9883 10.375 28.3906 10.375ZM26 23.5938C28.3203 23.5938 30.2188 21.7305 30.2188 19.375C30.2188 17.0547 28.3203 15.1562 26 15.1562C23.6445 15.1562 21.7812 17.0547 21.7812 19.375C21.7812 21.7305 23.6445 23.5938 26 23.5938ZM26 16.8438C27.3711 16.8438 28.5312 18.0039 28.5312 19.375C28.5312 20.7812 27.3711 21.9062 26 21.9062C24.5938 21.9062 23.4688 20.7812 23.4688 19.375C23.4688 18.0039 24.5938 16.8438 26 16.8438Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Camera';

export const CameraIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
