import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 32, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 32 32' {...otherProps}>
      <Path
        d='M10.756 21C10.5437 21 10.3646 20.9285 10.2188 20.7856C10.0729 20.6427 10 20.4656 10 20.2544C10 20.0431 10.0719 19.8646 10.2156 19.7188C10.3594 19.5729 10.5375 19.5 10.75 19.5H11V14.5C11 13.2917 11.3715 12.2292 12.1146 11.3125C12.8576 10.3958 13.8194 9.82639 15 9.60417V8.5C15 8.22222 15.0972 7.98611 15.2917 7.79167C15.4861 7.59722 15.7222 7.5 16 7.5C16.2778 7.5 16.5139 7.59722 16.7083 7.79167C16.9028 7.98611 17 8.22222 17 8.5V9.60417C18.1806 9.82639 19.1424 10.3958 19.8854 11.3125C20.6285 12.2292 21 13.2917 21 14.5V19.5H21.25C21.4625 19.5 21.6406 19.5715 21.7844 19.7144C21.9281 19.8573 22 20.0344 22 20.2456C22 20.4569 21.9282 20.6354 21.7846 20.7812C21.6408 20.9271 21.4628 21 21.2504 21H10.756ZM15.9956 23.5C15.5819 23.5 15.2292 23.3531 14.9375 23.0594C14.6458 22.7656 14.5 22.4125 14.5 22H17.5C17.5 22.4167 17.3527 22.7708 17.0581 23.0625C16.7635 23.3542 16.4094 23.5 15.9956 23.5Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Bell';

export const BellIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
