import React, {memo, NamedExoticComponent} from 'react';
import {Path, Svg} from 'react-native-svg';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 18 19' {...otherProps}>
      <Path
        d='M15.75 0.25C16.9805 0.25 18 1.26953 18 2.5V12.625C18 13.8906 16.9805 14.875 15.75 14.875H10.6875L6.29297 18.1797C6.1875 18.25 6.11719 18.25 6.04688 18.25C5.80078 18.25 5.625 18.1094 5.625 17.8281V14.875H2.25C0.984375 14.875 0 13.8906 0 12.625V2.5C0 1.26953 0.984375 0.25 2.25 0.25H15.75ZM16.875 12.625V2.5C16.875 1.90234 16.3477 1.375 15.75 1.375H2.25C1.61719 1.375 1.125 1.90234 1.125 2.5V12.625C1.125 13.2578 1.61719 13.75 2.25 13.75H6.75V16.4219L9.98438 13.9961L10.3008 13.75H15.75C16.3477 13.75 16.875 13.2578 16.875 12.625ZM9.84375 8.6875C9.98438 8.6875 10.125 8.82812 10.125 8.96875V9.53125C10.125 9.70703 9.98438 9.8125 9.84375 9.8125H4.78125C4.60547 9.8125 4.5 9.70703 4.5 9.53125V8.96875C4.5 8.82812 4.60547 8.6875 4.78125 8.6875H9.84375ZM13.2188 5.3125C13.3594 5.3125 13.5 5.45312 13.5 5.59375V6.15625C13.5 6.33203 13.3594 6.4375 13.2188 6.4375H4.78125C4.60547 6.4375 4.5 6.33203 4.5 6.15625V5.59375C4.5 5.45312 4.60547 5.3125 4.78125 5.3125H13.2188Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Comment';

export const CommentIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
