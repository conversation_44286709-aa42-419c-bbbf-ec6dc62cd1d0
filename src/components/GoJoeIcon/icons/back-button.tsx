import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';
import {useRouter} from 'expo-router';
import {triggerHaptics} from '@/src/utils/haptics';

const Icon = (props: any) => {
  const {color = 'black', size = 32, ...otherProps} = props;
  return (
    <Svg
      width={size}
      height={size}
      viewBox='0 0 32 32'
      fill='none'
      stroke={color}
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
      {...otherProps}>
      <Path
        d='M13.8333 15.9998L19.7083 21.8748C19.9028 22.0693 20 22.3054 20 22.5832C20 22.8609 19.9028 23.0971 19.7083 23.2915C19.5139 23.4859 19.2778 23.5832 19 23.5832C18.7222 23.5832 18.4861 23.4859 18.2917 23.2915L12.0625 17.0623C11.9097 16.9096 11.7986 16.7429 11.7292 16.5623C11.6597 16.3818 11.625 16.1943 11.625 15.9998C11.625 15.8054 11.6597 15.6179 11.7292 15.4373C11.7986 15.2568 11.9097 15.0901 12.0625 14.9373L18.2917 8.70817C18.4861 8.51373 18.7222 8.4165 19 8.4165C19.2778 8.4165 19.5139 8.51373 19.7083 8.70817C19.9028 8.90262 20 9.13873 20 9.4165C20 9.69428 19.9028 9.93039 19.7083 10.1248L13.8333 15.9998Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'BackButton';

const BackButtonIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);

export const BackButton = () => {
  const router = useRouter();
  const handleBack = async () => {
    if (router.canGoBack()) {
      await triggerHaptics();
      router.back();
    } else {
      await triggerHaptics();
      router.replace('/');
    }
  };

  return <BackButtonIcon onPress={handleBack} />;
};
