import React, {memo, NamedExoticComponent} from 'react';
import {Path, Svg} from 'react-native-svg';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 42 47' {...otherProps}>
      <Path
        d='M18.1562 10.8164C18.293 10.6797 18.4844 10.6797 18.6211 10.8164L24.3633 16.5312C24.4727 16.668 24.4727 16.8594 24.3633 16.9961L18.6211 22.7109C18.4844 22.8477 18.293 22.8477 18.1562 22.7109L17.6094 22.1914C17.5 22.0547 17.5 21.8359 17.6094 21.7266L22.5586 16.75L17.6094 11.8008C17.5 11.6914 17.5 11.4727 17.6094 11.3359L18.1562 10.8164Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'ChevronRight';

export const ChevronRightIcon: NamedExoticComponent<IconProps> =
  memo<IconProps>(themed(Icon));
