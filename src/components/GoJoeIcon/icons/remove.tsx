import React, {memo, NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 16, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 14 15' {...otherProps}>
      <Path
        d='M7 0.96875C10.7461 0.96875 13.7812 4.00391 13.7812 7.75C13.7812 11.4961 10.7461 14.5312 7 14.5312C3.25391 14.5312 0.21875 11.4961 0.21875 7.75C0.21875 4.00391 3.25391 0.96875 7 0.96875ZM10.3086 9.55469L8.53125 7.75L10.3086 5.97266C10.4453 5.86328 10.4453 5.64453 10.3086 5.50781L9.24219 4.44141C9.10547 4.30469 8.88672 4.30469 8.77734 4.44141L7 6.21875L5.22266 4.44141C5.08594 4.30469 4.86719 4.30469 4.75781 4.44141L3.66406 5.50781C3.52734 5.64453 3.52734 5.86328 3.66406 5.97266L5.46875 7.75L3.66406 9.52734C3.52734 9.66406 3.52734 9.88281 3.66406 9.99219L4.73047 11.0859C4.86719 11.2227 5.08594 11.2227 5.19531 11.0859L7 9.28125L8.77734 11.0859C8.88672 11.2227 9.10547 11.2227 9.24219 11.0859L10.3086 10.0195C10.4453 9.88281 10.4453 9.66406 10.3086 9.55469Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'RemoveIcon';

export const RemoveIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
