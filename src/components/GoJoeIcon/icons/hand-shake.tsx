import React, {NamedExoticComponent} from 'react';
import {memo} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg
      width={size}
      height={size}
      viewBox='0 0 24 24'
      fill='none'
      {...otherProps}>
      <Path
        d='M13.5625 0C13.8438 0 14.0938 0.125 14.2812 0.3125L15.9688 2V8.0625C15.9062 7.96875 15.8125 7.90625 15.75 7.8125L11.1875 4.125L12 3.375C12.2188 3.1875 12.2188 2.875 12.0312 2.6875C11.8438 2.46875 11.5312 2.46875 11.3125 2.65625L8.84375 4.9375C8.8125 4.9375 8.8125 4.9375 8.8125 4.9375C8.28125 5.40625 7.46875 5.28125 7.0625 4.84375C6.625 4.375 6.625 3.59375 7.125 3.09375L10.2188 0.28125C10.4062 0.09375 10.625 0 10.875 0H13.5625ZM17 2.03125H20V10.0312H18C17.4375 10.0312 17 9.5625 17 9.03125V2.03125ZM18.5 9.03125C18.75 9.03125 19 8.78125 19 8.53125C19 8.25 18.75 8.03125 18.5 8.03125C18.2188 8.03125 18 8.25 18 8.53125C18 8.78125 18.2188 9.03125 18.5 9.03125ZM0 10V2.03125H3V9C3 9.5625 2.53125 10 2 10H0ZM1.5 8.03125C1.21875 8.03125 1 8.25 1 8.53125C1 8.78125 1.21875 9.03125 1.5 9.03125C1.75 9.03125 2 8.78125 2 8.53125C2 8.25 1.75 8.03125 1.5 8.03125ZM15.0938 8.59375C15.5312 8.9375 15.5938 9.5625 15.25 10L14.9688 10.375C14.5938 10.8125 13.9688 10.875 13.5625 10.5312L13.375 10.375L12.4062 11.5938C12 12.0938 11.25 12.1562 10.75 11.75L10.2188 11.2812H10.1875C9.5 12.125 8.25 12.2812 7.375 11.5625L4.5625 9H4V2L5.6875 0.3125C5.875 0.125 6.125 0 6.40625 0H9.03125L6.46875 2.34375C5.5625 3.1875 5.5 4.625 6.3125 5.53125C7.15625 6.4375 8.5625 6.53125 9.5 5.65625L10.4375 4.8125L15.0938 8.59375Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'HandShake';

export const HandShakeIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
