import React, {memo, NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 64 64' {...otherProps}>
      <Path
        d='M49.3395 16.5399C47.7129 13.1171 42.9468 14.0431 40.4255 15.1509L39.7911 15.4486C37.4897 13.8533 34.7688 13 31.9832 13C29.1976 13 26.4767 13.8533 24.1753 15.4486L23.5409 15.184C21.0359 14.0431 16.3186 13.1171 14.6269 16.5399C14.2154 17.3146 14 18.1812 14 19.0616C14 19.942 14.2154 20.8086 14.6269 21.5833C15.4371 23.0764 16.756 24.2172 18.3356 24.7911C18.2182 25.5293 18.1584 26.2757 18.1567 27.0234L15.9119 49.181C15.8887 49.4104 15.9128 49.6421 15.9828 49.8614C16.0529 50.0806 16.1672 50.2826 16.3186 50.4543C16.4707 50.6256 16.6563 50.7626 16.8637 50.8566C17.071 50.9506 17.2953 50.9994 17.5223 51H46.4441C46.6738 51.0017 46.9013 50.954 47.1116 50.86C47.3219 50.7659 47.5102 50.6276 47.6641 50.4543C47.8154 50.2826 47.9298 50.0806 47.9998 49.8614C48.0699 49.6421 48.094 49.4104 48.0708 49.181L45.8097 27.0234C45.808 26.2757 45.7482 25.5293 45.6308 24.7911C47.0218 24.3017 48.2275 23.3824 49.0793 22.162C49.6314 21.3417 49.9482 20.3814 49.9942 19.3885C50.0401 18.3956 49.8135 17.4093 49.3395 16.5399ZM36.7493 22.0628C37.1514 22.0628 37.5446 22.184 37.8789 22.4111C38.2133 22.6382 38.4739 22.9611 38.6278 23.3387C38.7817 23.7164 38.822 24.132 38.7435 24.533C38.6651 24.9339 38.4714 25.3022 38.1871 25.5913C37.9027 25.8803 37.5404 26.0772 37.146 26.1569C36.7515 26.2367 36.3427 26.1958 35.9712 26.0393C35.5996 25.8829 35.2821 25.618 35.0587 25.2781C34.8352 24.9381 34.716 24.5385 34.716 24.1297C34.716 23.5815 34.9302 23.0558 35.3115 22.6682C35.6928 22.2805 36.21 22.0628 36.7493 22.0628ZM35.2365 31.1573C35.4522 31.1573 35.6591 31.2444 35.8116 31.3995C35.9641 31.5545 36.0498 31.7648 36.0498 31.9841C35.9589 33.0167 35.4911 33.9772 34.7385 34.6768C33.9859 35.3764 33.003 35.7644 31.9832 35.7644C30.9634 35.7644 29.9805 35.3764 29.2279 34.6768C28.4753 33.9772 28.0075 33.0167 27.9166 31.9841C27.9166 31.7648 28.0023 31.5545 28.1548 31.3995C28.3073 31.2444 28.5142 31.1573 28.7299 31.1573H35.2365ZM26.9894 22.0628C27.3916 22.0628 27.7847 22.184 28.1191 22.4111C28.4534 22.6382 28.714 22.9611 28.8679 23.3387C29.0218 23.7164 29.0621 24.132 28.9836 24.533C28.9052 24.9339 28.7115 25.3022 28.4272 25.5913C28.1428 25.8803 27.7805 26.0772 27.3861 26.1569C26.9917 26.2367 26.5828 26.1958 26.2113 26.0393C25.8398 25.8829 25.5222 25.618 25.2988 25.2781C25.0753 24.9381 24.9561 24.5385 24.9561 24.1297C24.9561 23.5815 25.1703 23.0558 25.5516 22.6682C25.933 22.2805 26.4501 22.0628 26.9894 22.0628ZM36.4565 44.5842C35.596 44.5839 34.7468 44.386 33.9722 44.0052C33.1975 43.6243 32.5176 43.0705 31.9832 42.3849C31.2507 43.3308 30.2462 44.0209 29.1097 44.3591C27.9732 44.6974 26.7613 44.667 25.6426 44.2722C24.5239 43.8773 23.5541 43.1377 22.8684 42.1563C22.1826 41.1749 21.815 40.0005 21.8167 38.7967C21.8167 38.4678 21.9452 38.1524 22.174 37.9198C22.4028 37.6872 22.7131 37.5566 23.0366 37.5566C23.3602 37.5566 23.6705 37.6872 23.8993 37.9198C24.1281 38.1524 24.2566 38.4678 24.2566 38.7967C24.2566 39.6738 24.5994 40.515 25.2095 41.1352C25.8196 41.7554 26.6471 42.1038 27.5099 42.1038C28.3728 42.1038 29.2002 41.7554 29.8104 41.1352C30.4205 40.515 30.7632 39.6738 30.7632 38.7967C30.7632 38.4678 30.8918 38.1524 31.1206 37.9198C31.3493 37.6872 31.6597 37.5566 31.9832 37.5566C32.3068 37.5566 32.6171 37.6872 32.8459 37.9198C33.0747 38.1524 33.2032 38.4678 33.2032 38.7967C33.2032 39.6738 33.546 40.515 34.1561 41.1352C34.7662 41.7554 35.5937 42.1038 36.4565 42.1038C37.3193 42.1038 38.1468 41.7554 38.7569 41.1352C39.367 40.515 39.7098 39.6738 39.7098 38.7967C39.7098 38.4678 39.8383 38.1524 40.0671 37.9198C40.2959 37.6872 40.6062 37.5566 40.9298 37.5566C41.2533 37.5566 41.5636 37.6872 41.7924 37.9198C42.0212 38.1524 42.1498 38.4678 42.1498 38.7967C42.1498 40.3317 41.5499 41.8037 40.4822 42.8891C39.4145 43.9744 37.9664 44.5842 36.4565 44.5842Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'EagerBeaver';

export const EagerBeaverIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
