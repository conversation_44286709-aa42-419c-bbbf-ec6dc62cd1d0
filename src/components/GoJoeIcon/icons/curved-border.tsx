import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' {...otherProps}>
      <Path
        d='M24 24C24 10.7452 13.2548 -4.69685e-07 0 -1.04907e-06L24 0L24 24Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'CurvedBorder';

export const CurvedBorderIcon: NamedExoticComponent<IconProps> =
  memo<IconProps>(themed(Icon));
