import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 32, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 57 57' {...otherProps}>
      <Path
        d='M28.1255 1.00195C13.1411 1.00195 1.00049 13.252 1.00049 28.127C1.00049 43.1113 13.1411 55.252 28.1255 55.252C43.0005 55.252 55.2505 43.1113 55.2505 28.127C55.2505 13.252 43.0005 1.00195 28.1255 1.00195ZM28.1255 13.0332C30.6411 13.0332 32.7192 15.1113 32.7192 17.627C32.7192 20.252 30.6411 22.2207 28.1255 22.2207C25.5005 22.2207 23.5317 20.252 23.5317 17.627C23.5317 15.1113 25.5005 13.0332 28.1255 13.0332ZM34.2505 40.8145C34.2505 41.5801 33.5942 42.127 32.938 42.127H23.313C22.5474 42.127 22.0005 41.5801 22.0005 40.8145V38.1895C22.0005 37.5332 22.5474 36.877 23.313 36.877H24.6255V29.877H23.313C22.5474 29.877 22.0005 29.3301 22.0005 28.5645V25.9395C22.0005 25.2832 22.5474 24.627 23.313 24.627H30.313C30.9692 24.627 31.6255 25.2832 31.6255 25.9395V36.877H32.938C33.5942 36.877 34.2505 37.5332 34.2505 38.1895V40.8145Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'InfoOutline';

export const InfoOutlineIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
