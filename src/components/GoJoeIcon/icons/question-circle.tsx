import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 16 16' {...otherProps}>
      <Path
        d='M15.75 8C15.75 12.2812 12.25 15.75 8 15.75C3.71875 15.75 0.25 12.2812 0.25 8C0.25 3.75 3.71875 0.25 8 0.25C12.25 0.25 15.75 3.75 15.75 8ZM8.1875 2.8125C6.5 2.8125 5.40625 3.53125 4.5625 4.8125C4.4375 5 4.46875 5.21875 4.625 5.34375L5.71875 6.15625C5.875 6.28125 6.125 6.25 6.25 6.09375C6.8125 5.375 7.1875 4.96875 8.03125 4.96875C8.65625 4.96875 9.46875 5.375 9.46875 6C9.46875 6.46875 9.0625 6.71875 8.4375 7.0625C7.71875 7.46875 6.75 7.96875 6.75 9.25V9.375C6.75 9.59375 6.90625 9.75 7.125 9.75H8.875C9.0625 9.75 9.25 9.59375 9.25 9.375V9.34375C9.25 8.46875 11.8438 8.4375 11.8438 6C11.8438 4.1875 9.96875 2.8125 8.1875 2.8125ZM8 10.5625C7.1875 10.5625 6.5625 11.2188 6.5625 12C6.5625 12.8125 7.1875 13.4375 8 13.4375C8.78125 13.4375 9.4375 12.8125 9.4375 12C9.4375 11.2188 8.78125 10.5625 8 10.5625Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'QuestionCircle';

export const QuestionCircleIcon: NamedExoticComponent<IconProps> =
  memo<IconProps>(themed(Icon));
