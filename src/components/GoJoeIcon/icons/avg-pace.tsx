import React, {memo, NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 16, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 16 13' {...otherProps}>
      <Path
        d='M3.625 4.78125C3.625 4.42578 3.89844 4.125 4.28125 4.125C4.63672 4.125 4.9375 4.42578 4.9375 4.78125C4.9375 5.16406 4.63672 5.4375 4.28125 5.4375C3.89844 5.4375 3.625 5.16406 3.625 4.78125ZM8 3.90625C7.61719 3.90625 7.34375 3.63281 7.34375 3.25C7.34375 2.89453 7.61719 2.59375 8 2.59375C8.35547 2.59375 8.65625 2.89453 8.65625 3.25C8.65625 3.63281 8.35547 3.90625 8 3.90625ZM15.875 8.5C15.875 9.94922 15.4648 11.3164 14.7812 12.4648C14.6445 12.7383 14.3438 12.875 14.043 12.875H1.92969C1.62891 12.875 1.32812 12.7383 1.19141 12.4648C0.507812 11.3164 0.125 9.94922 0.125 8.5C0.125 4.15234 3.625 0.625 8 0.625C12.3477 0.625 15.875 4.15234 15.875 8.5ZM15 8.5C15 4.64453 11.8555 1.5 8 1.5C4.11719 1.5 1 4.64453 1 8.5C1 9.75781 1.32812 10.9609 1.92969 12.0273L14.043 12C14.6719 10.9609 15 9.75781 15 8.5ZM9.75 9.375C9.75 10.3594 8.95703 11.125 8 11.125C7.07031 11.125 6.33203 10.4414 6.25 9.53906L2.66797 8.9375C2.42188 8.91016 2.25781 8.69141 2.3125 8.44531C2.33984 8.19922 2.58594 8.03516 2.80469 8.08984L6.38672 8.69141C6.66016 8.0625 7.26172 7.625 8 7.625C8.95703 7.625 9.75 8.41797 9.75 9.375ZM8.875 9.375C8.875 8.91016 8.46484 8.5 8 8.5C7.50781 8.5 7.125 8.91016 7.125 9.375C7.125 9.86719 7.50781 10.25 8 10.25C8.46484 10.25 8.875 9.86719 8.875 9.375ZM13.25 7.84375C13.6055 7.84375 13.9062 8.14453 13.9062 8.5C13.9062 8.88281 13.6055 9.15625 13.25 9.15625C12.8672 9.15625 12.5938 8.88281 12.5938 8.5C12.5938 8.14453 12.8672 7.84375 13.25 7.84375ZM11.7188 4.125C12.0742 4.125 12.375 4.42578 12.375 4.78125C12.375 5.16406 12.0742 5.4375 11.7188 5.4375C11.3359 5.4375 11.0625 5.16406 11.0625 4.78125C11.0625 4.42578 11.3359 4.125 11.7188 4.125Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'AvgPaceIcon';

export const AvgPaceIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
