import React, {memo, NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 64 64' {...otherProps}>
      <Path
        d='M48.1824 24.3151C49.8823 26.9879 50.8922 30.0409 51.1216 33.2002C51.7041 34.4002 52.0045 35.7175 51.9999 37.0515C51.9775 38.3335 51.6778 39.5955 51.1216 40.7508V48.3184C51.113 48.6261 50.9927 48.9201 50.7832 49.1456C50.5736 49.3711 50.2892 49.5126 49.9829 49.5436C49.6766 49.5747 49.3696 49.4931 49.119 49.3143C48.8685 49.1354 48.6917 48.8715 48.6216 48.5717C48.3483 47.2657 47.8694 46.0113 47.2027 44.8555C45.9711 45.5176 44.5974 45.8711 43.1993 45.8859C41.7962 45.8863 40.4132 45.5525 39.1648 44.9121C37.9164 44.2718 36.8385 43.3433 36.0203 42.2035L33.3176 45.2947C33.159 45.4745 32.9641 45.6184 32.7457 45.717C32.5272 45.8156 32.2903 45.8666 32.0507 45.8666C31.811 45.8666 31.5741 45.8156 31.3557 45.717C31.1372 45.6184 30.9423 45.4745 30.7838 45.2947L28.0811 42.2035C27.2629 43.3433 26.1849 44.2718 24.9365 44.9121C23.6881 45.5525 22.3051 45.8863 20.9021 45.8859C19.4696 45.8877 18.0591 45.5336 16.7973 44.8555C16.1096 46.0187 15.6134 47.2848 15.3277 48.6055C15.2699 48.9149 15.096 49.1904 14.8416 49.3757C14.5873 49.561 14.2717 49.642 13.9595 49.6021C13.6503 49.569 13.3642 49.4233 13.1555 49.1928C12.9468 48.9623 12.8301 48.6631 12.8278 48.3521V40.7846C12.2842 39.615 12.0017 38.3412 12.0001 37.0515C11.9954 35.7175 12.2959 34.4002 12.8784 33.2002C13.103 30.0374 14.1195 26.9819 15.8345 24.3151C15.8686 24.2638 15.9134 24.2206 15.966 24.1885C16.0185 24.1564 16.0774 24.1362 16.1386 24.1292C16.2003 24.1189 16.2636 24.1243 16.3226 24.145C16.3817 24.1657 16.4346 24.2009 16.4764 24.2475C18.9401 26.61 21.9528 28.3225 25.2433 29.2306C25.5584 29.2999 25.8881 29.2464 26.1651 29.081C26.4422 28.9155 26.6456 28.6506 26.734 28.3402C26.8224 28.0299 26.789 27.6975 26.6406 27.4109C26.4923 27.1244 26.2402 26.9052 25.9358 26.7982C19.9223 25.0752 15.6318 20.9367 14.5676 16.1056C14.5094 15.6576 14.6315 15.2048 14.9071 14.8469C15.1827 14.4889 15.5892 14.2551 16.0372 14.1968C16.4852 14.1386 16.938 14.2607 17.296 14.5363C17.6539 14.8119 17.8877 15.2184 17.946 15.6664C18.3649 17.1323 19.1296 18.4763 20.1757 19.5853C23.5675 16.9492 27.7475 15.5307 32.0432 15.5583C36.3389 15.5859 40.5004 17.0579 43.8581 19.7374C44.8737 18.6196 45.609 17.2763 46.0033 15.8185C46.0616 15.3705 46.2954 14.9639 46.6534 14.6883C47.0113 14.4127 47.4641 14.2906 47.9121 14.3489C48.3601 14.4071 48.7666 14.6409 49.0422 14.9989C49.3178 15.3569 49.4399 15.8096 49.3817 16.2576C48.3175 21.0887 44.027 25.261 38.0135 26.9502C37.6909 27.0443 37.419 27.2626 37.2574 27.5572C37.0958 27.8518 37.0579 28.1986 37.152 28.5211C37.2461 28.8437 37.4644 29.1157 37.7591 29.2772C38.0537 29.4388 38.4004 29.4767 38.7229 29.3826C42.0361 28.4607 45.0631 26.7177 47.5236 24.3151C47.5632 24.2657 47.6133 24.2259 47.6703 24.1986C47.7273 24.1712 47.7898 24.157 47.853 24.157C47.9162 24.157 47.9786 24.1712 48.0357 24.1986C48.0927 24.2259 48.1428 24.2657 48.1824 24.3151ZM17.7933 41.5882C18.699 42.1909 19.7634 42.5109 20.8514 42.5076C22.2986 42.5031 23.6856 41.9274 24.7105 40.9056C25.7354 39.8839 26.3154 38.4987 26.3243 37.0515C26.331 35.9636 26.0143 34.8982 25.4144 33.9906C24.8145 33.083 23.9585 32.3741 22.955 31.9539C21.9514 31.5337 20.8457 31.4211 19.7781 31.6305C18.7105 31.8399 17.7292 32.3617 16.9587 33.1298C16.1882 33.8979 15.6633 34.8776 15.4506 35.9446C15.238 37.0116 15.3471 38.1176 15.7642 39.1225C16.1813 40.1273 16.8876 40.9855 17.7933 41.5882ZM29.9392 39.2305L32 41.6123L34.0608 39.2643C33.437 38.899 32.7226 38.7175 32 38.7407C31.2807 38.7072 30.5665 38.877 29.9392 39.2305ZM40.0905 41.5882C40.9963 42.1909 42.0607 42.5109 43.1486 42.5076C44.5958 42.5031 45.9828 41.9274 47.0077 40.9056C48.0327 39.8839 48.6127 38.4987 48.6216 37.0515C48.6283 35.9636 48.3116 34.8982 47.7117 33.9906C47.1118 33.083 46.2557 32.3741 45.2522 31.9539C44.2487 31.5337 43.1429 31.4211 42.0753 31.6305C41.0077 31.8399 40.0264 32.3617 39.2559 33.1298C38.4854 33.8979 37.9605 34.8776 37.7479 35.9446C37.5352 37.0116 37.6444 38.1176 38.0615 39.1225C38.4785 40.1273 39.1848 40.9855 40.0905 41.5882ZM43.1486 39.1461C44.3241 39.1461 45.277 38.1932 45.277 37.0177C45.277 35.8423 44.3241 34.8894 43.1486 34.8894C41.9732 34.8894 41.0203 35.8423 41.0203 37.0177C41.0203 38.1932 41.9732 39.1461 43.1486 39.1461ZM20.851 39.1461C22.0265 39.1461 22.9794 38.1932 22.9794 37.0178C22.9794 35.8423 22.0265 34.8894 20.851 34.8894C19.6756 34.8894 18.7227 35.8423 18.7227 37.0178C18.7227 38.1932 19.6756 39.1461 20.851 39.1461Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'NightOwl';

export const NightOwlIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
