import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 32, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 16 16' {...otherProps}>
      <Path
        d='M6 0.75C3.07422 0.75 0.75 3.10156 0.75 6C0.75 8.13281 1.46094 8.73438 5.45312 14.4766C5.69922 14.8594 6.27344 14.8594 6.51953 14.4766C10.5117 8.73438 11.25 8.13281 11.25 6C11.25 3.10156 8.89844 0.75 6 0.75ZM6 13.7109C2.17188 8.21484 1.625 7.77734 1.625 6C1.625 4.85156 2.0625 3.75781 2.88281 2.91016C3.73047 2.08984 4.82422 1.625 6 1.625C7.14844 1.625 8.24219 2.08984 9.08984 2.91016C9.91016 3.75781 10.375 4.85156 10.375 6C10.375 7.77734 9.80078 8.21484 6 13.7109Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Pin';

export const PinIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
