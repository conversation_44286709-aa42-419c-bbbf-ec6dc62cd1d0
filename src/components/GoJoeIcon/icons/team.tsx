import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg
      width={size}
      height={size}
      viewBox='0 0 20 20'
      fill='none'
      {...otherProps}>
      <Path
        d='M19.4375 3.5C19.5313 3.75 19.5 4 19.375 4.21875L17.7813 7.5C17.6563 7.71875 17.4688 7.875 17.2188 7.96875C17 8.0625 16.7188 8.03125 16.5 7.90625L15 7.15625V14.5938C15 15.375 14.375 16 13.5938 16H6.37504C5.62504 16 5.00004 15.375 5.00004 14.5938V7.15625L3.46879 7.90625C3.25004 8.03125 3.00004 8.0625 2.75004 7.96875C2.50004 7.875 2.31254 7.71875 2.18754 7.46875L0.593788 4.21875C0.468788 4 0.468788 3.75 0.531288 3.5C0.625038 3.25 0.781288 3.0625 1.00004 2.9375L6.87504 0L7.12504 0.375C7.59379 1.0625 8.71879 1.5 10 1.5C11.25 1.5 12.375 1.0625 12.8438 0.375L13.0938 0L18.9688 2.9375C19.1875 3.0625 19.3438 3.25 19.4375 3.5ZM16.9375 7L18.4688 3.8125L13.4063 1.28125C12.6563 2.03125 11.375 2.5 10 2.5C8.59379 2.5 7.34379 2.03125 6.59379 1.28125L1.46879 3.84375L3.09379 7.03125L6.00004 5.5625V14.5938C6.00004 14.8125 6.15629 15 6.37504 15H13.5938C13.8125 15 14 14.8125 14 14.5938V5.5625L16.9375 7Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Team';

export const TeamIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
