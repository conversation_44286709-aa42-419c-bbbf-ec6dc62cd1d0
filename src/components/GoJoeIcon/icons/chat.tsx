import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 32, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 32 32' {...otherProps}>
      <Path
        d='M11 21L9.27333 22.7267C9.03556 22.9644 8.76389 23.018 8.45833 22.8873C8.15278 22.7567 8 22.5235 8 22.1875V9.5C8 9.0875 8.14688 8.73438 8.44063 8.44063C8.73438 8.14688 9.0875 8 9.5 8H22.5C22.9125 8 23.2656 8.14688 23.5594 8.44063C23.8531 8.73438 24 9.0875 24 9.5V19.5C24 19.9125 23.8531 20.2656 23.5594 20.5594C23.2656 20.8531 22.9125 21 22.5 21H11ZM11.75 18H17.25C17.4625 18 17.6406 17.9285 17.7844 17.7856C17.9281 17.6427 18 17.4656 18 17.2544C18 17.0431 17.9281 16.8646 17.7844 16.7188C17.6406 16.5729 17.4625 16.5 17.25 16.5H11.75C11.5375 16.5 11.3594 16.5715 11.2156 16.7144C11.0719 16.8573 11 17.0344 11 17.2456C11 17.4569 11.0719 17.6354 11.2156 17.7812C11.3594 17.9271 11.5375 18 11.75 18ZM11.75 15.25H20.25C20.4625 15.25 20.6406 15.1785 20.7844 15.0356C20.9281 14.8927 21 14.7156 21 14.5044C21 14.2931 20.9281 14.1146 20.7844 13.9688C20.6406 13.8229 20.4625 13.75 20.25 13.75H11.75C11.5375 13.75 11.3594 13.8215 11.2156 13.9644C11.0719 14.1073 11 14.2844 11 14.4956C11 14.7069 11.0719 14.8854 11.2156 15.0312C11.3594 15.1771 11.5375 15.25 11.75 15.25ZM11.75 12.5H20.25C20.4625 12.5 20.6406 12.4285 20.7844 12.2856C20.9281 12.1427 21 11.9656 21 11.7544C21 11.5431 20.9281 11.3646 20.7844 11.2188C20.6406 11.0729 20.4625 11 20.25 11H11.75C11.5375 11 11.3594 11.0715 11.2156 11.2144C11.0719 11.3573 11 11.5344 11 11.7456C11 11.9569 11.0719 12.1354 11.2156 12.2812C11.3594 12.4271 11.5375 12.5 11.75 12.5Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Chat';

export const ChatIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
