import React, {memo, NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 16, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 14 15' {...otherProps}>
      <Path
        d='M11.8125 2.5C12.5234 2.5 13.125 3.10156 13.125 3.8125V13.4375C13.125 14.1758 12.5234 14.75 11.8125 14.75H2.1875C1.44922 14.75 0.875 14.1758 0.875 13.4375V3.8125C0.875 3.10156 1.44922 2.5 2.1875 2.5H3.5V1.07812C3.5 0.914062 3.63672 0.75 3.82812 0.75H4.04688C4.21094 0.75 4.375 0.914062 4.375 1.07812V2.5H9.625V1.07812C9.625 0.914062 9.76172 0.75 9.95312 0.75H10.1719C10.3359 0.75 10.5 0.914062 10.5 1.07812V2.5H11.8125ZM2.1875 3.375C1.94141 3.375 1.75 3.59375 1.75 3.8125V5.125H12.25V3.8125C12.25 3.59375 12.0312 3.375 11.8125 3.375H2.1875ZM11.8125 13.875C12.0312 13.875 12.25 13.6836 12.25 13.4375V6H1.75V13.4375C1.75 13.6836 1.94141 13.875 2.1875 13.875H11.8125Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'CalendarIcon';

export const CalendarIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
