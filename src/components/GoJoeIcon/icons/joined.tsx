import React, {memo, NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg
      width={size}
      height={size}
      fill='none'
      viewBox='0 0 24 24'
      {...otherProps}>
      <Path
        d='M16.5793 10.8102L12.8793 8.87491C12.6089 8.73348 12.3071 8.6595 12.0006 8.6595C11.6941 8.6595 11.3924 8.73348 11.122 8.87491L7.4219 10.8102C7.17097 10.9413 6.88968 11.0065 6.60542 10.9995C6.32115 10.9924 6.0436 10.9133 5.79976 10.7699C5.55592 10.6265 5.35411 10.4236 5.21396 10.181C5.07381 9.93841 5.0001 9.66438 5 9.38558C4.99998 9.09302 5.08103 8.80595 5.23447 8.55503C5.38791 8.30412 5.60798 8.09878 5.87119 7.96095L11.1213 5.21541C11.3918 5.07398 11.6935 5 12 5C12.3065 5 12.6082 5.07398 12.8787 5.21541L18.1288 7.96095C18.3921 8.09873 18.6122 8.30405 18.7656 8.55498C18.9191 8.80591 19.0001 9.09301 19 9.38558C18.9998 9.66421 18.9261 9.93804 18.786 10.1805C18.6459 10.4229 18.4442 10.6256 18.2006 10.769C17.9569 10.9124 17.6796 10.9916 17.3955 10.9988C17.1114 11.006 16.8302 10.9411 16.5793 10.8102V10.8102Z'
        fill={color}
      />
      <Path
        d='M16.5793 14.8102L12.8793 12.8749C12.6089 12.7335 12.3071 12.6595 12.0006 12.6595C11.6941 12.6595 11.3924 12.7335 11.122 12.8749L7.4219 14.8102C7.17097 14.9413 6.88968 15.0065 6.60542 14.9995C6.32115 14.9924 6.0436 14.9133 5.79976 14.7699C5.55592 14.6265 5.35411 14.4236 5.21396 14.181C5.07381 13.9384 5.0001 13.6644 5 13.3856V13.3856C4.99998 13.093 5.08103 12.8059 5.23447 12.555C5.38791 12.3041 5.60798 12.0988 5.87119 11.961L11.1213 9.21541C11.3918 9.07398 11.6935 9 12 9C12.3065 9 12.6082 9.07398 12.8787 9.21541L18.1288 11.961C18.3921 12.0987 18.6122 12.3041 18.7656 12.555C18.9191 12.8059 19.0001 13.093 19 13.3856V13.3856C18.9998 13.6642 18.9261 13.938 18.786 14.1805C18.6459 14.4229 18.4442 14.6256 18.2006 14.769C17.9569 14.9124 17.6796 14.9916 17.3955 14.9988C17.1114 15.006 16.8302 14.9411 16.5793 14.8102V14.8102Z'
        fill={color}
      />
      <Path
        d='M16.5793 18.8102L12.8793 16.8743C12.6089 16.7329 12.3071 16.6589 12.0006 16.6589C11.6941 16.6589 11.3924 16.7329 11.122 16.8743L7.4219 18.8102C7.17097 18.9413 6.88968 19.0065 6.60542 18.9995C6.32115 18.9924 6.0436 18.9133 5.79976 18.7699C5.55592 18.6265 5.35411 18.4236 5.21396 18.181C5.07381 17.9384 5.0001 17.6644 5 17.3856V17.3856C4.99992 17.093 5.08093 16.8059 5.23438 16.555C5.38783 16.3041 5.60794 16.0987 5.87119 15.961L11.1213 13.2154C11.3918 13.074 11.6935 13 12 13C12.3065 13 12.6082 13.074 12.8787 13.2154L18.1288 15.961C18.3921 16.0986 18.6123 16.3039 18.7658 16.5549C18.9193 16.8058 19.0002 17.093 19 17.3856V17.3856C18.9998 17.6642 18.9261 17.938 18.786 18.1805C18.6459 18.4229 18.4442 18.6256 18.2006 18.769C17.9569 18.9124 17.6796 18.9916 17.3955 18.9988C17.1114 19.006 16.8302 18.9411 16.5793 18.8102Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Joined';

export const JoinedIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
