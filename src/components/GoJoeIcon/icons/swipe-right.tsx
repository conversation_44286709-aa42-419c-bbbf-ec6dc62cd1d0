import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg
      width={size}
      height={size}
      viewBox='0 0 24 24'
      fill='none'
      stroke={color}
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
      {...otherProps}>
      <Path
        d='M10.6078 20.75L6.81883 17.656C6.57404 17.4603 6.39058 17.1984 6.29025 16.9015C6.18992 16.6046 6.17693 16.2851 6.25283 15.981C6.31215 15.743 6.4242 15.5214 6.58069 15.3325C6.73718 15.1436 6.93412 14.9923 7.15694 14.8898C7.37976 14.7873 7.62278 14.7361 7.86802 14.7401C8.11327 14.7442 8.35448 14.8032 8.57383 14.913L9.74783 15.5L9.74783 8.75C9.74783 8.35218 9.90586 7.97064 10.1872 7.68934C10.4685 7.40804 10.85 7.25 11.2478 7.25C11.6457 7.25 12.0272 7.40804 12.3085 7.68934C12.5898 7.97064 12.7478 8.35217 12.7478 8.75L12.7478 13.25L14.7418 13.582C15.4421 13.6989 16.0783 14.0604 16.5371 14.6022C16.996 15.144 17.2478 15.831 17.2478 16.541L17.2478 20.75'
        stroke={color}
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <Path
        d='M22.25 7.25L17 7.25'
        stroke={color}
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <Path
        d='M18.5 3.5L22.25 7.25L18.5 11'
        stroke={color}
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
};

Icon.displayName = 'SwipeRight';

export const SwipeRight: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
