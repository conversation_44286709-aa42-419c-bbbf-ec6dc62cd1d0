import React, {memo, NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 16, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 14 15' {...otherProps}>
      <Path
        d='M7 0.96875C10.7461 0.96875 13.7812 4.00391 13.7812 7.75C13.7812 11.4961 10.7461 14.5312 7 14.5312C3.25391 14.5312 0.21875 11.4961 0.21875 7.75C0.21875 4.00391 3.25391 0.96875 7 0.96875ZM12.9062 7.75C12.9062 4.49609 10.2266 1.84375 7 1.84375C3.71875 1.84375 1.09375 4.52344 1.09375 7.75C1.09375 11.0312 3.74609 13.6562 7 13.6562C10.2539 13.6562 12.9062 11.0039 12.9062 7.75ZM8.83203 10.1836L6.58984 8.57031C6.50781 8.48828 6.48047 8.40625 6.48047 8.29688V3.92188C6.48047 3.75781 6.61719 3.59375 6.80859 3.59375H7.19141C7.35547 3.59375 7.51953 3.75781 7.51953 3.92188V7.94141L9.43359 9.33594C9.57031 9.44531 9.625 9.63672 9.51562 9.80078L9.26953 10.1016C9.1875 10.2383 8.96875 10.293 8.83203 10.1836Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'ClockIcon';

export const ClockIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
