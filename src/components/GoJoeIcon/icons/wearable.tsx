import React, {memo, NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 14 19' {...otherProps}>
      <Path
        d='M5.03125 8.40625C5.17188 8.40625 5.3125 8.54688 5.3125 8.6875V9.8125C5.3125 9.98828 5.17188 10.0938 5.03125 10.0938H3.90625C3.73047 10.0938 3.625 9.98828 3.625 9.8125V8.6875C3.625 8.54688 3.73047 8.40625 3.90625 8.40625H5.03125ZM7.5625 10.9375C7.70312 10.9375 7.84375 11.0781 7.84375 11.2188V12.3438C7.84375 12.5195 7.70312 12.625 7.5625 12.625H6.4375C6.26172 12.625 6.15625 12.5195 6.15625 12.3438V11.2188C6.15625 11.0781 6.26172 10.9375 6.4375 10.9375H7.5625ZM5.03125 10.9375C5.17188 10.9375 5.3125 11.0781 5.3125 11.2188V12.3438C5.3125 12.5195 5.17188 12.625 5.03125 12.625H3.90625C3.73047 12.625 3.625 12.5195 3.625 12.3438V11.2188C3.625 11.0781 3.73047 10.9375 3.90625 10.9375H5.03125ZM10.0938 10.9375C10.2344 10.9375 10.375 11.0781 10.375 11.2188V12.3438C10.375 12.5195 10.2344 12.625 10.0938 12.625H8.96875C8.79297 12.625 8.6875 12.5195 8.6875 12.3438V11.2188C8.6875 11.0781 8.79297 10.9375 8.96875 10.9375H10.0938ZM7.5625 8.40625C7.70312 8.40625 7.84375 8.54688 7.84375 8.6875V9.8125C7.84375 9.98828 7.70312 10.0938 7.5625 10.0938H6.4375C6.26172 10.0938 6.15625 9.98828 6.15625 9.8125V8.6875C6.15625 8.54688 6.26172 8.40625 6.4375 8.40625H7.5625ZM11.5 3.13281C12.7656 3.41406 13.75 4.53906 13.75 5.875V12.625C13.75 13.9961 12.7656 15.1211 11.5 15.4023V17.125C11.5 17.7578 10.9727 18.25 10.375 18.25H3.625C2.99219 18.25 2.5 17.7578 2.5 17.125V15.4023C1.19922 15.1211 0.25 13.9961 0.25 12.625V5.875C0.25 4.53906 1.19922 3.41406 2.5 3.13281V1.375C2.5 0.777344 2.99219 0.25 3.625 0.25H10.375C10.9727 0.25 11.5 0.777344 11.5 1.375V3.13281ZM3.625 1.375V3.0625H10.375V1.375H3.625ZM10.375 17.125V15.4375H3.625V17.125H10.375ZM12.625 12.625V5.875C12.5898 4.96094 11.8516 4.22266 10.9375 4.1875H3.0625C2.11328 4.22266 1.375 4.96094 1.375 5.875V12.625C1.375 13.5742 2.11328 14.3125 3.0625 14.3125H10.9375C11.8516 14.3125 12.5898 13.5742 12.625 12.625ZM10.0938 5.875C10.2344 5.875 10.375 6.01562 10.375 6.15625V7.28125C10.375 7.45703 10.2344 7.5625 10.0938 7.5625H3.90625C3.73047 7.5625 3.625 7.45703 3.625 7.28125V6.15625C3.625 6.01562 3.73047 5.875 3.90625 5.875H10.0938ZM10.0938 8.40625C10.2344 8.40625 10.375 8.54688 10.375 8.6875V9.8125C10.375 9.98828 10.2344 10.0938 10.0938 10.0938H8.96875C8.79297 10.0938 8.6875 9.98828 8.6875 9.8125V8.6875C8.6875 8.54688 8.79297 8.40625 8.96875 8.40625H10.0938Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Wearable';

export const WearableIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
