import React, {memo, NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';
import {Path, Svg} from 'react-native-svg';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 64 64' {...otherProps}>
      <Path
        d='M50.4232 12.0977C50.5254 11.8062 50.7523 11.6099 51.053 11.5527C51.1323 11.5377 51.32 11.5494 51.407 11.5749C51.7626 11.679 54.2771 12.4855 54.3342 12.5137C54.5299 12.6104 54.6862 12.8035 54.7553 13.0341C54.7898 13.149 54.7898 13.3654 54.7554 13.4901C54.7087 13.659 54.6127 13.8135 54.4925 13.9129C54.4146 13.9774 54.2648 14.0577 54.1785 14.0813C54.0809 14.1081 53.9319 14.1129 53.8358 14.0924C53.7907 14.0828 53.4507 13.9787 53.0804 13.8611C52.71 13.7435 52.4053 13.6491 52.4032 13.6513C52.401 13.6535 52.615 14.4004 52.8787 15.3111C53.1424 16.2218 53.3708 17.0201 53.3863 17.085C53.436 17.2932 53.4521 17.439 53.4521 17.6806C53.4521 18.0903 53.379 18.4186 53.2047 18.7919C53.0873 19.0433 52.9224 19.2764 52.7139 19.486C52.3531 19.8486 51.9316 20.0689 51.4122 20.1664C51.2632 20.1943 50.8075 20.1946 50.6488 20.1669C50.3895 20.1216 50.1896 20.0548 49.9503 19.9335C49.0329 19.4686 48.4954 18.4443 48.6146 17.3887C48.6463 17.1074 48.7267 16.8253 48.8478 16.5699C49.2485 15.7249 50.0892 15.1727 50.9814 15.1686C51.0701 15.1682 51.1376 15.1635 51.1376 15.1579C51.1376 15.1524 50.9727 14.5769 50.7712 13.8791C50.5697 13.1812 50.3997 12.5884 50.3935 12.5616C50.3588 12.4128 50.3691 12.2523 50.4232 12.0977ZM40.1258 5.14812C40.2246 5.07968 40.3317 5.03333 40.4437 5.0106C40.538 4.99145 40.6907 4.99893 40.789 5.02752C41.0227 5.09549 41.1949 5.23922 41.3026 5.45622C41.4028 5.65819 43.4229 10.1373 43.4582 10.2359C43.5076 10.3736 43.5606 10.591 43.5842 10.7525C43.6098 10.9278 43.61 11.2822 43.5846 11.4554C43.5033 12.0103 43.269 12.4951 42.8945 12.883C42.5203 13.2705 42.0517 13.5135 41.5161 13.5978C41.3502 13.624 41.0077 13.6236 40.8374 13.5972C40.0315 13.4721 39.3559 12.9634 38.9978 12.2119C38.9144 12.0369 38.8334 11.7916 38.7972 11.6038C38.6907 11.0529 38.7654 10.4768 39.0083 9.97552C39.3591 9.25149 40.0497 8.73264 40.8164 8.61704C40.8789 8.60762 40.9324 8.59741 40.9324 8.59741C40.9324 8.59741 40.7436 8.17256 40.5099 7.65633C39.858 6.21653 39.8214 6.13586 39.7994 6.0514C39.7981 6.04632 39.7968 6.04123 39.7954 6.03582C39.7766 5.96063 39.7738 5.92552 39.7777 5.81174C39.7816 5.69647 39.787 5.66253 39.8146 5.57865C39.8769 5.38938 39.9765 5.25156 40.1258 5.14812ZM50.6455 16.9436C50.7355 16.8961 50.8098 16.8684 50.8865 16.8539C50.9674 16.8386 51.1644 16.8503 51.2499 16.8754C51.5102 16.952 51.7171 17.1639 51.8003 17.4391C51.8312 17.5416 51.8399 17.742 51.8179 17.8488C51.7537 18.1614 51.5298 18.4065 51.2303 18.492C51.1276 18.5214 50.9193 18.5189 50.8118 18.4872C50.6669 18.4443 50.5757 18.3872 50.4584 18.2659C50.3683 18.1727 50.3476 18.1442 50.3058 18.0561C50.2421 17.9218 50.2229 17.8347 50.2229 17.6806C50.2229 17.5264 50.2421 17.4393 50.3058 17.305C50.3476 17.2169 50.3683 17.1884 50.4584 17.0952C50.5405 17.0103 50.5802 16.9781 50.6455 16.9436ZM40.3907 10.9163C40.4684 10.5741 40.7447 10.3168 41.079 10.2752C41.468 10.2268 41.8362 10.4753 41.953 10.8652C41.974 10.9353 41.9771 10.9671 41.9766 11.112C41.9761 11.2611 41.9733 11.2869 41.9495 11.3606C41.9061 11.4949 41.8543 11.5789 41.7448 11.6923C41.6354 11.8056 41.5544 11.8592 41.4246 11.9044C41.3524 11.9295 41.33 11.932 41.1794 11.932C41.0287 11.932 41.0063 11.9295 40.9341 11.9044C40.6718 11.8131 40.4635 11.5865 40.3961 11.319C40.3694 11.2129 40.3668 11.0213 40.3907 10.9163ZM38.0581 23.5287L48.1831 23.1406C48.5154 23.1453 48.8389 23.248 49.1131 23.4359C49.3872 23.6237 49.5998 23.8883 49.7242 24.1965C49.8485 24.5047 49.8791 24.8427 49.8121 25.1683C49.7451 25.4938 49.5835 25.7922 49.3475 26.0262C49.3475 26.0262 39.3912 35.9487 39.2225 36.0331C39.0089 37.6931 38.5718 39.3166 37.9231 40.8594L37.9172 40.8742C36.8397 43.6016 35.9194 45.931 38.3956 50.9844C38.4667 51.1075 38.5041 51.2472 38.5041 51.3894C38.5041 51.5316 38.4667 51.6712 38.3956 51.7944C38.3199 51.9188 38.2132 52.0214 38.0861 52.0924C37.9589 52.1634 37.8156 52.2002 37.67 52.1994H16.5762C16.4331 52.199 16.2926 52.1614 16.1684 52.0904C16.0441 52.0194 15.9405 51.9174 15.8675 51.7944C15.7964 51.6712 15.7589 51.5316 15.7589 51.3894C15.7589 51.2472 15.7964 51.1075 15.8675 50.9844C15.9409 50.8357 16.0364 50.6661 16.1443 50.4746C17.0284 48.9054 18.7401 45.8676 15.8675 40.8594C14.7788 39.0599 14.1624 37.0146 14.0752 34.9132C13.988 32.8119 14.433 30.7226 15.3688 28.8391C16.3047 26.9556 17.7011 25.339 19.4285 24.1393C21.1558 22.9396 23.1583 22.1955 25.25 21.9762C25.25 21.875 31.6625 12.4925 31.6625 12.4925C31.8406 12.2307 32.0887 12.0243 32.3786 11.8968C32.6685 11.7694 32.9883 11.7261 33.3016 11.7718C33.615 11.8176 33.9091 11.9506 34.1504 12.1556C34.3917 12.3607 34.5704 12.6294 34.6662 12.9312L38.0581 23.5287ZM27.1831 29.7531C26.9056 29.5677 26.5793 29.4687 26.2456 29.4687H26.2962C26.0704 29.462 25.8455 29.5006 25.6349 29.5824C25.4243 29.6641 25.2322 29.7874 25.0701 29.9447C24.908 30.1021 24.7792 30.2904 24.6912 30.4986C24.6033 30.7067 24.558 30.9303 24.5581 31.1562C24.5581 31.49 24.6571 31.8163 24.8425 32.0938C25.0279 32.3713 25.2915 32.5876 25.5998 32.7153C25.9082 32.843 26.2475 32.8764 26.5748 32.8113C26.9021 32.7462 27.2028 32.5855 27.4388 32.3495C27.6748 32.1135 27.8355 31.8128 27.9007 31.4855C27.9658 31.1581 27.9324 30.8188 27.8046 30.5105C27.6769 30.2021 27.4606 29.9386 27.1831 29.7531ZM38.7934 31.7278C38.9106 31.6848 39.0168 31.6164 39.1043 31.5275L44.015 26.6844L36.9275 26.9375C36.5574 26.9556 36.1916 26.8514 35.8866 26.641C35.5816 26.4305 35.3544 26.1255 35.24 25.7731L32.5062 17.2344L29.1312 21.9762C29.0677 22.0703 29.0234 22.176 29.0009 22.2873C28.9783 22.3985 28.9781 22.5131 29 22.6245C29.022 22.7358 29.0657 22.8417 29.1288 22.9361C29.1919 23.0305 29.273 23.1115 29.3675 23.1744C32.6387 25.4063 35.4942 28.1933 37.805 31.4094C37.875 31.5126 37.9672 31.5991 38.0747 31.6624C38.1822 31.7258 38.3024 31.7645 38.4267 31.7758C38.551 31.7871 38.6762 31.7707 38.7934 31.7278Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'EarlyBird';

export const EarlyBirdIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
