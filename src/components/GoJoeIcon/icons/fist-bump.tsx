import React, {memo, NamedExoticComponent} from 'react';
import {Path, Svg} from 'react-native-svg';
import type {IconProps} from '@tamagui/helpers-icon';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 24, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 20 17' {...otherProps}>
      <Path
        d='M10.4016 0.121571C10.2242 0.184048 9.96422 0.310821 9.82394 0.403292C9.36423 0.706275 8.91664 1.27276 8.72616 1.79266L8.65968 1.97412L8.55311 1.8136C8.26545 1.38029 7.74734 0.971439 7.31459 0.836209C6.81637 0.680549 6.09795 0.751958 5.60504 1.00614C5.04108 1.29695 4.53762 1.8528 4.29539 2.45217L4.16585 2.77262L4.04757 2.6247C3.83111 2.35401 3.61747 2.17859 3.32449 2.03103C3.04687 1.89117 3.01753 1.88497 2.64088 1.88623C2.33548 1.88726 2.19586 1.90682 2.01738 1.97361C1.41642 2.1985 0.968163 2.68809 0.615924 3.50425C0.427714 3.94033 0.346579 4.25307 0.217668 5.03912C-0.0914051 6.92411 -0.0719124 8.67471 0.279858 10.6174C0.513147 11.9059 1.05242 12.5319 2.10402 12.735C2.38009 12.7883 3.04741 12.7626 3.30141 12.6889C3.56954 12.6111 3.94776 12.4121 4.16077 12.2366L4.36559 12.0678L4.43 12.2044C4.7138 12.8064 5.43422 13.3257 6.18655 13.4705C6.58195 13.5465 7.21928 13.5188 7.56327 13.4105C7.9209 13.2978 8.32576 13.0673 8.54221 12.8528L8.73554 12.6613L8.86262 12.9092C8.9325 13.0456 9.08532 13.2641 9.20224 13.3947C9.34955 13.5594 9.39845 13.6387 9.36154 13.6531C9.33224 13.6645 9.27966 13.8018 9.24474 13.9584C8.94848 15.2854 9.74319 16.6026 11.0423 16.9376C11.3206 17.0094 12.2091 17.0229 12.4238 16.9587C12.5966 16.9069 14.8887 16.258 16.2641 15.8713C16.8403 15.7094 17.4439 15.5151 17.6056 15.4395C18.6961 14.9298 19.3388 13.9621 19.7453 12.2179C20.0397 10.955 20.0802 9.5394 19.8601 8.20686C19.6349 6.84389 19.2618 5.94838 18.6658 5.34076L18.4537 5.12448L18.4054 4.7293C18.177 2.85944 17.8952 2.05924 17.2297 1.39005C16.9213 1.07995 16.6437 0.911609 16.2787 0.813328C16.0134 0.74188 15.2019 0.741604 14.9372 0.812894C14.5104 0.92789 14.1073 1.22317 13.799 1.6468C13.7041 1.77724 13.6161 1.8809 13.6035 1.87707C13.5909 1.87323 13.5532 1.80175 13.5197 1.71817C13.2728 1.10299 12.6782 0.464347 12.1283 0.223566C11.5383 -0.0348011 10.9451 -0.0698137 10.4016 0.121571ZM11.6965 0.861224C12.3704 1.09347 12.8639 1.71896 12.9763 2.48295C13.0129 2.73223 13.0424 2.81265 13.1297 2.90093C13.2296 3.00202 13.2612 3.01028 13.5481 3.01028C13.9486 3.01028 14.0134 2.95965 14.247 2.4639C14.57 1.77863 14.9524 1.5086 15.6002 1.5086C16.1812 1.5086 16.5259 1.66296 16.8688 2.07671C17.1915 2.46628 17.3161 2.82482 17.4813 3.84015C17.8538 6.12779 17.8579 7.68289 17.4988 10.4791C17.3779 11.4206 17.1986 11.8914 16.8096 12.2889C16.4338 12.673 15.917 12.8194 15.2283 12.737C14.5032 12.6503 14.0262 12.2869 13.7944 11.6449C13.7482 11.517 13.7331 11.0908 13.7152 9.41215C13.703 8.27107 13.6788 7.3151 13.6613 7.28779C13.6438 7.26045 13.5757 7.20998 13.51 7.1756C13.3525 7.09317 13.1539 7.13186 13.0502 7.26523C12.9721 7.36576 12.9703 7.41334 12.9691 9.54592C12.9684 10.7438 12.9525 11.8438 12.9338 11.9904C12.846 12.6799 12.3722 13.2193 11.6688 13.4306C11.3548 13.5249 10.8521 13.5082 10.5258 13.3925C10.1733 13.2675 9.98973 13.1488 9.75464 12.8938C9.54217 12.6634 9.3958 12.3711 9.33775 12.0614C9.31451 11.9375 9.29837 11.019 9.29837 9.8191C9.29837 8.34975 9.28579 7.7628 9.25294 7.7004C9.18185 7.56537 9.09661 7.51791 8.92281 7.51656C8.80027 7.51561 8.73769 7.54004 8.65913 7.61947L8.55616 7.72367L8.55487 9.34837C8.55417 10.242 8.53799 11.0958 8.51889 11.2457C8.43026 11.9416 7.96778 12.4655 7.25233 12.6804C6.83356 12.8062 6.14033 12.7388 5.7427 12.5336C5.52152 12.4194 5.21081 12.1142 5.07956 11.8821C4.86517 11.5031 4.84654 11.3098 4.84576 9.45894C4.84529 8.26459 4.83212 7.76205 4.79966 7.7004C4.72857 7.56537 4.64333 7.51791 4.46953 7.51656C4.34808 7.51561 4.28437 7.54004 4.20863 7.6167L4.10835 7.7181L4.09479 9.30608C4.08147 10.8676 4.07968 10.898 3.98881 11.1288C3.74841 11.7394 3.11136 12.0607 2.30415 11.9783C1.56115 11.9026 1.28466 11.5995 1.08067 10.6372C0.812142 9.3705 0.710888 7.7187 0.821478 6.4088C1.02941 3.94582 1.64143 2.65197 2.59681 2.65549C3.02128 2.65703 3.30516 2.85094 3.62771 3.35961C3.73869 3.53464 3.86623 3.68635 3.92522 3.71353C3.98198 3.7397 4.1612 3.76111 4.32347 3.76111C4.60473 3.76111 4.6245 3.75507 4.74646 3.63169C4.84779 3.52918 4.88006 3.45726 4.90146 3.28591C5.01373 2.3885 5.6461 1.67588 6.44671 1.54449C7.20627 1.41985 7.75555 1.77001 8.18759 2.65438C8.26119 2.80502 8.35549 2.94669 8.39713 2.96926C8.43881 2.99182 8.60765 3.01028 8.77234 3.01028C9.0586 3.01028 9.07747 3.00455 9.19974 2.88086C9.30107 2.77835 9.33333 2.70642 9.35474 2.53508C9.41705 2.03719 9.60666 1.64071 9.94476 1.30137C10.1799 1.06537 10.543 0.857272 10.8219 0.798628C11.0515 0.750337 11.4597 0.779541 11.6965 0.861224ZM18.9888 7.47985C19.1724 8.21871 19.2969 9.18879 19.2975 9.88636C19.298 10.361 19.2084 11.2223 19.1055 11.733C18.8196 13.1522 18.3407 14.0596 17.6432 14.5033C17.3399 14.6963 16.9351 14.839 15.7634 15.1659C15.2478 15.3097 14.4831 15.524 14.0642 15.6421C12.2538 16.1523 12.0326 16.2104 11.814 16.2332C11.3741 16.2789 10.8242 16.1006 10.4898 15.8036C10.2931 15.629 10.0911 15.2916 10.0189 15.0174C9.96027 14.7944 9.94554 14.4079 9.98758 14.1951C10.0159 14.052 10.0314 14.0512 10.4312 14.1723C10.7646 14.2732 11.3697 14.295 11.7313 14.2192C12.4943 14.0593 13.2105 13.5285 13.5086 12.902L13.5686 12.7759L13.7871 12.9738C14.0664 13.2267 14.4643 13.4197 14.8813 13.5045C15.3271 13.5951 16.1204 13.5673 16.4889 13.4481C16.9545 13.2975 17.3905 12.9513 17.7057 12.4817C18.0452 11.976 18.1641 11.5677 18.3236 10.3606C18.4852 9.13742 18.5513 8.36019 18.582 7.32364L18.6082 6.43998L18.7612 6.80967C18.8453 7.01303 18.9477 7.31459 18.9888 7.47985Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'FistBump';

export const FistBumpIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
