import {memo} from 'react';
import type {NamedExoticComponent} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {Svg, Path} from 'react-native-svg';
import {themed} from '@tamagui/helpers-icon';

const Icon = (props: any) => {
  const {color = 'black', size = 32, ...otherProps} = props;
  return (
    <Svg width={size} height={size} viewBox='0 0 36 36' {...otherProps}>
      <Path
        d='M2.86719 17.998C2.86719 22.0426 4.47388 25.9215 7.33381 28.7814C10.1937 31.6414 14.0726 33.248 18.1172 33.248C22.1617 33.248 26.0406 31.6414 28.9006 28.7814C31.7605 25.9215 33.3672 22.0426 33.3672 17.998C33.3672 13.9535 31.7605 10.0746 28.9006 7.21467C26.0406 4.35474 22.1617 2.74805 18.1172 2.74805C14.0726 2.74805 10.1937 4.35474 7.33381 7.21467C4.47388 10.0746 2.86719 13.9535 2.86719 17.998ZM5.9196 30.1956C2.68459 26.9606 0.867188 22.573 0.867188 17.998C0.867188 13.4231 2.68459 9.03546 5.9196 5.80045C9.1546 2.56545 13.5422 0.748047 18.1172 0.748047C22.6922 0.748047 27.0798 2.56545 30.3148 5.80045C33.5498 9.03546 35.3672 13.4231 35.3672 17.998C35.3672 22.573 33.5498 26.9606 30.3148 30.1956C27.0798 33.4306 22.6922 35.248 18.1172 35.248C13.5422 35.248 9.1546 33.4306 5.9196 30.1956Z'
        fill={color}
      />
      <Path
        d='M9.86719 17.998C9.86719 20.1861 10.7364 22.2845 12.2836 23.8317C13.8307 25.3789 15.9292 26.248 18.1172 26.248C20.3052 26.248 22.4036 25.3789 23.9508 23.8317C25.498 22.2845 26.3672 20.1861 26.3672 17.998C26.3672 15.81 25.498 13.7116 23.9508 12.1644C22.4036 10.6172 20.3052 9.74805 18.1172 9.74805C15.9292 9.74805 13.8307 10.6172 12.2836 12.1644C10.7364 13.7116 9.86719 15.81 9.86719 17.998Z'
        fill={color}
      />
      <Path
        d='M11.5764 11.4573C13.3112 9.7226 15.6639 8.74805 18.1172 8.74805C20.5704 8.74805 22.9232 9.7226 24.6579 11.4573C26.3926 13.192 27.3672 15.5448 27.3672 17.998C27.3672 20.4513 26.3926 22.8041 24.6579 24.5388C22.9232 26.2735 20.5704 27.248 18.1172 27.248C15.6639 27.248 13.3112 26.2735 11.5764 24.5388C9.84174 22.8041 8.86719 20.4513 8.86719 17.998C8.86719 15.5448 9.84174 13.192 11.5764 11.4573ZM18.1172 10.748C16.1944 10.748 14.3503 11.5119 12.9907 12.8715C11.631 14.2312 10.8672 16.0752 10.8672 17.998C10.8672 19.9209 11.631 21.7649 12.9907 23.1246C14.3503 24.4842 16.1944 25.248 18.1172 25.248C20.04 25.248 21.8841 24.4842 23.2437 23.1246C24.6034 21.7649 25.3672 19.9209 25.3672 17.998C25.3672 16.0752 24.6034 14.2312 23.2437 12.8715C21.8841 11.5119 20.04 10.748 18.1172 10.748Z'
        fill={color}
      />
    </Svg>
  );
};

Icon.displayName = 'Record';

export const RecordIcon: NamedExoticComponent<IconProps> = memo<IconProps>(
  themed(Icon),
);
