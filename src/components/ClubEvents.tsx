import React from 'react';
import {XStack, YStack} from 'tamagui';

import useClubEvents from '@/src/hooks/api/useClubEvents';
import Event from '@/src/components/ClubEvent';
import I18nText from '@/src/components/I18nText';
import {StyledEntypo} from './StyledIcons';
import {triggerHaptics} from '../utils/haptics';
import {useRouter} from 'expo-router';

interface Props {
  clubId: string;
  isAdmin?: boolean;
}
const Events: React.FC<Props> = ({clubId, isAdmin = false}) => {
  const router = useRouter();
  const {data} = useClubEvents(clubId);

  const handleAddEvent = async () => {
    await triggerHaptics();

    router.push({
      pathname: '/club/[clubId]/add-club-event',
      params: {
        clubId,
      },
    });
  };

  const handleAddPost = async () => {
    await triggerHaptics();

    router.push({
      pathname: '/club/[clubId]/add-club-post',
      params: {
        clubId,
      },
    });
  };

  return (
    <YStack>
      <XStack
        alignItems='center'
        justifyContent='space-between'
        marginBottom={8}>
        {data && data.length > 0 && (
          <I18nText fontSize={10} fontWeight='bold' textTransform='uppercase'>
            Upcoming Club Events
          </I18nText>
        )}

        {isAdmin && (
          <XStack gap='$1.5' alignItems='center' onPress={handleAddEvent}>
            <StyledEntypo name='circle-with-plus' color='$primary' size={16} />
            <I18nText fontSize={12} fontWeight={700} color='$primary'>
              add event
            </I18nText>
          </XStack>
        )}
      </XStack>
      {data &&
        data.map((event) => (
          <Event clubId={clubId} key={event.id} event={event} />
        ))}
      <YStack height='$4' />

      <XStack
        alignItems='center'
        justifyContent='space-between'
        marginBottom={8}>
        <I18nText fontSize={10} fontWeight='bold' textTransform='uppercase'>
          Announcements
        </I18nText>

        {isAdmin && (
          <XStack gap='$1.5' alignItems='center' onPress={handleAddPost}>
            <StyledEntypo name='circle-with-plus' color='$primary' size={16} />
            <I18nText fontSize={12} fontWeight={700} color='$primary'>
              add post
            </I18nText>
          </XStack>
        )}
      </XStack>
    </YStack>
  );
};

export default Events;
