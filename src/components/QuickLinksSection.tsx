import React, {useCallback} from 'react';
import {ScrollView, XStack, YStack, YStackProps} from 'tamagui';
import {Image} from 'expo-image';
import {useRouter} from 'expo-router';
import * as Linking from 'expo-linking';
import {BenefitDto} from '@gojoe/typescript-sdk';

import I18nText from './I18nText';
import StyledText from './UI/StyledText';
import {useBenefits} from '@/src/hooks/api/useBenefits';
import {triggerHaptics} from '@/src/utils/haptics';

const QuickLinksSection: React.FC<YStackProps> = (props) => {
  const {data} = useBenefits();
  const router = useRouter();

  const handleOnPress = useCallback(
    async (benefit: BenefitDto) => {
      await triggerHaptics();
      switch (benefit.name) {
        case 'Les Mills':
          router.push('/les-mills');
          return;
        case 'House Of Wellbeing':
          router.push('/house-of-wellbeing');
          return;
      }
      if (benefit.description) {
        router.push({
          pathname: '/benefits/[benefitId]',
          params: {
            benefitId: benefit.id,
          },
        });
        return;
      }
      if (benefit.url) {
        Linking.canOpenURL(benefit.url).then(async (supported) => {
          if (supported) {
            await Linking.openURL(benefit.url);
          }
        });
        return;
      }
    },
    [router],
  );

  if (!data || data.length === 0) {
    return null;
  }

  return (
    <YStack {...props}>
      <I18nText
        fontSize={12}
        fontWeight='700'
        color='$grey1'
        paddingHorizontal={'$5'}>
        Quick Links
      </I18nText>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        marginTop='$3.5'>
        <XStack gap='$3' paddingHorizontal={'$5'}>
          {data.map((benefit) => (
            <YStack
              key={benefit.id}
              onPress={() => handleOnPress(benefit)}
              width={80}
              height={92}
              alignItems='center'
              overflow='hidden'>
              <Image
                source={{uri: benefit.image}}
                style={{width: 60, height: 60, borderRadius: 8}}
              />
              <StyledText
                fontWeight='500'
                fontSize={11}
                numberOfLines={1}
                marginTop='$1'>
                {benefit.name}
              </StyledText>
            </YStack>
          ))}
        </XStack>
      </ScrollView>
    </YStack>
  );
};

export default QuickLinksSection;
