import React from 'react';
import {GetProps, styled} from 'tamagui';
import {
  Entypo,
  Feather,
  FontAwesome,
  FontAwesome5,
  MaterialIcons,
  MaterialCommunityIcons,
} from '@expo/vector-icons';

const StyledEntypoIcon = styled(Entypo, {});
export const StyledEntypo: React.FC<GetProps<typeof StyledEntypoIcon>> = ({
  ...tamaguiProps
}) => {
  return <StyledEntypoIcon {...tamaguiProps} />;
};

const StyledFeatherIcon = styled(Feather, {});
export const StyledFeather: React.FC<GetProps<typeof StyledFeatherIcon>> = ({
  ...tamaguiProps
}) => {
  return <StyledFeatherIcon {...tamaguiProps} />;
};

const StyledFontAwesomeIcon = styled(FontAwesome, {});

export const StyledFontAwesome: React.FC<
  GetProps<typeof StyledFontAwesomeIcon>
> = ({...tamaguiProps}) => {
  return <StyledFontAwesomeIcon {...tamaguiProps} />;
};

const StyledFontAwesome5Icon = styled(FontAwesome5, {});
export const StyledFontAwesome5: React.FC<
  GetProps<typeof StyledFontAwesome5Icon>
> = ({...tamaguiProps}) => {
  return <StyledFontAwesome5Icon {...tamaguiProps} />;
};

const StyledMaterialIconsIcon = styled(MaterialIcons, {});
export const StyledMaterialIcons: React.FC<
  GetProps<typeof StyledMaterialIconsIcon>
> = ({...tamaguiProps}) => {
  return <StyledMaterialIconsIcon {...tamaguiProps} />;
};

const StyledMaterialCommunityIconsIcon = styled(MaterialCommunityIcons, {});
export const StyledMaterialCommunityIcons: React.FC<
  GetProps<typeof StyledMaterialCommunityIconsIcon>
> = ({...tamaguiProps}) => {
  return <StyledMaterialCommunityIconsIcon {...tamaguiProps} />;
};
