import React, {memo} from 'react';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import type {ParamListBase} from '@react-navigation/native';
import {useTheme, View, XStack, YStack} from 'tamagui';
import {NAVIGATION_HEADER_HEIGHT} from '@/src/constants/navigation';
import StyledText from '@/src/components/UI/StyledText';

const Tab = createMaterialTopTabNavigator();

// Custom Tab Bar
function CustomTabBar({state, descriptors, navigation}) {
  const theme = useTheme();
  const color = theme.color ? theme.color.get() : 'black';
  const backgroundColor = theme.background ? theme.background.get() : 'black';
  return (
    <YStack backgroundColor='$background'>
      <XStack
        marginHorizontal={24}
        marginVertical={12}
        backgroundColor='$accentGrey'
        borderWidth={1}
        borderColor='$accentGrey'
        borderRadius={24}
        height={32}
        overflow='hidden'
        alignItems='center'>
        {state.routes.map((route, index: number) => {
          const isFocused = state.index === index;
          return (
            <YStack flex={1} key={route.key}>
              <YStack
                backgroundColor={isFocused ? color : 'transparent'}
                height={32}
                borderRadius={16}>
                <StyledText
                  onPress={() => navigation.navigate(route.name)}
                  style={{
                    textAlign: 'center',
                    fontWeight: 'bold',
                    color: isFocused ? backgroundColor : '#9E9E9E',
                    lineHeight: 32, // Center text vertically
                  }}>
                  {route.name}
                </StyledText>
              </YStack>
            </YStack>
          );
        })}
      </XStack>
    </YStack>
  );
}

const StyledTopTabButtonsNavigator: React.FC<
  ParamListBase & {children: React.ReactNode}
> = ({children, ...props}) => {
  const theme = useTheme();
  const color = theme.color ? theme.color.get() : 'black';
  const backgroundColor = theme.background ? theme.background.get() : 'black';
  return (
    <Tab.Navigator
      tabBar={(props) => <CustomTabBar {...props} />}
      {...props}
      screenOptions={{
        tabBarStyle: {
          //   height: 36, // Set height to 36px
          backgroundColor: 'ted', // No default background
          //   elevation: 0, // Remove shadow on Android
          //   shadowOpacity: 0, // Remove shadow on iOS
          // },
          // tabBarItemStyle: {
          //   flex: 1, // Equal spacing
          // },
          // tabBarLabelStyle: {
          //   fontWeight: 'bold',
          //   textTransform: 'none', // Prevent uppercase text
        },
        // tabBarActiveTintColor: '#000', // Active text color
        // tabBarInactiveTintColor: '#9E9E9E', // Inactive text color
      }}>
      {children}
    </Tab.Navigator>
  );
};

export default memo(StyledTopTabButtonsNavigator);
