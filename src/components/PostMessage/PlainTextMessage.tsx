import React, {useEffect, useState} from 'react';
import {LayoutChangeEvent, Pressable, View} from 'react-native';

import styles from './styles';
import StyledText from '@/src/components/UI/StyledText';

interface Props {
  message: string;
}

const MAX_LINES = 3;

enum STATUS {
  INIT = 'INIT',
  TRIM = 'TRIM',
  FULL = 'FULL',
}

type StateType = {
  status: STATUS;
  fullTextHeight: number;
  trimmedTextHeight: number;
};
const PlainTextMessage: React.FC<Props> = ({message}) => {
  const [state, setState] = useState<StateType>({
    status: STATUS.INIT,
    fullTextHeight: 0,
    trimmedTextHeight: 0,
  });

  useEffect(() => {
    if (
      state.status === STATUS.INIT &&
      state.fullTextHeight &&
      state.trimmedTextHeight
    ) {
      const status =
        state.trimmedTextHeight < state.fullTextHeight
          ? STATUS.TRIM
          : STATUS.FULL;
      setState({
        ...state,
        status,
      });
    }
  }, [state]);

  const handleReadMore = () => {
    setState({
      ...state,
      status: STATUS.FULL,
    });
  };

  const onLayoutTrimmedText = (event: LayoutChangeEvent) => {
    setState({
      ...state,
      trimmedTextHeight: event.nativeEvent.layout.height,
    });
  };

  const onLayoutFullText = (event: LayoutChangeEvent) => {
    setState({
      ...state,
      fullTextHeight: event.nativeEvent.layout.height,
    });
  };

  return (
    <View>
      <View
        style={
          [STATUS.INIT, STATUS.TRIM].includes(state.status)
            ? styles.content
            : styles.hide
        }
        onLayout={onLayoutTrimmedText}>
        <StyledText style={styles.text} numberOfLines={MAX_LINES}>
          {message}
        </StyledText>
        <Pressable
          style={styles.textContainerContinue}
          onPress={handleReadMore}>
          <StyledText style={styles.text}>{' Continue reading '}</StyledText>
        </Pressable>
      </View>
      <View
        style={
          [STATUS.INIT, STATUS.FULL].includes(state.status)
            ? styles.content
            : styles.hide
        }
        onLayout={onLayoutFullText}>
        <StyledText style={styles.text}>{message}</StyledText>
      </View>
    </View>
  );
};

export default PlainTextMessage;
