import React, {useCallback, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useQueryClient} from '@tanstack/react-query';
import {Spinner, YStack} from 'tamagui';
import {ListRenderItem} from '@shopify/flash-list';
import {FlashList} from 'react-native-actions-sheet/dist/src/views/FlashList';
import {SearchUserListDto} from '@gojoe/typescript-sdk';

import useUserSearch from '@/src/hooks/api/useUserSearch';
import useDebounce from '@/src/hooks/useDebounce';
import {useSession} from '@/src/contexts/SessionContext';
import UserItem from './UserItem';
import StyledText from '../UI/StyledText';
import {StyledInput} from '../UI/StyledInput';

const ItemSeparatorComponent = () => <YStack height={8} />;

const FindFriends = () => {
  const {t} = useTranslation();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm || '', 500);
  const {user} = useSession();
  const {
    data,
    isLoading,
    isRefetching,
    refetch,
    fetchNextPage,
    isFetchingNextPage,
  } = useUserSearch(debouncedSearchTerm);

  const updated = useCallback(
    (userId: string) => {
      return Promise.all([
        queryClient.invalidateQueries({
          queryKey: ['SEARCH_USER', debouncedSearchTerm],
        }),
        queryClient.invalidateQueries({
          queryKey: ['user', userId],
        }),
      ]);
    },
    [queryClient, debouncedSearchTerm],
  );

  const handleOnSearch = (value: string) => {
    setSearchTerm(value);
  };

  const keyExtractor = useCallback((item: SearchUserListDto) => item.id, []);

  const renderItem: ListRenderItem<any> = useCallback(
    ({item}) => (
      <UserItem currentUserId={user?.id ?? ''} user={item} updated={updated} />
    ),
    [user?.id, updated],
  );

  const onEndReached = () => fetchNextPage();

  const listFooterComponent = useCallback(() => {
    if (isFetchingNextPage) {
      return <Spinner marginVertical='$3' />;
    }
    return <YStack height={48} />;
  }, [isFetchingNextPage]);

  const listEmptyComponent = useCallback(() => {
    if (isLoading) {
      return <Spinner marginTop='$5' />;
    }
    return (
      <StyledText
        fontSize={14}
        fontWeight={500}
        textAlign='center'
        marginTop='$5'>
        No users found
      </StyledText>
    );
  }, [isLoading]);

  return (
    <YStack flex={1} padding='$5'>
      <StyledInput
        placeholder={t('Search...')}
        value={searchTerm}
        onChangeText={handleOnSearch}
        style={{marginVertical: 16}}
      />
      <FlashList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        estimatedItemSize={56}
        refreshing={isRefetching}
        onRefresh={refetch}
        onEndReached={onEndReached}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={ItemSeparatorComponent}
        ListFooterComponent={listFooterComponent}
        ListEmptyComponent={listEmptyComponent}
      />
    </YStack>
  );
};

export default FindFriends;
