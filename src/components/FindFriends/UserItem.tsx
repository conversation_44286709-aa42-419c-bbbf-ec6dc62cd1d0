import React, {useCallback} from 'react';
import {useRouter} from 'expo-router';
import {XStack} from 'tamagui';

import {triggerHaptics} from '@/src/utils/haptics';
import {checkIfAccountIsPrivate} from '@/src/utils/users';
import FollowerButton from '../FollowerButton';
import StyledText from '../UI/StyledText';
import UserAvatar from '../UI/Avatar/UserAvatar';
import {SheetManager} from 'react-native-actions-sheet';

interface Props {
  currentUserId: string;
  user: any;
  updated?: (userId: string) => void;
}

const UserItem: React.FC<Props> = ({currentUserId, user, updated}) => {
  const router = useRouter();
  const followerButton = () => {
    const isPrivate = checkIfAccountIsPrivate({
      currentUserId: currentUserId,
      privacy: user?.privacy || false,
      isFollowed: user.iFollow || false,
      userFollowId: user.id,
    });

    return (
      <FollowerButton
        isPrivate={isPrivate}
        userId={user.id}
        isFollowed={user.iFollow}
        updated={updated}
      />
    );
  };

  const goToProfilePage = useCallback(
    async (userId: string) => {
      await triggerHaptics();
      await SheetManager.hide('find_friends');
      router.push({
        pathname: '/user/[userId]',
        params: {
          userId: userId,
        },
      });
    },
    [router],
  );

  return (
    <XStack alignItems='center' marginBottom={8}>
      <XStack
        flex={1}
        alignItems='center'
        onPress={() => goToProfilePage(user.id)}>
        <UserAvatar user={user} size={48} circular />
        <StyledText fontWeight={700} fontSize={14} flex={1} marginLeft={8}>
          {user.name}
        </StyledText>
      </XStack>
      {followerButton()}
    </XStack>
  );
};

export default UserItem;
