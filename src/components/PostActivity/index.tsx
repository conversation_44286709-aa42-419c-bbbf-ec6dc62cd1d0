import React from 'react';
import convert from 'convert';
import {useRouter} from 'expo-router';
import {XStack, YStack} from 'tamagui';
import {ActivityListDto} from '@gojoe/typescript-sdk';

import {DistanceUnit} from '@/src/contexts/LocaleContext';
import {triggerHaptics} from '@/src/utils/haptics';
import ActivityIcon from '@/src/components/ActivityIcon';
import I18nText from '@/src/components/I18nText';
import {formatDuration} from './helper';
import StyledText from '../UI/StyledText';
import {PointsCircle} from './PointsCircle';

interface Props {
  postId: string;
  activity: ActivityListDto;
  unit: DistanceUnit;
}

const PostActivity: React.FC<Props> = ({postId, activity, unit}) => {
  const router = useRouter();

  const time = formatDuration(activity.time ?? 0);

  const distance = activity.distance
    ? `${convert(activity.distance, 'm').to(unit).toFixed(2)} ${unit}`
    : null;

  const handlePressActivity = async () => {
    await triggerHaptics();
    router.push({
      pathname: '/post/[postId]',
      params: {
        postId: postId,
        activityId: activity.id,
      },
    });
  };

  return (
    <XStack
      justifyContent='space-between'
      alignItems='center'
      marginTop='$3.5'
      onPress={handlePressActivity}>
      <XStack alignItems='center' gap='$3.5'>
        <XStack
          alignItems='center'
          justifyContent='center'
          backgroundColor='$windowBackground'
          height='$3.5'
          width='$3.5'
          borderRadius={50}>
          <ActivityIcon
            name={activity.activityType.name}
            color='$primary'
            size={20}
          />
        </XStack>

        <YStack>
          <I18nText fontSize={12} color='$grey1'>
            Time
          </I18nText>
          <StyledText fontSize={14} fontWeight={700} color='$color'>
            {time}
          </StyledText>
        </YStack>

        {distance && (
          <YStack>
            <I18nText fontSize={12} color='$grey1'>
              Distance
            </I18nText>
            <StyledText fontSize={14} fontWeight={700} color='$color'>
              {distance}
            </StyledText>
          </YStack>
        )}
      </XStack>

      <PointsCircle points={activity.points ?? 0} />
    </XStack>
  );
};

export default PostActivity;
