import React from 'react';
import {XStack, YStack} from 'tamagui';
import Svg, {Path} from 'react-native-svg';

import StyledText from '../UI/StyledText';
import I18nText from '../I18nText';

const colorIndex = ['#7ED957', '#C9E265', '#FFDE59', '#FF914D', '#C43F2D'];
const colorBasic = '#E0E0E0';

const getIntensity = (points = 0): number => {
  if (points <= 5) return 0;
  if (points <= 10) return 1;
  if (points <= 20) return 2;
  if (points <= 40) return 3;
  return 4;
};

const polarToCartesian = (
  centerX: number,
  centerY: number,
  radius: number,
  angleInDegrees: number,
) => {
  const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180.0;
  return {
    x: centerX + radius * Math.cos(angleInRadians),
    y: centerY + radius * Math.sin(angleInRadians),
  };
};

const describeArc = (
  x: number,
  y: number,
  radius: number,
  startAngle: number,
  endAngle: number,
) => {
  const start = polarToCartesian(x, y, radius, endAngle);
  const end = polarToCartesian(x, y, radius, startAngle);
  const largeArcFlag = endAngle - startAngle <= 180 ? '0' : '1';

  return [
    'M',
    start.x,
    start.y,
    'A',
    radius,
    radius,
    0,
    largeArcFlag,
    0,
    end.x,
    end.y,
  ].join(' ');
};

interface Props {
  points: number;
}

export const PointsCircle: React.FC<Props> = ({points}) => {
  const intensity = getIntensity(points);

  const parts = 5;
  const gapDegrees = 20; // 🌟 Make gaps bigger: 8 degrees real empty space between arcs
  const totalArcDegrees = 360 - parts * gapDegrees;
  const arcDegrees = totalArcDegrees / parts;

  const center = 35;
  const radius = 30;
  const strokeWidth = 8;

  return (
    <XStack alignItems='center'>
      <Svg height='36' width='36' viewBox='0 0 70 70'>
        {Array.from({length: parts}).map((_, i) => {
          const startAngle = i * (arcDegrees + gapDegrees);
          const endAngle = startAngle + arcDegrees;
          const color = i <= intensity ? colorIndex[i] : colorBasic;

          return (
            <Path
              key={i}
              d={describeArc(center, center, radius, startAngle, endAngle)}
              stroke={color}
              strokeWidth={strokeWidth}
              fill='none'
              strokeLinecap='round'
            />
          );
        })}
      </Svg>
      <YStack paddingLeft='$2'>
        <I18nText fontSize={12} color='#656874'>
          Points
        </I18nText>
        <StyledText
          fontSize={14}
          fontWeight={700}
          color={points ? colorIndex[intensity] : '#000'}>
          {points}
        </StyledText>
      </YStack>
    </XStack>
  );
};
