import React, {useEffect, useState} from 'react';
import {ActivityIndicator, Dimensions} from 'react-native';
import {Image} from 'expo-image';
import {XStack, YStack} from 'tamagui';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import {
  SurveyQuestionDto,
  SurveyQuestionOptionsDto,
} from '@gojoe/typescript-sdk';
import StyledText from '@/src/components/UI/StyledText';
import useMutationAddQuestionAnswer from '@/src/hooks/api/useMutationAddQuestionAnswer.ts';
import I18nText from '@/src/components/I18nText';

interface Props {
  question: SurveyQuestionDto;
  options: SurveyQuestionOptionsDto[];
  buttonTitle: string;
  handleNext: () => void;
  handleBack: () => void;
  hasBackButton: boolean;
}

const windowWidth = Dimensions.get('window').width;

const QuestionScale: React.FC<Props> = ({
  question,
  options,
  buttonTitle,
  handleNext,
  handleBack,
  hasBackButton,
}) => {
  const [value, setValue] = useState(0);
  const [isMove, setIsMove] = useState(false);
  const {addAnswer, isPending} = useMutationAddQuestionAnswer();

  useEffect(() => {
    setValue(Math.floor(options.length / 2));
  }, [options.length]);

  if (!options || options.length === 0) {
    return null;
  }

  const isFirst = value === 0;
  const isLast = value === options.length - 1;

  const onValuesChange = (values: number[]) => {
    setValue(values[0]);
    setIsMove(true);
  };

  const handleOnPress = async () => {
    await addAnswer({
      questionId: question.id,
      optionId: options[value].id,
    });

    handleNext();
  };

  const customLabel = () => {
    const labelName = options[value].name;
    const contentWidth = windowWidth - 24 * 4;
    const stepWidth = contentWidth / (options.length - 1);
    let position = stepWidth * value - 50;

    if (isFirst) {
      position = -15;
    } else if (isLast) {
      position = position - 35;
    }

    return (
      <CustomLabel
        value={labelName}
        position={position}
        isFirst={isFirst}
        isLast={isLast}
      />
    );
  };

  return (
    <YStack flex={1}>
      <YStack paddingTop='$11'>
        <StyledText
          fontSize={24}
          fontWeight={700}
          color='#2F3542'
          textAlign='center'
          marginBottom='$10'>
          {question.question}
        </StyledText>

        <YStack alignItems='center' justifyContent='center'>
          <XStack width='100%' justifyContent='space-between' height={50}>
            {!isMove && (
              <YStack>
                {options[0].name.split(' ').map((word, index) => (
                  <StyledText
                    key={`left-${index}`}
                    fontSize={12}
                    fontWeight={700}
                    textTransform='uppercase'
                    color='#7A7E86'
                    width={100}
                    textAlign='left'>
                    {word}
                  </StyledText>
                ))}
              </YStack>
            )}
            {!isMove && (
              <YStack>
                {options[options.length - 1].name
                  .split(' ')
                  .map((word, index) => (
                    <StyledText
                      key={`left-${index}`}
                      fontSize={12}
                      fontWeight={700}
                      textTransform='uppercase'
                      color='#7A7E86'
                      width={100}
                      textAlign='right'>
                      {word}
                    </StyledText>
                  ))}
              </YStack>
            )}
          </XStack>
          <XStack>
            <MultiSlider
              trackStyle={{
                height: 15,
                borderWidth: 1,
                borderColor: '#64687580',
                borderRadius: 8,
                backgroundColor: '#FFF',
              }}
              selectedStyle={{
                height: 15,
                borderWidth: 1,
                borderColor: '#64687580',
                borderRadius: 8,
                backgroundColor: '#FFF',
              }}
              values={[value]}
              min={0}
              max={options.length - 1}
              step={1}
              sliderLength={windowWidth - 24 * 4}
              snapped
              allowOverlap={true}
              onValuesChange={onValuesChange}
              enableLabel
              customMarker={customMarker}
              customLabel={customLabel}
            />
            {!isMove && (
              <Image
                source={require('@/assets/images/drag.png')}
                style={{
                  width: 104,
                  height: 67,
                  position: 'absolute',
                  left: (windowWidth - 24 * 4) / 2 - 80,
                  top: 50,
                }}
              />
            )}
          </XStack>
        </YStack>
      </YStack>

      <YStack gap='$3.5' marginTop='auto'>
        <XStack gap='$3.5'>
          {hasBackButton && (
            <XStack
              flex={1}
              backgroundColor='#FFFFFF'
              borderWidth={1}
              borderColor='#ACAEB3'
              borderRadius='$3'
              justifyContent='center'
              alignItems='center'
              height={44}
              padding={8}
              onPress={handleBack}>
              <I18nText fontWeight={500} color='#ACAEB3'>
                Back
              </I18nText>
            </XStack>
          )}

          <XStack
            disabled={isPending || !isMove}
            flex={1}
            backgroundColor={!isMove ? '#EAEBEC' : '#C43F2D'}
            borderRadius='$3'
            justifyContent='center'
            alignItems='center'
            height={44}
            padding={8}
            onPress={handleOnPress}>
            {isPending ? (
              <ActivityIndicator color='#FFF' />
            ) : (
              <I18nText fontWeight={500} color='#FFF'>
                {buttonTitle}
              </I18nText>
            )}
          </XStack>
        </XStack>

        {question.isOptional && (
          <XStack
            width='100%'
            backgroundColor='#FFF'
            alignItems='center'
            justifyContent='center'
            height={44}
            padding={8}
            borderWidth={1}
            borderColor='#ACAEB3'
            borderRadius={8}
            onPress={handleNext}>
            <I18nText fontSize={16} fontWeight={500} color='#ACAEB3'>
              Skip
            </I18nText>
          </XStack>
        )}
      </YStack>
    </YStack>
  );
};

type CustomLabelType = {
  value: string;
  position: number;
  isLast: boolean;
  isFirst: boolean;
};

const CustomLabel: React.FC<CustomLabelType> = ({
  value,
  position,
  isFirst,
  isLast,
}) => {
  return (
    <XStack position='relative' bottom={20}>
      <YStack position='absolute' bottom={0} left={position}>
        {value.split(' ').map((word, index) => (
          <StyledText
            key={`left-${index}`}
            fontSize={12}
            fontWeight={700}
            textTransform='uppercase'
            color='#7A7E86'
            width={100}
            textAlign={isLast ? 'right' : isFirst ? 'left' : 'center'}>
            {word}
          </StyledText>
        ))}
      </YStack>
    </XStack>
  );
};

const customMarker = () => {
  return (
    <XStack
      width={28}
      height={44}
      borderRadius={4}
      backgroundColor='#BF3F2E'
      alignItems='center'
      justifyContent='center'
      marginTop={12}>
      <StyledText color='#E0E1E3' textAlign='center'>
        ||
      </StyledText>
    </XStack>
  );
};

export default QuestionScale;
