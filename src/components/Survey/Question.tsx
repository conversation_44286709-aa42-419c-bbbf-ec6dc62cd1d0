import React from 'react';
import {
  SurveyQuestionDto,
  SurveyQuestionDtoTypeEnum,
} from '@gojoe/typescript-sdk';

import useSurveyQuestionOptions from '@/src/hooks/api/useSurveyQuestionOptions';
import QuestionSingle from './QuestionSingle';
import QuestionScale from './QuestionScale';

interface Props {
  question: SurveyQuestionDto;
  buttonTitle: string;
  handleNext: () => Promise<void>;
  handleBack: () => void;
  hasBackButton: boolean;
}

const Question: React.FC<Props> = ({
  question,
  buttonTitle,
  handleNext,
  handleBack,
  hasBackButton,
}) => {
  const {options} = useSurveyQuestionOptions(question.id);

  switch (question.type) {
    case SurveyQuestionDtoTypeEnum.Single:
      return (
        <QuestionSingle
          question={question}
          options={options}
          buttonTitle={buttonTitle}
          handleNext={handleNext}
          handleBack={handleBack}
          hasBackButton={hasBackButton}
        />
      );

    case SurveyQuestionDtoTypeEnum.Scale:
      return (
        <QuestionScale
          question={question}
          options={options}
          buttonTitle={buttonTitle}
          handleNext={handleNext}
          handleBack={handleBack}
          hasBackButton={hasBackButton}
        />
      );
  }
};

export default Question;
