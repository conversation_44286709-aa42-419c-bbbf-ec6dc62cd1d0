import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, YStack} from 'tamagui';

import useSurvey from '@/src/hooks/api/useSurvey';
import {
  addCompletedSurvey,
  addSkipSurvey,
  getPercentageAnswered,
} from '@/src/utils/surveys';
import IntroductionSlide from './IntroductionSlide';
import CompletionSlide from './CompletionSlide';
import Question from './Question';
import Footer from './Footer';
import {SheetManager} from 'react-native-actions-sheet';

interface Props {
  surveyId: string;
  userId: string;
  onComplete: () => void;
}

const Survey: React.FC<Props> = ({surveyId, userId, onComplete}) => {
  const {data, isLoading} = useSurvey(surveyId);
  const [step, setStep] = useState(0);

  useEffect(() => {
    if (!data) {
      return;
    }
    const lastQuestionAnswered = getPercentageAnswered(userId, data);

    if (lastQuestionAnswered) {
      const nextStep = lastQuestionAnswered;
      if (nextStep < data.questions.length) {
        setStep(nextStep);
        return;
      }
    }

    if (data.startTitle) {
      setStep(-1);
    }
  }, [data, userId]);

  if (isLoading || !data) {
    return <Spinner size='large' />;
  }

  const handleNext = async () => {
    let nextStep = step + 1;

    if (step === -1) {
      setStep(nextStep);
    } else if (
      nextStep < data.questions.length ||
      (nextStep === data.questions.length && data.endTitle)
    ) {
      setStep(nextStep);
    } else {
      addSkipSurvey(surveyId);
      addCompletedSurvey(surveyId);
      onComplete();
      await SheetManager.hide('survey');
    }
  };

  const handleBack = () => {
    if (step > 0) {
      setStep(step - 1);
    }
  };

  const handleSkipSurvey = async () => {
    addSkipSurvey(surveyId);
    onComplete();
    await SheetManager.hide('survey');
  };

  const buttonTitle =
    step < data.questions.length - 1 ? 'Continue' : 'Complete survey';

  const hasBackButton = step > 0;

  if (!data) {
    return null;
  }

  return (
    <YStack flex={1} paddingTop='$6' paddingHorizontal='$5'>
      <YStack
        flex={1}
        paddingVertical='$5'
        backgroundColor='$background'
        borderTopRightRadius={24}
        borderTopLeftRadius={24}
        borderBottomLeftRadius={24}
        borderBottomRightRadius={data?.brandedUrl ? 0 : 24}>
        {step < 0 && (
          <IntroductionSlide
            survey={data}
            onStartSurvey={handleNext}
            onSkipSurvey={handleSkipSurvey}
          />
        )}

        {step >= 0 && step < data.questions.length && (
          <YStack
            flex={1}
            alignItems='center'
            paddingHorizontal={24}
            marginBottom={8}>
            {data.questions.map((question, index) => {
              if (index !== step) {
                return null;
              }

              return (
                <Question
                  key={question.id}
                  question={question}
                  buttonTitle={buttonTitle}
                  handleNext={handleNext}
                  handleBack={handleBack}
                  hasBackButton={hasBackButton}
                />
              );
            })}
          </YStack>
        )}

        {step === data.questions.length && (
          <CompletionSlide
            survey={data}
            buttonTitle={buttonTitle}
            onFinishSurvey={handleNext}
          />
        )}
      </YStack>
      <Footer step={step} survey={data} />
    </YStack>
  );
};

export default Survey;
