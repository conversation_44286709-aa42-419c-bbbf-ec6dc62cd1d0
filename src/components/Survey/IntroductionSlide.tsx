import React from 'react';
import {Dimensions} from 'react-native';
import {XStack, YStack} from 'tamagui';
import {Image} from 'expo-image';
import {SurveyDto} from '@gojoe/typescript-sdk';

import StyledText from '@/src/components/UI/StyledText';
import I18nText from '@/src/components/I18nText';

interface Props {
  survey: SurveyDto;
  onStartSurvey: () => Promise<void>;
  onSkipSurvey?: () => Promise<void>;
}

const IntroductionSlide: React.FC<Props> = ({
  survey,
  onStartSurvey,
  onSkipSurvey,
}) => {
  const layout = Dimensions.get('window');
  const coverWidth = layout.width - 48 - 80;
  const coverHeight = coverWidth * 1.21;

  return (
    <YStack flex={1} alignItems='center'>
      <Image
        source={{uri: survey?.cover}}
        style={{height: coverHeight, width: coverWidth, borderRadius: 24}}
      />
      <StyledText
        fontSize={24}
        fontWeight={700}
        lineHeight={24}
        color='$color'
        textAlign='center'
        marginTop='$3.5'
        paddingHorizontal={layout.height < 700 ? 20 : 40}>
        {survey?.startTitle}
      </StyledText>
      <StyledText
        fontSize={16}
        fontWeight={500}
        lineHeight={19.6}
        color='#2F3542'
        textAlign='center'
        marginTop='$2'
        paddingHorizontal={layout.height < 700 ? 20 : 40}>
        {survey?.startDescription}
      </StyledText>

      <YStack mt='auto'>
        <XStack
          width={layout.width - 96}
          marginTop='$5'
          backgroundColor='#C43F2D'
          alignItems='center'
          justifyContent='center'
          height={44}
          padding={8}
          borderRadius={8}
          onPress={onStartSurvey}>
          <I18nText fontSize={16} fontWeight={500} color='#FFF'>
            Start survey
          </I18nText>
        </XStack>

        {survey.isOptional && (
          <XStack
            width={layout.width - 96}
            marginTop='$3'
            backgroundColor='#FFF'
            alignItems='center'
            justifyContent='center'
            height={44}
            padding={8}
            borderWidth={1}
            borderColor='#ACAEB3'
            borderRadius={8}
            onPress={onSkipSurvey}>
            <I18nText fontSize={16} fontWeight={500} color='#ACAEB3'>
              Skip survey
            </I18nText>
          </XStack>
        )}
      </YStack>
    </YStack>
  );
};

export default IntroductionSlide;
