import React, {useState} from 'react';
import {ActivityIndicator} from 'react-native';
import {useTranslation} from 'react-i18next';
import {Input, XStack, YStack} from 'tamagui';
import {
  SurveyQuestionDto,
  SurveyQuestionOptionsDto,
} from '@gojoe/typescript-sdk';

import useMutationAddQuestionAnswer from '@/src/hooks/api/useMutationAddQuestionAnswer.ts';
import StyledText from '@/src/components/UI/StyledText';
import I18nText from '@/src/components/I18nText';

interface Props {
  question: SurveyQuestionDto;
  options: SurveyQuestionOptionsDto[];
  buttonTitle: string;
  handleNext: () => void;
  handleBack: () => void;
  hasBackButton: boolean;
}

const QuestionSingle: React.FC<Props> = ({
  question,
  options,
  buttonTitle,
  handleNext,
  handleBack,
  hasBackButton,
}) => {
  const {t} = useTranslation();
  const [value, setValue] = useState('');
  const [text, setText] = useState('');
  const {addAnswer, isPending} = useMutationAddQuestionAnswer();

  const isValid = value || text;

  const onChangeText = (newText: string) => {
    setText(newText);
  };

  const handleOnPress = async () => {
    if (!isValid) {
      return;
    }

    await addAnswer({
      questionId: question.id,
      optionId: value ? value : undefined,
      answerText: text ? text : undefined,
    });

    handleNext();
  };

  return (
    <YStack flex={1}>
      <YStack paddingTop='$11'>
        <StyledText
          fontSize={24}
          fontWeight={700}
          color='#2F3542'
          textAlign='center'>
          {question.question}
        </StyledText>

        <XStack
          alignItems='center'
          justifyContent='center'
          paddingHorizontal={20}
          flexWrap='wrap'
          gap='$2'
          marginTop='$10'>
          {options.map((option) => {
            const handleOnPressItem = () => {
              setValue(option.id);
            };

            const isSelected = value === option.id;

            return (
              <XStack
                key={option.id}
                height={38}
                borderWidth={1}
                borderColor='#E8E8EA'
                justifyContent='center'
                alignItems='center'
                paddingHorizontal={16}
                borderRadius={19}
                backgroundColor={isSelected ? '#00CF8F' : '#FFF'}
                onPress={handleOnPressItem}>
                <StyledText
                  fontSize={14}
                  fontWeight={600}
                  color={isSelected ? '#FFF' : '#000'}>
                  {option.name}
                </StyledText>
              </XStack>
            );
          })}
        </XStack>
      </YStack>

      <YStack gap='$3.5' marginTop='auto'>
        {question.isOpenEnded && (
          <Input
            value={text}
            onChangeText={onChangeText}
            placeholder={t(
              options.length === 0 ? 'Please fill in your answer' : 'Other',
            )}
            multiline={true}
            verticalAlign='top'
            backgroundColor='$background'
            borderWidth={1}
            borderColor='$grey3'
            borderRadius={8}
            padding={8}
            height={100}
            marginTop={60}
          />
        )}
        <XStack gap='$3.5'>
          {hasBackButton && (
            <XStack
              flex={1}
              backgroundColor='#FFFFFF'
              borderWidth={1}
              borderColor='#ACAEB3'
              borderRadius='$3'
              justifyContent='center'
              alignItems='center'
              height={44}
              padding={8}
              onPress={handleBack}>
              <I18nText fontWeight={500} color='#ACAEB3'>
                Back
              </I18nText>
            </XStack>
          )}

          <XStack
            disabled={isPending || !isValid}
            flex={1}
            backgroundColor={isPending || !isValid ? '#EAEBEC' : '#C43F2D'}
            borderRadius='$3'
            justifyContent='center'
            alignItems='center'
            height={44}
            padding={8}
            onPress={handleOnPress}>
            {isPending ? (
              <ActivityIndicator color='#FFF' />
            ) : (
              <I18nText fontWeight={500} color='#FFF'>
                {buttonTitle}
              </I18nText>
            )}
          </XStack>
        </XStack>

        {question.isOptional && (
          <XStack
            width='100%'
            backgroundColor='#FFF'
            alignItems='center'
            justifyContent='center'
            height={44}
            padding={8}
            borderWidth={1}
            borderColor='#ACAEB3'
            borderRadius={8}
            onPress={handleNext}>
            <I18nText fontSize={16} fontWeight={500} color='#ACAEB3'>
              Skip
            </I18nText>
          </XStack>
        )}
      </YStack>
    </YStack>
  );
};

export default QuestionSingle;
