import React, {memo} from 'react';
import {FeaturedChallengeListItemDto} from '@gojoe/typescript-sdk';
import {secondsToDays} from '@/src/utils/challenge';
import I18nText from '@/src/components/I18nText';

interface Props {
  secondsToStart?: number;
  secondsRemaining?: number;
}
const TimeStatus: React.FC<Props> = ({secondsToStart, secondsRemaining}) => {
  if (secondsToStart) {
    const daysToStart = secondsToDays(secondsToStart);
    if (daysToStart) {
      return (
        <I18nText
          color='$color'
          fontSize={12}
          fontWeight='700'
          marginTop={8}
          i18nParams={{count: daysToStart}}>
          {`Starts in {{count}} day`}
        </I18nText>
      );
    }
    return (
      <I18nText fontSize={12} fontWeight='700' marginTop={8}>
        {secondsToStart}
      </I18nText>
    );
  } else if (secondsRemaining) {
    const daysRemaining = secondsToDays(secondsRemaining);
    if (daysRemaining) {
      return (
        <I18nText
          color='$color'
          fontSize={12}
          fontWeight='700'
          marginTop={8}
          i18nParams={{count: daysRemaining}}>
          {`{{count}} days left`}
        </I18nText>
      );
    }
    return (
      <I18nText fontSize={12} fontWeight='700' marginTop={8}>
        Today
      </I18nText>
    );
  }

  return null;
};

export default memo(TimeStatus);
