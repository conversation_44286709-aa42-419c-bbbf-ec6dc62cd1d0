import React, {memo, useCallback} from 'react';
import {StyleSheet} from 'react-native';
import {FeaturedChallengeListItemDto} from '@gojoe/typescript-sdk';
import {XStack, YStack} from 'tamagui';
import StyledText from '@/src/components/UI/StyledText';
import {ImageBackground} from 'expo-image';
import BusinessAvatar from '@/src/components/UI/Avatar/BusinessAvatar';
import I18nText from '@/src/components/I18nText';
import {Flame, Shirt, Users2} from '@tamagui/lucide-icons';
import {challengeDate} from '@/src/utils/challenge';
import {useLocales} from '@/src/contexts/LocaleContext';

import TimeStatus from './TimeStatus';
import {triggerHaptics} from '@/src/utils/haptics';
import {useRouter} from 'expo-router';

interface Props {
  challenge: FeaturedChallengeListItemDto;
}
const FeaturedChallengeList: React.FC<Props> = ({challenge}) => {
  const {locales} = useLocales();
  const router = useRouter();
  const cover = challenge.cover
    ? {uri: challenge.cover}
    : require('@/assets/images/challenge_cover.jpg');

  const handleOnPress = useCallback(async () => {
    await triggerHaptics();
    router.push({
      pathname: '/challenge/[challengeId]',
      params: {
        challengeId: challenge.id,
      },
    });
  }, [challenge.id, router]);

  const handleTeamStandingsPress = useCallback(async () => {
    await triggerHaptics();
    router.push({
      pathname: '/challenge/[challengeId]/team',
      params: {
        challengeId: challenge.id,
        tab: 'Team',
      },
    });
  }, [challenge.id, router]);

  const handleSoloStandingsPress = useCallback(async () => {
    await triggerHaptics();
    router.push({
      pathname: '/challenge/[challengeId]/solo',
      params: {
        challengeId: challenge.id,
      },
    });
  }, [challenge.id, router]);

  const handleJoinChallengePress = useCallback(async () => {
    await triggerHaptics();

    // Navigate to the challenge screen with Team tab
    router.push({
      pathname: '/challenge/[challengeId]',
      params: {
        challengeId: challenge.id,
      },
    });
  }, [challenge.id, router]);

  return (
    <YStack
      backgroundColor='$background'
      borderRadius={16}
      overflow='hidden'
      onPress={handleOnPress}>
      <ImageBackground source={cover} style={styles.cover}>
        {challenge.business && (
          <XStack
            height={24}
            gap={4}
            backgroundColor='$white1'
            borderRadius={12}
            alignItems='center'
            paddingHorizontal={8}
            alignSelf='flex-start'>
            <BusinessAvatar
              business={challenge.business}
              size={16}
              borderRadius={8}
            />
            <StyledText fontSize={12} fontWeight='500' color='black'>
              {challenge.business.name}
            </StyledText>
          </XStack>
        )}
      </ImageBackground>
      <YStack padding='$3.5'>
        <StyledText fontSize={18} fontWeight='700' numberOfLines={1}>
          {challenge.name}
        </StyledText>
        <StyledText fontSize={12} fontWeight='700' marginTop={8}>
          {challengeDate(
            challenge.startDate,
            challenge.endDate,
            locales.timezone,
          )}
        </StyledText>
        <XStack marginVertical={16}>
          <XStack flex={1} gap={8} alignItems='center'>
            <Shirt color='$grey2' size={20} />
            <I18nText
              fontSize={12}
              fontWeight='500'
              color='$grey2'
              i18nParams={{
                count: challenge.countTeams,
              }}>{`{{count}} Team`}</I18nText>
          </XStack>
          <XStack flex={1} gap={8} alignItems='center'>
            <Users2 color='$grey2' size={20} />
            <I18nText
              fontSize={12}
              fontWeight='500'
              color='$grey2'
              i18nParams={{
                count: challenge.countUsers,
              }}>{`{{count}} People`}</I18nText>
          </XStack>
          <XStack flex={1} gap={8} alignItems='center'>
            <Flame color='$grey2' size={20} />
            <I18nText
              fontSize={12}
              fontWeight='500'
              color='$grey2'
              i18nParams={{
                count: challenge.countActivityTypes,
              }}>{`{{count}} Activity`}</I18nText>
          </XStack>
        </XStack>
        <TimeStatus
          secondsToStart={challenge.secondsToStart}
          secondsRemaining={challenge.secondsRemaining}
        />
      </YStack>
      {challenge.inChallenge ? (
        <XStack height={48} backgroundColor='$grey3' paddingTop={1} gap={1}>
          <XStack
            backgroundColor='$background'
            flex={1}
            gap={8}
            paddingHorizontal={16}
            alignItems='center'
            onPress={handleTeamStandingsPress}>
            <I18nText color='$primary' fontSize={12} fontWeight='600'>
              Team Standings
            </I18nText>
            <StyledText color='$primary' fontWeight='600'>{`-->`}</StyledText>
          </XStack>
          <XStack
            backgroundColor='$background'
            flex={1}
            gap={8}
            paddingHorizontal={16}
            alignItems='center'
            onPress={handleSoloStandingsPress}>
            <I18nText color='$primary' fontSize={12} fontWeight='600'>
              Solo Standings
            </I18nText>
            <StyledText color='$primary' fontWeight='600'>{`-->`}</StyledText>
          </XStack>
        </XStack>
      ) : (
        <YStack
          height={48}
          justifyContent='center'
          alignItems='center'
          backgroundColor='$primary'
          onPress={handleJoinChallengePress}>
          <I18nText color='$white1' fontWeight='700'>
            Join Challenge
          </I18nText>
        </YStack>
      )}
    </YStack>
  );
};

const styles = StyleSheet.create({
  cover: {
    width: '100%',
    height: 220,
    padding: 16,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
});

export default memo(FeaturedChallengeList);
