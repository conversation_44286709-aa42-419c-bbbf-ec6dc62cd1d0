import React from 'react';
import {LineChart as GiftedLineChart} from 'react-native-gifted-charts';

import {BusinessTrendsOverviewKey} from '@/src/hooks/api/useBusinessTrends';
import {ScrollView, XStack, YStack} from 'tamagui';
import StyledText from '../UI/StyledText';
import I18nText from '../I18nText';
import {humaniseNumber} from '@/src/utils/numbers';
import {triggerHaptics} from '@/src/utils/haptics';
import useBusinessActivityStats from '@/src/hooks/api/useBusinessActivityStats';
import logger from '@/src/utils/logger';
import { formatLabel } from '@/src/utils/date';

const chartHeight = 120;

const timeFrameValues = ['Day', 'Week', 'Month'];

export default function LineChart({
  businessId,
  color = '#C43F2D',
}: {
  businessId: string;
  color?: string;
}) {
  const [timeFrame, setTimeFrame] = React.useState<'day' | 'week' | 'month'>(
    'day',
  );
  const [metric, setMetric] = React.useState<
    'points' | 'time' | 'distance' | 'calories'
  >('points');
  const {data} = useBusinessActivityStats(businessId, timeFrame);
  logger.debug('BusinessTrends data:', data);
  const {summary, trend} = data;
  if (data.length === 0) {
    return null;
  }

  const chartData = trend.map((item) => ({
    value: item[metric],
    label: formatLabel(item.label, timeFrame),
    labelTextStyle: {color: '#999', fontSize: 10},
  }));
  const spacing = 35;
  const chartWidth = spacing * data.length;

  return (
    <YStack
      backgroundColor='$background'
      flex={1}
      borderWidth={1}
      borderColor='$grey3'
      borderRadius={16}>
      <XStack
        height={36}
        borderRadius={8}
        paddingHorizontal={4}
        backgroundColor='$grey3'
        marginTop='$3.5'
        marginHorizontal='$3.5'
        alignItems='center'
        justifyContent='space-between'>
        {timeFrameValues.map((item) => {
          const key = item.toLocaleLowerCase() as 'day' | 'week' | 'month';
          const handlePressTimeFrame = async () => {
            await triggerHaptics();
            setTimeFrame(key);
          };
          return (
            <YStack
              height={28}
              key={item}
              flex={1}
              justifyContent='center'
              alignItems='center'
              onPress={handlePressTimeFrame}
              backgroundColor={timeFrame === key ? '$black1' : undefined}
              borderRadius={4}>
              <I18nText
                color={timeFrame === key ? '$white1' : '$grey1'}
                fontSize={12}
                fontWeight='700'>
                {item}
              </I18nText>
            </YStack>
          );
        })}
      </XStack>
      <XStack paddingHorizontal='$3.5' marginVertical='$5' gap='$5'>
        {['Points', 'Time', 'Distance', 'Calories'].map((item) => {
          const key = item.toLocaleLowerCase() as BusinessTrendsOverviewKey;
          const handlePressMetric = async () => {
            await triggerHaptics();
            setMetric(key);
          };
          return (
            <YStack key={key} onPress={handlePressMetric}>
              <StyledText
                fontSize={18}
                fontWeight='700'
                color={metric === key ? '$primary' : '$color'}>
                {humaniseNumber(summary[key])}
              </StyledText>
              <I18nText
                fontWeight='700'
                fontSize={12}
                color={metric === key ? '$primary' : '$color'}>
                {item}
              </I18nText>
            </YStack>
          );
        })}
      </XStack>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <GiftedLineChart
          data={chartData}
          curved
          isAnimated
          color={color}
          thickness={3}
          hideRules
          hideYAxisText
          yAxisColor='transparent'
          xAxisColor='transparent'
          spacing={spacing}
          dataPointsColor={color}
          dataPointsRadius={4}
          animateOnDataChange
          focusEnabled
          showStripOnFocus={false}
          showDataPointLabelOnFocus
          showTextOnFocus
          height={chartHeight}
          width={chartWidth}
          textColor1={color}
          showValuesAsDataPointsText
          focusedDataPointColor={color}
        />
      </ScrollView>
    </YStack>
  );
}
