import React, {forwardRef, memo, useCallback, useState} from 'react';
import {useRouter} from 'expo-router';
import Animated from 'react-native-reanimated';
import {useTranslation} from 'react-i18next';
import {Spin<PERSON>, XStack, YStack} from 'tamagui';
import {FlashList, FlashListProps, ListRenderItem} from '@shopify/flash-list';
import {ClubMemberDto, ClubMemberDtoRoleEnum} from '@gojoe/typescript-sdk';

import {triggerHaptics} from '../utils/haptics';
import useClubMembers from '@/src/hooks/api/useClubMembers';
import useDebounce from '../hooks/useDebounce';
import UserAvatar from './UI/Avatar/UserAvatar';
import StyledText from './UI/StyledText';
import I18nText from './I18nText';
import {StyledInput} from './UI/StyledInput';

export const AnimatedFlatList = Animated.createAnimatedComponent(
  FlashList<ClubMemberDto>,
);

type Props = Omit<
  FlashListProps<ClubMemberDto>,
  'renderItem' | 'keyExtractor' | 'data'
>;

const ClubMembersList = forwardRef<
  FlashList<ClubMemberDto>,
  Props & {clubId: string}
>((props, ref) => {
  const router = useRouter();
  const {t} = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm || '', 500);
  const {data, isRefetching, isFetchingNextPage, fetchNextPage, refetch} =
    useClubMembers(props.clubId, debouncedSearchTerm);

  const handleOnSearch = (value: string) => {
    setSearchTerm(value);
  };

  const goToProfilePage = useCallback(
    async (userId: string) => {
      await triggerHaptics();
      router.push({
        pathname: '/user/[userId]',
        params: {
          userId: userId,
        },
      });
    },
    [router],
  );

  const keyExtractor = useCallback((item: ClubMemberDto) => item.id, []);

  const renderItem = useCallback<ListRenderItem<ClubMemberDto>>(
    ({item}) => {
      return (
        <XStack
          height={48}
          alignItems='center'
          paddingHorizontal='$3.5'
          marginBottom='$2'
          gap='$2'
          onPress={() => goToProfilePage(item.user.id)}>
          <UserAvatar
            user={{
              id: item.id,
              name: item.user.name,
              profilePicture: item.user.profilePicture,
              firstName: item.user.name.split(' ')[0] ?? '',
              lastName: item.user.name.split(' ')[1] ?? '',
            }}
            size={48}
            circular
          />
          <YStack>
            <StyledText fontSize={14} fontWeight={700} color='$color'>
              {item.user.name}
            </StyledText>
            {item.role === ClubMemberDtoRoleEnum.Admin && (
              <I18nText fontSize={11} fontWeight={700} color='$red'>
                Admin
              </I18nText>
            )}
          </YStack>
        </XStack>
      );
    },
    [goToProfilePage],
  );

  const listHeaderComponent = useCallback(() => {
    return (
      <YStack marginVertical='$3.5' paddingHorizontal='$3.5'>
        <StyledInput
          placeholder={t('Search for members')}
          onChangeText={handleOnSearch}
          //style={styles.input}
        />
      </YStack>
    );
  }, [t]);

  const listFooterComponent = useCallback(() => {
    if (isFetchingNextPage) {
      return <Spinner marginVertical='$3' />;
    }
    return <YStack height={48} />;
  }, [isFetchingNextPage]);

  const onEndReached = () => fetchNextPage();

  return (
    <AnimatedFlatList
      ref={ref}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      estimatedItemSize={48}
      scrollEventThrottle={16}
      data={data}
      onRefresh={refetch}
      refreshing={isRefetching}
      onEndReached={onEndReached}
      onEndReachedThreshold={0.5}
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={listHeaderComponent}
      ListFooterComponent={listFooterComponent}
      {...props}
    />
  );
});
ClubMembersList.displayName = 'ClubMembersList';

export default memo(
  ClubMembersList,
  (prev, next) => prev.clubId === next.clubId,
);
