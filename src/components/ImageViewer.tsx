import React, {forwardRef, useImperativeHandle, useRef, useState} from 'react';

import {Dimensions, StyleSheet} from 'react-native';
import {Image} from 'expo-image';
import {Zoomable} from '@likashefqet/react-native-image-zoom';
import {Ionicons} from '@expo/vector-icons';
import {YStack} from 'tamagui';
import {GestureDetector, Gesture} from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

export interface ImageViewerRef {
  show: (uri: string) => void;
  hide: () => void;
}

const ImageViewer = forwardRef<ImageViewerRef>((_, ref) => {
  const {top} = useSafeAreaInsets();
  const [show, setShow] = useState(false);
  const imageUriRef = useRef<string>('');

  const translateY = useSharedValue(0);

  useImperativeHandle(ref, () => ({
    show: (uri: string) => {
      translateY.value = 0;
      imageUriRef.current = uri;
      setShow(true);
    },
    hide: () => {
      imageUriRef.current = '';
      setShow(false);
    },
  }));

  const hide = () => {
    imageUriRef.current = '';
    setShow(false);
  };

  const panGesture = Gesture.Pan()
    .minPointers(1)
    .maxPointers(1)
    .onUpdate((event) => {
      translateY.value = event.translationY;
    })
    .onEnd((event) => {
      if (Math.abs(event.translationY) > 100) {
        runOnJS(hide)();
      } else {
        translateY.value = withTiming(0);
      }
    });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateY: translateY.value}],
  }));

  const backdropAnimatedStyle = useAnimatedStyle(() => ({
    opacity: 1 - Math.min(Math.abs(translateY.value) / 1000, 1),
  }));

  if (!show) return null;

  return (
    <YStack
      position='absolute'
      top={0}
      left={0}
      right={0}
      bottom={0}
      zIndex={1000000}>
      <Animated.View
        style={[
          {...StyleSheet.absoluteFillObject, backgroundColor: '#000'},
          backdropAnimatedStyle,
        ]}
      />

      <YStack
        flex={1}
        marginHorizontal={10}
        justifyContent='center'
        alignItems='center'>
        <YStack
          backgroundColor='$grey3'
          position='absolute'
          top={top + 10}
          right={0}
          padding={5}
          borderRadius={20}
          zIndex={100001}
          onPress={hide}
          borderWidth={1}
          borderColor='#FFF'>
          <Ionicons name='close' size={24} color='#FFF' />
        </YStack>

        <GestureDetector gesture={panGesture}>
          <Animated.View style={[{flex: 1}, animatedStyle]}>
            <Zoomable key={imageUriRef.current} isDoubleTapEnabled>
              <Image
                source={{uri: imageUriRef.current}}
                contentFit='contain'
                style={styles.fullScreenImage}
              />
            </Zoomable>
          </Animated.View>
        </GestureDetector>
      </YStack>
    </YStack>
  );
});

ImageViewer.displayName = 'ImageViewer';

const styles = StyleSheet.create({
  fullScreenImage: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
  },
});

export default ImageViewer;
