import React, {useState} from 'react';
import {
  DrawerContentComponentProps,
  DrawerContentScrollView,
} from '@react-navigation/drawer';
import {useRouter} from 'expo-router';
import {Image, useTheme, XStack, YStack} from 'tamagui';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useSession} from '@/src/contexts/SessionContext';
import I18nText from '@/src/components/I18nText';
import UserAvatar from '@/src/components/UI/Avatar/UserAvatar';
import StyledText from '@/src/components/UI/StyledText';
import {ChevronRight, Plus} from '@tamagui/lucide-icons';
import {triggerHaptics} from '@/src/utils/haptics';
import {DrawerActions} from '@react-navigation/native';
import {BusinessListDto} from '@gojoe/typescript-sdk';
import {FontAwesome, FontAwesome5, MaterialIcons} from '@expo/vector-icons';
import intercom from '@/src/services/intercom';
import Businesses from '../Businesses';
import ChatBox from './Chat';
import {SheetManager} from 'react-native-actions-sheet';
import useUserBusinesses from '@/src/hooks/api/useUserBusinesses';

const SPACE = 24;

const DrawerContent: React.FC<DrawerContentComponentProps> = ({...props}) => {
  const {navigation} = props;
  const {user, selectBusiness} = useSession();
  const {top, bottom} = useSafeAreaInsets();
  const router = useRouter();
  const theme = useTheme();
  const {data} = useUserBusinesses({limit: 4});
  const [showViewAll, setShowViewAll] = useState(false);

  const iconColor = theme.grey ? theme.grey.get() : '#ACAFBB';

  const handlePressJourneys = async () => {
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    router.push({pathname: '/journeys'});
  };

  const handlePressChat = async () => {
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    router.push({pathname: '/chat'});
  };

  const handlePressLesMills = async () => {
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    router.push({pathname: '/les-mills'});
  };

  const handlePressLesHow = async () => {
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    router.push({pathname: '/house-of-wellbeing'});
  };

  const handlePressFaq = async () => {
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    router.push({pathname: '/faq'});
  };

  const handlePressFindFriends = async () => {
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    setTimeout(async () => {
      await SheetManager.show('find_friends');
    }, 500);
  };

  const handlePressProfile = async () => {
    if (!user) return;
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    router.push({
      pathname: '/user/[userId]',
      params: {
        userId: user.id,
      },
    });
  };

  const handlePressSettings = async () => {
    if (!user) return;
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    router.push({
      pathname: '/user/settings',
    });
  };

  const handlePressFairPlay = async () => {
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    router.push({
      pathname: '/fair-play',
    });
  };

  const handlePressLiveSupport = async () => {
    if (!user) {
      return;
    }
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    await intercom.openChat(user);
  };

  const handlePressBusiness = async (business: BusinessListDto) => {
    await triggerHaptics();
    selectBusiness({
      id: business.id,
      name: business.name,
      logo: business.avatar,
    });
    navigation.dispatch(DrawerActions.closeDrawer());
    router.push({
      pathname: '/business/[businessId]',
      params: {
        businessId: business.id,
      },
    });
  };

  const handlePressBusinessCode = async () => {
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    router.push({
      pathname: '/passcode',
    });
  };

  const handlePressViewAllOrgs = async () => {
    await triggerHaptics();
    navigation.dispatch(DrawerActions.closeDrawer());
    router.push('/businesses');
  };

  return (
    <YStack flex={1} backgroundColor='$windowBackground'>
      {user && (
        <XStack
          paddingTop={SPACE + top}
          paddingHorizontal='$5'
          paddingBottom='$5'
          backgroundColor='$background'
          gap={8}
          borderBottomWidth={1}
          borderBottomColor='$grey'
          onPress={handlePressProfile}>
          <UserAvatar user={user} width={48} height={48} borderRadius={8} />
          <YStack flex={1} justifyContent='center' gap='$1'>
            <StyledText color='$color' fontWeight='700' numberOfLines={1}>
              {user.firstName} {user.lastName}
            </StyledText>
            <I18nText color='$grey' fontWeight='600'>
              view profile
            </I18nText>
          </YStack>
          <ChevronRight color='$grey' />
        </XStack>
      )}
      <DrawerContentScrollView
        showsVerticalScrollIndicator={false}
        {...props}
        contentContainerStyle={{
          paddingTop: 0,
          paddingBottom: 0,
          paddingStart: 0,
          paddingEnd: 0,
        }}>
        <YStack
          paddingTop='$5'
          paddingHorizontal='$5'
          flex={1}
          gap={16}
          paddingBottom={bottom}>
          <YStack
            padding='$4'
            backgroundColor='$background'
            borderWidth={1}
            borderColor='$grey'
            borderRadius={8}>
            <I18nText
              fontWeight='700'
              fontSize={11}
              textTransform='uppercase'
              letterSpacing={0.44}
              color='$grey'>
              Train with Gojoe
            </I18nText>
            <YStack gap={16} marginTop={16}>
              <XStack alignItems='center' gap={8} onPress={handlePressJourneys}>
                <Image
                  source={require('@/assets/images/mobile/buttons/icon_journeys.png')}
                  width={32}
                  height={32}
                  borderRadius={4}
                />
                <I18nText fontWeight='700'>Journeys</I18nText>
              </XStack>
              <XStack alignItems='center' gap={8} onPress={handlePressLesMills}>
                <Image
                  source={require('@/assets/images/mobile/buttons/icon_les_mills.png')}
                  width={32}
                  height={32}
                  borderRadius={4}
                />
                <I18nText fontWeight='700'>Les Mills</I18nText>
              </XStack>
              <XStack alignItems='center' gap={8} onPress={handlePressLesHow}>
                <Image
                  source={require('@/assets/images/mobile/buttons/icon_how.png')}
                  width={32}
                  height={32}
                  borderRadius={4}
                />
                <I18nText fontWeight='700'>House of Wellbeing</I18nText>
              </XStack>
            </YStack>
          </YStack>
          <ChatBox onPress={handlePressChat} />

          <YStack
            padding='$4'
            backgroundColor='$background'
            borderWidth={1}
            borderColor='$grey'
            borderRadius={8}>
            <I18nText
              fontWeight='700'
              fontSize={11}
              textTransform='uppercase'
              letterSpacing={0.44}
              color='$grey'>
              Your Communities
            </I18nText>
            <Businesses data={data} onPress={handlePressBusiness} />
            <XStack
              marginTop={16}
              gap={8}
              alignItems='center'
              onPress={handlePressBusinessCode}>
              <YStack
                borderWidth={1}
                borderStyle='dashed'
                width={32}
                height={32}
                borderRadius={8}
                justifyContent='center'
                alignItems='center'
                borderColor='$primary'>
                <Plus size={16} color='$primary' />
              </YStack>
              <I18nText fontWeight='700' color='$primary'>
                Enter Code
              </I18nText>
            </XStack>
            {data.length > 3 && (
              <XStack
                marginTop={16}
                onPress={handlePressViewAllOrgs}
                alignItems='center'
                gap={8}>
                <YStack width={32} />
                <I18nText flex={1} color='$primary' fontWeight='500'>
                  View all Communities
                </I18nText>
                <ChevronRight color='$primary' />
              </XStack>
            )}
          </YStack>
          <YStack
            backgroundColor='$background'
            borderWidth={1}
            borderColor='$grey'
            borderRadius={8}>
            <XStack height={48} onPress={handlePressFindFriends}>
              <YStack
                justifyContent='center'
                alignItems='center'
                width={48}
                height={48}>
                <FontAwesome5 name='user-friends' />
              </YStack>
              <XStack
                flex={1}
                borderBottomWidth={1}
                borderColor='$accentGrey'
                alignItems='center'
                justifyContent='space-between'
                paddingEnd='$4'>
                <I18nText fontWeight='700'>Find friends</I18nText>
                <ChevronRight color='$grey' />
              </XStack>
            </XStack>
            <XStack height={48} onPress={handlePressSettings}>
              <YStack
                justifyContent='center'
                alignItems='center'
                width={48}
                height={48}>
                <FontAwesome5 name='cogs' />
              </YStack>
              <XStack
                flex={1}
                alignItems='center'
                justifyContent='space-between'
                paddingEnd='$4'>
                <I18nText fontWeight='700'>Settings</I18nText>
                <ChevronRight color='$grey' />
              </XStack>
            </XStack>
          </YStack>

          <YStack
            backgroundColor='$background'
            borderWidth={1}
            borderColor='$grey'
            borderRadius={8}>
            <YStack paddingHorizontal='$4' paddingTop='$4' paddingBottom='$2'>
              <I18nText
                fontWeight='700'
                fontSize={11}
                textTransform='uppercase'
                letterSpacing={0.44}
                color='$grey'>
                Need Help?
              </I18nText>
            </YStack>
            <XStack height={48} onPress={handlePressFaq}>
              <YStack
                justifyContent='center'
                alignItems='center'
                width={48}
                height={48}>
                <FontAwesome5
                  name='question-circle'
                  size={18}
                  color={iconColor}
                />
              </YStack>
              <XStack
                flex={1}
                borderBottomWidth={1}
                borderColor='$accentGrey'
                alignItems='center'
                justifyContent='space-between'
                paddingEnd='$4'>
                <I18nText fontWeight='700' color='$grey'>
                  FAQs
                </I18nText>
                <ChevronRight color='$grey' />
              </XStack>
            </XStack>
            <XStack height={48} onPress={handlePressFairPlay}>
              <YStack
                justifyContent='center'
                alignItems='center'
                width={48}
                height={48}>
                <FontAwesome name='handshake-o' size={18} color={iconColor} />
              </YStack>
              <XStack
                flex={1}
                borderBottomWidth={1}
                borderColor='$accentGrey'
                alignItems='center'
                justifyContent='space-between'
                paddingEnd='$4'>
                <I18nText fontWeight='700' color='$grey'>
                  The Fair Play Book of Rules
                </I18nText>
                <ChevronRight color='$grey' />
              </XStack>
            </XStack>
            <XStack height={48} onPress={handlePressLiveSupport}>
              <YStack
                justifyContent='center'
                alignItems='center'
                width={48}
                height={48}>
                <MaterialIcons
                  name='chat-bubble-outline'
                  size={18}
                  color={iconColor}
                />
              </YStack>
              <XStack
                flex={1}
                alignItems='center'
                justifyContent='space-between'
                paddingEnd='$4'>
                <I18nText fontWeight='700' color='$grey'>
                  Live Chat Support
                </I18nText>
                <ChevronRight color='$grey' />
              </XStack>
            </XStack>
          </YStack>
        </YStack>
      </DrawerContentScrollView>
    </YStack>
  );
};

export default DrawerContent;
