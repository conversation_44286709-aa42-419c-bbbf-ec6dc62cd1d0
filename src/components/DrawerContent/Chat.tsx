import React, {useEffect, useState} from 'react';
import {Image, XStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';
import {useChat} from '@/src/contexts/ChatContext';
import {ArrowRight, ChevronRight} from '@tamagui/lucide-icons';
import StyledText from '@/src/components/UI/StyledText';

interface Props {
  onPress: () => Promise<void>;
}
const ChatBox: React.FC<Props> = ({onPress}) => {
  const [unreadMessages, setUnreadMessages] = useState(0);
  const {connection} = useChat();

  useEffect(() => {
    if (connection && connection.me) {
      setUnreadMessages(connection.me.unread_count);
    }
  }, [connection]);

  return (
    <XStack
      onPress={onPress}
      padding='$4'
      backgroundColor='$background'
      borderWidth={1}
      borderColor='$grey'
      borderRadius={8}
      gap={8}
      alignItems='center'>
      <Image
        source={require('@/assets/images/icons/icon-chat-1.png')}
        width={24}
        height={24}
      />
      <I18nText flex={1} fontWeight='700'>
        Chat
      </I18nText>
      {unreadMessages ? (
        <XStack
          height={24}
          backgroundColor='$primary'
          gap='$2'
          alignItems='center'
          justifyContent='space-between'
          paddingHorizontal={10}
          borderRadius={16}>
          <StyledText color='$white1' fontWeight='700' fontSize={11}>
            {unreadMessages}
          </StyledText>
          <ArrowRight color='$white1' size={14} />
        </XStack>
      ) : (
        <ChevronRight color='$grey' />
      )}
    </XStack>
  );
};

export default ChatBox;
