import React from 'react';
import {XStack, YStack} from 'tamagui';
import {Image, ImageBackground} from 'expo-image';
import {SheetManager} from 'react-native-actions-sheet';

import {getBadge, getLevelName} from '@/src/utils/joeLevel';
import {triggerHaptics} from '@/src/utils/haptics';
import I18nText from '../I18nText';

interface Props {
  level: number;
}

const ProfileJoeLevel: React.FC<Props> = ({level}) => {
  const handlePress = async () => {
    await triggerHaptics();
    await SheetManager.show('joe_level');
  };

  if (!level) {
    level = 1;
  }

  return (
    <XStack onPress={handlePress}>
      <ImageBackground
        source={require('@/assets/images/joeLevel/profile_level_bg.png')}
        style={{
          flex: 1,
          height: 80,
          flexDirection: 'row',
          alignItems: 'center',
          borderRadius: 16,
          paddingHorizontal: 8,
          overflow: 'hidden',
        }}>
        <Image
          key={`img_badge_level_${level}`}
          source={getBadge(level)}
          style={{
            width: 64,
            height: 64,
          }}
        />

        <YStack
          flex={1}
          alignSelf='flex-end'
          marginLeft='$1.5'
          marginBottom='$3.5'>
          <XStack alignItems='center' justifyContent='space-between'>
            <I18nText fontSize={16} fontWeight={700} color='#FFF'>
              {getLevelName(level)}
            </I18nText>
            <I18nText
              fontSize={12}
              fontWeight={700}
              color='#FFF'
              i18nParams={{level}}>
              {`Level {{level}}`}
            </I18nText>
          </XStack>

          <XStack
            width='100%'
            height={8}
            backgroundColor='#FFF'
            marginTop='$1.5'
            borderRadius={8}>
            <XStack
              width={`${level * 10}%`}
              height={8}
              backgroundColor='#FFCE00'
              borderRadius={4}
            />
          </XStack>
        </YStack>
      </ImageBackground>
    </XStack>
  );
};

export default ProfileJoeLevel;
