import React from 'react';
import {XStack, YStack} from 'tamagui';
import I18nText from '../I18nText';
import StyledText from '../UI/StyledText';
import {UserOverviewStatsDto, UserProfileDto} from '@gojoe/typescript-sdk';
import FollowerButton from '../FollowerButton';
import {useSession} from '@/src/contexts/SessionContext';
import {checkIfAccountIsPrivate} from '@/src/utils/users';

interface Props {
  userData?: UserProfileDto;
  dataOverview?: UserOverviewStatsDto;
  updated?: (userId: string) => void;
}

const FollowStats: React.FC<Props> = ({userData, dataOverview, updated}) => {
  const {user} = useSession();

  const isPrivate = checkIfAccountIsPrivate({
    currentUserId: user?.id || '',
    privacy: userData?.settings?.privacy || 'public',
    isFollowed: userData?.isFollowed || false,
    userFollowId: userData?.id || '',
  });

  if (!userData || !dataOverview || !user) {
    return null;
  }

  if (isPrivate) {
    return (
      <XStack>
        <StyledText>Private Account</StyledText>
      </XStack>
    );
  }

  return (
    <XStack alignItems='center' justifyContent='space-between'>
      <XStack alignItems='center' gap='$5'>
        <YStack>
          <I18nText fontSize={12} fontWeight={500} color='#656874'>
            Following
          </I18nText>
          <StyledText fontSize={14} fontWeight={700} color='#000'>
            {dataOverview?.countFollowing || 0}
          </StyledText>
        </YStack>

        <YStack>
          <I18nText fontSize={12} fontWeight={500} color='#656874'>
            Followers
          </I18nText>
          <StyledText fontSize={14} fontWeight={700} color='#000'>
            {dataOverview?.countFollowers || 0}
          </StyledText>
        </YStack>
      </XStack>

      {user?.id !== userData?.id && (
        <FollowerButton
          userId={userData?.id || ''}
          isFollowed={userData?.isFollowed || false}
          updated={updated}
        />
      )}
    </XStack>
  );
};

export default FollowStats;
