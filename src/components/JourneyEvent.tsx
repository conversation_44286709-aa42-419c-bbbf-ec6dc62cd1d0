import React, {useCallback} from 'react';
import {useTranslation} from 'react-i18next';
import {DateTime} from 'luxon';
import {XStack, YStack} from 'tamagui';
import {Image, ImageBackground} from 'expo-image';
import {useRouter} from 'expo-router';
import {CircleCheck} from '@tamagui/lucide-icons';
import {JourneyDto, JourneyStepsDto} from '@gojoe/typescript-sdk';

import {getResizeImage} from '../utils/image';
import {colorHash} from '../utils/image';
import StyledText from './UI/StyledText';
import I18nText from './I18nText';

import {triggerHaptics} from '../utils/haptics';
import Avatar from './UI/Avatar/Avatar';

interface Props {
  journey: JourneyDto | undefined;
  event: JourneyStepsDto;
}

const JourneyEvent: React.FC<Props> = ({journey, event}) => {
  const router = useRouter();
  const {t} = useTranslation();

  const handleNavigate = useCallback(async () => {
    await triggerHaptics();
    router.push({
      pathname: '/journey-event/[eventId]',
      params: {journeyId: journey?.id, eventId: event.id},
    });
  }, [router, journey?.id, event.id]);

  if (!journey) return null;

  if (DateTime.fromISO(event.publishedAt) > DateTime.utc()) {
    return (
      <XStack>
        <StyledText>
          {event.name} | {t('Available soon')}
        </StyledText>
      </XStack>
    );
  } else if (
    !journey.myJourney ||
    journey.myJourney.step + 1 < event.position
  ) {
    return (
      <XStack
        flex={1}
        backgroundColor='#FFF'
        borderRadius={16}
        padding={8}
        marginTop={8}
        zIndex={1}>
        <XStack borderRadius={8} overflow='hidden' position='relative'>
          {event.cover ? (
            <ImageBackground
              source={{uri: getResizeImage(event.cover, 192 * 3, 128 * 3)}}
              style={{width: 96, height: 64, borderRadius: 8, opacity: 0.24}}
            />
          ) : (
            <XStack
              backgroundColor={colorHash.hex(event.id)}
              width={96}
              height={64}
              borderRadius={8}
            />
          )}
          <Image
            source={require('@/assets/images/lock.png')}
            style={{
              width: 16,
              height: 18,
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: [{translateX: -8}, {translateY: -9}],
              tintColor: '#000',
              zIndex: 1,
            }}
          />
        </XStack>
        <XStack flex={1} marginLeft={8}>
          <StyledText
            flex={1}
            fontSize='$4'
            fontWeight='$7'
            marginTop={8}
            lineHeight={16}
            numberOfLines={2}>
            {event.name}
          </StyledText>
        </XStack>
      </XStack>
    );
  }

  return (
    <XStack
      flex={1}
      backgroundColor='#FFF'
      borderRadius={16}
      padding={8}
      marginTop={8}
      onPress={handleNavigate}>
      <XStack borderRadius={8} overflow='hidden'>
        {event.cover ? (
          <ImageBackground
            nativeID={`event-${event.id}-image-list`}
            source={{uri: getResizeImage(event.cover, 192 * 3, 128 * 3)}}
            style={{width: 96, height: 64, borderRadius: 8}}
          />
        ) : (
          <XStack
            width={96}
            height={64}
            backgroundColor={colorHash.hex(event.id)}
            borderRadius={8}
          />
        )}
      </XStack>

      <YStack flex={1} marginLeft={8}>
        <StyledText
          flex={1}
          fontSize='$4'
          fontWeight='$7'
          marginTop={8}
          lineHeight={16}
          numberOfLines={2}>
          {event.name}
        </StyledText>
        <XStack alignItems='center'>
          <>
            {event.users.slice(0, 3).map((user: any, index: number) => {
              const marginLeft = index === 0 ? 0 : -10;
              return (
                <XStack
                  key={user.id}
                  marginLeft={marginLeft}
                  borderRadius={12}
                  backgroundColor='$background'
                  padding='$0.75'
                  overflow='hidden'>
                  <Avatar
                    id={user.id}
                    name={user.name}
                    avatar={user.avatar}
                    size={22}
                    circular
                  />
                </XStack>
              );
            })}
            <I18nText
              flex={1}
              fontSize={12}
              fontWeight='$5'
              marginLeft={8}
              color='#888'>
              {event.completedByPercentage}% Completed
            </I18nText>
          </>
          {journey.myJourney.step >= event.position && (
            <CircleCheck size={18} color='#0BB87A' />
          )}
        </XStack>
      </YStack>
    </XStack>
  );
};

export default JourneyEvent;
