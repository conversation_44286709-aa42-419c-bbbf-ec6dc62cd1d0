import React, {memo} from 'react';
import {StyleSheet} from 'react-native';
import {XStack} from 'tamagui';
import {ClubDto} from '@gojoe/typescript-sdk';
import {CLUB_COVER_HEIGHT} from '@/src/constants/club';
import {Image, ImageBackground} from 'expo-image';
import {useWindowDimensions} from 'react-native';
import {LinearGradient} from 'expo-linear-gradient';
import StyledText from '@/src/components/UI/StyledText';

interface Props {
  club: ClubDto;
}

const ClubHeader: React.FC<Props> = ({club}) => {
  const {width} = useWindowDimensions();
  return (
    <ImageBackground
      source={{uri: club.profilePicture}}
      transition={500}
      style={{
        height: CLUB_COVER_HEIGHT,
        width: width,
      }}
      placeholder={{blurhash: club.coverHash}}>
      <LinearGradient
        colors={['transparent', '#00000060', '#00000096']}
        style={styles.content}>
        {club.business && (
          <XStack
            alignSelf='flex-start'
            borderRadius={24}
            paddingVertical={4}
            alignItems='center'
            gap={4}
            marginTop={8}>
            {club.business.logo && (
              <Image source={{uri: club.business.logo}} style={styles.logo} />
            )}
            <StyledText numberOfLines={1} color='white' fontWeight='700'>
              {club.business.name}
            </StyledText>
          </XStack>
        )}
      </LinearGradient>
    </ImageBackground>
  );
};
const styles = StyleSheet.create({
  content: {
    paddingHorizontal: 24,
    paddingBottom: 24,
    gap: 8,
    height: '100%',
  },
  logo: {
    width: 24,
    height: 24,
    borderRadius: 8,
    overflow: 'hidden',
  },
});
export default memo(ClubHeader, (prev, next) => prev.club.id === next.club.id);
