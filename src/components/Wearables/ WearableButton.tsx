import React, {useEffect, useState} from 'react';
import {Alert} from 'react-native';
import {Spinner, StackProps, XStack, YStack} from 'tamagui';
import {useRouter} from 'expo-router';
import * as Linking from 'expo-linking';
import {triggerHaptics} from '@/src/utils/haptics';
import {useWearableUrlScheme} from '@/src/hooks/useWearableUrlScheme';
import {useMutationDisconnectWearable} from '@/src/hooks/api/useMutationDisconnectWearable';
import logger from '@/src/utils/logger';
import {ChevronRight} from '@tamagui/lucide-icons';
import Svg, {Circle, Path} from 'react-native-svg';
import {useTranslation} from 'react-i18next';
import I18nText from '../I18nText';

interface WearableButtonProps extends Partial<StackProps> {
  wearable: string;
  isConnected: boolean;
  hasManualSync: boolean;
  isOnboarding?: boolean;
  logo: React.ReactNode;
}

const WearableButton: React.FC<WearableButtonProps> = ({
  wearable,
  isConnected,
  hasManualSync,
  isOnboarding = false,
  logo,
  ...props
}: WearableButtonProps) => {
  const [connected, setConnected] = useState(isConnected);
  const router = useRouter();
  const scheme = useWearableUrlScheme(wearable);
  const {disconnect, isPending} = useMutationDisconnectWearable(wearable);
  const {t} = useTranslation();

  const callbackUrl = `${scheme}://callback`;

  // Handle deep link events
  useEffect(() => {
    const handleUrlEvent = ({url}: {url: string}) => {
      if (url.startsWith(callbackUrl)) {
        logger.debug(`✅ ${wearable} deep link detected:`, url);
        setConnected(true);
      }
    };

    const subscription = Linking.addEventListener('url', handleUrlEvent);

    Linking.getInitialURL().then((initialUrl) => {
      if (initialUrl?.startsWith(callbackUrl)) {
        logger.info(`✅ App launched with ${wearable} deep link:`, initialUrl);
        setConnected(true);
      }
    });

    return () => {
      subscription.remove();
    };
  }, [callbackUrl, wearable]);

  // Handle navigation after successful connection during onboarding
  useEffect(() => {
    if (connected && isOnboarding && router.canGoBack()) {
      router.back();
    }
  }, [connected, isOnboarding, router]);

  const handleConnect = async () => {
    await triggerHaptics();
    switch (wearable) {
      case 'coros':
        return router.push({
          pathname: `/wearables-coros`,
          params: {isOnboarding: String(isOnboarding)},
        });
      case 'fitbit':
        return router.push({
          pathname: `/wearables-fitbit`,
          params: {isOnboarding: String(isOnboarding)},
        });
      case 'garmin':
        return router.push({
          pathname: `/wearables-garmin`,
          params: {isOnboarding: String(isOnboarding)},
        });
      case 'oura':
        return router.push({
          pathname: `/wearables-oura`,
          params: {isOnboarding: String(isOnboarding)},
        });
      case 'polar':
        return router.push({
          pathname: `/wearables-polar`,
          params: {isOnboarding: String(isOnboarding)},
        });
      case 'suunto':
        return router.push({
          pathname: `/wearables-suunto`,
          params: {isOnboarding: String(isOnboarding)},
        });
      case 'wahoo':
        return router.push({
          pathname: `/wearables-wahoo`,
          params: {isOnboarding: String(isOnboarding)},
        });
      case 'whoop':
        return router.push({
          pathname: `/wearables-whoop`,
          params: {isOnboarding: String(isOnboarding)},
        });
      case 'withings':
        return router.push({
          pathname: `/wearables-withings`,
          params: {isOnboarding: String(isOnboarding)},
        });
    }
  };

  const handleDisconnect = () => {
    Alert.alert(
      'Disconnect wearable',
      `Are you sure you want to disconnect from ${wearable}?`,
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Disconnect',
          onPress: () => {
            disconnect(undefined, {
              onSuccess: () => setConnected(false),
              onError: () =>
                Alert.alert('Error', `Could not disconnect from ${wearable}`),
            });
          },
          style: 'destructive',
        },
      ],
    );
  };

  const handleOnPress = () => {
    return connected ? handleDisconnect() : handleConnect();
  };

  return (
    <YStack
      borderWidth={1}
      borderColor='#E1E5E9'
      borderRadius={8}
      backgroundColor='white'
      paddingHorizontal={14}
      height={48}
      flexDirection='row'
      alignItems='center'
      justifyContent='space-between'
      onPress={isPending ? undefined : handleOnPress}>
      {logo}
      <XStack alignItems='center' space='$2'>
        {connected && !isPending && (
          <XStack
            minWidth={85}
            height={20}
            borderRadius={20}
            backgroundColor='rgba(134, 175, 124, 0.2)'
            alignItems='center'
            position='relative'>
            <XStack
              position='absolute'
              alignItems='center'
              justifyContent='space-between'
              paddingLeft={8}
              width='100%'>
              <I18nText
                fontSize={8}
                fontWeight='800'
                color='#86AF7C'
                textTransform='uppercase'
                fontFamily='Inter'>
                Connected
              </I18nText>
              <Svg
                width={16}
                height={16}
                viewBox='0 0 20 20'
                fill='none'
                style={{alignSelf: 'flex-end'}}>
                <Circle cx='10' cy='10' r='10' fill='#86AF7C' />
                <Path
                  d='M6 10L9 13L14 7'
                  stroke='white'
                  strokeWidth='2'
                  strokeLinecap='round'
                  strokeLinejoin='round'
                />
              </Svg>
            </XStack>
          </XStack>
        )}
        {!connected && (
          <Svg
            width={16}
            height={16}
            viewBox='0 0 20 20'
            fill='none'
            style={{alignSelf: 'flex-end'}}>
            <Circle
              cx='10'
              cy='10'
              r='10'
              fill='#ffffff'
              stroke='#E1E5E9'
              strokeWidth='2'
            />
          </Svg>
        )}
        {isPending && <Spinner size='small' />}
        {hasManualSync && <ChevronRight size={16} color='#888' />}
      </XStack>
    </YStack>
  );
};

export default WearableButton;
