import React, {useState} from 'react';
import {Image, Text, StackProps, XStack, YStack} from 'tamagui';
import {useFocusEffect, useRouter} from 'expo-router';
import {triggerHaptics} from '@/src/utils/haptics';
import healthConnectService from '@/src/services/healthConnectService';
import logger from '@/src/utils/logger';
import {ChevronRight} from '@tamagui/lucide-icons';
import Svg, {Circle, Path} from 'react-native-svg';
import I18nText from '../I18nText';
const logo = require('@/assets/devices/logo_health-connect.png');

const HealthConnectButton: React.FC<StackProps> = (
  props: Partial<StackProps>,
) => {
  const [connected, setConnected] = useState(false);
  const router = useRouter();
  useFocusEffect(
    React.useCallback(() => {
      const checkStatus = async () => {
        const backendStatus = await healthConnectService.getStatus();
        logger.debug(
          '🔁 Screen focused. Current HealthConnect status:',
          backendStatus,
        );
        setConnected(backendStatus === 'connected');
      };
      checkStatus(); // call the async function but don't await it
      // no cleanup needed, so just return nothing
      return;
    }, []),
  );
  const handleOnPress = async () => {
    await triggerHaptics();
    router.push({
      pathname: `/wearables-healthconnect`,
    });
  };

  return (
    <YStack
      borderWidth={1}
      borderColor='#E1E5E9'
      borderRadius={8}
      backgroundColor='white'
      paddingHorizontal={14}
      height={48}
      flexDirection='row'
      alignItems='center'
      justifyContent='space-between'
      onPress={handleOnPress}>
      <XStack alignItems='center' space='$2'>
        <Image source={logo} width={24} height={24} />
        <Text fontSize={14} fontWeight='600' color='$color' fontFamily='Inter'>
          HealthConnect
        </Text>
      </XStack>
      <XStack alignItems='center' space='$2'>
        {connected && (
          <XStack
            minWidth={85}
            height={20}
            borderRadius={20}
            backgroundColor='rgba(134, 175, 124, 0.2)'
            alignItems='center'
            position='relative'>
            <XStack
              position='absolute'
              alignItems='center'
              justifyContent='space-between'
              paddingLeft={8}
              width='100%'>
              <I18nText
                fontSize={8}
                fontWeight='800'
                color='#86AF7C'
                textTransform='uppercase'
                fontFamily='Inter'>
                Connected
              </I18nText>
              <Svg
                width={16}
                height={16}
                viewBox='0 0 20 20'
                fill='none'
                style={{alignSelf: 'flex-end'}}>
                <Circle cx='10' cy='10' r='10' fill='#86AF7C' />
                <Path
                  d='M6 10L9 13L14 7'
                  stroke='white'
                  strokeWidth='2'
                  strokeLinecap='round'
                  strokeLinejoin='round'
                />
              </Svg>
            </XStack>
          </XStack>
        )}
        {!connected && (
          <Svg
            width={16}
            height={16}
            viewBox='0 0 20 20'
            fill='none'
            style={{alignSelf: 'flex-end'}}>
            <Circle
              cx='10'
              cy='10'
              r='10'
              fill='#ffffff'
              stroke='#E1E5E9'
              strokeWidth='2'
            />
          </Svg>
        )}
        <ChevronRight size={16} color='#888' />
      </XStack>
    </YStack>
  );
};

export default HealthConnectButton;
