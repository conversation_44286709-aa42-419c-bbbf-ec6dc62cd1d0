import React from 'react';
import {Linking, Platform, Pressable} from 'react-native';
import {FlashList} from '@shopify/flash-list';
import {XStack, YStack, Image} from 'tamagui';
import {ChevronRight} from '@tamagui/lucide-icons';

import HealthConnectButton from './HealthConnectButton';
import AppleHealthButton from './ AppleHealthButton';
import WearableButton from './ WearableButton';
import I18nText from '../I18nText';
import {WpDto} from '@gojoe/typescript-sdk';
import healthConnectService from '@/src/services/healthConnectService';
import appleHealthService from '@/src/services/appleHealthService';

interface WearablesListProps {
  data: WpDto[];
  isRefetching: boolean;
  refetch: () => void;
  isOnboarding?: boolean;
}

const isConnected = (wearableProviders: WpDto[], name: string): boolean => {
  const provider = wearableProviders.find(
    (p) => p.name.toLowerCase() === name.toLowerCase(),
  );
  const connected = !!provider?.isConnected;

  if (name === 'HealthConnect') {
    healthConnectService.setStatus(connected ? 'connected' : 'disconnected');
  }
  if (name === 'Apple') {
    appleHealthService.setStatus(connected ? 'connected' : 'disconnected');
  }

  return connected;
};

const WearablesList: React.FC<WearablesListProps> = ({
  data,
  isRefetching,
  refetch,
  isOnboarding,
}) => {
  const openLinkExternal = () => {
    Linking.openURL(
      'https://intercom.help/gojoe/en/articles/7193504-how-to-connect-samsung-health',
    );
  };

  const wearableItems = [
    ...(Platform.OS === 'ios'
      ? [{type: 'custom', key: 'apple', element: <AppleHealthButton />}]
      : []),
    {
      wearable: 'coros',
      logo: require('@/assets/devices/logo_coros.png'),
      width: 82,
      height: 20,
    },
    {
      wearable: 'fitbit',
      logo: require('@/assets/devices/logo_fitbit.png'),
      width: 57,
      height: 16,
    },
    {
      wearable: 'garmin',
      logo: require('@/assets/devices/logo_garmin.png'),
      width: 65,
      height: 19,
    },
    ...(Platform.OS === 'android'
      ? [
          {
            type: 'custom',
            key: 'healthconnect',
            element: <HealthConnectButton />,
          },
        ]
      : []),
    {
      wearable: 'polar',
      logo: require('@/assets/devices/logo_polar.png'),
      width: 84,
      height: 14,
    },
    {
      wearable: 'suunto',
      logo: require('@/assets/devices/logo_suunto.png'),
      width: 62,
      height: 20,
    },
    {
      wearable: 'wahoo',
      logo: require('@/assets/devices/logo_wahoo.png'),
      width: 64,
      height: 14,
    },
    {
      wearable: 'whoop',
      logo: require('@/assets/devices/logo_whoop.png'),
      width: 94,
      height: 14,
    },
  ];

  const renderItem = ({item}: {item: any}) => {
    if (item.type === 'custom') {
      return item.element;
    }

    return (
      <WearableButton
        hasManualSync={false}
        wearable={item.wearable}
        isConnected={isConnected(data, item.wearable)}
        isOnboarding={!!isOnboarding}
        logo={
          <Image source={item.logo} width={item.width} height={item.height} />
        }
      />
    );
  };

  return (
    <FlashList
      data={wearableItems}
      renderItem={renderItem}
      keyExtractor={(item, index) =>
        item.key || item.wearable || `item-${index}`
      }
      showsVerticalScrollIndicator={false}
      estimatedItemSize={48}
      refreshing={isRefetching}
      onRefresh={refetch}
      ItemSeparatorComponent={() => <YStack height={4} />}
      ListFooterComponent={
        <YStack gap='$2' paddingTop='$4'>
          <I18nText fontSize={14} color='$gray10'>
            <I18nText fontWeight='700'>GoJoe is not</I18nText> a step tracker.
          </I18nText>
          <I18nText fontSize={14} color='$gray10'>
            We pull workouts (walk, run, cycle, swim, etc.) which are logged as{' '}
            <I18nText fontWeight='700'>activities</I18nText> – from your
            wearable.
          </I18nText>

          {Platform.OS === 'android' && (
            <YStack paddingTop='$5' gap='$3'>
              <I18nText fontSize={16} fontWeight='700' color='$gray12'>
                Having troubles?
              </I18nText>

              <YStack
                backgroundColor='#FFF5CC'
                borderRadius={12}
                paddingHorizontal='$3'
                paddingVertical='$3'
                gap='$3'>
                <Pressable onPress={openLinkExternal}>
                  <XStack alignItems='center' justifyContent='space-between'>
                    <I18nText fontSize={12} color='$gray12'>
                      Need help connecting HealthConnect?
                    </I18nText>
                    <ChevronRight size={16} color='#ACA898' />
                  </XStack>
                </Pressable>
              </YStack>
            </YStack>
          )}
        </YStack>
      }
    />
  );
};

export default WearablesList;
