import React, {useState, useEffect} from 'react';
import {StyleSheet} from 'react-native';
import {Text, useTheme, YStack, Button} from 'tamagui';
import {useTranslation} from 'react-i18next';
import {Image} from 'expo-image';
import {AxiosError} from 'axios';
import {networkStatus} from '@/src/utils/network';

interface Props {
  error?: Error | null;
}

const ApiFetchError: React.FC<Props> = ({error}) => {
  const {t} = useTranslation();
  const theme = useTheme();
  const color = theme.color ? theme.color.get() : 'black';
  const [isConnected, setIsConnected] = useState<boolean>(
    networkStatus.getIsConnected(),
  );

  // Check if it's a network error
  const isNetworkError =
    error instanceof AxiosError &&
    !error.response &&
    error.message === 'Network Error';

  // Listen for network status changes
  useEffect(() => {
    const removeListener = networkStatus.addListener((connected) => {
      setIsConnected(connected);
    });

    return () => removeListener();
  }, []);

  const handleRetry = () => {
    // Force reload the current screen
    if (isConnected) {
      window.location.reload();
    }
  };

  // If it's a network error, show a specific message
  if (isNetworkError) {
    return (
      <YStack
        justifyContent='center'
        alignItems='center'
        flex={1}
        padding='$4'
        paddingHorizontal='$12'>
        <Image
          source={require('@/assets/images/errors/whoops.png')}
          style={styles.image}
          tintColor={color}
        />
        <Text
          fontWeight='700'
          fontSize={18}
          textAlign='center'
          marginBottom='$3'>
          {isConnected ? t('Connection restored') : t('No internet connection')}
        </Text>
        <Text lineHeight={16.8} textAlign='center' marginBottom='$4'>
          {isConnected
            ? t('Your connection has been restored. Try again?')
            : t('Please check your internet connection and try again.')}
        </Text>

        {isConnected && (
          <Button
            backgroundColor='$primary'
            color='white'
            onPress={handleRetry}
            paddingHorizontal='$4'
            paddingVertical='$2'
            borderRadius='$4'>
            {t('Retry')}
          </Button>
        )}
      </YStack>
    );
  }

  // Default error view
  return (
    <YStack
      justifyContent='center'
      alignItems='center'
      flex={1}
      padding='$4'
      paddingHorizontal='$12'>
      <Image
        source={require('@/assets/images/errors/whoops.png')}
        style={styles.image}
        tintColor={color}
      />
      <Text lineHeight={16.8} textAlign='center'>
        {t(
          "Joe suffered a fall which caused a double fracture on his lower leg bones. But don't worry he's recovering fast!",
        )}
      </Text>
    </YStack>
  );
};

const styles = StyleSheet.create({
  image: {
    width: 124,
    height: 124,
  },
});

export default ApiFetchError;
