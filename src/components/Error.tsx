import React, {useEffect, useState} from 'react';
import {YStack} from 'tamagui';
import {AxiosError} from 'axios';
import intercom from '@/src/services/intercom';
import {useSession} from '@/src/contexts/SessionContext';
import {triggerHaptics} from '@/src/utils/haptics';
import I18nText from '@/src/components/I18nText';
import StyledButton from '@/src/components/UI/Button';
import {networkStatus, getNetworkErrorMessage} from '@/src/utils/network';

interface Props {
  error: Error;
}

const ErrorResponse: React.FC<Props> = ({error}) => {
  const {user} = useSession();
  const [message, setMessage] = useState<string>(
    'Whoops! Something went wrong',
  );
  const [isConnected, setIsConnected] = useState<boolean>(
    networkStatus.getIsConnected(),
  );

  // Listen for network status changes
  useEffect(() => {
    const removeListener = networkStatus.addListener((connected) => {
      setIsConnected(connected);
    });

    return () => removeListener();
  }, []);

  // Update error message based on error type and network status
  useEffect(() => {
    if (error instanceof AxiosError) {
      // Check if it's a network error
      if (!error.response && error.message === 'Network Error') {
        setMessage(getNetworkErrorMessage());
      } else if (error.response?.status === 500) {
        setMessage('Whoops! Something went wrong');
      } else {
        setMessage(error.response?.data?.message || error.message);
      }
    }
  }, [error, isConnected]);

  const handlePressLiveChat = async () => {
    if (!user) {
      return;
    }
    await triggerHaptics();
    await intercom.openChat(user);
  };

  // Show a retry button if it's a network error and we're back online
  const isNetworkError =
    error instanceof AxiosError &&
    !error.response &&
    error.message === 'Network Error';

  const handleRetry = () => {
    // Force reload the current screen
    if (isConnected) {
      // This will trigger a refresh of the current screen
      window.location.reload();
    }
  };

  return (
    <YStack flex={1} alignItems='center' justifyContent='center' padding='$5'>
      <I18nText fontWeight={700} fontSize={18} textAlign='center'>
        {message}
      </I18nText>

      {isNetworkError ? (
        <>
          <I18nText marginVertical='$4' textAlign='center'>
            {isConnected
              ? 'Your connection has been restored. Try again?'
              : 'Please check your internet connection and try again.'}
          </I18nText>

          {isConnected && (
            <StyledButton onPress={handleRetry}>
              <I18nText color='$white1' fontWeight='700'>
                Retry
              </I18nText>
            </StyledButton>
          )}
        </>
      ) : (
        <>
          <I18nText marginVertical='$4' textAlign='center'>
            If you think this is a mistake, press the button below to start a
            chat with our team
          </I18nText>
          <StyledButton onPress={handlePressLiveChat}>
            <I18nText color='$white1' fontWeight='700'>
              Start Chat
            </I18nText>
          </StyledButton>
        </>
      )}
    </YStack>
  );
};

export default ErrorResponse;
