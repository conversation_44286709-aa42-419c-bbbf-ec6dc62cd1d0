import React, {useEffect, useState} from 'react';
import {Modal, StyleSheet} from 'react-native';
import {YStack, XStack, Progress} from 'tamagui';
import * as Updates from 'expo-updates';
import I18nText from '@/src/components/I18nText';
import StyledButton from '@/src/components/UI/Button';
import {triggerHaptics} from '@/src/utils/haptics';
import logger from '@/src/utils/logger';

interface UpdateState {
  isChecking: boolean;
  isUpdateAvailable: boolean;
  isDownloading: boolean;
  downloadProgress: number;
  isReadyToRestart: boolean;
  error: string | null;
}

const UpdateManager: React.FC = () => {
  const [updateState, setUpdateState] = useState<UpdateState>({
    isChecking: false,
    isUpdateAvailable: false,
    isDownloading: false,
    downloadProgress: 0,
    isReadyToRestart: false,
    error: null,
  });

  // Check for updates when the app starts
  useEffect(() => {
    checkForUpdates();
  }, []);

  // Function to check for updates
  const checkForUpdates = async () => {
    if (__DEV__) {
      logger.debug('Skipping update check in development mode');
      return;
    }

    try {
      setUpdateState((prev) => ({...prev, isChecking: true, error: null}));

      logger.debug('Checking for updates...');
      const update = await Updates.checkForUpdateAsync();

      if (update.isAvailable) {
        logger.debug('Update available!');
        setUpdateState((prev) => ({
          ...prev,
          isChecking: false,
          isUpdateAvailable: true,
        }));
      } else {
        logger.debug('No updates available');
        setUpdateState((prev) => ({
          ...prev,
          isChecking: false,
          isUpdateAvailable: false,
        }));
      }
    } catch (error) {
      logger.error('Error checking for updates:', error);
      setUpdateState((prev) => ({
        ...prev,
        isChecking: false,
        error: 'Failed to check for updates',
      }));
    }
  };

  // Function to download and apply the update
  const downloadUpdate = async () => {
    try {
      await triggerHaptics();

      setUpdateState((prev) => ({
        ...prev,
        isDownloading: true,
        downloadProgress: 0,
        error: null,
      }));

      // Simulate progress for both iOS and Android since onDownloadProgress is not supported
      const simulateProgress = () => {
        let progress = 0;
        const interval = setInterval(() => {
          progress += 0.05;
          if (progress >= 0.95) {
            clearInterval(interval);
          } else {
            setUpdateState((prev) => ({
              ...prev,
              downloadProgress: progress,
            }));
          }
        }, 100);

        return () => clearInterval(interval);
      };

      const clearSimulation = simulateProgress();

      // Fetch the actual update
      const result = await Updates.fetchUpdateAsync();

      // Clear the simulation and set to 100%
      clearSimulation();
      setUpdateState((prev) => ({
        ...prev,
        downloadProgress: 1,
        isDownloading: false,
        isReadyToRestart: true,
      }));

      return result;
    } catch (error) {
      logger.error('Error downloading update:', error);
      setUpdateState((prev) => ({
        ...prev,
        isDownloading: false,
        error: 'Failed to download update',
      }));
    }
  };

  // Function to restart the app and apply the update
  const restartApp = async () => {
    try {
      await triggerHaptics();
      await Updates.reloadAsync();
    } catch (error) {
      logger.error('Error restarting app:', error);
      setUpdateState((prev) => ({
        ...prev,
        error: 'Failed to restart app',
      }));
    }
  };

  // Function to dismiss the update modal
  const dismissUpdate = () => {
    triggerHaptics();
    setUpdateState((prev) => ({
      ...prev,
      isUpdateAvailable: false,
      isDownloading: false,
      isReadyToRestart: false,
    }));
  };

  return (
    <Modal
      visible={
        updateState.isUpdateAvailable ||
        updateState.isDownloading ||
        updateState.isReadyToRestart
      }
      transparent
      animationType='fade'>
      <YStack style={styles.modalContainer}>
        <YStack style={styles.modalContent} padding='$5' gap='$4'>
          {/* Title */}
          <I18nText fontSize={20} fontWeight='600' textAlign='center'>
            {updateState.isReadyToRestart
              ? 'Update Ready to Install'
              : updateState.isDownloading
                ? 'Downloading Update'
                : 'Update Available'}
          </I18nText>

          {/* Message */}
          <I18nText textAlign='center'>
            {updateState.isReadyToRestart
              ? 'The update has been downloaded and is ready to install. The app will restart to apply the update.'
              : updateState.isDownloading
                ? 'Please wait while we download the latest version of the app.'
                : 'A new version of the app is available. Would you like to update now?'}
          </I18nText>

          {/* Progress indicator */}
          {updateState.isDownloading && (
            <YStack gap='$2' width='100%'>
              <Progress value={updateState.downloadProgress * 100} width='100%'>
                <Progress.Indicator
                  animation='bouncy'
                  backgroundColor='$primary'
                />
              </Progress>
              <I18nText textAlign='center' fontSize={12}>
                {Math.round(updateState.downloadProgress * 100)}%
              </I18nText>
            </YStack>
          )}

          {/* Error message */}
          {updateState.error && (
            <I18nText color='$red10' textAlign='center'>
              {updateState.error}
            </I18nText>
          )}

          {/* Buttons */}
          <XStack gap='$3' justifyContent='center' marginTop='$2'>
            {!updateState.isDownloading && !updateState.isReadyToRestart && (
              <StyledButton variant='secondary' onPress={dismissUpdate}>
                <I18nText>Later</I18nText>
              </StyledButton>
            )}

            {updateState.isReadyToRestart ? (
              <StyledButton onPress={restartApp}>
                <I18nText color='$white1'>Restart Now</I18nText>
              </StyledButton>
            ) : !updateState.isDownloading ? (
              <StyledButton onPress={downloadUpdate}>
                <I18nText color='$white1'>Update Now</I18nText>
              </StyledButton>
            ) : null}
          </XStack>
        </YStack>
      </YStack>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

export default UpdateManager;
