import I18nText from '@/src/components/I18nText';
import {ListItem, XStack, YStack} from 'tamagui';
import {
  ChevronRight,
  MessageCircleQuestion,
  MessageSquareText,
} from '@tamagui/lucide-icons';
import React from 'react';
import {triggerHaptics} from '@/src/utils/haptics';
import intercom from '@/src/services/intercom';
import {useSession} from '@/src/contexts/SessionContext';
import {useRouter} from 'expo-router';

const NeedHelp: React.FC = () => {
  const {user} = useSession();
  const router = useRouter();
  const handlePressLiveSupport = async () => {
    if (!user) {
      return;
    }
    await triggerHaptics();
    await intercom.openChat(user);
  };

  const handlePressFaq = async () => {
    await triggerHaptics();
    router.push({pathname: '/faq'});
  };

  return (
    <YStack marginTop='$5'>
      <I18nText color='$color' fontWeight='700'>
        Need Help?
      </I18nText>
      <YStack
        backgroundColor='$background'
        borderWidth={1}
        borderColor='$grey3'
        borderRadius={16}
        padding={16}
        marginVertical='$5'
        gap={16}>
        <XStack
          height={24}
          onPress={handlePressFaq}
          alignItems='center'
          gap={8}>
          <MessageCircleQuestion color='$grey2' size={18} />
          <I18nText fontWeight='700' color='$grey' flex={1}>
            FAQs
          </I18nText>
          <ChevronRight color='$grey' />
        </XStack>
        <XStack
          height={24}
          onPress={handlePressLiveSupport}
          alignItems='center'
          gap={8}>
          <MessageSquareText color='$grey2' size={18} />
          <I18nText fontWeight='700' color='$grey' flex={1}>
            Live Chat Support
          </I18nText>
          <ChevronRight color='$grey' />
        </XStack>
      </YStack>
    </YStack>
  );
};

export default NeedHelp;
