import React from 'react';
import StyledText from '@/src/components/UI/StyledText';
import I18nText from '@/src/components/I18nText';
import {
  addTimeToDateTime,
  formatDurationHuman,
  formatTimeStringDuration,
} from '@/src/utils/challenge';
import {useLocales} from '../contexts/LocaleContext';
import {humaniseNumber} from '@/src/utils/numbers';
import convert from 'convert';

interface Props {
  name: string;
  value: string;
  challengeStartDate: string;
}

const AwardValue: React.FC<Props> = ({name, value, challengeStartDate}) => {
  const {locales} = useLocales();
  if (!value) {
    return null;
  }
  switch (name.toLowerCase()) {
    case 'woodpecker':
    case 'slammer':
      return (
        <I18nText
          i18nParams={{count: parseInt(value, 10)}}
          fontSize={12}
          color='$grey1'>
          {`{{count}} activity`}
        </I18nText>
      );

    case 'big hiiter':
    case 'cat cow':
    case 'cool boarder':
    case 'lifter':
      return (
        <StyledText fontSize={12} color='$grey1'>
          {formatDurationHuman(parseInt(value, 10))}
        </StyledText>
      );

    case 'bandit':
      return (
        <StyledText fontSize={12} color='$grey1'>
          {formatTimeStringDuration(value)}
        </StyledText>
      );

    case 'early bird':
    case 'night owl':
      return (
        <StyledText fontSize={12} color='$grey1'>
          {value.split('.')[0]}
        </StyledText>
      );

    case 'eager beaver':
      return (
        <StyledText fontSize={12} color='$grey1'>
          {addTimeToDateTime(challengeStartDate, value)}
        </StyledText>
      );

    case 'goldfish':
    case 'postie':
    case 'road runner':
    case 'yellow jersey':
      const intValue = parseInt(value, 10);
      const convertedValue = convert(intValue, 'm').to(locales.distanceUnit);
      return (
        <StyledText fontSize={12} color='$grey1'>
          {`${humaniseNumber(convertedValue, locales.languageCode)} ${locales.distanceUnit}`}
        </StyledText>
      );

    default:
      return (
        <StyledText fontSize={12} color='$grey1'>
          {value}
        </StyledText>
      );
  }
};

export default AwardValue;
