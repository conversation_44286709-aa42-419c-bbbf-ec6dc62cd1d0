import React, {memo, ReactNode} from 'react';
import {Text, TextProps} from 'tamagui';

export type StyledTextProps = TextProps & {
  children?: string | ReactNode;
};

const StyledText: React.FC<StyledTextProps> = ({children, ...props}) => {
  return (
    <Text allowFontScaling={false} fontFamily='Inter' color='$color' {...props}>
      {children}
    </Text>
  );
};

export default memo(StyledText);
