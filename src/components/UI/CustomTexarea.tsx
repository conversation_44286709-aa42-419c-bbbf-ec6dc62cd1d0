import React from 'react';
import {Input} from 'tamagui';

interface Props {
  value: string;
  onChange: (text: string) => void;
  placeholder?: string;
  height?: string | number;
}

const CustomTextarea: React.FC<Props> = ({
  value,
  onChange,
  height = '$12',
  placeholder = 'Description',
}) => {
  return (
    <Input
      flex={1}
      autoComplete='off'
      backgroundColor='$background'
      borderRadius={8}
      minHeight={height}
      borderWidth={1}
      borderColor='$grey3'
      paddingHorizontal='$3'
      paddingVertical='$2'
      placeholder={placeholder}
      value={value}
      multiline={true}
      verticalAlign='top'
      onChangeText={onChange}
      focusStyle={{
        borderColor: '$grey3',
        backgroundColor: '$basckground',
      }}
    />
  );
};

export default CustomTextarea;
