import React from 'react';
import {YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';

interface Props {
  name: string;
  borderColor?: string;
}

const Tag: React.FC<Props> = ({name, borderColor = '$primary'}) => {
  return (
    <YStack
      height={28}
      paddingHorizontal='$2'
      backgroundColor={borderColor}
      justifyContent='center'
      alignItems='center'
      borderRadius='$2'>
      <I18nText color='$white' fontWeight='900' fontSize={12}>
        {name}
      </I18nText>
    </YStack>
  );
};

export default Tag;
