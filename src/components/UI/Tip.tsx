import React, {ReactNode} from 'react';
import {YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';

interface Props {
  borderColor?: string;
  children: ReactNode;
}

const Tip: React.FC<Props> = ({children, borderColor = '#9E65C6'}) => {
  return (
    <YStack
      padding='$5'
      borderColor={borderColor}
      backgroundColor={`${borderColor}16`}
      borderWidth={1}
      borderRadius='$6'>
      <YStack
        position='absolute'
        height={28}
        top={-14}
        start={16}
        paddingHorizontal='$2'
        backgroundColor={borderColor}
        justifyContent='center'
        alignItems='center'
        borderRadius='$2'>
        <I18nText color='$white' fontWeight='900' fontSize={12}>
          TIP
        </I18nText>
      </YStack>
      {children}
    </YStack>
  );
};

export default Tip;
