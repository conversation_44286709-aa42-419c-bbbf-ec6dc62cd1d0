import React, {memo} from 'react';
import {TextProps} from 'react-native';
import {
  AntDesign,
  MaterialIcons,
  FontAwesome,
  Ionicons,
  MaterialCommunityIcons,
  Entypo,
  Feather,
  FontAwesome6,
  FontAwesome5,
} from '@expo/vector-icons';
import {useTheme} from 'tamagui';
import logger from '@/src/utils/logger';

export interface IconProps extends TextProps {
  size?: number;
  iconSize?: string;
  name: string; // Use string for flexibility
  color?: string;
}

export type IconFontName =
  | 'AntDesign'
  | 'Entypo'
  | 'Feather'
  | 'MaterialIcons'
  | 'FontAwesome'
  | 'FontAwesome5'
  | 'FontAwesome6'
  | 'Ionicons'
  | 'MaterialCommunityIcons';

const StyledIcon: React.FC<IconProps & {fontName: IconFontName}> = ({
  fontName,
  name,
  size,
  color,
  iconSize = '$6',
  ...props
}) => {
  const theme = useTheme();
  const resolvedColor = color ?? theme.color.val;
  logger.debug('resolvedColor', resolvedColor, theme.color);
  let iconFontSize = size;

  if (iconSize && theme[iconSize]) {
    iconFontSize = theme[iconSize]?.val;
  }

  switch (fontName) {
    case 'AntDesign':
      return (
        <AntDesign
          name={name as keyof typeof AntDesign.glyphMap} // Cast to specific type
          size={iconFontSize}
          color={resolvedColor}
          {...props}
        />
      );
    case 'Entypo':
      return (
        <Entypo
          name={name as keyof typeof Entypo.glyphMap} // Cast to specific type
          size={iconFontSize}
          color={resolvedColor}
          {...props}
        />
      );
    case 'MaterialIcons':
      return (
        <MaterialIcons
          name={name as keyof typeof MaterialIcons.glyphMap}
          size={iconFontSize}
          color={resolvedColor}
          {...props}
        />
      );
    case 'FontAwesome':
      return (
        <FontAwesome
          name={name as keyof typeof FontAwesome.glyphMap}
          size={iconFontSize}
          color={resolvedColor}
          {...props}
        />
      );
    case 'FontAwesome5':
      return (
        <FontAwesome5
          name={name as keyof typeof FontAwesome5.glyphMap}
          size={iconFontSize}
          color={resolvedColor}
          {...props}
        />
      );
    case 'FontAwesome6':
      return (
        <FontAwesome6
          name={name as keyof typeof FontAwesome6.glyphMap}
          size={iconFontSize}
          color={resolvedColor}
          {...props}
        />
      );
    case 'Ionicons':
      return (
        <Ionicons
          name={name as keyof typeof Ionicons.glyphMap}
          size={iconFontSize}
          color={resolvedColor}
          {...props}
        />
      );
    case 'MaterialCommunityIcons':
      return (
        <MaterialCommunityIcons
          name={name as keyof typeof MaterialCommunityIcons.glyphMap}
          size={iconFontSize}
          color={resolvedColor}
          {...props}
        />
      );
    case 'Feather':
      return (
        <Feather
          name={name as keyof typeof Feather.glyphMap}
          size={iconFontSize}
          color={resolvedColor}
          {...props}
        />
      );
    default:
      logger.warn(`Unsupported font: ${fontName}`);
      return null;
  }
};

export default memo(StyledIcon);
