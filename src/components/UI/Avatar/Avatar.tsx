import React, {memo} from 'react';
import {Avatar} from 'tamagui';
import {AvatarProps} from '@tamagui/avatar';
import {colorHash, getTextInitials} from '@/src/utils/image';
import StyledText from '@/src/components/UI/StyledText';

interface Props extends AvatarProps {
  id: string;
  name: string;
  avatar?: string;
}

const CustomAvatar: React.FC<Props> = ({id, name, avatar, ...props}) => {
  const bgColor = colorHash.hex(id);
  return (
    <Avatar {...props}>
      {!!avatar ? (
        <Avatar.Image accessibilityLabel={name} src={avatar} />
      ) : null}
      <Avatar.Fallback
        backgroundColor={bgColor}
        justifyContent='center'
        alignItems='center'>
        <StyledText fontSize='$3' color='$white' fontWeight='700'>
          {getTextInitials(name)}
        </StyledText>
      </Avatar.Fallback>
    </Avatar>
  );
};

export default memo(CustomAvatar);
