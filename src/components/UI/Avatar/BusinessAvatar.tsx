import React, {memo} from 'react';
import {AvatarProps} from '@tamagui/avatar';
import CustomAvatar from '@/src/components/UI/Avatar/Avatar';

type BusinessAvatarProps = {
  id: string;
  name: string;
  logo?: string;
  avatar?: string;
};

interface Props extends AvatarProps {
  business: BusinessAvatarProps;
}

const BusinessAvatar: React.FC<Props> = ({business, ...props}) => {
  return (
    <CustomAvatar
      {...props}
      id={business.id}
      name={business.name}
      avatar={business.logo ?? business.avatar}
    />
  );
};

export default memo(BusinessAvatar);
