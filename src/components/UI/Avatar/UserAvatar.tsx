import React, {memo} from 'react';
import {AvatarProps} from '@tamagui/avatar';
import CustomAvatar from '@/src/components/UI/Avatar/Avatar';

type UserAvatarProps = {
  id: string;
  name?: string;
  avatar?: string;
  profilePicture?: string;
  firstName?: string;
  lastName?: string;
};

interface Props extends AvatarProps {
  user: UserAvatarProps;
}
const UserAvatar: React.FC<Props> = ({user, ...props}) => {
  let name = user.name || '';
  if (!name && user.firstName && user.lastName) {
    name = `${user.firstName} ${user.lastName}`;
  }
  return (
    <CustomAvatar
      {...props}
      id={user.id}
      name={name}
      avatar={user.avatar ?? user.profilePicture}
    />
  );
};

export default memo(UserAvatar);
