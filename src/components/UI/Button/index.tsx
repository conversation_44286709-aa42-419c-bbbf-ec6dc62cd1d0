import {styled, Button, ButtonProps, Spinner} from 'tamagui';
import React from 'react';

const CustomStyledButton = styled(But<PERSON>, {
  name: 'CustomButton',

  variants: {
    variant: {
      primary: {
        backgroundColor: '$primary',
        color: 'white',
        borderRadius: '$6',
      },
      outlined: {
        backgroundColor: '$background',
        borderColor: '$color',
        color: 'white',
        borderRadius: '$6',
        borderWidth: 1,
      },
      secondary: {
        backgroundColor: '$background',
        color: '$gray12',
        borderColor: '$color',
        borderRadius: '$6',
        borderWidth: 1,
      },
      ghost: {
        backgroundColor: '$windowBackground',
        color: '$grey2',
        borderColor: '$grey2',
        borderRadius: '$6',
        borderWidth: 1,
      },
    },

    size: {
      1: {
        paddingVertical: '$2',
        paddingHorizontal: '$4',
        fontSize: '$2',
      },
      2: {
        paddingHorizontal: '$5',
        fontWeight: '600',
        height: '$4.5',
      },
      3: {
        paddingVertical: '$4',
        paddingHorizontal: '$6',
        fontSize: '$4',
      },
    },
    disabled: {
      true: {
        backgroundColor: '$color',
        opacity: 0.3,
        color: '$background',
        fontWeight: '500',
      },
    },
  },

  defaultVariants: {
    variant: 'primary',
    size: 2,
  },
});

interface Props extends Omit<ButtonProps, 'variant'> {
  variant?: 'primary' | 'outlined' | 'secondary' | 'ghost';
  loading?: boolean;
}
const StyledButton: React.FC<Props> = (props) => {
  return (
    <CustomStyledButton {...props}>
      <>
        {props.children}
        {props.loading && (
          <Spinner color='$primary' position='absolute' end={10} />
        )}
      </>
    </CustomStyledButton>
  );
};

export default StyledButton;
