import React, {memo} from 'react';
import {YStack, YStackProps} from 'tamagui';
import {TWO_BOX_WIDTH} from '@/src/constants/Constants';

interface Props extends YStackProps {
  highlighted?: boolean;
}
const BoxTwo: React.FC<Props> = ({highlighted, children, ...props}) => {
  return (
    <YStack
      width={TWO_BOX_WIDTH}
      backgroundColor={highlighted ? '$backgroundHighlight' : '$background'}
      borderRadius={16}
      padding={16}
      borderWidth={1}
      borderColor={highlighted ? '$borderHighlight' : '$grey3'}
      justifyContent={'center'}
      alignItems={'center'}
      {...props}>
      {children}
    </YStack>
  );
};

export default memo(BoxTwo);
