import React, {memo, useCallback} from 'react';
import {Image, ImageBackground, StyleSheet} from 'react-native';
import {Pressable} from 'react-native';
import {Play} from '@tamagui/lucide-icons';
import {
  MediaListDto,
  MediaListDtoTypeEnum,
  PostListDto,
  PostSingleDto,
} from '@gojoe/typescript-sdk';

import {useImage} from '@/src/contexts/ImageContext';

interface Props {
  post: PostListDto | PostSingleDto;
  media: MediaListDto;
  width: number;
  category?: string;
}

const PostMedia: React.FC<Props> = ({media, width, post, category}) => {
  const {showImage} = useImage();
  const height = media.orientation === 'PORTRAIT' ? width * 1.77 : 240;

  const handleImagePress = useCallback(
    (uri: string) => {
      showImage(uri);
    },
    [showImage],
  );

  if (media.type === MediaListDtoTypeEnum.Image) {
    return (
      <>
        <Pressable onPress={() => handleImagePress(media.uri)}>
          <Image
            source={{uri: media.uri}}
            style={{
              height: width / 1.56,
              width: width,
              borderRadius: 8,
            }}
            resizeMode='cover'
          />
        </Pressable>
      </>
    );
  } else if (media.type === MediaListDtoTypeEnum.Video) {
    const bgSource = post.cover
      ? {uri: post.cover.uri}
      : require('@/assets/images/video_thumb.jpg');
    const handleNavigateToVideo = () => {
      // navigateToVideoPlayer({
      //   uri: media.uri,
      //   trackOptions: {
      //     asset_id: post.id,
      //     content_asset_id: post.id,
      //     title: post.caption,
      //     keywords: [category],
      //     publisher: post.business ? post.business.name : post.user.name,
      //     airdate: post.publishedAt,
      //   },
      // });
    };
    return (
      <Pressable
        onPress={handleNavigateToVideo}
        style={[styles.videoContainer, {width}]}>
        <ImageBackground
          source={bgSource}
          style={[styles.imageBackground, {width, height}]}>
          <Play color='#FFFFFF' size={98} />
        </ImageBackground>
      </Pressable>
    );
  }
  return null;
};

export default memo(PostMedia);

const styles = StyleSheet.create({
  videoContainer: {
    height: 240,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageBackground: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
