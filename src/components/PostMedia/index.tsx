import React from 'react';
import {FlatList, ListRenderItemInfo, useWindowDimensions} from 'react-native';
import {YStack} from 'tamagui';
import {MediaListDto, PostListDto} from '@gojoe/typescript-sdk';
import Media from '@/src/components/PostMedia/Media';

interface Props {
  post: PostListDto;
  category?: string;
}
const PostMedia: React.FC<Props> = ({post, category}) => {
  const layout = useWindowDimensions();
  if (post.attachments?.length === 0) {
    return null;
  }
  const renderItem = ({item}: ListRenderItemInfo<MediaListDto>) => {
    return (
      <YStack
        marginTop={16}
        marginRight={16}
        borderRadius={4}
        overflow='hidden'
        alignItems='center'
        justifyContent='center'>
        <Media
          media={item}
          width={layout.width - 200}
          post={post}
          category={category}
        />
      </YStack>
    );
  };

  if (post.attachments?.length === 1) {
    return (
      <YStack
        marginTop={16}
        borderRadius={4}
        overflow='hidden'
        alignItems='center'
        justifyContent='center'>
        <Media
          media={post.attachments[0]}
          width={layout.width - 80}
          post={post}
          category={category}
        />
      </YStack>
    );
  }

  const keyExtractor = (item: MediaListDto) => item.id;

  return (
    <FlatList
      showsHorizontalScrollIndicator={false}
      horizontal={true}
      data={post.attachments}
      renderItem={renderItem}
      pagingEnabled
      keyExtractor={keyExtractor}
    />
  );
};

export default PostMedia;
