import React from 'react';
import {useRouter} from 'expo-router';
import {useQueryClient} from '@tanstack/react-query';
import {Sheet<PERSON>anager} from 'react-native-actions-sheet';
import {XStack, YStack, Text} from 'tamagui';
import {PostListDto} from '@gojoe/typescript-sdk';

import {updateCollectionItem, updateSingleItem} from '@/src/utils/query';
import {useSession} from '@/src/contexts/SessionContext';
import {triggerHaptics} from '@/src/utils/haptics';
import {postDateTime} from '@/src/utils/date';
import {MenuIcon} from '../GoJoeIcon/icons/menu';
import {WearableIcon} from '../GoJoeIcon';
import UserAvatar from '../UI/Avatar/UserAvatar';

interface Props {
  post: PostListDto;
  languageTag?: string;
}

const UserHeader: React.FC<Props> = ({post, languageTag}) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const {user} = useSession();

  const goToProfilePage = async () => {
    await triggerHaptics();
    router.push({
      pathname: '/user/[userId]',
      params: {
        userId: post.user?.id,
      },
    });
  };

  const onSuccessReportMutate = () => {
    return Promise.all([
      updateCollectionItem({
        queryClient,
        id: post?.id,
        queryKey: ['feed'],
        update: {
          data: {
            data: {
              reported: true,
            },
          },
        },
        fields: ['reported'],
      }),
      updateSingleItem({
        queryClient,
        queryKey: ['post', post?.id],
        update: {
          data: {
            data: {
              reported: true,
            },
          },
        },
        fields: ['reported'],
      }),
      updateSingleItem({
        queryClient,
        queryKey: ['post', 'activities', post?.user?.id],
        update: {
          data: {
            data: {
              reported: true,
            },
          },
        },
        fields: ['reported'],
      }),
    ]);
  };

  const handlePressOnMenu = async () => {
    await triggerHaptics();
    await SheetManager.show('post_menu', {
      payload: {
        post: post,
        onSuccessReportMutate,
      },
    });
  };

  if (!user) {
    return null;
  }

  return (
    <XStack flex={1} alignItems='center' height='$4.5' gap='$2.5'>
      <UserAvatar
        user={post.user}
        size='$3.5'
        circular
        onPress={goToProfilePage}
      />

      <YStack flex={1} justifyContent='flex-start' gap='$1'>
        <Text
          fontWeight='700'
          onPress={
            goToProfilePage
          }>{`${post.user?.firstName} ${post.user?.lastName}`}</Text>
        <Text fontSize={12} color='$grey2' numberOfLines={1}>
          {postDateTime(post, languageTag)}
        </Text>
      </YStack>

      <XStack alignItems='center' gap={8}>
        {!!post?.activity?.isWearable ? (
          <WearableIcon size={16} color='#B1B3BA' />
        ) : null}
        <XStack alignItems='center' onPress={handlePressOnMenu}>
          <MenuIcon size={16} color='#B1B3BA' />
        </XStack>
      </XStack>
    </XStack>
  );
};

export default UserHeader;
