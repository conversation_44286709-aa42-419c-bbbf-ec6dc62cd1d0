import React from 'react';
import {Sheet<PERSON>anager} from 'react-native-actions-sheet';
import {XStack, YStack, Text} from 'tamagui';
import {PostListDto} from '@gojoe/typescript-sdk';

import {postDateTime} from '@/src/utils/date';
import {triggerHaptics} from '@/src/utils/haptics';
import {MenuIcon} from '../GoJoeIcon/icons/menu';
import BusinessAvatar from '../UI/Avatar/BusinessAvatar';
import {useSession} from '@/src/contexts/SessionContext';

interface Props {
  post: PostListDto;
  languageTag?: string;
}

const BusinessHeader: React.FC<Props> = ({post, languageTag}) => {
  const {user} = useSession();
  const handlePressOnMenu = async () => {
    await triggerHaptics();
    await SheetManager.show('post_menu', {
      payload: {
        post: post,
      },
    });
  };

  if (!user) {
    return null;
  }

  return (
    <XStack flex={1} alignItems='center' height='$4.5' gap='$2.5'>
      <BusinessAvatar business={post.business} size='$4.5' circular />

      <YStack flex={1} justifyContent='flex-start' gap='$1'>
        <Text fontWeight='700'>{post.business?.name}</Text>
        <Text fontSize={12} color='$grey2' numberOfLines={1}>
          {postDateTime(post, languageTag)}
        </Text>
      </YStack>

      <XStack alignItems='center' onPress={handlePressOnMenu}>
        <MenuIcon size={16} color='#B1B3BA' />
      </XStack>
    </XStack>
  );
};

export default BusinessHeader;
