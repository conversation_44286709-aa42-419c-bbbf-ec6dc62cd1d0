import React from 'react';
import {PostListDto} from '@gojoe/typescript-sdk';
import BusinessHeader from './BusinessHeader';
import UserHeader from './UserHeader';

interface Props {
  post: PostListDto;
  languageTag?: string;
}

const PostHeader: React.FC<Props> = ({post, languageTag}) => {
  const isBusiness = !!post.business;
  return isBusiness ? (
    <BusinessHeader post={post} languageTag={languageTag} />
  ) : (
    <UserHeader post={post} languageTag={languageTag} />
  );
};

export default PostHeader;
