import React from 'react';
import {StyleSheet} from 'react-native';
import {useTranslation} from 'react-i18next';
import {FlashList} from '@shopify/flash-list';
import {LesMillsVideoListDto} from '@gojoe/typescript-sdk';
import {Spin<PERSON>, XStack, YStack} from 'tamagui';
import {Video} from '@tamagui/lucide-icons';
import {Image} from 'expo-image';
import {useRouter} from 'expo-router';
import {useLesMillsVideos} from '@/src/hooks/api/useLesMillsVideos';
import I18nText from '@/src/components/I18nText';
import StyledText from '@/src/components/UI/StyledText';
import {triggerHaptics} from '@/src/utils/haptics';

interface Props {
  programId: string;
  description?: string;
}
const LesMillsProgramList: React.FC<Props> = ({programId, description}) => {
  const {t} = useTranslation();
  const {data, isLoading, refetch, isRefetching, fetchNextPage} =
    useLesMillsVideos(programId);

  const router = useRouter();

  const onEndReached = () => fetchNextPage();

  const renderItem = ({item}: {item: LesMillsVideoListDto}) => {
    const handlePnPress = async () => {
      if (!item.videoUrl) {
        return;
      }
      await triggerHaptics();
      router.push({
        pathname: '/video',
        params: {
          uri: item.videoUrl,
        },
      });
      // if (authUser.isAccountFree()) {
      //   return openModalGetPremium();
      // }
      // await navigateToVideoPlayer({
      //   uri: item.videoUrl,
      //   trackOptions: {
      //     asset_id: item.id,
      //     content_asset_id: item.id,
      //     title: item.title,
      //     program: item.program.name,
      //     publisher: 'Les Mills',
      //     total_length: item.duration * 60,
      //   },
      // });
    };

    return (
      <XStack
        onPress={handlePnPress}
        backgroundColor='$background'
        height={96}
        borderRadius='$5'
        marginBottom={8}
        padding={8}
        justifyContent='center'
        gap={8}>
        <Image source={{uri: item.thumbnail}} style={styles.image} />
        <YStack flex={1} justifyContent='space-between'>
          <YStack gap='$1'>
            <StyledText color='$color' fontWeight='700' numberOfLines={1}>
              {item.title}
            </StyledText>
            <StyledText fontWeight='500' lineHeight={19.6} color='$grey'>
              {item.intensity}
            </StyledText>
          </YStack>
          <XStack gap='$1'>
            <Video size={14} />
            <StyledText fontWeight='500' fontSize={12}>
              {t('{{count}} min', {count: item.duration})}
            </StyledText>
          </XStack>
        </YStack>
      </XStack>
    );
  };

  const listHeaderComponent = () => {
    if (!description) {
      return null;
    }
    return (
      <I18nText lineHeight={19.6} fontWeight='500' marginBottom={24}>
        {description}
      </I18nText>
    );
  };

  if (isLoading) {
    return <Spinner marginTop='$5' />;
  }

  return (
    <FlashList
      data={data}
      renderItem={renderItem}
      estimatedItemSize={96}
      showsVerticalScrollIndicator={false}
      onRefresh={refetch}
      refreshing={isRefetching}
      onEndReached={onEndReached}
      ListHeaderComponent={listHeaderComponent}
    />
  );
};

const styles = StyleSheet.create({
  image: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
});

export default LesMillsProgramList;
