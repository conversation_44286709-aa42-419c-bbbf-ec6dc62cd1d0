import React from 'react';
import {StyleSheet} from 'react-native';
import {useRouter} from 'expo-router';
import {Image, XStack, YStack} from 'tamagui';
import {GiftCardDto} from '@gojoe/typescript-sdk';

import StyledText from '@/src/components/UI/StyledText';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';

interface Props {
  item: GiftCardDto;
  isFeatured?: boolean;
}
const ListItem: React.FC<Props> = ({item, isFeatured = false}) => {
  const router = useRouter();
  const handleOnPress = async () => {
    await triggerHaptics();
    router.push({
      pathname: '/gift-card',
      params: {giftCardId: item.id},
    });
  };
  return (
    <YStack
      borderRadius={16}
      padding={8}
      flex={1}
      marginVertical={2}
      marginHorizontal='$5'
      onPress={handleOnPress}>
      <Image
        source={{uri: item.image}}
        style={[styles.image, isFeatured ? {height: 210} : {height: 100}]}
      />
      <XStack padding={8} justifyContent='space-between'>
        <StyledText numberOfLines={1} fontWeight='700'>
          {item.name}
        </StyledText>
      </XStack>
      <XStack padding={8} justifyContent='space-between'>
        <XStack gap={4} alignItems='center'>
          <Image
            source={require('@/assets/images/reword_circle_3.png')}
            style={styles.imageCircle}
            tintColor='#FFCE1F'
          />
          <I18nText fontSize={9} opacity={0.4} fontWeight='600'>
            from
          </I18nText>
          <StyledText fontWeight='700'>{item.fromPoints}</StyledText>
        </XStack>
      </XStack>
    </YStack>
  );
};
const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: 100,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imageCircle: {
    width: 18,
    height: 18,
  },
});

export default ListItem;
