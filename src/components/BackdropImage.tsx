import React from 'react';
import Animated, {
  interpolate,
  SharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';
import {StyleSheet} from 'react-native';

interface Props {
  uri: string;
  index: number;
  activeIndex: SharedValue<number>;
  blurRadius?: number;
}

const BackdropImage: React.FC<Props> = ({
  uri,
  index,
  activeIndex,
  blurRadius = 24,
}) => {
  const stylez = useAnimatedStyle(() => {
    return {
      opacity: interpolate(
        activeIndex.value,
        [index - 1, index, index + 1],
        [0, 0.8, 0],
      ),
    };
  });
  return (
    <Animated.Image
      source={{uri: uri}}
      style={[StyleSheet.absoluteFillObject, stylez]}
      blurRadius={blurRadius}
    />
  );
};

export default BackdropImage;
