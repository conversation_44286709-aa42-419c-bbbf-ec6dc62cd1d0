import React from 'react';
import {Theme, YStack} from 'tamagui';
import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';
import {useTranslation} from 'react-i18next';
import {StyledInput} from '../UI/StyledInput';
import {zodResolver} from '@hookform/resolvers/zod';
import {
  sigInEmailInputSchema,
  SignInInputFormInterface,
} from '@/src/forms/schema/email-sign-in';
import {Controller, useForm} from 'react-hook-form';
import {useRouter} from 'expo-router';
import {SheetManager} from 'react-native-actions-sheet';
import {useAuthWithEmail} from '@/src/hooks/api/useAuthWithEmail';
import StyledButton from '../UI/Button';
import logger from '@/src/utils/logger';
import {getUserFriendlyErrorMessage} from '@/src/utils/getError';

interface Props {
  onError: (message: string) => void;
}

const EmailSignIn: React.FC<Props> = ({onError}) => {
  const {t} = useTranslation();
  const router = useRouter();
  const {isPending, requestPin} = useAuthWithEmail();

  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm<SignInInputFormInterface>({
    resolver: zodResolver(sigInEmailInputSchema),
    defaultValues: {
      email: '',
    },
    mode: 'onChange',
  });

  const onSubmit = async (formData: SignInInputFormInterface) => {
    await triggerHaptics();
    try {
      await SheetManager.hide('signIn');
      await requestPin({email: formData.email});
      router.push({
        pathname: '/sign-in-email',
        params: {
          email: formData.email,
        },
      });
    } catch (error) {
      logger.error('Email sign-in error:', error);

      // Get a user-friendly error message based on the error type
      const errorMessage = getUserFriendlyErrorMessage(error);
      onError(errorMessage);
    }
  };

  return (
    <YStack gap='$3.5'>
      {/* Email Section */}
      <Controller
        control={control}
        name='email'
        render={({field: {value, onChange}}) => (
          <StyledInput
            autoCorrect={false}
            keyboardType='email-address'
            submitBehavior='submit'
            onChangeText={onChange}
            value={value}
            placeholder={t('E-mail address')}
            autoCapitalize='none'
          />
        )}
      />
      {errors.email && <I18nText color='$red'>{errors.email.message}</I18nText>}

      <YStack gap='$2'>
        <Theme name='redGoJoe'>
          <StyledButton
            loading={isPending}
            onPress={handleSubmit(onSubmit)}
            theme='redGoJoe'
            height='$4.5'
            borderRadius='$6'>
            <I18nText fontWeight='bold' color='$white1'>
              Continue
            </I18nText>
          </StyledButton>
        </Theme>
      </YStack>
    </YStack>
  );
};

export default EmailSignIn;
