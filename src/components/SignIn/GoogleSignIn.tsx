import React, {useEffect} from 'react';
import {Platform} from 'react-native';
import Constants from 'expo-constants';
import * as Crypto from 'expo-crypto';
import {<PERSON>ton, ButtonProps, Spinner} from 'tamagui';
import {
  GoogleOneTapSignIn,
  WebOneTapSignInCallbacks,
  OneTapExplicitSignInResponse,
  isSuccessResponse,
  isNoSavedCredentialFoundResponse,
  OneTapUser,
} from '@react-native-google-signin/google-signin';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import {LoginProviderBodyDtoProviderEnum} from '@gojoe/typescript-sdk';

import {useSession} from '@/src/contexts/SessionContext';
import {useAuthWithProvider} from '@/src/hooks/api/useAuthWithProvider';
import {triggerHaptics} from '@/src/utils/haptics';
import {SheetManager} from 'react-native-actions-sheet';
import logger from '@/src/utils/logger';

GoogleOneTapSignIn.configure({
  webClientId: Constants.expoConfig?.extra?.google.webClientId,
});

interface GoogleSignInProps extends ButtonProps {
  onError: (message: string) => void;
}

const GoogleSignIn: React.FC<GoogleSignInProps> = ({onError, ...props}) => {
  const {signIn} = useSession();
  const [isLoading, setIsLoading] = React.useState(false);
  const {mutateAsync} = useAuthWithProvider();

  useEffect(() => {
    if (Platform.OS === 'web') {
      setIsLoading(true);
      const scriptTag = document.createElement('script');
      scriptTag.src = 'https://accounts.google.com/gsi/client';
      scriptTag.async = true;
      scriptTag.onload = () => {
        setIsLoading(false);
      };

      scriptTag.onerror = () => {
        logger.warn('Failed to load Google script');
      };
      document.body.appendChild(scriptTag);
    }
  }, []);

  const handleError = (
    message: string = "We couldn't sign you in. Please try again or use another sign-in method.",
  ) => {
    if (onError) {
      onError(message);
    }
    setIsLoading(false);
  };

  const webOneTapSignInCallbacks: WebOneTapSignInCallbacks = {
    onResponse: async (response: OneTapExplicitSignInResponse) => {
      if (response.type === 'success') {
        const apiResponse = await mutateAsync({
          idToken: response.data.idToken,
          provider: LoginProviderBodyDtoProviderEnum.Google,
        });
        await signIn(apiResponse);
      } else {
        logger.error('response error:', response);
        handleError();
      }
      setIsLoading(false);
    },
    onError: (error: any) => {
      logger.error(error);
      handleError();
    },
  };

  const handleSuccessResponse = async (data: OneTapUser) => {
    logger.debug('handleSuccessResponse: google data:', data);
    const apiResponse = await mutateAsync({
      provider: LoginProviderBodyDtoProviderEnum.Google,
      idToken: data.idToken,
    });
    await signIn(apiResponse);
    await SheetManager.hide('signIn');
  };

  const handleOnPress = async () => {
    try {
      setIsLoading(true);
      await triggerHaptics();
      if (Platform.OS === 'web') {
        GoogleOneTapSignIn.signIn({}, webOneTapSignInCallbacks);
      } else {
        await GoogleOneTapSignIn.checkPlayServices();
        const response = await GoogleOneTapSignIn.signIn();
        logger.debug('google response:', response);
        if (isSuccessResponse(response)) {
          logger.info('Sign in successful, handling response...');
          await handleSuccessResponse(response.data);
        } else if (isNoSavedCredentialFoundResponse(response)) {
          logger.info('No saved credential found, creating account...');
          try {
            // Skip the createAccount step since it's also returning noSavedCredentialFound
            // and go directly to explicit sign-in
            logger.info('Trying explicit sign in directly...');
            const presentExplicitSignInResponse =
              await GoogleOneTapSignIn.presentExplicitSignIn({
                nonce: Crypto.randomUUID(),
              });
            logger.debug(
              'presentExplicitSignInResponse:',
              presentExplicitSignInResponse,
            );

            if (isSuccessResponse(presentExplicitSignInResponse)) {
              logger.info('Explicit sign in successful!');
              await handleSuccessResponse(presentExplicitSignInResponse.data);
            } else if (presentExplicitSignInResponse.type === 'cancelled') {
              logger.error('User cancelled the sign-in process');
              handleError('Sign-in was cancelled. Please try again.');
            } else {
              logger.error(
                'Explicit sign in did not succeed:',
                presentExplicitSignInResponse,
              );
              handleError(
                'Could not sign in with Google. Please try again or use another sign-in method.',
              );
            }
          } catch (signInError) {
            logger.error('Error during explicit sign in:', signInError);
            handleError('An error occurred during sign-in. Please try again.');
          }
        }
        setIsLoading(false);
      }
    } catch (reason) {
      logger.error('google signin error', reason);
      handleError();
      logger.error(reason);
    }
  };
  return (
    <Button
      flex={1}
      borderColor='$color'
      size='$4.5'
      borderRadius='$6'
      disabled={isLoading}
      onPress={handleOnPress}
      {...props}>
      <Button.Icon>
        {isLoading ? (
          <Spinner size='small' />
        ) : (
          <FontAwesome6 name='google' size={18} />
        )}
      </Button.Icon>
    </Button>
  );
};

export default GoogleSignIn;
