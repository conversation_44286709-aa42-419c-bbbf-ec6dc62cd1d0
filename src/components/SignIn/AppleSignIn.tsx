import React, {useState} from 'react';
import {<PERSON><PERSON>, Spin<PERSON>} from 'tamagui';
import * as AppleAuthentication from 'expo-apple-authentication';
import * as Application from 'expo-application';

import {ButtonProps} from '@tamagui/button/src/Button';
import {triggerHaptics} from '@/src/utils/haptics';
import {useAuthWithProvider} from '@/src/hooks/api/useAuthWithProvider';
import {LoginProviderBodyDtoProviderEnum} from '@gojoe/typescript-sdk';
import {SheetManager} from 'react-native-actions-sheet';
import {useSession} from '@/src/contexts/SessionContext';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import logger from '@/src/utils/logger';

interface AppleSignInProps extends ButtonProps {
  onError: (message: string) => void;
}
const AppleSignIn: React.FC<AppleSignInProps> = ({onError, ...props}) => {
  const {signIn} = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const {mutateAsync} = useAuthWithProvider();

  const handleOnPress = async () => {
    try {
      setIsLoading(true);
      await triggerHaptics();
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      const {givenName, familyName} = credential.fullName ?? {};

      const response = await mutateAsync({
        provider: LoginProviderBodyDtoProviderEnum.Apple,
        options: {
          nonce: credential.authorizationCode ?? '',
          audience: Application.applicationId ?? 'com.gojoe.app',
          identityToken: credential.identityToken ?? '',
          fullName: `${givenName ?? ''} ${familyName ?? ''}`.trim(),
          givenName: givenName ?? '',
          familyName: familyName ?? '',
        },
      });
      await signIn(response);
      await SheetManager.hide('signIn');
    } catch (error: any) {
      setIsLoading(false);
      onError(
        'We couldn’t sign you in. Please try again or use another sign-in method.',
      );
      logger.error(error);
    }
  };
  return (
    <Button
      borderColor='$color'
      flex={1}
      size='$4.5'
      borderRadius='$6'
      onPress={handleOnPress}
      disabled={isLoading}>
      <Button.Icon>
        {isLoading ? (
          <Spinner size='small' />
        ) : (
          <FontAwesome6 name='apple' size={18} />
        )}
      </Button.Icon>
    </Button>
  );
};

export default AppleSignIn;
