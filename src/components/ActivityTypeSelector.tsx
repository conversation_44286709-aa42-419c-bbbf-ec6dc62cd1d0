import React, {useEffect, useState} from 'react';
import {Di<PERSON><PERSON>, ScrollView} from 'react-native';
import {XStack, YStack} from 'tamagui';
import {ActivityTypeDto} from '@gojoe/typescript-sdk';
import {Sheet<PERSON>anager} from 'react-native-actions-sheet';
import {triggerHaptics} from '@/src/utils/haptics';

import I18nText from '@/src/components/I18nText';
import ActivityIcon from '@/src/components/ActivityIcon';
import {HISTORY_KEY, useActivityTypes} from '@/src/hooks/api/useActivityTypes';
import {storage} from '../utils/localStorage';

const screenWidth = Dimensions.get('window').width;

const itemSize = Math.floor((screenWidth - (48 + 24)) / 4);
const itemSpacing = (screenWidth - itemSize * 4 - 48) / 3;
const RECENT_HISTORY_LIMIT = 3;

interface ActivityTypeSelectorProps {
  selectedActivityType: ActivityTypeDto | null;
  onSelectActivityType: (activityType: ActivityTypeDto) => void;
}

const ActivityTypeSelector: React.FC<ActivityTypeSelectorProps> = ({
  selectedActivityType,
  onSelectActivityType,
}) => {
  const {data} = useActivityTypes();
  const [featuredActivities, setFeaturedActivities] = useState<
    ActivityTypeDto[]
  >([]);

  useEffect(() => {
    if (data && data.length > 0) {
      const history = storage.getObject<string[]>(HISTORY_KEY) ?? [];
      const recent = history
        .slice(0, RECENT_HISTORY_LIMIT)
        .map((id) => data.find((item) => item.id === id))
        .filter((item): item is ActivityTypeDto => item !== undefined);

      const recentIds = new Set(recent.map((item) => item.id));
      const fillIn = data
        .filter((item) => !recentIds.has(item.id))
        .slice(0, RECENT_HISTORY_LIMIT - recent.length);

      const featured = [...recent, ...fillIn];
      setFeaturedActivities(featured);
    }
  }, [data, selectedActivityType]);

  const handleSelectActivityType = (activityType: ActivityTypeDto) => {
    onSelectActivityType(activityType);
    triggerHaptics();
  };

  const handlePressOther = async () => {
    await triggerHaptics();
    await SheetManager.show('activity_types', {
      payload: {
        activityTypeId: selectedActivityType?.id,
        useCategories: true,
        onSelect: (activityType: ActivityTypeDto) => {
          onSelectActivityType(activityType);
        },
      },
    });
  };

  return (
    <YStack>
      <I18nText fontSize={18} fontWeight='700' marginBottom='$2'>
        Choose activity type
      </I18nText>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <XStack paddingVertical='$2' gap={itemSpacing}>
          {featuredActivities.map((activity) => (
            <YStack
              key={activity.id}
              width={itemSize}
              height={itemSize}
              alignItems='center'
              justifyContent='center'
              onPress={() => handleSelectActivityType(activity)}
              borderWidth={selectedActivityType?.id === activity.id ? 1 : 0}
              borderColor='$primary'
              backgroundColor='$background'
              borderRadius='$4'
              padding='$2'>
              <ActivityIcon
                name={activity.name}
                color={
                  selectedActivityType?.id === activity.id
                    ? '$primary'
                    : '$grey1'
                }
                size={36}
              />
              <I18nText
                fontSize={11}
                fontWeight='500'
                color={
                  selectedActivityType?.id === activity.id
                    ? '$primary'
                    : '$grey1'
                }
                marginTop='$1'>
                {activity.name}
              </I18nText>
            </YStack>
          ))}
          <YStack
            alignItems='center'
            justifyContent='center'
            onPress={handlePressOther}
            padding='$2'
            borderRadius='$4'
            borderWidth={1}
            borderStyle='dashed'
            borderColor='$grey1'
            width={itemSize}
            height={itemSize}>
            <I18nText
              fontSize={11}
              fontWeight='500'
              color='$grey1'
              marginTop='$1'
              textAlign='center'>
              Choose another activity type
            </I18nText>
          </YStack>
        </XStack>
      </ScrollView>
    </YStack>
  );
};

export default ActivityTypeSelector;
