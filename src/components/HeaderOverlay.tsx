import React, {FC, memo} from 'react';
import {StyleSheet} from 'react-native';
import {Text, YStack} from 'tamagui';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {LinearGradient} from 'expo-linear-gradient';
import {ClubDto} from '@gojoe/typescript-sdk';
import {ImageBackground} from 'expo-image';
import {NAVIGATION_HEADER_HEIGHT} from '@/src/constants/navigation';

interface Props {
  club: ClubDto;
}
const HeaderOverlay: FC<Props> = ({club}) => {
  const {top} = useSafeAreaInsets();
  const marginTop = top ? top - 10 : 0;

  return (
    <ImageBackground
      source={{uri: club.coverHash}}
      blurRadius={50}
      contentFit='cover'
      style={[styles.container, {height: NAVIGATION_HEADER_HEIGHT + top}]}
      contentPosition={{top: 0, right: 0}}
      placeholder={{blurhash: club.coverHash}}>
      <LinearGradient
        colors={['rgba(0,0,0,0.5)', 'transparent']}
        style={styles.flex}>
        <YStack
          flex={1}
          paddingStart={72}
          height={NAVIGATION_HEADER_HEIGHT}
          marginTop={marginTop}
          justifyContent='center'>
          <Text numberOfLines={1} fontSize={16} fontWeight='bold' color='white'>
            {club.name}
          </Text>
        </YStack>
      </LinearGradient>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  flex: {
    flex: 1,
  },
  container: {
    width: '100%',
    position: 'absolute',
    top: 0,
  },
});

export default memo(
  HeaderOverlay,
  (prev, next) => prev.club.id === next.club.id,
);
