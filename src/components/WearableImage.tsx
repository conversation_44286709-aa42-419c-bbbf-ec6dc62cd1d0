import {Image} from 'tamagui';

interface Props {
  name: string;
  size?: number;
}

const WearableImage: React.FC<Props> = ({name, size = 24}) => {
  const imageSource = getWearableImage(name);
  if (!imageSource) {
    return null;
  }
  return (
    <Image src={imageSource} height={size} width={size} objectFit='contain' />
  );
};

const getWearableImage = (name: string) => {
  switch (name.toLowerCase()) {
    case 'applehealth':
    case 'apple':
      return require('@/assets/devices/square/apple.png');
    case 'fitbit':
      return require('@/assets/devices/square/fitbit.png');
    case 'garmin':
      return require('@/assets/devices/square/garmin.png');
    case 'polar':
      return require('@/assets/devices/square/polar.png');
    case 'suunto':
      return require('@/assets/devices/square/suunto.png');
    case 'wahoo':
      return require('@/assets/devices/square/wahoo.png');
    case 'healthconnect':
      return require('@/assets/devices/square/health-connect.png');
    case 'coros':
      return require('@/assets/devices/square/coros.png');
    // case 'oura':
    // return require('@/assets/devices/square/oura.png');
    case 'whoop':
      return require('@/assets/devices/square/whoop.png');
    // case 'withings':
    //   return require('@/assets/devices/square/withings.png');
    default:
      return null;
  }
};

export default WearableImage;
