import React from 'react';
import {<PERSON><PERSON>, FlatList} from 'react-native';
import {XStack, YStack, Image, Spinner} from 'tamagui';
import * as ImagePicker from 'expo-image-picker';
import {Camera, X} from '@tamagui/lucide-icons';

import I18nText from '@/src/components/I18nText';
import {triggerHaptics} from '@/src/utils/haptics';
import {bulkUploadMedia, uploadImage} from '@/src/utils/upload';
import logger from '@/src/utils/logger';

type Media = {
  id?: string;
  url: string;
};

interface MediaPickerProps {
  media: Media[];
  onMediasChange: (media: Media[]) => void;
  label?: string;
  maxImages?: number;
  allowsEditing?: boolean;
  canTakePhoto?: boolean;
}

const MediaPicker: React.FC<MediaPickerProps> = ({
  media,
  onMediasChange: onImagesChange,
  label,
  maxImages = 5,
  allowsEditing = false,
  canTakePhoto = true,
}) => {
  const [uploading, setUplaoding] = React.useState(false);
  const canAddMore = media.length < maxImages;

  logger.debug('media', media);

  const takePhotoWithCamera = async () => {
    setUplaoding(true);
    await triggerHaptics();

    // Request camera permissions
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

    if (!permissionResult.granted) {
      Alert.alert('Camera access is required to take a photo.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: allowsEditing,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      const imageUri = await uploadImage(result.assets[0], 'post');
      if (imageUri) {
        onImagesChange([...media]);
      }
    }
    setUplaoding(false);
  };

  const pickImageFromLibrary = async () => {
    setUplaoding(true);
    await triggerHaptics();

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: allowsEditing,
      quality: 0.8,
      allowsMultipleSelection: canAddMore && maxImages - media.length > 1,
      selectionLimit: maxImages - media.length,
    });

    if (!result.canceled) {
      const newImages = result.assets.map((asset) => asset);
      logger.debug('newImages', newImages);

      // Only add up to the maximum allowed
      const imagesToAdd = newImages.slice(0, maxImages - media.length);
      const newMedia = await bulkUploadMedia(imagesToAdd, 'post');

      onImagesChange([...media, ...newMedia]);
    }
    setUplaoding(false);
  };

  const removeImage = (index: number) => {
    const newImages = [...media];
    newImages.splice(index, 1);
    onImagesChange(newImages);
    //TODO if id exists, delete from db and s3 bucket
  };

  const showImagePickerOptions = async () => {
    await triggerHaptics();
    if (!canTakePhoto) {
      pickImageFromLibrary();
      return;
    }

    Alert.alert('Add Image', 'Choose an option', [
      {text: 'Take Photo', onPress: takePhotoWithCamera},
      {text: 'Choose from Library', onPress: pickImageFromLibrary},
      {text: 'Cancel', style: 'cancel'},
    ]);
  };

  const renderItem = ({item, index}: {item: Media; index: number}) => {
    return (
      <YStack marginRight={10} position='relative'>
        <Image
          source={{uri: item.url}}
          width={136}
          height={96}
          borderRadius={8}
          objectFit='cover'
        />
        <YStack
          position='absolute'
          top={5}
          right={5}
          backgroundColor='rgba(0,0,0,0.5)'
          borderRadius={15}
          width={24}
          height={24}
          justifyContent='center'
          alignItems='center'
          onPress={() => removeImage(index)}>
          <X size={14} color='$white' />
        </YStack>
      </YStack>
    );
  };
  return (
    <YStack gap='$2'>
      <FlatList
        data={media}
        renderItem={renderItem}
        keyExtractor={(_, index) => index.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
      />
      {uploading && (
        <YStack height={56} justifyContent='center'>
          <Spinner size='small' color='$primary' />
        </YStack>
      )}
      {canAddMore && !uploading && (
        <YStack
          borderWidth={1}
          height={56}
          width='100%'
          backgroundColor='$background'
          borderColor='$primary'
          borderStyle='dashed'
          onPress={showImagePickerOptions}
          justifyContent='center'
          alignItems='center'>
          <XStack alignItems='center' gap='$2'>
            <Camera size={24} color='$primary' />
            {label && <I18nText color='$primary'>{label}</I18nText>}
          </XStack>
        </YStack>
      )}
    </YStack>
  );
};

export default MediaPicker;
