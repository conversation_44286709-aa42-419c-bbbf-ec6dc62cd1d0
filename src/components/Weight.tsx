import React from 'react';
import convert from 'convert';
import {WeightUnit, useLocales} from '@/src/contexts/LocaleContext';
import StyledText from '@/src/components/UI/StyledText';

interface Props {
  value?: number | null;
  placeholder?: string;
}

const Weight: React.FC<Props> = ({value, placeholder}) => {
  const {locales} = useLocales();
  let display = placeholder;
  if (value) {
    const convertedValue = convert(value, 'grams').to(locales.weightUnit);
    if (locales.weightUnit === WeightUnit.Kilograms) {
      const kgWhole = Math.floor(value / 1000);
      let kgGrams = value % 1000;
      if (kgGrams) {
        kgGrams = Math.floor(kgGrams / 100);
      }
      display = kgGrams ? `${kgWhole}.${kgGrams} kg` : `${kgWhole} kg`;
    } else if (locales.weightUnit === WeightUnit.Pounds) {
      display = `${Math.round(convertedValue)} lbs`;
    } else if (locales.weightUnit === WeightUnit.Stones) {
      const weightInPounds = convert(value, 'grams').to('lb');
      const stones = Math.floor(weightInPounds / 14);
      const pounds = Math.round(weightInPounds % 14);
      display = `${stones}st ${pounds}lbs`;
    }
  }

  if (display) {
    return (
      <StyledText color='$grey' flex={1}>
        {display}
      </StyledText>
    );
  }
  return null;
};

export default Weight;
