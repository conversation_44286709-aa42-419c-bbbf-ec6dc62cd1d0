import React, {memo, useCallback, useEffect, useState} from 'react';
import {<PERSON><PERSON>, YStack} from 'tamagui';
import convert from 'convert';

import {useChallengeLeaderboardTeams} from '@/src/hooks/api/useChallengeLeaderboardTeams';
import useDebounce from '@/src/hooks/useDebounce';
import {
  ActivityTypeDto,
  ChallengeTeamsStandingsItemDto,
} from '@gojoe/typescript-sdk';
import {FlashList} from '@shopify/flash-list';
import {ChallengeEventType} from '@/src/utils/challenge';
import {DistanceUnit} from '@/src/contexts/LocaleContext';
import ListItemSeparator from '@/src/components/ListItemSeparator';
import {Leaderboard} from '@/src/components/AnimatedLeaderboard';
import {shuffleArray} from '@/src/utils/array';

import Item from './Item';
import Header from './Header';

const listItemSeparator = () => <ListItemSeparator size={4} />;
interface Props {
  challengeId: string;
  columnType: ChallengeEventType;
  unit: DistanceUnit;
}

const ChallengeStandings: React.FC<Props> = ({
  challengeId,
  columnType,
  unit,
}) => {
  const [q, setQ] = useState<string>('');
  const searchDebounce = useDebounce(q, 500);
  const [activityTypes, setActivityTypes] = useState<ActivityTypeDto[]>([]);
  const [withFlaggedUsers, setWithFlaggedUsers] = useState(false);
  const [top, setTop] = useState<Leaderboard[]>([]);
  const {data, isLoading, isRefetching, refetch, isFetchingNextPage} =
    useChallengeLeaderboardTeams({
      challengeId: challengeId,
      sports: activityTypes.map((s: ActivityTypeDto) => s.name),
      name: searchDebounce,
      userBelongsToTeam: false,
      withFlaggedUsers,
    });

  useEffect(() => {
    if (data.length === 0 || top.length > 0) return;

    const sliced = data.slice(0, 7);
    const shuffled = shuffleArray(sliced);

    const items = shuffled.map((item) => ({
      id: item.team.id,
      name: item.team.name,
      avatar: item.team.logo,
      score:
        columnType === ChallengeEventType.Distance
          ? convert(item.distance ?? 0, 'm').to(unit)
          : (item.points ?? 0),
    }));

    setTop(items);
  }, [data, top.length, columnType, unit]);

  const renderItem = useCallback(
    ({item}: {item: ChallengeTeamsStandingsItemDto}) => {
      return <Item item={item} columnType={columnType} unit={unit} />;
    },
    [columnType, unit],
  );

  const listHeaderComponent = useCallback(() => {
    if (isLoading) {
      return <Spinner marginTop='$5' />;
    }
    return <TeamHeader unit={unit} columnType={columnType} leaderboard={top} />;
  }, [columnType, isLoading, top, unit]);

  const listFooterComponent = useCallback(() => {
    if (isFetchingNextPage) {
      return <Spinner marginVertical='$3' />;
    }
    return <YStack height={48} />;
  }, [isFetchingNextPage]);

  return (
    <FlashList
      data={data}
      renderItem={renderItem}
      estimatedItemSize={56}
      showsVerticalScrollIndicator={false}
      ItemSeparatorComponent={listItemSeparator}
      ListHeaderComponent={listHeaderComponent}
      ListFooterComponent={listFooterComponent}
      refreshing={isRefetching}
      onRefresh={refetch}
    />
  );
};

export default memo(ChallengeStandings);
