import {memo} from 'react';
import {YStack, XStack, Text, YStackProps} from 'tamagui';
import convert from 'convert';
import I18nText from '@/src/components/I18nText';
import StyledText from '@/src/components/UI/StyledText';

const EARTH_CIRCUMFERENCE_KM = 40075;

function getDistanceComparison(distanceMeters: number) {
  const distanceKm = convert(distanceMeters, 'm').to('km');

  const options = [
    {
      i18nKey: 'distanceComparison.earthTimes',
      i18nParams: {count: Math.floor(distanceKm / EARTH_CIRCUMFERENCE_KM)},
      icon: '🌍',
    },
    {
      i18nKey: 'distanceComparison.moonTrips',
      i18nParams: {count: Math.floor(distanceKm / 768800)},
      icon: '🌕',
    },
    {
      i18nKey: 'distanceComparison.atlanticCrossings',
      i18nParams: {count: Math.floor(distanceKm / 5600)},
      icon: '🌊',
    },
    {
      i18nKey: 'distanceComparison.everestClimbs',
      i18nParams: {count: Math.floor(distanceKm / 8.848)},
      icon: '🏔️',
    },
    {
      i18nKey: 'distanceComparison.amazonTreks',
      i18nParams: {count: Math.floor(distanceKm / 6400)},
      icon: '🌳',
    },
    {
      i18nKey: 'distanceComparison.tourDeFrance',
      i18nParams: {count: Math.floor(distanceKm / 3500)},
      icon: '🚴‍♂️',
    },
  ];

  // Only return comparisons with count > 0
  const validOptions = options.filter((option) => option.i18nParams.count > 0);

  if (validOptions.length === 0) return null;

  return validOptions[Math.floor(Math.random() * validOptions.length)];
}

type Props = YStackProps & {
  distanceMeters: number;
  unit: 'km' | 'mi';
};

const DistanceInsightCard = ({distanceMeters, unit, ...props}: Props) => {
  const distanceConverted = convert(distanceMeters, 'm').to(unit);
  const distanceFormatted = `${Intl.NumberFormat('en-US', {
    // notation: 'compact',
    // compactDisplay: 'short',
    maximumFractionDigits: 0,
  }).format(distanceConverted)}`;
  const comparison = getDistanceComparison(distanceMeters);

  return (
    <YStack
      backgroundColor='$background'
      padding='$5'
      gap={16}
      borderRadius={16}
      borderWidth={1}
      borderColor='$grey3'
      width='100%'
      {...props}>
      <XStack justifyContent='space-between'>
        <I18nText fontSize={18} fontWeight='600' color='$color'>
          insights.distance
        </I18nText>
        <YStack alignItems='flex-end'>
          <StyledText fontSize={18} fontWeight='700' color='$color'>
            {distanceFormatted}
          </StyledText>
          <I18nText fontSize={12} color='$grey2' fontWeight='500' marginTop={2}>
            {unit === 'mi' ? 'insights.miles' : 'insights.kilometers'}
          </I18nText>
        </YStack>
      </XStack>

      {comparison && (
        <XStack alignItems='center' gap={8}>
          <Text fontSize={48} color='$color'>
            {comparison.icon}
          </Text>
          <I18nText
            flex={1}
            color='$grey1'
            fontWeight='500'
            lineHeight={18}
            i18nParams={comparison.i18nParams}>
            {comparison.i18nKey}
          </I18nText>
        </XStack>
      )}
    </YStack>
  );
};

export default memo(DistanceInsightCard);
