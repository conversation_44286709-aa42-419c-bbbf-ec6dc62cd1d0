import React from 'react';
import Animated, {
  FadeInRight,
  interpolate,
  interpolateColor,
  runOnJS,
  SharedValue,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withDelay,
  withSpring,
} from 'react-native-reanimated';
import {XStack, YStack} from 'tamagui';

import CustomAvatar from '@/src/components/UI/Avatar/Avatar';

export type Leaderboard = {
  id: string;
  name: string;
  avatar?: string;
  score: number;
};

// Constants
const _avatarSize = 28;
const _spacing = 4;
const _staggerDuration = 50;
const _initialDelayDuration = 0;
// The height of the container where the bars are going to be rendered.
const _containerSize = 150;

// Re-maps a number from one range to another.
// https://processing.org/reference/map_.html
function mapRange(
  value: number,
  low1: number,
  high1: number,
  low2: number,
  high2: number,
) {
  return low2 + ((high2 - low2) * (value - low1)) / (high1 - low1);
}

type PlaceProps = {
  item: Leaderboard;
  index: number;
  onFinish?: () => void;
  minMax: number[];
  anim: SharedValue<number>;
};

function Place({item, onFinish, index, minMax, anim}: PlaceProps) {
  const _height = mapRange(
    item.score,
    minMax[0],
    minMax[1],
    _spacing * 4,
    _containerSize - _avatarSize,
  );

  const _anim = useDerivedValue(() => {
    return withDelay(
      _staggerDuration * index,
      withSpring(anim.value, {damping: 80, stiffness: 200}),
    );
  });

  const stylez = useAnimatedStyle(() => {
    const height = _anim.value * _height + _avatarSize + _spacing;
    const radius = interpolate(_anim.value, [0, 1], [0, 8]);
    let backgroundColor = 'rgba(0,0,0,0.05)';
    if (minMax[1] === item.score) {
      backgroundColor = interpolateColor(
        _anim.value,
        [0, 1],
        ['rgba(0,0,0,0.05)', 'gold'],
      );
    }
    return {
      height: height,
      borderBottomLeftRadius: radius,
      borderBottomRightRadius: radius,
      backgroundColor: backgroundColor,
    };
  });

  // Only used for the text to be displayed after the animation has started.
  // We're going to run the animation after 20% of the total duration.
  const textStylez = useAnimatedStyle(() => {
    return {
      opacity: interpolate(_anim.value, [0, 0.2, 1], [0, 0, 1]),
    };
  });

  return (
    <Animated.View
      entering={FadeInRight.delay(
        _staggerDuration * index + _initialDelayDuration,
      )
        .springify()
        .damping(80)
        .stiffness(200)
        .withCallback((finished) => {
          if (finished && onFinish) {
            // Reminder, we're running Layout animations on the UI thread so we need to use runOnJS.
            runOnJS(onFinish)();
          }
        })}
      style={{alignItems: 'center'}}>
      <Animated.View
        style={[
          {
            backgroundColor: 'rgba(0,0,0,0.1)',
            padding: _spacing / 2,
            borderRadius: _avatarSize / 2 + _spacing,
            gap: _spacing / 2,
            alignItems: 'center',
          },
          stylez,
        ]}>
        <YStack
          width={_avatarSize}
          aspectRatio={1}
          borderRadius={_avatarSize / 2}
          borderWidth={1}
          borderColor='$white1'
          padding={_spacing / 4}>
          <CustomAvatar
            id={item.id}
            name={item.name}
            avatar={item.avatar}
            circular
            size={_avatarSize - 4}
          />
        </YStack>
      </Animated.View>
    </Animated.View>
  );
}

interface Props {
  items: Leaderboard[];
}
const AnimatedLeaderBoard: React.FC<Props> = ({items}) => {
  // Find the min and max score of the users
  const minMaxScoreOfUsers = items.reduce(
    (acc, user) => {
      if (user.score < acc[0]) {
        acc[0] = user.score;
      }
      if (user.score > acc[1]) {
        acc[1] = user.score;
      }
      return acc;
    },
    [Infinity, -Infinity],
  );

  const _anim = useSharedValue(0);

  return (
    <XStack
      gap={16}
      alignItems='flex-end'
      justifyContent='center'
      height={_containerSize}>
      {items.map((item, index) => (
        <Place
          key={index}
          item={item}
          index={index}
          minMax={minMaxScoreOfUsers}
          anim={_anim}
          onFinish={
            // When last has finished, we'd like to kick the animation
            // on the bars.
            index === items.length - 1
              ? () => {
                  _anim.value = 1;
                }
              : undefined
          }
        />
      ))}
    </XStack>
  );
};

export default AnimatedLeaderBoard;
