import React from 'react';
import {XStack, YStack} from 'tamagui';
import {BusinessListDto} from '@gojoe/typescript-sdk';

import BusinessAvatar from '@/src/components/UI/Avatar/BusinessAvatar';
import StyledText from '@/src/components/UI/StyledText';

interface Props {
  data: BusinessListDto[];
  onPress?: (business: BusinessListDto) => Promise<void>;
}
const Businesses: React.FC<Props> = ({data, onPress}) => {
  return (
    <YStack>
      {data.splice(0, 3).map((business) => {
        const handlePressBusiness = async () =>
          onPress ? onPress(business) : undefined;
        return (
          <XStack
            onPress={handlePressBusiness}
            key={business.id}
            justifyContent='flex-start'
            alignItems='center'
            gap={8}
            marginTop={16}>
            <BusinessAvatar
              business={{
                id: business.id,
                name: business.name,
                logo: business.avatar,
              }}
              size={32}
              borderRadius={4}
            />
            <StyledText numberOfLines={1} fontWeight='700'>
              {business.name}
            </StyledText>
          </XStack>
        );
      })}
    </YStack>
  );
};

export default Businesses;
