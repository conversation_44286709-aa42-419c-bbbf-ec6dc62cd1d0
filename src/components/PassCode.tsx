import React from 'react';
import {XStack, XStackProps} from 'tamagui';
import {Barcode} from '@tamagui/lucide-icons';
import I18nText from '@/src/components/I18nText';

const PassCode: React.FC<XStackProps> = ({...props}) => {
  return (
    <XStack
      backgroundColor='$background'
      borderWidth={1}
      borderStyle='dashed'
      borderColor='$primary'
      height={56}
      borderRadius={16}
      alignItems='center'
      justifyContent='center'
      gap={8}
      paddingHorizontal={4}
      {...props}>
      <Barcode color='$primary' size={16} />
      <I18nText fontWeight='bold' color='$primary'>
        Have a code? Add Code
      </I18nText>
    </XStack>
  );
};

export default PassCode;
