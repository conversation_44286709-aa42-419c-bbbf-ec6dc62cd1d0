import React from 'react';
import {Dimensions, Pressable} from 'react-native';
import {Image as ExpoImage} from 'expo-image';
import {MediaListDto} from '@gojoe/typescript-sdk';
import {useRouter} from 'expo-router';
import {YStack, XStack} from 'tamagui';
import {Video as VideoIcon} from '@tamagui/lucide-icons';

interface Props {
  attachments: MediaListDto[];
}

const MediaGallery: React.FC<Props> = ({attachments}) => {
  const router = useRouter();
  const screenWidth = Dimensions.get('window').width;
  const horizontalPadding = 32 + 48; // 16px left + 16px right + 24px padding
  const spacing = 8; // assuming $2 gap

  const fullWidth = screenWidth - horizontalPadding;
  const isSingle = attachments.length === 1;

  if (!attachments?.length) return null;

  return (
    <XStack
      flexWrap='wrap'
      gap='$2'
      marginTop='$3'
      justifyContent={isSingle ? 'center' : 'flex-start'}>
      {attachments.map((item) => {
        const isVideo = item.type === 'VIDEO';

        const width = isSingle ? fullWidth : (fullWidth - 4) / 2;
        const height =
          item.orientation === 'PORTRAIT'
            ? width * 1.4
            : isSingle
              ? width * 0.75
              : width * 0.7;

        return (
          <Pressable
            key={item.id}
            onPress={() =>
              isVideo
                ? router.push({
                    pathname: '/video',
                    params: {uri: item.uri},
                  })
                : null
            }>
            <YStack position='relative'>
              <ExpoImage
                source={{uri: item.uri}}
                contentFit='cover'
                style={{
                  width,
                  height,
                  borderRadius: 12,
                  alignSelf: 'center',
                  flexShrink: 1,
                }}
              />
              {isVideo && (
                <YStack
                  position='absolute'
                  top={0}
                  left={0}
                  right={0}
                  bottom={0}
                  justifyContent='center'
                  alignItems='center'
                  backgroundColor='rgba(0,0,0,0.25)'
                  borderRadius={12}>
                  <VideoIcon size={28} color='white' />
                </YStack>
              )}
            </YStack>
          </Pressable>
        );
      })}
    </XStack>
  );
};

export default MediaGallery;
