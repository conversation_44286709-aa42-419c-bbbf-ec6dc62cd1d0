import React, {memo} from 'react';
import {YStack, Text, XStack} from 'tamagui';
import {PostListDto} from '@gojoe/typescript-sdk';
import {DistanceUnit, useLocales} from '@/src/contexts/LocaleContext';
import PostActivity from '../PostActivity';
import PostMessage from './Message';
import PostMedia from '../PostMedia';
import PostHeader from '../PostHeader';
import PostFooter from '../PostFooter';
import PostReported from '../PostReported';
import {useSession} from '@/src/contexts/SessionContext';

interface Props {
  post: PostListDto;
  unit?: DistanceUnit;
  languageTag?: string;
}

const PostCard: React.FC<Props> = ({post, languageTag}) => {
  const {user} = useSession();
  const {locales} = useLocales();
  const isActivity = post.type === 'ACTIVITY';
  const hasMedia = post.attachments?.length > 0 || !!post.cover;
  const distanceUnit = locales.distanceUnit;

  return (
    <YStack>
      {post.business && (
        <XStack
          flex={1}
          height={24}
          backgroundColor={post.business?.color ?? '$primary'}
          borderTopRightRadius={12}
          borderTopLeftRadius={12}
          opacity={0.2}
        />
      )}
      <YStack
        borderBottomRightRadius={12}
        borderBottomLeftRadius={12}
        borderTopRightRadius={post.business ? 0 : 12}
        borderTopLeftRadius={post.business ? 0 : 12}
        borderWidth={1}
        borderColor='$grey3'
        padding='$3.5'
        backgroundColor='$background'>
        <PostHeader post={post} languageTag={languageTag} />

        {isActivity && post.activity && distanceUnit && (
          <PostActivity
            postId={post.id}
            activity={post.activity}
            unit={distanceUnit}
          />
        )}

        {!!post.caption && (
          <Text marginTop='$3' fontWeight='700'>
            {post.caption}
          </Text>
        )}

        {!!post.message && (
          <PostMessage message={post.message} formatting={post.formatting} />
        )}

        {hasMedia && (
          <YStack>
            <PostMedia post={post} />
          </YStack>
        )}

        {post.reported && (
          <PostReported
            isInternal={post.user?.id === user?.id}
            isActivity={!!post.activity}
          />
        )}

        <PostFooter post={post} />
      </YStack>
    </YStack>
  );
};

export default memo(PostCard, (prev, next) => {
  return (
    prev.post.id === next.post.id &&
    prev.post.reported === next.post.reported &&
    prev.post.reactionsCountTotal === next.post.reactionsCountTotal &&
    JSON.stringify(prev.post.myReactions) ===
      JSON.stringify(next.post.myReactions)
  );
});
