import React from 'react';
import {Stack} from 'expo-router';

const RootLayout: React.FC = () => {
  return (
    <Stack
      initialRouteName='(app)'
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen name='(app)' options={{animation: 'default'}} />
      <Stack.Screen name='sign-in' options={{animation: 'fade'}} />
      <Stack.Screen name='sign-in-email' />
      <Stack.Screen name='onboarding' />
      <Stack.Screen
        name='(modals)'
        options={{
          presentation: 'modal',
        }}
      />
    </Stack>
  );
};

export default RootLayout;
