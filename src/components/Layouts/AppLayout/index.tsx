import React from 'react';

import {Drawer} from 'expo-router/drawer';
import {DrawerContentComponentProps} from '@react-navigation/drawer';
import DrawerContent from '../../DrawerContent';

const drawerContent = (props: DrawerContentComponentProps) => (
  <DrawerContent {...props} />
);

const AppLayout: React.FC = () => {
  return (
    <Drawer
      drawerContent={drawerContent}
      initialRouteName='(tabs)'
      screenOptions={{
        headerShown: false,
      }}>
      <Drawer.Screen
        name='(tabs)'
        options={{
          title: '',
        }}
      />
    </Drawer>
  );
};

export default AppLayout;
