import React, {memo} from 'react';
import type {IconProps} from '@tamagui/helpers-icon';
import {
  BanditIcon,
  EagerBeaverIcon,
  EarlyBirdIcon,
  NightOwlIcon,
  SlammerIcon,
  WoodpeckerIcon,
} from './GoJoeIcon';

interface Props extends IconProps {
  name: string;
}
const AwardIcon: React.FC<Props> = ({name, ...props}) => {
  switch (name.toLowerCase()) {
    case 'woodpecker':
      return <WoodpeckerIcon {...props} />;
    case 'early bird':
      return <EarlyBirdIcon {...props} />;
    case 'night owl':
      return <NightOwlIcon {...props} />;
    case 'eager beaver':
      return <EagerBeaverIcon {...props} />;
    case 'bandit':
      return <BanditIcon {...props} />;
    // case 'cat cow':
    //   return <CatCowIcon  {...props} />;
    // case 'lifter':
    //   return <LifterIcon  {...props} />;
    // case 'cool boarder':
    //   return <CoolBoarderIcon  {...props} />;
    // case 'big hiiter':
    //   return <BigHIITerIcon  {...props} />;
    // case 'yellow jersey':
    //   return <YellowJerseyIcon  {...props} />;
    // case 'road runner':
    //   return <RoadRunnerIcon  {...props} />;
    // case 'postie':
    //   return <PostieIcon  {...props} />;
    // case 'goldfish':
    // return <GoldfishIcon  {...props} />;

    case 'slammer':
    default:
      return <SlammerIcon {...props} />;
  }
};

export default memo(AwardIcon);
