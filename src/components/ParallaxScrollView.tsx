import type {PropsWithChildren, ReactElement} from 'react';
import {StyleSheet} from 'react-native';
import Animated, {
  interpolate,
  useAnimatedRef,
  useAnimatedStyle,
  useScrollViewOffset,
} from 'react-native-reanimated';
import {YStack} from 'tamagui';

// import {useBottomTabOverflow} from '@/src/components/UI/TabBarBackground.ios';

type Props = PropsWithChildren<{
  headerImage: ReactElement;
  height?: number;
  backgroundColor?: string;
}>;

export default function ParallaxScrollView({
  children,
  headerImage,
  height = 220,
  backgroundColor = '$background',
}: Props) {
  const scrollRef = useAnimatedRef<Animated.ScrollView>();
  const scrollOffset = useScrollViewOffset(scrollRef);

  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: interpolate(
            scrollOffset.value,
            [-height, 0, height],
            [-height / 2, 0, height * 0.75],
          ),
        },
        {
          scale: interpolate(
            scrollOffset.value,
            [-height, 0, height],
            [2, 1, 1],
          ),
        },
      ],
    };
  });

  return (
    <YStack backgroundColor={backgroundColor} flex={1}>
      <Animated.ScrollView
        ref={scrollRef}
        scrollEventThrottle={16}
        contentContainerStyle={{flexGrow: 1, backgroundColor}}
        showsVerticalScrollIndicator={false}>
        <Animated.View style={[styles.header, headerAnimatedStyle, {height}]}>
          {headerImage}
        </Animated.View>
        <YStack backgroundColor={backgroundColor} flex={1}>
          {children}
        </YStack>
      </Animated.ScrollView>
    </YStack>
  );
}

const styles = StyleSheet.create({
  header: {
    overflow: 'hidden',
  },
});
