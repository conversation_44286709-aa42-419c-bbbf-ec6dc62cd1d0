import useBusinessStats from '@/src/hooks/api/useBusinessStats';
import React from 'react';
import {XStack, YStack} from 'tamagui';
import StyledText from '../UI/StyledText';
import {Flag, Users2} from '@tamagui/lucide-icons';
import I18nText from '../I18nText';

interface Props {
  businessId: string;
}

const BusinessStats: React.FC<Props> = ({businessId}) => {
  const {data} = useBusinessStats(businessId);

  if (!data) {
    return null;
  }
  return (
    <YStack gap='$3.5' marginTop='$5'>
      <XStack alignItems='center' justifyContent='space-between'>
        <XStack alignItems='center' gap='$2'>
          <Users2 color='$grey2' size={24} />
          <I18nText fontSize={12} fontWeight={500} color='#2F3542'>
            Active members
          </I18nText>
        </XStack>
        <StyledText fontSize={12} fontWeight={500} color='#2F3542'>
          {data.usersCount ?? 0}
        </StyledText>
      </XStack>

      <XStack alignItems='center' justifyContent='space-between'>
        <XStack alignItems='center' gap='$2'>
          <Flag color='$grey2' size={24} />
          <I18nText fontSize={12} fontWeight={500} color='#2F3542'>
            Challenges
          </I18nText>
        </XStack>
        <StyledText fontSize={12} fontWeight={500} color='#2F3542'>
          {data.usersCount ?? 0}
        </StyledText>
      </XStack>
    </YStack>
  );
};

export default BusinessStats;
