import React, {memo, useState} from 'react';
import {ImageBackground} from 'expo-image';
import {useRouter} from 'expo-router';
import {StyleSheet} from 'react-native';
import {XStack, YStack} from 'tamagui';
import {AlertTriangle} from '@tamagui/lucide-icons';
import {BusinessListDto} from '@gojoe/typescript-sdk';

import {handleCacheJoinBusiness} from '@/src/services/queryClient';
import {useSession} from '@/src/contexts/SessionContext';
import {triggerHaptics} from '@/src/utils/haptics';
import useMutationJoinBusiness from '@/src/hooks/api/useMutationJoinBusiness';
import I18nText from '@/src/components/I18nText';
import BusinessAvatar from '../UI/Avatar/BusinessAvatar';
import StyledText from '../UI/StyledText';
import BusinessStats from './BusinessStats';
import StyledButton from '../UI/Button';

interface Props {
  business: BusinessListDto;
  passcode: string;
}

const PasscodeBusiness: React.FC<Props> = ({business, passcode}) => {
  const {user, selectBusiness} = useSession();
  const router = useRouter();
  const {mutateAsync, isPending} = useMutationJoinBusiness(
    business.id,
    passcode,
    handleCacheJoinBusiness(business, user),
  );
  const [hasJoined, setHasJoined] = useState(
    (business.users && business.users.length > 0) || false,
  );

  const cover = business.cover
    ? {uri: business.cover}
    : require('@/assets/images/business_cover.jpg');

  const handleJoinBusiness = async () => {
    await triggerHaptics();
    await mutateAsync();
    setHasJoined(true);

    selectBusiness({
      id: business.id,
      name: business.name,
      logo: business.avatar,
    });

    // First go back to close the modal
    router.back();

    // Then navigate to the business screen after a short delay
    // This ensures the modal is fully closed before navigation
    setTimeout(() => {
      router.push({
        pathname: '/business/[businessId]',
        params: {
          businessId: business.id,
        },
      });
    }, 100);
  };

  const goToBusiness = async () => {
    await triggerHaptics();

    // First go back to close the modal
    router.back();

    // Then navigate to the business screen after a short delay
    // This ensures the modal is fully closed before navigation
    setTimeout(() => {
      router.push({
        pathname: '/business/[businessId]',
        params: {
          businessId: business.id,
        },
      });
    }, 100);
  };

  return (
    <YStack>
      <I18nText
        fontSize={20}
        fontWeight='600'
        textAlign='center'
        marginBottom='$3.5'>
        The code belongs to a Community
      </I18nText>
      <YStack
        backgroundColor='$background'
        borderRadius={16}
        overflow='hidden'
        marginBottom='$3.5'>
        <ImageBackground source={cover} style={styles.cover} />
        <YStack paddingHorizontal='$3.5' marginTop='$-5' paddingBottom='$3.5'>
          <BusinessAvatar business={business} size={48} borderRadius={8} />

          <StyledText
            fontSize={14}
            fontWeight={700}
            marginTop='$2'
            numberOfLines={1}>
            {business.name}
          </StyledText>

          {hasJoined && <BusinessStats businessId={business.id} />}
        </YStack>
      </YStack>
      {hasJoined ? (
        <YStack>
          <XStack
            alignItems='flex-start'
            backgroundColor='#FFF5D2'
            padding='$3.5'
            borderRadius={8}
            gap='$2'>
            <AlertTriangle color='#2F3542' size={16} />
            <YStack gap='$1.5'>
              <I18nText fontWeight={500} color='#2F3542'>
                You are already part of the business.
              </I18nText>
              <I18nText color='#2F3542'>
                Press Go to Business button to go to the business screen.
              </I18nText>
            </YStack>
          </XStack>

          <StyledButton variant='primary' marginTop='$5' onPress={goToBusiness}>
            <I18nText color='$white1'>Go to Business</I18nText>
          </StyledButton>
        </YStack>
      ) : (
        <StyledButton
          variant='primary'
          onPress={handleJoinBusiness}
          loading={isPending}>
          <I18nText color='$white1'>Join Business</I18nText>
        </StyledButton>
      )}
    </YStack>
  );
};

export default memo(PasscodeBusiness);

const styles = StyleSheet.create({
  cover: {
    width: '100%',
    height: 80,
    padding: 16,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
});
