import React, {memo} from 'react';
import {StyleSheet} from 'react-native';
import {ChallengeListDto} from '@gojoe/typescript-sdk';
import {XStack, YStack} from 'tamagui';
import StyledText from '@/src/components/UI/StyledText';
import {ImageBackground} from 'expo-image';
import BusinessAvatar from '@/src/components/UI/Avatar/BusinessAvatar';
import I18nText from '@/src/components/I18nText';
import {challengeDate} from '@/src/utils/challenge';
import {useLocales} from '@/src/contexts/LocaleContext';

interface Props {
  challenge: ChallengeListDto;
  onPress?: () => void;
}
const ChallengeList: React.FC<Props> = ({challenge, onPress}) => {
  const {locales} = useLocales();
  const cover = challenge.cover
    ? {uri: challenge.cover}
    : require('@/assets/images/challenge_cover.jpg');

  return (
    <YStack backgroundColor='$background' borderRadius={16} overflow='hidden'>
      <ImageBackground source={cover} style={styles.cover}>
        {challenge.business && (
          <XStack
            height={24}
            gap={4}
            backgroundColor='$white1'
            borderRadius={12}
            alignItems='center'
            paddingHorizontal={8}
            alignSelf='flex-start'>
            <BusinessAvatar
              business={challenge.business}
              size={16}
              borderRadius={8}
            />
            <StyledText fontSize={12} fontWeight='500'>
              {challenge.business.name}
            </StyledText>
          </XStack>
        )}
      </ImageBackground>
      <YStack padding='$3.5'>
        <StyledText fontSize={18} fontWeight='700' numberOfLines={1}>
          {challenge.name}
        </StyledText>
        <StyledText fontSize={12} fontWeight='700' marginTop={8}>
          {challengeDate(
            challenge.startDate,
            challenge.endDate,
            locales.timezone,
          )}
        </StyledText>
      </YStack>
      <YStack
        height={48}
        justifyContent='center'
        alignItems='center'
        backgroundColor='$primary'
        onPress={onPress}>
        <I18nText color='$white1' fontWeight='700'>
          Join Challenge
        </I18nText>
      </YStack>
    </YStack>
  );
};

const styles = StyleSheet.create({
  cover: {
    width: '100%',
    height: 220,
    padding: 16,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
});

export default memo(ChallengeList);
