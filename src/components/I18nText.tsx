import React from 'react';
import {Text as TamaguiText, TextProps as TamaguiTextProps} from 'tamagui';
import {useTranslation} from 'react-i18next';

interface Props extends TamaguiTextProps {
  i18nParams?: Record<string, any>;
}

const I18nText: React.FC<Props> = ({i18nParams, children, ...props}) => {
  const {t} = useTranslation();
  const content =
    typeof children === 'string' ? t(children, i18nParams) : children;

  return (
    <TamaguiText allowFontScaling={false} color='$color' {...props}>
      {content}
    </TamaguiText>
  );
};

export default I18nText;
