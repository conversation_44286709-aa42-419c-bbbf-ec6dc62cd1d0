import React from 'react';
import {StyleSheet} from 'react-native';
import {JourneyDto} from '@gojoe/typescript-sdk';
import Animated, {
  Extrapolation,
  interpolate,
  SharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';
import {Dimensions} from 'react-native';
import I18nText from '@/src/components/I18nText';
import {ImageBackground} from 'expo-image';
import {XStack, YStack} from 'tamagui';
import {BarChart, CalendarDays, CheckSquare} from '@tamagui/lucide-icons';
import StyledText from '@/src/components/UI/StyledText';
import {useTranslation} from 'react-i18next';
import {journeyLevel} from '../utils/helper';

interface Props {
  journey: JourneyDto;
  index: number;
  totalLength: number;
  activeIndex: SharedValue<number>;
}
const {width} = Dimensions.get('window');
const maxVisibleItems = 6;
const _size = width * 0.9;
const layout = {
  borderRadius: 16,
  width: _size,
  height: _size * 1.27,
  spacing: 12,
  cardsGap: 22,
};
const colors = {
  primary: '#6667AB',
  light: '#fff',
  dark: '#111',
};
const JourneyCard: React.FC<Props> = ({
  journey,
  index,
  totalLength,
  activeIndex,
}) => {
  const {t} = useTranslation();
  const animatedStyles = useAnimatedStyle(() => {
    return {
      position: 'absolute',
      zIndex: totalLength - index,
      opacity: interpolate(
        activeIndex.value,
        [index - 1, index, index + 1],
        [1 - 1 / maxVisibleItems, 1, 1],
      ),
      transform: [
        {
          translateY: interpolate(
            activeIndex.value,
            [index - 1, index, index + 1],
            [-layout.cardsGap, 0, layout.height - layout.cardsGap * 2],
            {
              // If you'd like to stack the bottom cards on top of eachother
              // add CLAMP instead of EXTEND.
              // extrapolateRight: Extrapolate.CLAMP,
              extrapolateRight: Extrapolation.EXTEND,
            },
          ),
        },
        {
          scale: interpolate(
            activeIndex.value,
            [index - 1, index, index + 1],
            [0.95, 1, 1],
          ),
        },
      ],
    };
  });
  return (
    <Animated.View style={[styles.card, animatedStyles]}>
      <ImageBackground
        source={{uri: journey.cover}}
        style={{width: '100%', height: '100%'}}>
        <YStack flex={1} padding='$4' backgroundColor='#00000075'>
          <I18nText color='$white1' fontWeight='600' fontSize={36}>
            {journey.name}
          </I18nText>
          <YStack marginTop='$4' gap='$3'>
            {journey.length > 0 && (
              <XStack alignItems='center' gap='$2'>
                <CalendarDays color='$white1' />
                <StyledText color='$white1'>
                  {t('dayWithCount', {count: journey.length})}
                </StyledText>
              </XStack>
            )}
            <XStack alignItems='center' gap='$2'>
              <BarChart color='$white1' />
              <StyledText color='$white1'>
                {t(journeyLevel(journey.level))}
              </StyledText>
            </XStack>
            <XStack alignItems='center' gap='$2'>
              <CheckSquare color='$white1' />
              <StyledText color='$white1'>
                {t('{{count}} started', {count: journey.totalUsers})}
              </StyledText>
            </XStack>
          </YStack>
        </YStack>
      </ImageBackground>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: layout.borderRadius,
    overflow: 'hidden',
    width: layout.width,
    height: layout.height,
    backgroundColor: colors.light,
  },
});
export default JourneyCard;
