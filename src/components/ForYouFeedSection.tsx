import React from 'react';
import {<PERSON>rollView, XStack, YStack, YStackProps, Image, Circle} from 'tamagui';
import I18nText from './I18nText';
import {ArrowRight} from '@tamagui/lucide-icons';
import {Trans} from 'react-i18next';
import StyledText from './UI/StyledText';
import {triggerHaptics} from '@/src/utils/haptics';
import {SheetManager} from 'react-native-actions-sheet';
import useUserInsightsOverview from '../hooks/api/useUserInsightsOverview';
import {UserProfileDto} from '@gojoe/typescript-sdk';

interface Props extends YStackProps {
  user: UserProfileDto;
}

const ForYouFeedSection: React.FC<Props> = ({user, ...props}) => {
  const {data: userInsightsOverview} = useUserInsightsOverview(user.id);

  const handlePressStreak = async () => {
    await triggerHaptics();
    await SheetManager.show('streak', {
      payload: {
        count: userInsightsOverview ? userInsightsOverview.currentStreak : 0,
      },
    });
  };

  const handlePressQuote = async () => {
    await triggerHaptics();
    await SheetManager.show('quote');
  };

  return (
    <YStack {...props}>
      <I18nText
        fontSize={12}
        fontWeight='700'
        color='$grey1'
        paddingHorizontal={'$5'}>
        For You
      </I18nText>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        marginTop='$3.5'>
        <XStack gap='$3' paddingHorizontal={'$5'}>
          {userInsightsOverview && (
            <YStack
              onPress={handlePressStreak}
              width={172}
              height={120}
              padding='$3.5'
              justifyContent='space-between'>
              <I18nText color='$white' opacity={0.4}>
                Streak
              </I18nText>
              <XStack gap='$3' alignItems='flex-end'>
                <StyledText color='$white' fontWeight='600' flex={1}>
                  <Trans
                    i18nKey="You're on a <b>{{count}}-day</b> GoJoe streak"
                    components={{
                      b: (
                        <I18nText
                          color='$yellow'
                          fontWeight='700'
                          fontStyle='italic'
                          flex={1}
                        />
                      ),
                    }}
                    count={userInsightsOverview.currentStreak}
                  />
                </StyledText>
                <Circle size={24} backgroundColor='$white1'>
                  <ArrowRight size={14} color='$primary' />
                </Circle>
              </XStack>
              <Image
                position='absolute'
                borderRadius='$4'
                width={172}
                height={120}
                objectFit='cover'
                zIndex={-1}
                source={require('@/assets/images/streak_small_bg.png')}
              />
            </YStack>
          )}

          <YStack
            onPress={handlePressQuote}
            width={172}
            height={120}
            justifyContent='flex-end'
            padding='$3.5'>
            <XStack gap='$3' alignItems='flex-end'>
              <I18nText color='$white' fontWeight='700' flex={1}>
                Quote of the day
              </I18nText>
              <Circle size={24} backgroundColor='$white1'>
                <ArrowRight size={14} color='$primary' />
              </Circle>
            </XStack>
            <Image
              position='absolute'
              borderRadius='$4'
              width={172}
              height={120}
              objectFit='cover'
              zIndex={-1}
              source={require('@/assets/images/qotd_small_bg.jpg')}
            />
          </YStack>
        </XStack>
      </ScrollView>
    </YStack>
  );
};

export default ForYouFeedSection;
