import React, {memo} from 'react';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import type {ParamListBase} from '@react-navigation/native';
import {useTheme} from 'tamagui';
import {NAVIGATION_HEADER_HEIGHT} from '@/src/constants/navigation';
import I18nText from '@/src/components/I18nText';

const Tab = createMaterialTopTabNavigator();

const StyledTabNavigator: React.FC<
  ParamListBase & {
    children: React.ReactNode;
    rightElement?: React.ReactNode;
  }
> = ({children, rightElement, ...props}) => {
  const theme = useTheme();
  const color = theme.color ? theme.color.get() : '#000000';
  const inactiveColor = theme.grey1 ? theme.grey1.get() : '#656874';
  const backgroundColor = theme.background ? theme.background.get() : 'black';
  return (
    <>
      <Tab.Navigator
        {...props}
        screenOptions={{
          tabBarLabel: ({color, children}) => (
            <I18nText
              color={color}
              allowFontScaling={false}
              fontWeight='500'
              textTransform='none'
              fontSize={13}>
              {children}
            </I18nText>
          ),
          tabBarStyle: {
            paddingHorizontal: 24,
            backgroundColor: backgroundColor,
            height: NAVIGATION_HEADER_HEIGHT,
          },
          tabBarIndicatorContainerStyle: {
            width: 'auto',
            marginHorizontal: 24,
          },
          tabBarScrollEnabled: true,
          tabBarIndicatorStyle: {
            backgroundColor: color,
            height: 4,
          },
          tabBarInactiveTintColor: inactiveColor,
          tabBarItemStyle: {
            width: 'auto',
            padding: 0,
            marginHorizontal: 8,
          },
          tabBarContentContainerStyle: {
            alignItems: 'center',
          },
        }}>
        {children}
      </Tab.Navigator>
      {rightElement}
    </>
  );
};

export default memo(StyledTabNavigator);
