import React from 'react';
import {XStack, YStack} from 'tamagui';
import StyledText from '@/src/components/UI/StyledText';
import ActivityPressable from '@/src/components/ActivityPressable';

interface ExampleActivityButtonProps {
  activityId: string;
  activityName: string;
  activityType?: string;
}

/**
 * Example component showing how to use ActivityPressable
 * This creates a pressable button that navigates to the activity post
 */
const ExampleActivityButton: React.FC<ExampleActivityButtonProps> = ({
  activityId,
  activityName,
  activityType = 'Activity',
}) => {
  return (
    <ActivityPressable activityId={activityId}>
      <XStack
        backgroundColor="$background"
        borderRadius="$4"
        padding="$3"
        borderWidth={1}
        borderColor="$borderColor"
        alignItems="center"
        justifyContent="space-between"
      >
        <YStack flex={1}>
          <StyledText fontWeight="600" fontSize={16}>
            {activityName}
          </StyledText>
          <StyledText color="$gray10" fontSize={14}>
            {activityType}
          </StyledText>
        </YStack>
        <StyledText color="$blue10" fontSize={14}>
          View →
        </StyledText>
      </XStack>
    </ActivityPressable>
  );
};

export default ExampleActivityButton;
