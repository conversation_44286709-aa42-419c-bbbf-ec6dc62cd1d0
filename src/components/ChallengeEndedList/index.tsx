import React, {memo, useCallback} from 'react';
import {StyleSheet} from 'react-native';
import {EndedChallengeListDto} from '@gojoe/typescript-sdk';
import {XStack, YStack} from 'tamagui';
import StyledText from '@/src/components/UI/StyledText';
import {ImageBackground} from 'expo-image';
import BusinessAvatar from '@/src/components/UI/Avatar/BusinessAvatar';
import {challengeDate} from '@/src/utils/challenge';
import {useLocales} from '@/src/contexts/LocaleContext';

import {triggerHaptics} from '@/src/utils/haptics';
import {useRouter} from 'expo-router';

interface Props {
  challenge: EndedChallengeListDto;
}
const EndedChallengeList: React.FC<Props> = ({challenge}) => {
  const {locales} = useLocales();
  const router = useRouter();
  const cover = challenge.cover
    ? {uri: challenge.cover}
    : require('@/assets/images/challenge_cover.jpg');

  const handleOnPress = useCallback(async () => {
    await triggerHaptics();
    router.push({
      pathname: '/challenge/[challengeId]',
      params: {
        challengeId: challenge.id,
      },
    });
  }, [challenge.id, router]);

  return (
    <YStack
      backgroundColor='$background'
      borderRadius={16}
      overflow='hidden'
      onPress={handleOnPress}>
      <ImageBackground source={cover} style={styles.cover}>
        {challenge.business && (
          <XStack
            height={24}
            gap={4}
            backgroundColor='$white1'
            borderRadius={12}
            alignItems='center'
            paddingHorizontal={8}
            alignSelf='flex-start'>
            <BusinessAvatar
              business={challenge.business}
              size={16}
              borderRadius={8}
            />
            <StyledText fontSize={12} fontWeight='500'>
              {challenge.business.name}
            </StyledText>
          </XStack>
        )}
      </ImageBackground>
      <YStack padding='$3.5'>
        <StyledText fontSize={18} fontWeight='700' numberOfLines={1}>
          {challenge.name}
        </StyledText>
        <StyledText fontSize={12} fontWeight='700' marginTop={8}>
          {challengeDate(
            challenge.startDate,
            challenge.endDate,
            locales.timezone,
          )}
        </StyledText>
      </YStack>
    </YStack>
  );
};

const styles = StyleSheet.create({
  cover: {
    width: '100%',
    height: 220,
    padding: 16,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
});

export default memo(EndedChallengeList);
