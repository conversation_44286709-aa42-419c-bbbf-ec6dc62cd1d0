import axios from 'axios';
import NetInfo from '@react-native-community/netinfo';
import {
  logger as RNLogger,
  consoleTransport,
  transportFunctionType,
} from 'react-native-logs';
import {isDevelopment, isProduction, isAlpha} from './bundle-id';
import {<PERSON>uffer} from 'buffer';
import {Platform} from 'react-native';
import * as Application from 'expo-application';

const LOGS_BASE_URL = 'https://logs-prod-012.grafana.net/loki/api/v1/push';
const PRODUCTION_ID = '1175824';
const QA_ID = '1134308';

// Tokens
const PROD_TOKEN = process.env.EXPO_PUBLIC_LOGS_TOKEN;
const QA_TOKEN = process.env.EXPO_PUBLIC_LOGS_TOKEN_QA;

if (!PROD_TOKEN && isProduction) {
  console.warn('Production logging token is not set.');
}
if (!QA_TOKEN && isAlpha) {
  console.warn('QA logging token is not set.');
}

const severity = isDevelopment ? 'debug' : 'info';
let currentUserId: string | undefined = undefined;
let currentDeviceId: string | null | undefined = undefined;

// Base64 encoded authorization headers
const prodAuthHash = PROD_TOKEN
  ? Buffer.from(`${PRODUCTION_ID}:${PROD_TOKEN}`).toString('base64')
  : '';
const qaAuthHash = QA_TOKEN
  ? Buffer.from(`${QA_ID}:${QA_TOKEN}`).toString('base64')
  : '';

export type ConsoleTransportOptions = {
  colors?: Record<string, any>;
  extensionColors?: Record<string, any>;
  consoleFunc?: (msg: string) => void;
};

/**
 * Create the Loki log payload structure
 */
const createLogPayload = (msg: string, level: string, userId: string) => {
  const stream: Record<string, string> = {
    user_id: userId,
    level,
    service_name: 'mobile_app',
    version: Application.nativeApplicationVersion || 'unknown',
    build: Application.nativeBuildVersion || 'unknown',
    device_os: Platform.OS,
    platform: Platform.OS,
  };

  if (currentDeviceId) {
    stream.deviceId = currentDeviceId;
  }

  return {
    streams: [
      {
        stream,
        values: [[`${Date.now()}000000`, msg]],
      },
    ],
  };
};

// Extended console transport for development
const extendedConsoleTransport: transportFunctionType<
  ConsoleTransportOptions
> = (props) => {
  const {msg, extension} = props;
  const userId = extension || currentUserId || 'anonymous';
  let prefixedMsg = `[user:${userId}] ${msg}`;

  if (currentDeviceId) {
    prefixedMsg = `[user:${userId}] [device:${currentDeviceId}] ${msg}`;
  }
  consoleTransport({
    ...props,
    msg: prefixedMsg,
  });
};

/**
 * Send logs to Loki
 */
const sendToLoki = async (payload: any, authHash: string) => {
  try {
    const netInfoState = await NetInfo.fetch();
    if (netInfoState.isConnected) {
      await axios.post(LOGS_BASE_URL, payload, {
        headers: {
          Authorization: `Basic ${authHash}`,
          'Content-Type': 'application/json',
        },
      });
    }
  } catch (error) {
    console.error('Failed to send log to Loki:', error);
  }
};

/**
 * Loki Transport for QA
 */
const qaLogger: transportFunctionType<ConsoleTransportOptions> = async (
  props,
) => {
  const {msg, level, extension} = props;
  const userId = extension || currentUserId || 'anonymous';

  if (QA_TOKEN) {
    const payload = createLogPayload(msg, level.text, userId);
    await sendToLoki(payload, qaAuthHash);
  }
};

/**
 * Loki Transport for Production
 */
const prodLogger: transportFunctionType<ConsoleTransportOptions> = async (
  props,
) => {
  const {msg, level, extension} = props;
  const userId = extension || currentUserId || 'anonymous';

  if (PROD_TOKEN) {
    const payload = createLogPayload(msg, level.text, userId);
    await sendToLoki(payload, prodAuthHash);
  }
};

// Determine transport based on environment
const transport = isDevelopment
  ? extendedConsoleTransport
  : isAlpha
    ? [extendedConsoleTransport, qaLogger]
    : prodLogger;

const logger = RNLogger.createLogger({
  severity,
  transport,
  async: true,
  dateFormat: 'time',
  printLevel: true,
  printDate: true,
  fixedExtLvlLength: false,
  enabled: true,
  transportOptions: isDevelopment
    ? {
        colors: {
          info: 'blueBright',
          warn: 'yellowBright',
          error: 'redBright',
          debug: 'cyan',
        },
      }
    : undefined,
});

/**
 * Set the user ID for logs.
 */
export const setLoggerUserId = (userId: string) => {
  currentUserId = userId;
};

/**
 * Clear the user ID when logging out.
 */
export const clearLoggerUserId = () => {
  currentUserId = undefined;
};

/**
 * Set the device ID for logs.
 */
export const setLoggerDeviceId = (deviceId: string | null | undefined) => {
  currentDeviceId = deviceId;
};

/**
 * Clear the device ID when logging out.
 */
export const clearLoggerDeviceId = () => {
  currentDeviceId = undefined;
};

export default logger;
