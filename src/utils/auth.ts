import * as SecureStore from 'expo-secure-store';
import {AppState, Platform} from 'react-native';
import {storage} from '@/src/utils/localStorage';
import logger from '@/src/utils/logger';

const USER_ACCESS_TOKEN = 'USER_ACCESS_TOKEN';
const USER_REFRESH_ACCESS_TOKEN = 'USER_REFRESH_ACCESS_TOKEN';
const RETRY_DELAY = 1000;
const MAX_RETRY_COUNT = 3;

const waitForAppToBeActive = async (): Promise<void> => {
  if (AppState.currentState === 'active') return;

  return new Promise((resolve) => {
    const listener = AppState.addEventListener('change', (state) => {
      if (state === 'active') {
        listener.remove();
        resolve();
      }
    });
  });
};

const handleKeychainInteraction = async (
  fn: () => Promise<any>,
  retryCount = 0,
): Promise<any> => {
  try {
    await waitForAppToBeActive(); // ✅ wait until app is active
    return await fn();
  } catch (error: any) {
    if (
      error?.message.includes('User interaction is not allowed') &&
      retryCount < MAX_RETRY_COUNT
    ) {
      logger.info(
        `Retrying SecureStore access in ${RETRY_DELAY}ms... (Attempt ${retryCount + 1})`,
      );
      await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
      return handleKeychainInteraction(fn, retryCount + 1);
    }
    throw error;
  }
};

export const getUserAccessToken = async () => {
  if (Platform.OS === 'web') {
    return storage.getString(USER_ACCESS_TOKEN);
  }
  return handleKeychainInteraction(() =>
    SecureStore.getItemAsync(USER_ACCESS_TOKEN, {
      keychainAccessible: SecureStore.AFTER_FIRST_UNLOCK,
    }),
  );
};

export const setUserAccessToken = async (accessToken: string) => {
  if (Platform.OS === 'web') {
    return storage.set(USER_ACCESS_TOKEN, accessToken);
  }
  return handleKeychainInteraction(() =>
    SecureStore.setItemAsync(USER_ACCESS_TOKEN, accessToken, {
      keychainAccessible: SecureStore.AFTER_FIRST_UNLOCK,
    }),
  );
};

export const removeUserAccessToken = async () => {
  if (Platform.OS === 'web') {
    return storage.remove(USER_ACCESS_TOKEN);
  }
  return handleKeychainInteraction(() =>
    SecureStore.deleteItemAsync(USER_ACCESS_TOKEN),
  );
};

export const getUserRefreshAccessToken = async () => {
  if (Platform.OS === 'web') {
    return storage.getString(USER_REFRESH_ACCESS_TOKEN);
  }
  return handleKeychainInteraction(() =>
    SecureStore.getItemAsync(USER_REFRESH_ACCESS_TOKEN, {
      keychainAccessible: SecureStore.AFTER_FIRST_UNLOCK,
    }),
  );
};

export const setUserRefreshAccessToken = async (refreshAccessToken: string) => {
  if (Platform.OS === 'web') {
    return storage.set(USER_REFRESH_ACCESS_TOKEN, refreshAccessToken);
  }
  return handleKeychainInteraction(() =>
    SecureStore.setItemAsync(USER_REFRESH_ACCESS_TOKEN, refreshAccessToken, {
      keychainAccessible: SecureStore.AFTER_FIRST_UNLOCK,
    }),
  );
};

export const removeUserRefreshAccessToken = async () => {
  if (Platform.OS === 'web') {
    return storage.remove(USER_REFRESH_ACCESS_TOKEN);
  }
  return handleKeychainInteraction(() =>
    SecureStore.deleteItemAsync(USER_REFRESH_ACCESS_TOKEN),
  );
};

export const logLogout = async ({
  filePath,
  reason,
}: {
  filePath: string;
  reason: unknown;
}) => {
  await logger.info({
    title: 'User Logout',
    reason,
    filePath,
  });
};
