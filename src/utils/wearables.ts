import {WpDto} from '@gojoe/typescript-sdk';
import healthConnectService from '@/src/services/healthConnectService';
import appleHealthService from '@/src/services/appleHealthService';

export const isConnected = (
  wearableProviders: WpDto[],
  name: string,
): boolean => {
  let connected = false;
  for (let i = 0; i < wearableProviders.length; i += 1) {
    if (wearableProviders[i].name.toLowerCase() === name.toLowerCase()) {
      connected = !!wearableProviders[i].isConnected;
      if (name === 'HealthConnect') {
        healthConnectService.setStatus(
          connected ? 'connected' : 'disconnected',
        );
      }
      if (name === 'Apple') {
        appleHealthService.setStatus(connected ? 'connected' : 'disconnected');
      }
    }
  }
  return connected;
};
