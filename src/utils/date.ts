import {DateTime} from 'luxon';
import {PostListDto, PostSingleDto} from '@gojoe/typescript-sdk';

import * as Localization from 'expo-localization';

export const formatDateToUserLocale = (dateStringUTC: string) => {
  const userDate = DateTime.fromISO(dateStringUTC, {zone: 'utc'}).setZone();

  if (userDate.hasSame(DateTime.now(), 'day')) {
    return `Today at ${userDate.toLocaleString(DateTime.TIME_SIMPLE)}`;
  } else if (userDate.plus({days: 1}).hasSame(DateTime.now(), 'day')) {
    return `Yesterday at ${userDate.toLocaleString(DateTime.TIME_SIMPLE)}`;
  } else {
    return userDate.toLocaleString({
      ...DateTime.DATE_MED,
      hour: 'numeric',
      minute: '2-digit',
    });
  }
};

export function getRelativeTimeFromNow(
  isoDate: string,
  userTimezone: string,
  userLocale = 'en',
): string {
  return (
    DateTime.fromISO(isoDate, {zone: userTimezone}).toRelative({
      locale: userLocale,
    }) ?? ''
  );
}

// List of supported language codes in our polyfills
const supportedLanguageCodes = [
  'en',
  'en-GB',
  'pt',
  'fr-CA',
  'nl',
  'fr',
  'de',
  'it',
  'ja',
  'ro',
  'es',
  'et',
  'pl',
  'bg',
  'sv',
  'da',
  'fil',
  'lv',
  'hu',
  'cs',
  'zh',
  'ko',
  'hi',
  'th',
  'vi',
  'id',
];

// Check if a language code is supported by our polyfills
const isLanguageSupported = (languageCode: string): boolean => {
  // First check exact match
  if (supportedLanguageCodes.includes(languageCode)) {
    return true;
  }

  // Then check if the base language is supported (e.g., 'en-US' -> 'en')
  const baseLanguage = languageCode.split(/[-_]/)[0];
  return supportedLanguageCodes.includes(baseLanguage);
};

// Get a fallback language code that is supported
const getFallbackLanguage = (languageCode: string): string => {
  // If the language is supported, use it
  if (isLanguageSupported(languageCode)) {
    return languageCode;
  }

  // Try to use the base language if it's supported
  const baseLanguage = languageCode.split(/[-_]/)[0];
  if (isLanguageSupported(baseLanguage)) {
    return baseLanguage;
  }

  // Default fallback to English
  return 'en';
};

export const postDateTime = (
  post: PostListDto | PostSingleDto,
  languageCode = 'en',
): string => {
  let publishedAt = post.publishedAt;
  let timeZone = 'utc';
  if (post.activity) {
    publishedAt = post.activity.startTime.toString();
    timeZone = post.activity.timezone ?? timeZone;
  }

  // Use a supported language code to avoid Intl errors
  const safeLanguageCode = getFallbackLanguage(languageCode);

  const utcDateTime = DateTime.fromISO(publishedAt, {zone: 'utc'});
  const localizedDateTime = utcDateTime
    .setZone(timeZone)
    .setLocale(languageCode);

  const activityDate =
    utcDateTime.year === DateTime.now().year
      ? utcDateTime
          .setZone(timeZone)
          .toLocaleString(
            {month: 'long', day: 'numeric'},
            {locale: safeLanguageCode},
          )
      : utcDateTime.setZone(timeZone).toLocaleString(DateTime.DATE_MED, {
          locale: safeLanguageCode,
        });

  // Use toLocaleString for time format to respect locale conventions
  const timeFormat = localizedDateTime.toLocaleString(DateTime.TIME_SIMPLE, {
    locale: languageCode,
  });

  return `${activityDate} ${timeFormat}`;
};

export const timeAgo = (date?: string) => {
  if (!date) return '';

  const d = new Date(date);
  const now = new Date();
  const diff = (now.getTime() - d.getTime()) / 1000;

  const intervals = {
    year: 31536000,
    month: 2592000,
    week: 604800,
    day: 86400,
    hour: 3600,
    minute: 60,
    second: 1,
  };

  // Get the device locale and ensure it's supported
  const deviceLocale = Localization.getLocales()[0]?.languageTag || 'en';
  const safeLocale = getFallbackLanguage(deviceLocale);

  for (const [unit, secondsInUnit] of Object.entries(intervals)) {
    const value = Math.floor(diff / secondsInUnit);
    if (value >= 1) {
      try {
        return new Intl.RelativeTimeFormat(safeLocale, {
          numeric: 'auto',
        }).format(-value, unit as Intl.RelativeTimeFormatUnit);
      } catch {
        // If there's still an error, fall back to English
        return new Intl.RelativeTimeFormat('en', {
          numeric: 'auto',
        }).format(-value, unit as Intl.RelativeTimeFormatUnit);
      }
    }
  }

  return 'just now';
};

export const formatLabel = (
  label: string,
  timeFrame: 'day' | 'week' | 'month',
): string => {
  const date = new Date(label);

  // Get the device locale and ensure it's supported
  const deviceLocale = Localization.getLocales()[0]?.languageTag || 'en';
  const safeLocale = getFallbackLanguage(deviceLocale);

  try {
    switch (timeFrame) {
      case 'day':
        return new Intl.DateTimeFormat(safeLocale, {
          hour: '2-digit',
          minute: '2-digit',
        }).format(date);
      case 'week':
      case 'month':
        return new Intl.DateTimeFormat(safeLocale, {
          month: 'short',
          day: 'numeric',
        }).format(date);
      default:
        return label;
    }
  } catch {
    // If there's still an error, fall back to English
    try {
      switch (timeFrame) {
        case 'day':
          return new Intl.DateTimeFormat('en', {
            hour: '2-digit',
            minute: '2-digit',
          }).format(date);
        case 'week':
        case 'month':
          return new Intl.DateTimeFormat('en', {
            month: 'short',
            day: 'numeric',
          }).format(date);
        default:
          return label;
      }
    } catch {
      // Last resort fallback
      return label;
    }
  }
};
