export const emojiImage = (name: string): string => {
  switch (name.toLowerCase()) {
    case 'you':
      return '🫵';
    case 'advanced':
      return '🥷';
    case 'animals':
      return '🐶';
    case 'athletes':
      return '🏃‍';
    case 'balance':
      return '⚖️';
    case 'beginner':
      return '✨';
    case 'books':
      return '📚';
    case 'breathwork':
      return '🫁';
    case 'caffeine':
      return '☕️';
    case 'chair workout':
      return '🪑';
    case 'challenge':
      return '⚔️';
    case 'cold water':
      return '💧';
    case 'confidence':
      return '😎';
    case 'cool down':
      return '🧊';
    case 'core':
      return '🌋';
    case 'depression':
      return '😔';
    case 'emotions':
      return '😲';
    case 'fitness':
      return '🎽';
    case 'fullbody':
      return '🤺';
    case 'guided meditation':
      return '🧘';
    case 'gut health':
      return '🫃';
    case 'gym':
      return '🏋️‍♀️';
    case 'hiit':
      return '⌚';
    case 'habits':
      return '🔢';
    case 'happiness':
      return '😃';
    case 'homeworkout':
      return '🏠';
    case 'hydration':
      return '💦';
    case 'illness':
      return '🤒';
    case 'immune system':
      return '🧑‍';
    case 'intermediate':
      return '⚙️';
    case 'journaling':
      return '📝';
    case 'lifestyle':
      return '🍻';
    case 'loneliness':
      return '🐺';
    case 'meditation':
      return '🪷';
    case 'mental health':
      return '🧠';
    case 'mindfulness':
      return '😌';
    case 'mindset':
      return '🤔';
    case 'mobility':
      return '🤸‍';
    case 'morning':
      return '☀️';
    case 'motivation':
      return '📢';
    case 'movement':
      return '🏃‍';
    case 'nature':
      return '🌿';
    case 'nutrition':
      return '🍎';
    case 'overwhelm':
      return '😰';
    case 'paddleboard':
      return '🛶';
    case 'personal growth':
      return '🌱';
    case 'positivity':
      return '😊';
    case 'protein':
      return '🥚';
    case 'recipe':
      return '🥣';
    case 'recovery':
      return '❤️‍';
    case 'relationships':
      return '💑';
    case 'resilience':
      return '💪';
    case 'rowing':
      return '🚣‍';
    case 'running':
      return '🏃';
    case 'skin':
      return '🧖‍';
    case 'sleep':
      return '😴';
    case 'social media':
      return '📱';
    case 'strength':
      return '💪';
    case 'stress':
      return '😰';
    case 'stretching':
      return '🦒';
    case 'supermarket':
      return '🛒';
    case 'swimming':
      return '🏊‍';
    case 'teamwork':
      return '🏆';
    case 'training':
      return '🤼';
    case 'triathlon':
      return '🚴‍';
    case 'warm up':
      return '🌡️';
    case 'weight loss':
      return '📉';
    case "women's health":
      return '👩‍';
    case 'worklife':
      return '💼';
    case 'workout':
      return '😅';
    case 'yoga':
      return '🧘‍';
    case 'yoga classes':
      return '🧘🏽';
    case 'mobility & stretching':
      return '🙆🏾';
    case 'hiit workouts':
      return '💥';
    case 'strength workouts':
      return '🏋️‍♀️';
    default:
      return '💥';
  }
};
