import {GiftCardOptionsDto, UserRewardBalanceDto} from '@gojoe/typescript-sdk';
type OptionStatus = {
  isDisabled: boolean;
  reason?: string;
};
export const getOptionStatus = (
  option: GiftCardOptionsDto,
  userRewardBalance?: UserRewardBalanceDto,
  isPremium?: string,
): OptionStatus => {
  if (!userRewardBalance) {
    return {
      isDisabled: true,
    };
  }
  if (isPremium === 'FREEM') {
    return {
      isDisabled: true,
      reason: 'You need to be a premium user',
    };
  }

  if (option.points > userRewardBalance.balance) {
    return {
      isDisabled: true,
      reason: 'Not enough points',
    };
  }
  return {
    isDisabled: false,
  };
};
