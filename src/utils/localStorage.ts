import {Platform} from 'react-native';
import {MMKV} from 'react-native-mmkv';

const mmkvStorage = new MMKV();

export const saveData = (key: string, value: string | number) => {
  if (Platform.OS === 'web') {
    if (typeof window !== 'undefined') {
      window.localStorage.setItem(key, `${value}`);
    }
  } else {
    mmkvStorage.set(key, value);
  }
};
export const getNumber = (
  key: string,
  defaultValue = 0,
): number | undefined => {
  let value: string | null | undefined = null;
  if (Platform.OS === 'web') {
    if (typeof window !== 'undefined') {
      value = localStorage.getItem(key);
    }
  } else {
    return mmkvStorage.getNumber(key);
  }
  return value ? parseInt(value) : defaultValue;
};

export const getData = (key: string, defaultValue = ''): string => {
  let value: string | null | undefined = null;
  if (Platform.OS === 'web') {
    if (typeof window !== 'undefined') {
      value = localStorage.getItem(key);
    }
  } else {
    value = mmkvStorage.getString(key);
  }
  return value ? value : defaultValue;
};

export const removeData = (key: string) => {
  if (Platform.OS === 'web') {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(key);
    }
  } else {
    mmkvStorage.delete(key);
  }
};

export const getObject = <T>(key: string): T | undefined => {
  const data = getData(key);
  if (!data) {
    return undefined;
  }
  try {
    return JSON.parse(data) as T;
  } catch {
    return undefined;
  }
};
export const setObject = (key: string, value: any) => {
  return saveData(key, JSON.stringify(value));
};

export const storage = {
  getString: getData,
  getNumber: getNumber,
  getObject: getObject,
  setObject: setObject,
  set: saveData,
  remove: removeData,
};
