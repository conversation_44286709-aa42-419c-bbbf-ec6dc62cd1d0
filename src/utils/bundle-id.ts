import Constants from 'expo-constants';
import {Platform} from 'react-native';

export const getBundleId = () => {
  const {ios, android} = Constants.expoConfig ?? {};
  return Platform.OS === 'ios'
    ? ios?.bundleIdentifier || null
    : android?.package || null;
};

export const isDevelopment = getBundleId() === 'com.gojoe.app.dev' || __DEV__;
export const isProduction = getBundleId() === 'com.gojoe.app';
export const isAlpha = getBundleId() === 'com.gojoe.app.alpha';
export const isBeta = getBundleId() === 'com.gojoe.app.beta';
