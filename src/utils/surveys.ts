import {SurveyDto, SurveyDtoTypeEnum} from '@gojoe/typescript-sdk';
import {ChallengeDetailsDtoRunningStatusEnum} from '@gojoe/typescript-sdk';
import {storage} from '@/src/utils/localStorage';
import logger from '@/src/utils/logger';

export const checkSurveys = (
  userId: string,
  surveys: SurveyDto[],
  runningStatus: ChallengeDetailsDtoRunningStatusEnum,
) => {
  const skipSurveys = getSkipSurveys();
  const completedSurveys = getCompletedSurveys();
  for (const survey of surveys) {
    if (completedSurveys.includes(survey.id)) {
      continue;
    }
    if (survey.isOptional && skipSurveys.includes(survey.id)) {
      continue;
    }
    if (checkCompletedSurveys(userId, survey)) {
      continue;
    }
    if (
      survey.type === 'CHALLENGE_ONBOARDING' &&
      (runningStatus === 'UPCOMING' || runningStatus === 'ONGOING')
    ) {
      return survey.id;
    }
    if (survey.type === 'CHALLENGE_EXIT' && runningStatus === 'ENDED') {
      return survey.id;
    }
    if (survey.endDate && new Date(survey.endDate) > new Date()) {
      return survey.id;
    }
    if (survey.startDate && new Date(survey.startDate) < new Date()) {
      return survey.id;
    }
  }
  return undefined;
};

export const checkBusinessSurveys = (userId: string, surveys: SurveyDto[]) => {
  for (const survey of surveys) {
    if (checkCompletedSurveys(userId, survey)) {
      continue;
    }
    if (survey.type === SurveyDtoTypeEnum.BusinessOnboarding) {
      return survey.id;
    }
  }
  return undefined;
};

const skipSurveysKey = 'skipSurveys';
const completedSurveysKey = 'completedSurveysKey';

export const getSkipSurveys = (): string[] => {
  try {
    const storedValue = storage.getString(skipSurveysKey);
    return storedValue ? (JSON.parse(storedValue) as string[]) : [];
  } catch (e) {
    logger.error('Error parsing skipSurveys:', e);
    return [];
  }
};

// Rename to addSkipSurvey for clarity
export const addSkipSurvey = (surveyId: string) => {
  const savedValue = getSkipSurveys();
  if (!savedValue.includes(surveyId)) {
    savedValue.push(surveyId);
    storage.set(skipSurveysKey, JSON.stringify(savedValue));
  }
};

export const getCompletedSurveys = (): string[] => {
  try {
    const storedValue = storage.getString(completedSurveysKey);
    return storedValue ? (JSON.parse(storedValue) as string[]) : [];
  } catch (e) {
    logger.error('Error parsing skipSurveys:', e);
    return [];
  }
};

// Rename to addSkipSurvey for clarity
export const addCompletedSurvey = (surveyId: string) => {
  const savedValue = getCompletedSurveys();
  if (!savedValue.includes(surveyId)) {
    savedValue.push(surveyId);
    storage.set(completedSurveysKey, JSON.stringify(savedValue));
  }
};

export const checkCompletedSurveys = (
  userId: string,
  survey: SurveyDto,
): boolean => {
  return survey.users.some(
    (user) => user.userId === userId && user.percentageAnswered === 100,
  );
};

export const getPercentageAnswered = (
  userId: string,
  survey: SurveyDto,
): number => {
  const user = survey.users.find((item) => item.userId === userId);

  return user ? user.totalAnswered : 0;
};
