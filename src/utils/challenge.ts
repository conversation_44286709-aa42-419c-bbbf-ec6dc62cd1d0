import {DateTime, Duration} from 'luxon';
import {ChallengeDetailsDto} from '@gojoe/typescript-sdk';

export enum ChallengeEventType {
  Points = 'points',
  Distance = 'distance',
}

export const challengeDate = (
  startDate: string,
  endDate: string,
  timezone = 'UTC',
): string => {
  let response = '';
  const startDateLocal = DateTime.fromISO(startDate, {zone: timezone});
  const endDateLocal = DateTime.fromISO(endDate, {zone: timezone});

  response += startDateLocal.toLocaleString(DateTime.DATE_MED);
  if (startDateLocal.year === endDateLocal.year) {
    response = response.replace(`${startDateLocal.year}`, '');
    response = response.trim();
  }
  response += ` - ${endDateLocal.toLocaleString(DateTime.DATE_MED)}`;
  return response;
};

export const secondsToDays = (seconds: number): number => {
  if (!seconds) {
    return 0;
  }
  return Math.floor(seconds / (3600 * 24));
};

export const getColumnType = (name: string): ChallengeEventType => {
  switch (name.toLowerCase()) {
    case 'most distance':
    case 'most kms':
      return ChallengeEventType.Distance;
    case 'team average':
    case 'most points':
    default:
      return ChallengeEventType.Points;
  }
};

export const canDisplayLeaderboard = (challenge: ChallengeDetailsDto) => {
  if (!challenge.daysWithoutDisplayLeaderboard) {
    return true;
  }

  if (new Date(challenge.endDate) < new Date()) {
    return true;
  }

  // get days left until finished challenge
  const daysLeft = Math.ceil(
    (new Date(challenge.endDate).getTime() - new Date().getTime()) /
      (1000 * 3600 * 24),
  );

  return daysLeft > challenge.daysWithoutDisplayLeaderboard;
};

export function formatDurationHuman(seconds: number): string {
  const duration = Duration.fromObject({seconds}).shiftTo(
    'days',
    'hours',
    'minutes',
  );

  const {days, hours, minutes} = duration.toObject();

  const parts = [];

  if (days && days >= 1)
    parts.push(`${Math.floor(days)} day${days > 1 ? 's' : ''}`);
  if (hours && hours >= 1)
    parts.push(`${Math.floor(hours)} hour${hours > 1 ? 's' : ''}`);
  if (minutes && minutes >= 1)
    parts.push(`${Math.floor(minutes)} min${minutes > 1 ? 's' : ''}`);

  return parts.join(', ') || '0 min';
}

export function formatTimeStringDuration(timeStr: string): string {
  // Split and parse the time string
  const [hoursStr, minutesStr, secondsStr] = timeStr.split(':');

  const hours = parseInt(hoursStr, 10) || 0;
  const minutes = parseInt(minutesStr, 10) || 0;
  const seconds = parseFloat(secondsStr) || 0;

  // Create a Luxon duration
  const duration = Duration.fromObject({hours, minutes, seconds});

  // Extract meaningful parts
  const {
    days,
    hours: h,
    minutes: m,
  } = duration.shiftTo('days', 'hours', 'minutes').toObject();

  const parts = [];

  if (days && days >= 1)
    parts.push(`${Math.floor(days)} day${days > 1 ? 's' : ''}`);
  if (h && h >= 1) parts.push(`${Math.floor(h)} hour${h > 1 ? 's' : ''}`);
  if (m && m >= 1) parts.push(`${Math.floor(m)} min${m > 1 ? 's' : ''}`);

  return parts.join(', ') || '0 min';
}

export const addTimeToDateTime = (utcTime: string, timeToAdd: string) => {
  const utcDateTime = DateTime.fromISO(utcTime, {zone: 'utc'});
  const localDateTime = utcDateTime.toLocal();

  const durationParts = timeToAdd.split(':');
  let hours = 0;
  let minutes = 0;
  let seconds = 0;

  if (durationParts.length === 3) {
    hours = parseInt(durationParts[0], 10);
    minutes = parseInt(durationParts[1], 10);
    seconds = parseInt(durationParts[2], 10);
  } else if (durationParts.length === 2) {
    minutes = parseInt(durationParts[0], 10);
    seconds = parseInt(durationParts[1], 10);
  } else if (durationParts.length === 1) {
    seconds = parseInt(durationParts[1], 10);
  } else {
    return;
  }
  const resultDateTime = localDateTime.plus({hours, minutes, seconds});
  const localTimeString = resultDateTime.toFormat('LLL dd, HH:mm');

  return `${localTimeString}`;
};
