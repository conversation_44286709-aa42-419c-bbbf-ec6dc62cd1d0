import NetInfo, {NetInfoState} from '@react-native-community/netinfo';
import logger from '@/src/utils/logger';

/**
 * Network status singleton to track and provide network connectivity information
 */
class NetworkStatus {
  private static instance: NetworkStatus;
  private isConnected: boolean = true;
  private listeners: Set<(isConnected: boolean) => void> = new Set();
  private unsubscribe: (() => void) | null = null;

  private constructor() {
    // Initialize network monitoring
    this.unsubscribe = NetInfo.addEventListener(this.handleNetInfoChange);

    // Initial network check
    this.checkNetworkStatus();
  }

  /**
   * Get the NetworkStatus singleton instance
   */
  public static getInstance(): NetworkStatus {
    if (!NetworkStatus.instance) {
      NetworkStatus.instance = new NetworkStatus();
    }
    return NetworkStatus.instance;
  }

  /**
   * Handle network state changes
   */
  private handleNetInfoChange = (state: NetInfoState) => {
    const previousState = this.isConnected;
    this.isConnected = Boolean(state.isConnected);

    // Log network state changes
    if (previousState !== this.isConnected) {
      if (this.isConnected) {
        logger.info('🌐 Network connection restored');
      } else {
        logger.warn('🌐 Network connection lost');
      }

      // Notify listeners of network state change
      this.notifyListeners();
    }
  };

  /**
   * Check current network status
   */
  private async checkNetworkStatus(): Promise<void> {
    try {
      const state = await NetInfo.fetch();
      this.isConnected = Boolean(state.isConnected);
    } catch (error) {
      logger.error('Failed to check network status:', error);
      this.isConnected = false;
    }
  }

  /**
   * Notify all listeners of network state change
   */
  private notifyListeners(): void {
    this.listeners.forEach((listener) => {
      try {
        listener(this.isConnected);
      } catch (error) {
        logger.error('Error in network status listener:', error);
      }
    });
  }

  /**
   * Get current network connection status
   */
  public getIsConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Add a listener for network status changes
   */
  public addListener(listener: (isConnected: boolean) => void): () => void {
    this.listeners.add(listener);

    // Return function to remove this listener
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
    this.listeners.clear();
  }
}

/**
 * Exponential backoff retry function for network operations
 * @param operation Function to retry
 * @param maxRetries Maximum number of retry attempts
 * @param baseDelay Base delay in milliseconds
 * @returns Promise that resolves with the operation result or rejects after max retries
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
): Promise<T> {
  const networkStatus = NetworkStatus.getInstance();
  let retries = 0;

  while (true) {
    try {
      return await operation();
    } catch (error: any) {
      // Don't retry if we've hit max retries
      if (retries >= maxRetries) {
        throw error;
      }

      // Don't retry if we're offline - wait for connection
      if (!networkStatus.getIsConnected()) {
        logger.warn(
          'Network operation failed - device is offline. Waiting for connection...',
        );

        // Wait for network to be restored before retrying
        await new Promise<void>((resolve) => {
          const removeListener = networkStatus.addListener((isConnected) => {
            if (isConnected) {
              removeListener();
              resolve();
            }
          });
        });

        logger.info('Network connection restored, retrying operation...');
      } else {
        // If we're online but still got an error, use exponential backoff
        const delay = baseDelay * Math.pow(2, retries);
        logger.info(
          `Retrying network operation in ${delay}ms (attempt ${retries + 1}/${maxRetries})...`,
        );
        await new Promise((resolve) => setTimeout(resolve, delay));
        retries++;
      }
    }
  }
}

// Export singleton instance
export const networkStatus = NetworkStatus.getInstance();

// Export a function to get user-friendly network error messages
export const getNetworkErrorMessage = (): string => {
  if (!networkStatus.getIsConnected()) {
    return 'No internet connection. Please check your network settings and try again.';
  }
  return 'Network error. Please try again later.';
};
