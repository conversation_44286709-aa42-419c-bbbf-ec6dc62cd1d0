import {DateTime} from 'luxon';
import convert from 'convert';

import {GPSSignal} from '@/src/screens/Tracker/types';
import {DistanceUnit} from '@/src/contexts/LocaleContext';

export enum TrackerRunningStatus {
  Idle = 'idle',
  Running = 'running',
  Paused = 'paused',
  Finished = 'finished',
}

export const getDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
) => {
  const toRad = (value: number) => (value * Math.PI) / 180;
  const R = 6371000; // Earth radius in meters
  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) ** 2 +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLon / 2) ** 2;
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

export const formatDuration = (seconds: number) => {
  const h = Math.floor(seconds / 3600)
    .toString()
    .padStart(2, '0');
  const m = Math.floor((seconds % 3600) / 60)
    .toString()
    .padStart(2, '0');
  const s = Math.floor(seconds % 60)
    .toString()
    .padStart(2, '0');
  return `${h}:${m}:${s}`;
};

export const gpsSignal = (quality: number): GPSSignal => {
  if (quality > 100) {
    return GPSSignal.Poor;
  }
  if (quality > 30) {
    return GPSSignal.Fair;
  }
  if (quality > 10) {
    return GPSSignal.Good;
  }
  return GPSSignal.Excellent;
};

export function calculateCalories(
  durationSeconds: number,
  met = 9.8,
  weightGrams = 70000,
): number {
  const hours = durationSeconds / 3600;
  return Math.round(met * (weightGrams / 1000) * hours);
}

export const formatPace = (
  elapsedTime: number,
  totalDistance: number,
  unit: DistanceUnit,
): string => {
  if (totalDistance === 0 || elapsedTime === 0) {
    return '00:00';
  }

  const distance =
    unit === DistanceUnit.Miles
      ? convert(totalDistance, 'm').to('mi')
      : totalDistance / 1000;

  const pace = elapsedTime / distance;
  const minutes = Math.floor(pace / 60);
  const seconds = Math.floor(pace % 60);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

export const formattedDistance = (
  totalDistance: number,
  unit: DistanceUnit,
): string => {
  const convertedDistance =
    unit === DistanceUnit.Miles
      ? convert(totalDistance, 'm').to('mi')
      : totalDistance / 1000;
  return convertedDistance.toFixed(2);
};

export const formattedTime = (startTime: number | null, locale = 'en') => {
  if (!startTime) {
    return '';
  }

  const relative = DateTime.fromMillis(startTime).toRelativeCalendar({
    locale: 'en',
  });

  const capitalizedRelative = relative
    ? relative.charAt(0).toUpperCase() + relative.slice(1)
    : '';

  return (
    capitalizedRelative +
    ' at ' +
    DateTime.fromMillis(startTime).toFormat('HH:mm')
  );
};

export const getTimeLabel = (date: Date): string => {
  const hour = date.getHours();
  if (hour >= 4 && hour < 7) return 'Early Morning';
  if (hour >= 7 && hour < 12) return 'Morning';
  if (hour >= 12 && hour < 17) return 'Afternoon';
  if (hour >= 17 && hour < 21) return 'Evening';
  if (hour >= 21 || hour < 0) return 'Night';
  return 'Late Night';
};
