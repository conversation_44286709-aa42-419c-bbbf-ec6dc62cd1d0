import axios, {AxiosError} from 'axios';
import {getNetworkErrorMessage} from './network';

type ApiError = {
  error: string;
  message: string;
  statusCode: number;
};

export const getApiError = (error?: Error): ApiError => {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<ApiError>;

    // Check if it's a network error
    if (!axiosError.response && axiosError.message === 'Network Error') {
      return {
        error: 'Network Error',
        message: getNetworkErrorMessage(),
        statusCode: 0, // Use 0 to indicate network error
      };
    }

    // Handle normal API errors with response
    if (axiosError.response && axiosError.response.data) {
      return axiosError.response.data;
    }
  }

  return {
    error: 'Something went wrong',
    message:
      'Please try again later or contact support if the problem persists',
    statusCode: 500,
  };
};

/**
 * Check if an error is an Axios error with a specific status code
 * @param error The error to check
 * @param statusCode The status code to check for
 * @returns True if the error is an Axios error with the specified status code
 */
export const isAxiosErrorWithStatus = (
  error: unknown,
  statusCode: number,
): boolean => {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;
    return axiosError.response?.status === statusCode;
  }
  return false;
};

/**
 * Get a user-friendly message for common HTTP error codes
 * @param error The error to get a message for
 * @returns A user-friendly error message
 */
export const getUserFriendlyErrorMessage = (error: unknown): string => {
  // Check for network errors first
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<ApiError>;
    if (!axiosError.response && axiosError.message === 'Network Error') {
      return getNetworkErrorMessage();
    }
  }

  if (isAxiosErrorWithStatus(error, 429)) {
    return 'Too many attempts. Please wait a moment before trying again.';
  }

  if (isAxiosErrorWithStatus(error, 401)) {
    return 'Authentication failed. Please check your credentials and try again.';
  }

  if (isAxiosErrorWithStatus(error, 403)) {
    return 'You do not have permission to perform this action.';
  }

  if (isAxiosErrorWithStatus(error, 404)) {
    return 'The requested resource was not found.';
  }

  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<ApiError>;
    if (axiosError.response?.data?.message) {
      return axiosError.response.data.message;
    }
  }

  return 'Something went wrong. Please try again later.';
};
