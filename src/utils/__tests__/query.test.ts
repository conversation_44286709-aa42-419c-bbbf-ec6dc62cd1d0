import {updateSingleItem, updateCollectionItem} from '../query';

describe('query utils', () => {
  describe('updateSingleItem', () => {
    it('should handle update with data.data structure', async () => {
      const mockQueryClient = {
        cancelQueries: jest.fn().mockResolvedValue(undefined),
        getQueryData: jest.fn().mockReturnValue({field1: 'old value'}),
        setQueryData: jest.fn(),
      };

      await updateSingleItem({
        queryClient: mockQueryClient as any,
        queryKey: ['test'],
        update: {
          data: {
            data: {
              field1: 'new value',
            },
          },
        },
        fields: ['field1'],
      });

      expect(mockQueryClient.setQueryData).toHaveBeenCalledWith(
        ['test'],
        expect.objectContaining({
          field1: 'new value',
        }),
      );
    });

    it('should handle update without data.data structure', async () => {
      const mockQueryClient = {
        cancelQueries: jest.fn().mockResolvedValue(undefined),
        getQueryData: jest.fn().mockReturnValue({field1: 'old value'}),
        setQueryData: jest.fn(),
      };

      await updateSingleItem({
        queryClient: mockQueryClient as any,
        queryKey: ['test'],
        update: {
          field1: 'new value',
        },
        fields: ['field1'],
      });

      expect(mockQueryClient.setQueryData).toHaveBeenCalledWith(
        ['test'],
        expect.objectContaining({
          field1: 'new value',
        }),
      );
    });

    it('should handle undefined update', async () => {
      const mockQueryClient = {
        cancelQueries: jest.fn().mockResolvedValue(undefined),
        getQueryData: jest.fn().mockReturnValue({field1: 'old value'}),
        setQueryData: jest.fn(),
      };

      await updateSingleItem({
        queryClient: mockQueryClient as any,
        queryKey: ['test'],
        update: undefined as any,
        fields: ['field1'],
      });

      // The field should remain unchanged
      expect(mockQueryClient.setQueryData).toHaveBeenCalledWith(
        ['test'],
        expect.objectContaining({
          field1: 'old value',
        }),
      );
    });
  });

  describe('updateCollectionItem', () => {
    it('should handle update with data.data structure', async () => {
      const mockQueryClient = {
        cancelQueries: jest.fn().mockResolvedValue(undefined),
        getQueryData: jest.fn().mockReturnValue({
          pages: [
            {
              data: [
                {id: '1', field1: 'old value'},
                {id: '2', field1: 'other value'},
              ],
            },
          ],
        }),
        setQueryData: jest.fn(),
      };

      await updateCollectionItem({
        queryClient: mockQueryClient as any,
        queryKey: ['test'],
        id: '1',
        update: {
          data: {
            data: {
              field1: 'new value',
            },
          },
        },
        fields: ['field1'],
      });

      expect(mockQueryClient.setQueryData).toHaveBeenCalledWith(
        ['test'],
        expect.objectContaining({
          pages: [
            {
              data: [
                {id: '1', field1: 'new value'},
                {id: '2', field1: 'other value'},
              ],
            },
          ],
        }),
      );
    });

    it('should handle update without data.data structure', async () => {
      const mockQueryClient = {
        cancelQueries: jest.fn().mockResolvedValue(undefined),
        getQueryData: jest.fn().mockReturnValue({
          pages: [
            {
              data: [
                {id: '1', field1: 'old value'},
                {id: '2', field1: 'other value'},
              ],
            },
          ],
        }),
        setQueryData: jest.fn(),
      };

      await updateCollectionItem({
        queryClient: mockQueryClient as any,
        queryKey: ['test'],
        id: '1',
        update: {
          field1: 'new value',
        },
        fields: ['field1'],
      });

      expect(mockQueryClient.setQueryData).toHaveBeenCalledWith(
        ['test'],
        expect.objectContaining({
          pages: [
            {
              data: [
                {id: '1', field1: 'new value'},
                {id: '2', field1: 'other value'},
              ],
            },
          ],
        }),
      );
    });
  });
});
