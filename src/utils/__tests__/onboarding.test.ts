import {isUserProfileComplete, shouldShowOnboarding} from '../onboarding';
import {storage} from '../localStorage';

// Mock the localStorage
jest.mock('../localStorage', () => ({
  storage: {
    getString: jest.fn(),
  },
}));

// Mock the logger
jest.mock('../logger', () => ({
  error: jest.fn(),
}));

describe('Onboarding Utils', () => {
  describe('isUserProfileComplete', () => {
    it('should return false if user is undefined', () => {
      expect(isUserProfileComplete(undefined)).toBe(false);
    });

    it('should return false if any required field is missing', () => {
      const user = {
        id: '123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateOfBirth: '1990-01-01',
        gender: 'male',
        // Missing weight and height
      };
      expect(isUserProfileComplete(user as any)).toBe(false);
    });

    it('should return false if all required fields are missing', () => {
      const user = {
        id: '123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      };
      expect(isUserProfileComplete(user as any)).toBe(false);
    });

    it('should return true if all required fields are present', () => {
      const user = {
        id: '123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateOfBirth: '1990-01-01',
        gender: 'male',
        weight: 70,
        height: 180,
      };
      expect(isUserProfileComplete(user as any)).toBe(true);
    });
  });

  describe('shouldShowOnboarding', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return true if onboarding is not completed in local storage', () => {
      (storage.getString as jest.Mock).mockReturnValue('false');
      expect(shouldShowOnboarding(undefined)).toBe(true);
    });

    it('should return true if onboarding status is not set in local storage', () => {
      (storage.getString as jest.Mock).mockReturnValue(null);
      expect(shouldShowOnboarding(undefined)).toBe(true);
    });

    it('should return true if onboarding is completed but user profile is incomplete', () => {
      (storage.getString as jest.Mock).mockReturnValue('true');
      const user = {
        id: '123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        // Missing required fields
      };
      expect(shouldShowOnboarding(user as any)).toBe(true);
    });

    it('should return false if onboarding is completed and user profile is complete', () => {
      (storage.getString as jest.Mock).mockReturnValue('true');
      const user = {
        id: '123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateOfBirth: '1990-01-01',
        gender: 'male',
        weight: 70,
        height: 180,
      };
      expect(shouldShowOnboarding(user as any)).toBe(false);
    });

    it('should return true if there is an error checking onboarding status', () => {
      (storage.getString as jest.Mock).mockImplementation(() => {
        throw new Error('Test error');
      });
      expect(shouldShowOnboarding(undefined)).toBe(true);
    });
  });
});
