import TrackPlayer, {
  Capability,
  AppKilledPlaybackBehavior,
} from 'react-native-track-player';
import logger from '@/src/utils/logger';

// Track if the player has been initialized
let isPlayerInitialized = false;

export async function setupPlayer() {
  if (isPlayerInitialized) {
    logger.debug('TrackPlayer already initialized, skipping setup');
    return;
  }

  try {
    await TrackPlayer.setupPlayer();
    isPlayerInitialized = true;
  } catch (error) {
    // Player is already initialized, which is fine
    logger.error('Error setting up TrackPlayer:', error);
    logger.warn('TrackPlayer already initialized');
    isPlayerInitialized = true;
  }

  await TrackPlayer.updateOptions({
    android: {
      appKilledPlaybackBehavior: AppKilledPlaybackBehavior.ContinuePlayback,
    },
    capabilities: [
      Capability.Play,
      Capability.Pause,
      Capability.SkipToNext,
      Capability.SkipToPrevious,
      Capability.Stop,
    ],
    compactCapabilities: [
      Capability.Play,
      Capability.Pause,
      Capability.SkipToNext,
      Capability.SkipToPrevious,
    ],

    // iOS: Ensure media controls appear in Control Center
    notificationCapabilities: [
      Capability.Play,
      Capability.Pause,
      Capability.SkipToNext,
      Capability.SkipToPrevious,
    ],
  });
}
