import {DateTime} from 'luxon';

import {DistanceUnit} from '../contexts/LocaleContext';

export const journeyLevel = (leven: number) => {
  switch (leven) {
    case 1:
      return 'Beginner';
    case 2:
      return 'Intermediate';
    case 3:
      return 'Advanced';
    default:
      return 'Beginner';
  }
};

export const isShortActivity = (activityTypeName: string) => {
  switch (activityTypeName.toLowerCase()) {
    case 'swim':
      return true;
    default:
      return false;
  }
};

export const getDistanceUnit = (
  unit: DistanceUnit,
  activityTypeName: string,
): 'm' | 'yd' | 'km' | 'mi' => {
  if (isShortActivity(activityTypeName)) {
    return unit === 'km' ? 'm' : 'yd';
  }

  return unit;
};

export const getShortDistanceOptions = (
  languageTag = 'en-US',
): {
  label: string;
  value: number;
}[] => {
  return Array.from({length: 400}, (_, i) => {
    const value = (i + 1) * 25;
    return {
      // label: new Intl.NumberFormat(languageTag).format(value),
      label: value.toString(),
      value,
    };
  });
};

export const getTimeOfDayLabel = (date: Date): string => {
  const hour = DateTime.fromJSDate(date).hour;

  if (hour < 6) return 'Night';
  if (hour < 12) return 'Morning';
  if (hour < 18) return 'Afternoon';
  if (hour < 21) return 'Evening';
  return 'Late Night';
};

export function camelToUnderscore(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2') // insert underscore between camelCase
    .toUpperCase();
}
