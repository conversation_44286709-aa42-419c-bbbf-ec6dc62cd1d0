export const getBadge = (level: number) => {
  switch (level) {
    case 10:
      return require('@/assets/images/joeLevel/badge-level-fill-10.png');
    case 9:
      return require('@/assets/images/joeLevel/badge-level-fill-09.png');
    case 8:
      return require('@/assets/images/joeLevel/badge-level-fill-08.png');
    case 7:
      return require('@/assets/images/joeLevel/badge-level-fill-07.png');
    case 6:
      return require('@/assets/images/joeLevel/badge-level-fill-06.png');
    case 5:
      return require('@/assets/images/joeLevel/badge-level-fill-05.png');
    case 4:
      return require('@/assets/images/joeLevel/badge-level-fill-04.png');
    case 3:
      return require('@/assets/images/joeLevel/badge-level-fill-03.png');
    case 2:
      return require('@/assets/images/joeLevel/badge-level-fill-02.png');
    case 1:
    case 0:
    default:
      return require('@/assets/images/joeLevel/badge-level-fill-01.png');
  }
};

export const getLevelName = (level: number) => {
  switch (level) {
    case 10:
      return 'Legendary Joe';
    case 9:
      return 'Mythical Joe';
    case 8:
      return 'Pro Joe';
    case 7:
      return 'Extraordinary Joe';
    case 6:
      return 'Warrior Joe';
    case 5:
      return 'Super Joe';
    case 4:
      return 'Mighty Joe';
    case 3:
      return 'G.I. Joe';
    case 2:
      return 'Regular Joe';
    case 1:
    case 0:
    default:
      return 'Ordinary Joe';
  }
};

export const getLevelPoints = (level: number) => {
  switch (level) {
    case 10:
      return '>175';
    case 9:
      return '150 - 175';
    case 8:
      return '125 - 150';
    case 7:
      return '100 - 125';
    case 6:
      return '75 - 100';
    case 5:
      return '50 - 75';
    case 4:
      return '35 - 50';
    case 3:
      return '20 - 35';
    case 2:
      return '10 - 20';
    case 1:
    case 0:
    default:
      return '0 - 10';
  }
};
