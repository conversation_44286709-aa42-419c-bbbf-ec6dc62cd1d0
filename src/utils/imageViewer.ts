import {createRef} from 'react';
import {ImageViewerRef} from '../components/ImageViewer';

// Create a global ref that can be accessed from anywhere
export const imageViewerRef = createRef<ImageViewerRef>();

// Helper functions to show and hide images
export const showImage = (uri: string) => {
  imageViewerRef.current?.show(uri);
};

export const hideImage = () => {
  imageViewerRef.current?.hide();
};
