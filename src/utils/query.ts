import {InfiniteData, QueryClient} from '@tanstack/react-query';
import {
  PostListDtoCursorBasePagination,
  PostListDtoResponse,
} from '@gojoe/typescript-sdk';
import logger from './logger';

type Params = {
  queryClient: QueryClient;
  queryKey: any[];
  update: any;
  id?: string;
  fields: string[];
};

export const updateCollectionItem = async ({
  queryClient,
  queryKey,
  update,
  id,
  fields,
}: Params) => {
  const matchingQueries = queryClient.getQueryCache().findAll({queryKey});

  if (!matchingQueries.length) {
    logger.debug(
      'updateCollectionItem: No matching queries for key:',
      queryKey,
    );
    return;
  }

  for (const query of matchingQueries) {
    await queryClient.cancelQueries({queryKey: query.queryKey});

    const dataCache = queryClient.getQueryData(query.queryKey);
    if (!dataCache) continue;

    const list = JSON.parse(JSON.stringify(dataCache));

    if (!list.pages) continue;

    let didUpdate = false;

    list.pages = list.pages.map((page: any) => {
      page.data = page.data.map((result: any) => {
        if (result.id === id) {
          const updated = {...result};
          fields.forEach((field) => {
            let newValue;

            if (update?.data?.data?.[field] !== undefined) {
              newValue = update.data.data[field];
            } else if (update?.[field] !== undefined) {
              newValue = update[field];
            }

            if (newValue !== undefined && updated[field] !== newValue) {
              logger.debug(
                `Field '${field}': '${updated[field]}' -> '${newValue}'`,
              );
              updated[field] = newValue;
              didUpdate = true;
            }
          });

          return updated;
        }

        return result;
      });

      return page;
    });

    if (didUpdate) {
      logger.debug('Updating queryKey:', query.queryKey);
      queryClient.setQueryData(query.queryKey, list); // ✅ CRUCIAL: apply updated list to cache
    }
  }
};

export const updateSingleItem = async ({
  queryClient,
  queryKey,
  update,
  fields,
}: Params) => {
  // Cancel any outgoing refetches
  // (so they don't overwrite our optimistic update)
  await queryClient.cancelQueries({queryKey});
  let dataCache: any = queryClient.getQueryData(queryKey);
  if (!dataCache) {
    return;
  }
  dataCache = JSON.parse(JSON.stringify(dataCache));
  fields.forEach((field) => {
    // Check if update.data and update.data.data exist before accessing properties
    if (
      update &&
      update.data &&
      update.data.data &&
      update.data.data[field] !== undefined
    ) {
      dataCache[field] = update.data.data[field];
    } else if (update && update[field] !== undefined) {
      // Fallback to directly accessing the field from update if the nested structure doesn't exist
      dataCache[field] = update[field];
    }
    // If neither structure exists, we don't update the field
  });
  queryClient.setQueryData(queryKey, dataCache);
};

export const handleReactionOnPost = async ({
  queryClient,
  post,
}: {
  queryClient: QueryClient;
  post: PostListDtoResponse;
}) => {
  const allQueries = queryClient.getQueryCache().findAll();
  const postQueries = allQueries.filter(
    (query) => query.queryKey.indexOf('posts') !== -1,
  );
  postQueries.forEach((query) => {
    queryClient.setQueryData(
      query.queryKey,
      (oldData: InfiniteData<PostListDtoCursorBasePagination>) => {
        return {
          ...oldData,
          pages: oldData.pages.map((page) => ({
            ...page,
            data: page.data.map((oldPost) =>
              oldPost.id === post.data.id
                ? {
                    ...oldPost,
                    reactionsCountTotal: post.data.reactionsCountTotal,
                    myReactions: post.data.myReactions,
                  }
                : oldPost,
            ),
          })),
        };
      },
    );
  });

  const postLibraryQueries = allQueries.filter(
    (query) => query.queryKey.indexOf('library') !== -1,
  );
  postLibraryQueries.forEach((query) => {
    queryClient.setQueryData(
      query.queryKey,
      (oldData: InfiniteData<PostListDtoCursorBasePagination>) => {
        return {
          ...oldData,
          pages: oldData.pages.map((page) => ({
            ...page,
            data: page.data.map((oldPost) =>
              oldPost.id === post.data.id
                ? {
                    ...oldPost,
                    reactionsCountTotal: post.data.reactionsCountTotal,
                    myReactions: post.data.myReactions,
                  }
                : oldPost,
            ),
          })),
        };
      },
    );
  });

  const postActivitiesQueries = allQueries.filter(
    (query) => query.queryKey.indexOf('activities') !== -1,
  );
  postActivitiesQueries.forEach((query) => {
    queryClient.setQueryData(
      query.queryKey,
      (oldData: InfiniteData<PostListDtoCursorBasePagination>) => {
        return {
          ...oldData,
          pages: oldData.pages.map((page) => ({
            ...page,
            data: page.data.map((oldPost) =>
              oldPost.id === post.data.id
                ? {
                    ...oldPost,
                    reactionsCountTotal: post.data.reactionsCountTotal,
                    myReactions: post.data.myReactions,
                  }
                : oldPost,
            ),
          })),
        };
      },
    );
  });

  const postFeedQueries = allQueries.filter(
    (query) => query.queryKey.indexOf('feed') !== -1,
  );
  postFeedQueries.forEach((query) => {
    queryClient.setQueryData(
      query.queryKey,
      (oldData: InfiniteData<PostListDtoCursorBasePagination>) => {
        return {
          ...oldData,
          pages: oldData.pages.map((page) => ({
            ...page,
            data: page.data.map((oldPost) =>
              oldPost.id === post.data.id
                ? {
                    ...oldPost,
                    reactionsCountTotal: post.data.reactionsCountTotal,
                    myReactions: post.data.myReactions,
                  }
                : oldPost,
            ),
          })),
        };
      },
    );
  });

  await updateSingleItem({
    queryClient,
    queryKey: ['post', post.data.id],
    update: {
      data: {
        data: {
          reactionsCountTotal: post.data.reactionsCountTotal,
          myReactions: post.data.myReactions,
        },
      },
    },
    fields: ['reactionsCountTotal', 'myReactions'],
  });
};
