export const humaniseNumber = (number: number, languageCode = 'en'): string => {
  const formatter = new Intl.NumberFormat(languageCode, {
    notation: 'compact',
    compactDisplay: 'short',
    maximumFractionDigits: 1,
  });

  return formatter.format(number);
};

export const humanisePoints = (
  points?: number,
  abbreviate: boolean = false,
  maximumFractionDigits = 2,
): string => {
  if (!points) {
    return '0';
  }

  if (abbreviate && points > 1000) {
    return `${new Intl.NumberFormat('en-US', {
      maximumFractionDigits,
    }).format(Math.round((points / 1000 + Number.EPSILON) * 100) / 100)}k`;
  }

  return `${new Intl.NumberFormat('en-US', {
    maximumFractionDigits,
  }).format(Math.round((points + Number.EPSILON) * 100) / 100)}`;
};
