import {Platform} from 'react-native';
import * as Haptics from 'expo-haptics';
import logger from '@/src/utils/logger';
import {storage} from '@/src/utils/localStorage';

// Key for storing haptic feedback preference
export const HAPTIC_FEEDBACK_KEY = 'HAPTIC_FEEDBACK_ENABLED';

export const triggerHaptics = async (
  style = Haptics.ImpactFeedbackStyle.Soft,
) => {
  // Check if haptics are enabled in storage
  const hapticFeedbackEnabled =
    storage.getString(HAPTIC_FEEDBACK_KEY, 'true') === 'true';

  if (
    hapticFeedbackEnabled &&
    (Platform.OS === 'ios' || Platform.OS === 'android')
  ) {
    await Haptics.impactAsync(style);
  } else if (!hapticFeedbackEnabled) {
    // Haptics are disabled by user preference
    return;
  } else {
    logger.warn('Haptics not supported on this platform.');
  }
};
