import {Platform, Share} from 'react-native';
import logger from '@/src/utils/logger';
import {generateBranchLink} from '../services/branchService';

export interface ShareData {
  path: string;
  params?: Record<string, string>;
  title?: string;
  description?: string;
  imageUrl?: string;
  type?: string;
  feature?: string;
  campaign?: string;
  tags?: string[];
}

export interface ChallengeInviteData {
  challengeId: string;
  passcode?: string;
  challengeName?: string;
  customShareMessage?: string;
}

/**
 * Generates a universal Branch.io deep link
 */
export const generateDeepLink = async (data: ShareData): Promise<string> => {
  try {
    const {
      path,
      params,
      title,
      description,
      imageUrl,
      type,
      feature,
      campaign,
      tags,
    } = data;

    // Ensure all values are properly defined to avoid Branch SDK crashes
    const safePath = path && typeof path === 'string' ? path : '';
    const safeTitle =
      title && typeof title === 'string' && title.trim()
        ? title.trim()
        : 'GoJoe App';
    const safeDescription =
      description && typeof description === 'string' && description.trim()
        ? description.trim()
        : 'Check this out on <PERSON>Joe!';
    const safeImageUrl =
      imageUrl && typeof imageUrl === 'string' && imageUrl.trim()
        ? imageUrl.trim()
        : undefined;
    const safeType =
      type && typeof type === 'string' && type.trim()
        ? type.trim()
        : 'app_share';

    // Build the URL with params, ensuring all param values are strings
    const safeParams = params
      ? Object.fromEntries(
          Object.entries(params).filter(
            ([key, value]) =>
              key &&
              typeof key === 'string' &&
              value &&
              typeof value === 'string',
          ),
        )
      : {};

    const queryString =
      Object.keys(safeParams).length > 0
        ? '?' +
          Object.entries(safeParams)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join('&')
        : '';
    const fullPath = `${safePath}${queryString}`;

    // Create the deep link data with only defined values
    const linkData: any = {
      $canonical_url: fullPath,
      $desktop_url: fullPath,
      $ios_url: fullPath,
      $android_url: fullPath,
      title: safeTitle,
      description: safeDescription,
      type: safeType,
      ...safeParams, // Include all params as top-level properties for Branch
    };

    // For challenge invites, explicitly add challengeId from the path
    if (safePath.startsWith('challenge/')) {
      const challengeId = safePath.replace('challenge/', '');
      if (challengeId) {
        linkData.challengeId = challengeId;
      }
    }

    // Only add imageUrl if it's defined
    if (safeImageUrl) {
      linkData.imageUrl = safeImageUrl;
    }

    // Create the link properties with safe values
    const safeFeature =
      feature && typeof feature === 'string' && feature.trim()
        ? feature.trim()
        : 'app_share';
    const safeCampaign =
      campaign && typeof campaign === 'string' && campaign.trim()
        ? campaign.trim()
        : 'general_sharing';
    const safeTags =
      Array.isArray(tags) && tags.length > 0
        ? tags.filter((tag) => tag && typeof tag === 'string')
        : ['app', 'share'];

    const linkProperties = {
      feature: safeFeature,
      channel: 'app_share',
      campaign: safeCampaign,
      tags: safeTags,
      // Remove alias to let Branch use our $canonical_url instead
    };

    // Generate the Branch link
    const branchLink = await generateBranchLink(linkData, linkProperties);

    logger.info('Generated deep link:', branchLink);
    return branchLink;
  } catch (error) {
    logger.error('Failed to generate deep link:', error);
    // Fallback to a basic URL if Branch fails
    const safePath =
      data.path && typeof data.path === 'string' ? data.path : '';
    const safeParams = data.params
      ? Object.fromEntries(
          Object.entries(data.params).filter(
            ([key, value]) =>
              key &&
              typeof key === 'string' &&
              value &&
              typeof value === 'string',
          ),
        )
      : {};

    const queryString =
      Object.keys(safeParams).length > 0
        ? '?' +
          Object.entries(safeParams)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join('&')
        : '';
    const fallbackUrl = `https://app.gojoe.com/${safePath}${queryString}`;
    return fallbackUrl;
  }
};

/**
 * Universal sharing function
 */
export const shareContent = async (data: ShareData): Promise<void> => {
  try {
    const {title, description} = data;
    logger.debug('shareContent data:', data);
    // Generate the deep link
    const deepLink = await generateDeepLink(data);
    logger.debug('share title:', title);
    logger.debug('share description:', description);
    logger.debug('share deepLink:', deepLink);

    // Use React Native's Share API which works on both iOS and Android
    const shareOptions = {
      message: `${description || 'Check this out on GoJoe!'}\n\n${deepLink}`,
      ...(Platform.OS === 'ios' ? {title: title || 'Share from GoJoe'} : {}),
    };

    const result = await Share.share(shareOptions);
    // Log the result for debugging
    if (result.action === Share.sharedAction) {
      logger.info('Content shared successfully');
    } else if (result.action === Share.dismissedAction) {
      logger.info('Share dialog was dismissed');
    }
  } catch (error) {
    logger.error('Failed to share content:', error);
    throw error;
  }
};

/**
 * Shares the challenge invite link using native share options
 * This is a wrapper around the universal shareContent function
 */
export const shareInviteLink = async (
  data: ChallengeInviteData,
): Promise<void> => {
  const {challengeId, passcode, challengeName, customShareMessage} = data;

  // Ensure all values are properly defined to avoid Branch SDK crashes
  const safeChallengeId =
    challengeId && typeof challengeId === 'string' && challengeId.trim()
      ? challengeId.trim()
      : '';
  const safePasscode =
    passcode &&
    typeof passcode === 'string' &&
    passcode.trim() &&
    passcode.trim() !== 'undefined'
      ? passcode.trim()
      : undefined;
  const safeChallengeNameForTitle =
    challengeName && typeof challengeName === 'string' && challengeName.trim()
      ? challengeName.trim()
      : null;
  const safeChallengeNameForDescription =
    challengeName && typeof challengeName === 'string' && challengeName.trim()
      ? challengeName.trim()
      : null;
  const safeCustomShareMessage =
    customShareMessage &&
    typeof customShareMessage === 'string' &&
    customShareMessage.trim()
      ? customShareMessage.trim()
      : null;

  // Generate a unique branch identifier for this link
  const branchId = `br_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

  // Build params object with passcode and unique branch identifier
  const linkParams: Record<string, string> = {
    branch: branchId, // Always include unique branch identifier
  };

  // Only add passcode if it's valid
  if (safePasscode) {
    linkParams.passcode = safePasscode;
  }

  // Convert challenge data to universal share data
  const shareData: ShareData = {
    path: 'challenge/' + safeChallengeId,
    params: linkParams,
    title: safeChallengeNameForTitle
      ? `${safeChallengeNameForTitle}`
      : 'Join me on GoJoe!',
    description:
      safeCustomShareMessage ||
      (safeChallengeNameForDescription
        ? `Join me in the ${safeChallengeNameForDescription} challenge on GoJoe!`
        : 'Join me in this challenge on GoJoe!'),

    type: 'challenge_invite',
    feature: 'challenge_invite',
    campaign: 'challenge_invitation',
    tags: ['challenge', 'invite'],
  };

  logger.debug('shareData', shareData);
  return shareContent(shareData);
};

// Helper functions for common sharing scenarios

/**
 * Share a user profile
 */
export const shareUserProfile = async (
  userId: string,
  userName?: string,
): Promise<void> => {
  const shareData: ShareData = {
    path: 'user/' + userId,
    title: userName ? `${userName}'s Profile` : 'User Profile',
    description: userName
      ? `Check out ${userName}'s profile on GoJoe!`
      : 'Check out this profile on GoJoe!',
    type: 'user_profile',
    feature: 'profile_share',
    campaign: 'user_sharing',
    tags: ['user', 'profile'],
  };

  return shareContent(shareData);
};

/**
 * Share a team
 */
export const shareTeam = async (
  teamId: string,
  teamName?: string,
  challengeId?: string,
): Promise<void> => {
  const shareData: ShareData = {
    path: 'team/' + teamId,
    params: challengeId ? {challengeId} : undefined,
    title: teamName ? `${teamName} Team` : 'Team',
    description: teamName
      ? `Join the ${teamName} team on GoJoe!`
      : 'Join this team on GoJoe!',
    type: 'team_invite',
    feature: 'team_share',
    campaign: 'team_sharing',
    tags: ['team', 'invite'],
  };

  return shareContent(shareData);
};

/**
 * Share a workout/activity
 */
export const shareActivity = async (
  activityId: string,
  activityName?: string,
): Promise<void> => {
  const shareData: ShareData = {
    path: 'activity/' + activityId,
    title: activityName ? `${activityName} Activity` : 'Activity',
    description: activityName
      ? `Check out my ${activityName} on GoJoe!`
      : 'Check out my activity on GoJoe!',
    type: 'activity_share',
    feature: 'activity_share',
    campaign: 'activity_sharing',
    tags: ['activity', 'workout'],
  };

  return shareContent(shareData);
};

/**
 * Share the app in general
 */
export const shareApp = async (customMessage?: string): Promise<void> => {
  const shareData: ShareData = {
    path: '',
    title: 'GoJoe - Health & Fitness',
    description:
      customMessage ||
      'Join me on GoJoe - the ultimate health and fitness platform!',
    type: 'app_share',
    feature: 'app_share',
    campaign: 'app_promotion',
    tags: ['app', 'general'],
  };

  return shareContent(shareData);
};

export const getChallengeShareMessage = (data: any, myTeamName: string) => {
  if (!data || !myTeamName) return;
  let msg;
  if (data.business && data.business?.passcode) {
    msg = `Join me on GoJoe. Download or open the app > sign-in > enter code \`${data.business?.passcode}\` to unlock premium > enter the \`${data.name}\` challenge by creating/joining a team (my team is called \`${myTeamName}\`).`;
  }
  if (data.business === null) {
    msg = `Join me on GoJoe. Download or open the app > sign-in > go to challenges > enter the \`${data.name}\` (my team is called \`${myTeamName}\`).`;
  }
  return msg;
};
