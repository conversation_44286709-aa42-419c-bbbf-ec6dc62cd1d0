/* eslint-env node */
// Learn more https://docs.expo.io/guides/customizing-metro
// const {getDefaultConfig} = require('expo/metro-config');
const {getSentryExpoConfig} = require('@sentry/react-native/metro');

/** @type {import('@sentry/react-native/metro').MetroConfig} */
const config = getSentryExpoConfig(__dirname, {
  // [Web-only]: Enables CSS support in Metro.
  isCSSEnabled: true,
});

config.resolver.sourceExts.push('cjs');

// add nice web support with optimizing compiler + CSS extraction
const {withTamagui} = require('@tamagui/metro-plugin');
module.exports = withTamagui(config, {
  components: ['tamagui'],
  config: './tamagui.config.ts',
  outputCSS: './tamagui-web.css',
});
