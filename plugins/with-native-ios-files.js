const {withXcodeProject} = require('@expo/config-plugins');
const fs = require('fs');
const path = require('path');

const SOURCE_DIR_NAME = 'native/ios';

const withNativeIosFiles = (config) => {
  return withXcodeProject(config, (config) => {
    const projectRoot = config.modRequest.projectRoot;
    const iosRoot = path.join(projectRoot, 'ios');
    const projectName = getIosProjectName(iosRoot);
    const projectFolder = path.join(iosRoot, projectName);
    const sourceRoot = path.join(projectRoot, SOURCE_DIR_NAME);

    const copiedFiles = [];
    copyRecursiveSync(sourceRoot, projectFolder, copiedFiles);
    addFilesToXcode(config.modResults, copiedFiles, projectFolder);

    return config;
  });
};

function getIosProjectName(iosRoot) {
  const projectDir = fs
    .readdirSync(iosRoot)
    .find((f) => f.endsWith('.xcodeproj'));
  if (!projectDir) throw new Error('❌ Could not find .xcodeproj in ios/');
  return path.basename(projectDir, '.xcodeproj');
}

function copyRecursiveSync(src, dest, copiedFiles = []) {
  const stat = fs.statSync(src);
  if (stat.isDirectory()) {
    fs.mkdirSync(dest, {recursive: true});
    fs.readdirSync(src).forEach((child) =>
      copyRecursiveSync(
        path.join(src, child),
        path.join(dest, child),
        copiedFiles,
      ),
    );
  } else {
    fs.copyFileSync(src, dest);
    copiedFiles.push(dest); // ✅ track this file
  }
}

function addFilesToXcode(project, filePaths, projectFolder) {
  const iosRoot = path.dirname(projectFolder); // ios/
  filePaths.forEach((filePath) => {
    const ext = path.extname(filePath);
    if (!['.m', '.mm', '.cpp', '.swift', '.h'].includes(ext)) return;

    try {
      addFileToXcode(project, filePath, iosRoot);
    } catch (err) {
      console.warn(`❌ Failed to add ${filePath}: ${err.message}`);
    }
  });
}

function addFileToXcode(project, filePath, iosRoot) {
  const relativeToIos = path.relative(iosRoot, filePath).replace(/\\/g, '/'); // eg: GoJoe/GJRNHealthKit.m
  const groupSegments = path.dirname(relativeToIos).split('/').filter(Boolean); // ['GoJoe']

  const groupUUID = ensureGroupRecursivelyAndReturnUUID(project, groupSegments);
  if (!groupUUID) {
    console.warn(`❌ Could not create group for ${relativeToIos}`);
    return;
  }

  const added = project.addSourceFile(
    relativeToIos,
    {target: project.getFirstTarget().uuid},
    groupUUID,
  );

  if (!added) {
    console.warn(`⚠️ File ${relativeToIos} may already be added to Xcode.`);
  }
}

function ensureGroupRecursivelyAndReturnUUID(project, pathSegments) {
  const objects = project.hash.project.objects;
  let parentUUID = project.getFirstProject().firstProject.mainGroup;
  let groupUUID = null;

  for (const name of pathSegments) {
    const existing = Object.entries(objects.PBXGroup).find(
      ([uuid, group]) =>
        group.name === name &&
        group.sourceTree === '"<group>"' &&
        (objects.PBXGroup[parentUUID]?.children || []).some(
          (child) => child.value === uuid,
        ),
    );

    if (existing) {
      groupUUID = existing[0];
    } else {
      groupUUID = project.generateUuid();
      objects.PBXGroup[groupUUID] = {
        isa: 'PBXGroup',
        children: [],
        name,
        sourceTree: '"<group>"',
      };

      if (!objects.PBXGroup[parentUUID]?.children) {
        objects.PBXGroup[parentUUID].children = [];
      }

      objects.PBXGroup[parentUUID].children.push({
        value: groupUUID,
        comment: name,
      });
    }

    parentUUID = groupUUID;
  }

  return groupUUID;
}

module.exports = withNativeIosFiles;
