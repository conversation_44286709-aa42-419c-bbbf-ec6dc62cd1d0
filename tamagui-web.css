._ovs-contain {overscroll-behavior:contain;}
  .is_Text .is_Text {display:inline-flex;}
  ._dsp_contents {display:contents;}
  :root {--c-white0:rgba(255,255,255,0);--c-white075:rgba(255,255,255,0.75);--c-white05:rgba(255,255,255,0.5);--c-white025:rgba(255,255,255,0.25);--c-black0:rgba(10,10,10,0);--c-black075:rgba(10,10,10,0.75);--c-black05:rgba(10,10,10,0.5);--c-black025:rgba(10,10,10,0.25);--c-white1:#fff;--c-white2:#f8f8f8;--c-white3:hsl(0, 0%, 96.3%);--c-white4:hsl(0, 0%, 94.1%);--c-white5:hsl(0, 0%, 92.0%);--c-white6:hsl(0, 0%, 90.0%);--c-white7:hsl(0, 0%, 88.5%);--c-white8:hsl(0, 0%, 81.0%);--c-white9:hsl(0, 0%, 56.1%);--c-white10:hsl(0, 0%, 50.3%);--c-white11:hsl(0, 0%, 42.5%);--c-white12:hsl(0, 0%, 9.0%);--c-black1:#050505;--c-black2:#151515;--c-black3:#191919;--c-black4:#232323;--c-black5:#282828;--c-black6:#323232;--c-black7:#424242;--c-black8:#494949;--c-black9:#545454;--c-black10:#626262;--c-black11:#a5a5a5;--c-black12:#fff;--c-blue1Light:hsl(206, 100%, 99.2%);--c-blue2Light:hsl(210, 100%, 98.0%);--c-blue3Light:hsl(209, 100%, 96.5%);--c-blue4Light:hsl(210, 98.8%, 94.0%);--c-blue5Light:hsl(209, 95.0%, 90.1%);--c-blue6Light:hsl(209, 81.2%, 84.5%);--c-blue7Light:hsl(208, 77.5%, 76.9%);--c-blue8Light:hsl(206, 81.9%, 65.3%);--c-blue9Light:hsl(206, 100%, 50.0%);--c-blue10Light:hsl(208, 100%, 47.3%);--c-blue11Light:hsl(211, 100%, 43.2%);--c-blue12Light:hsl(211, 100%, 15.0%);--c-gray1Light:hsl(0, 0%, 99.0%);--c-gray2Light:hsl(0, 0%, 97.3%);--c-gray3Light:hsl(0, 0%, 95.1%);--c-gray4Light:hsl(0, 0%, 93.0%);--c-gray5Light:hsl(0, 0%, 90.9%);--c-gray6Light:hsl(0, 0%, 88.7%);--c-gray7Light:hsl(0, 0%, 85.8%);--c-gray8Light:hsl(0, 0%, 78.0%);--c-gray9Light:hsl(0, 0%, 56.1%);--c-gray10Light:hsl(0, 0%, 52.3%);--c-gray11Light:hsl(0, 0%, 43.5%);--c-gray12Light:hsl(0, 0%, 9.0%);--c-green1Light:hsl(136, 50.0%, 98.9%);--c-green2Light:hsl(138, 62.5%, 96.9%);--c-green3Light:hsl(139, 55.2%, 94.5%);--c-green4Light:hsl(140, 48.7%, 91.0%);--c-green5Light:hsl(141, 43.7%, 86.0%);--c-green6Light:hsl(143, 40.3%, 79.0%);--c-green7Light:hsl(146, 38.5%, 69.0%);--c-green8Light:hsl(151, 40.2%, 54.1%);--c-green9Light:hsl(151, 55.0%, 41.5%);--c-green10Light:hsl(152, 57.5%, 37.6%);--c-green11Light:hsl(153, 67.0%, 28.5%);--c-green12Light:hsl(155, 40.0%, 14.0%);--c-orange1Light:hsl(24, 70.0%, 99.0%);--c-orange2Light:hsl(24, 83.3%, 97.6%);--c-orange3Light:hsl(24, 100%, 95.3%);--c-orange4Light:hsl(25, 100%, 92.2%);--c-orange5Light:hsl(25, 100%, 88.2%);--c-orange6Light:hsl(25, 100%, 82.8%);--c-orange7Light:hsl(24, 100%, 75.3%);--c-orange8Light:hsl(24, 94.5%, 64.3%);--c-orange9Light:hsl(24, 94.0%, 50.0%);--c-orange10Light:hsl(24, 100%, 46.5%);--c-orange11Light:hsl(24, 100%, 37.0%);--c-orange12Light:hsl(15, 60.0%, 17.0%);--c-pink1Light:hsl(322, 100%, 99.4%);--c-pink2Light:hsl(323, 100%, 98.4%);--c-pink3Light:hsl(323, 86.3%, 96.5%);--c-pink4Light:hsl(323, 78.7%, 94.2%);--c-pink5Light:hsl(323, 72.2%, 91.1%);--c-pink6Light:hsl(323, 66.3%, 86.6%);--c-pink7Light:hsl(323, 62.0%, 80.1%);--c-pink8Light:hsl(323, 60.3%, 72.4%);--c-pink9Light:hsl(322, 65.0%, 54.5%);--c-pink10Light:hsl(322, 63.9%, 50.7%);--c-pink11Light:hsl(322, 75.0%, 46.0%);--c-pink12Light:hsl(320, 70.0%, 13.5%);--c-purple1Light:hsl(280, 65.0%, 99.4%);--c-purple2Light:hsl(276, 100%, 99.0%);--c-purple3Light:hsl(276, 83.1%, 97.0%);--c-purple4Light:hsl(275, 76.4%, 94.7%);--c-purple5Light:hsl(275, 70.8%, 91.8%);--c-purple6Light:hsl(274, 65.4%, 87.8%);--c-purple7Light:hsl(273, 61.0%, 81.7%);--c-purple8Light:hsl(272, 60.0%, 73.5%);--c-purple9Light:hsl(272, 51.0%, 54.0%);--c-purple10Light:hsl(272, 46.8%, 50.3%);--c-purple11Light:hsl(272, 50.0%, 45.8%);--c-purple12Light:hsl(272, 66.0%, 16.0%);--c-red1Light:hsl(359, 100%, 99.4%);--c-red2Light:hsl(359, 100%, 98.6%);--c-red3Light:hsl(360, 100%, 96.8%);--c-red4Light:hsl(360, 97.9%, 94.8%);--c-red5Light:hsl(360, 90.2%, 91.9%);--c-red6Light:hsl(360, 81.7%, 87.8%);--c-red7Light:hsl(359, 74.2%, 81.7%);--c-red8Light:hsl(359, 69.5%, 74.3%);--c-red9Light:hsl(358, 75.0%, 59.0%);--c-red10Light:hsl(358, 69.4%, 55.2%);--c-red11Light:hsl(358, 65.0%, 48.7%);--c-red12Light:hsl(354, 50.0%, 14.6%);--c-yellow1Light:hsl(60, 54.0%, 98.5%);--c-yellow2Light:hsl(52, 100%, 95.5%);--c-yellow3Light:hsl(55, 100%, 90.9%);--c-yellow4Light:hsl(54, 100%, 86.6%);--c-yellow5Light:hsl(52, 97.9%, 82.0%);--c-yellow6Light:hsl(50, 89.4%, 76.1%);--c-yellow7Light:hsl(47, 80.4%, 68.0%);--c-yellow8Light:hsl(48, 100%, 46.1%);--c-yellow9Light:hsl(53, 92.0%, 50.0%);--c-yellow10Light:hsl(50, 100%, 48.5%);--c-yellow11Light:hsl(42, 100%, 29.0%);--c-yellow12Light:hsl(40, 55.0%, 13.5%);--c-blue1Dark:hsl(212, 35.0%, 9.2%);--c-blue2Dark:hsl(216, 50.0%, 11.8%);--c-blue3Dark:hsl(214, 59.4%, 15.3%);--c-blue4Dark:hsl(214, 65.8%, 17.9%);--c-blue5Dark:hsl(213, 71.2%, 20.2%);--c-blue6Dark:hsl(212, 77.4%, 23.1%);--c-blue7Dark:hsl(211, 85.1%, 27.4%);--c-blue8Dark:hsl(211, 89.7%, 34.1%);--c-blue9Dark:hsl(206, 100%, 50.0%);--c-blue10Dark:hsl(209, 100%, 60.6%);--c-blue11Dark:hsl(210, 100%, 66.1%);--c-blue12Dark:hsl(206, 98.0%, 95.8%);--c-gray1Dark:hsl(0, 0%, 8.5%);--c-gray2Dark:hsl(0, 0%, 11.0%);--c-gray3Dark:hsl(0, 0%, 13.6%);--c-gray4Dark:hsl(0, 0%, 15.8%);--c-gray5Dark:hsl(0, 0%, 17.9%);--c-gray6Dark:hsl(0, 0%, 20.5%);--c-gray7Dark:hsl(0, 0%, 24.3%);--c-gray8Dark:hsl(0, 0%, 31.2%);--c-gray9Dark:hsl(0, 0%, 43.9%);--c-gray10Dark:hsl(0, 0%, 49.4%);--c-gray11Dark:hsl(0, 0%, 62.8%);--c-gray12Dark:hsl(0, 0%, 93.0%);--c-green1Dark:hsl(146, 30.0%, 7.4%);--c-green2Dark:hsl(155, 44.2%, 8.4%);--c-green3Dark:hsl(155, 46.7%, 10.9%);--c-green4Dark:hsl(154, 48.4%, 12.9%);--c-green5Dark:hsl(154, 49.7%, 14.9%);--c-green6Dark:hsl(154, 50.9%, 17.6%);--c-green7Dark:hsl(153, 51.8%, 21.8%);--c-green8Dark:hsl(151, 51.7%, 28.4%);--c-green9Dark:hsl(151, 55.0%, 41.5%);--c-green10Dark:hsl(151, 49.3%, 46.5%);--c-green11Dark:hsl(151, 50.0%, 53.2%);--c-green12Dark:hsl(137, 72.0%, 94.0%);--c-orange1Dark:hsl(30, 70.0%, 7.2%);--c-orange2Dark:hsl(28, 100%, 8.4%);--c-orange3Dark:hsl(26, 91.1%, 11.6%);--c-orange4Dark:hsl(25, 88.3%, 14.1%);--c-orange5Dark:hsl(24, 87.6%, 16.6%);--c-orange6Dark:hsl(24, 88.6%, 19.8%);--c-orange7Dark:hsl(24, 92.4%, 24.0%);--c-orange8Dark:hsl(25, 100%, 29.0%);--c-orange9Dark:hsl(24, 94.0%, 50.0%);--c-orange10Dark:hsl(24, 100%, 58.5%);--c-orange11Dark:hsl(24, 100%, 62.2%);--c-orange12Dark:hsl(24, 97.0%, 93.2%);--c-pink1Dark:hsl(318, 25.0%, 9.6%);--c-pink2Dark:hsl(319, 32.2%, 11.6%);--c-pink3Dark:hsl(319, 41.0%, 16.0%);--c-pink4Dark:hsl(320, 45.4%, 18.7%);--c-pink5Dark:hsl(320, 49.0%, 21.1%);--c-pink6Dark:hsl(321, 53.6%, 24.4%);--c-pink7Dark:hsl(321, 61.1%, 29.7%);--c-pink8Dark:hsl(322, 74.9%, 37.5%);--c-pink9Dark:hsl(322, 65.0%, 54.5%);--c-pink10Dark:hsl(323, 72.8%, 59.2%);--c-pink11Dark:hsl(325, 90.0%, 66.4%);--c-pink12Dark:hsl(322, 90.0%, 95.8%);--c-purple1Dark:hsl(284, 20.0%, 9.6%);--c-purple2Dark:hsl(283, 30.0%, 11.8%);--c-purple3Dark:hsl(281, 37.5%, 16.5%);--c-purple4Dark:hsl(280, 41.2%, 20.0%);--c-purple5Dark:hsl(279, 43.8%, 23.3%);--c-purple6Dark:hsl(277, 46.4%, 27.5%);--c-purple7Dark:hsl(275, 49.3%, 34.6%);--c-purple8Dark:hsl(272, 52.1%, 45.9%);--c-purple9Dark:hsl(272, 51.0%, 54.0%);--c-purple10Dark:hsl(273, 57.3%, 59.1%);--c-purple11Dark:hsl(275, 80.0%, 71.0%);--c-purple12Dark:hsl(279, 75.0%, 95.7%);--c-red1Dark:hsl(353, 23.0%, 9.8%);--c-red2Dark:hsl(357, 34.4%, 12.0%);--c-red3Dark:hsl(356, 43.4%, 16.4%);--c-red4Dark:hsl(356, 47.6%, 19.2%);--c-red5Dark:hsl(356, 51.1%, 21.9%);--c-red6Dark:hsl(356, 55.2%, 25.9%);--c-red7Dark:hsl(357, 60.2%, 31.8%);--c-red8Dark:hsl(358, 65.0%, 40.4%);--c-red9Dark:hsl(358, 75.0%, 59.0%);--c-red10Dark:hsl(358, 85.3%, 64.0%);--c-red11Dark:hsl(358, 100%, 69.5%);--c-red12Dark:hsl(351, 89.0%, 96.0%);--c-yellow1Dark:hsl(45, 100%, 5.5%);--c-yellow2Dark:hsl(46, 100%, 6.7%);--c-yellow3Dark:hsl(45, 100%, 8.7%);--c-yellow4Dark:hsl(45, 100%, 10.4%);--c-yellow5Dark:hsl(47, 100%, 12.1%);--c-yellow6Dark:hsl(49, 100%, 14.3%);--c-yellow7Dark:hsl(49, 90.3%, 18.4%);--c-yellow8Dark:hsl(50, 100%, 22.0%);--c-yellow9Dark:hsl(53, 92.0%, 50.0%);--c-yellow10Dark:hsl(54, 100%, 68.0%);--c-yellow11Dark:hsl(48, 100%, 47.0%);--c-yellow12Dark:hsl(53, 100%, 91.0%);--t-radius-0:0px;--t-radius-1:3px;--t-radius-2:5px;--t-radius-3:7px;--t-radius-4:9px;--t-radius-5:10px;--t-radius-6:16px;--t-radius-7:19px;--t-radius-8:22px;--t-radius-9:26px;--t-radius-10:34px;--t-radius-11:42px;--t-radius-12:50px;--t-radius-true:9px;--t-zIndex-0:0;--t-zIndex-1:100;--t-zIndex-2:200;--t-zIndex-3:300;--t-zIndex-4:400;--t-zIndex-5:500;--t-space-0:0px;--t-space-1:2px;--t-space-2:7px;--t-space-3:13px;--t-space-4:18px;--t-space-5:24px;--t-space-6:32px;--t-space-7:39px;--t-space-8:46px;--t-space-9:53px;--t-space-10:60px;--t-space-11:74px;--t-space-12:88px;--t-space-13:102px;--t-space-14:116px;--t-space-15:130px;--t-space-16:144px;--t-space-17:144px;--t-space-18:158px;--t-space-19:172px;--t-space-20:186px;--t-space-0--25:0.5px;--t-space-0--5:1px;--t-space-0--75:1.5px;--t-space-1--5:4px;--t-space-2--5:10px;--t-space-3--5:16px;--t-space-true:18px;--t-space-4--5:21px;--t-space--0--25:-0.5px;--t-space--0--5:-1px;--t-space--0--75:-1.5px;--t-space--1:-2px;--t-space--1--5:-4px;--t-space--2:-7px;--t-space--2--5:-10px;--t-space--3:-13px;--t-space--3--5:-16px;--t-space--4:-18px;--t-space--true:-18px;--t-space--4--5:-21px;--t-space--5:-24px;--t-space--6:-32px;--t-space--7:-39px;--t-space--8:-46px;--t-space--9:-53px;--t-space--10:-60px;--t-space--11:-74px;--t-space--12:-88px;--t-space--13:-102px;--t-space--14:-116px;--t-space--15:-130px;--t-space--16:-144px;--t-space--17:-144px;--t-space--18:-158px;--t-space--19:-172px;--t-space--20:-186px;--t-size-0:0px;--t-size-1:20px;--t-size-2:28px;--t-size-3:36px;--t-size-4:44px;--t-size-5:52px;--t-size-6:64px;--t-size-7:74px;--t-size-8:84px;--t-size-9:94px;--t-size-10:104px;--t-size-11:124px;--t-size-12:144px;--t-size-13:164px;--t-size-14:184px;--t-size-15:204px;--t-size-16:224px;--t-size-17:224px;--t-size-18:244px;--t-size-19:264px;--t-size-20:284px;--t-size-0--25:2px;--t-size-0--5:4px;--t-size-0--75:8px;--t-size-1--5:24px;--t-size-2--5:32px;--t-size-3--5:40px;--t-size-true:44px;--t-size-4--5:48px}
:root .font_inter, :root .t_lang-inter-default .font_inter {--f-family:Inter;--f-size-4:14px;--f-size-5:16px;--f-size-6:18px;--f-weight-1:100;--f-weight-2:200;--f-weight-3:300;--f-weight-4:400;--f-weight-5:500;--f-weight-6:600;--f-weight-7:700;--f-weight-8:800;--f-weight-9:900}
:root .font_heading, :root .t_lang-heading-default .font_heading {--f-family:Inter, -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;--f-lineHeight-1:22px;--f-lineHeight-2:23px;--f-lineHeight-3:24px;--f-lineHeight-4:25px;--f-lineHeight-5:24px;--f-lineHeight-6:27px;--f-lineHeight-7:32px;--f-lineHeight-8:35px;--f-lineHeight-9:40px;--f-lineHeight-10:53px;--f-lineHeight-11:66px;--f-lineHeight-12:73px;--f-lineHeight-13:84px;--f-lineHeight-14:106px;--f-lineHeight-15:130px;--f-lineHeight-16:152px;--f-lineHeight-true:25px;--f-weight-1:400;--f-weight-2:400;--f-weight-3:400;--f-weight-4:400;--f-weight-5:400;--f-weight-6:400;--f-weight-7:700;--f-weight-8:700;--f-weight-9:700;--f-weight-10:700;--f-weight-11:700;--f-weight-12:700;--f-weight-13:700;--f-weight-14:700;--f-weight-15:700;--f-weight-16:700;--f-weight-true:700;--f-letterSpacing-1:2px;--f-letterSpacing-2:2px;--f-letterSpacing-3:2px;--f-letterSpacing-4:2px;--f-letterSpacing-5:2px;--f-letterSpacing-6:1px;--f-letterSpacing-7:0px;--f-letterSpacing-8:0px;--f-letterSpacing-9:-1px;--f-letterSpacing-10:-1.5px;--f-letterSpacing-11:-1.5px;--f-letterSpacing-12:-2px;--f-letterSpacing-13:-2px;--f-letterSpacing-14:-3px;--f-letterSpacing-15:-4px;--f-letterSpacing-16:-4px;--f-letterSpacing-true:-4px;--f-size-1:11px;--f-size-2:12px;--f-size-3:13px;--f-size-4:14px;--f-size-5:13px;--f-size-6:15px;--f-size-7:20px;--f-size-8:23px;--f-size-9:32px;--f-size-10:44px;--f-size-11:55px;--f-size-12:62px;--f-size-13:72px;--f-size-14:92px;--f-size-15:114px;--f-size-16:134px;--f-size-true:14px;--f-transform-1:uppercase;--f-transform-2:uppercase;--f-transform-3:uppercase;--f-transform-4:uppercase;--f-transform-5:uppercase;--f-transform-6:uppercase;--f-transform-7:none;--f-transform-8:none;--f-transform-9:none;--f-transform-10:none;--f-transform-11:none;--f-transform-12:none;--f-transform-13:none;--f-transform-14:none;--f-transform-15:none;--f-transform-16:none;--f-transform-true:none}
:root .font_body, :root .t_lang-body-default .font_body {--f-family:Inter, -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;--f-lineHeight-1:16px;--f-lineHeight-2:21px;--f-lineHeight-3:22px;--f-lineHeight-4:23px;--f-lineHeight-5:26px;--f-lineHeight-6:28px;--f-lineHeight-7:30px;--f-lineHeight-8:33px;--f-lineHeight-9:41px;--f-lineHeight-10:59px;--f-lineHeight-11:69px;--f-lineHeight-12:76px;--f-lineHeight-13:87px;--f-lineHeight-14:109px;--f-lineHeight-15:133px;--f-lineHeight-16:155px;--f-lineHeight-true:23px;--f-weight-1:400;--f-weight-2:400;--f-weight-3:400;--f-weight-4:400;--f-weight-5:400;--f-weight-6:400;--f-weight-7:600;--f-weight-8:600;--f-weight-9:600;--f-weight-10:600;--f-weight-11:600;--f-weight-12:600;--f-weight-13:600;--f-weight-14:600;--f-weight-15:600;--f-weight-16:600;--f-weight-true:600;--f-letterSpacing-1:0px;--f-letterSpacing-2:0px;--f-letterSpacing-3:0px;--f-letterSpacing-4:0px;--f-letterSpacing-5:0px;--f-letterSpacing-6:0px;--f-letterSpacing-7:0px;--f-letterSpacing-8:0px;--f-letterSpacing-9:0px;--f-letterSpacing-10:0px;--f-letterSpacing-11:0px;--f-letterSpacing-12:0px;--f-letterSpacing-13:0px;--f-letterSpacing-14:0px;--f-letterSpacing-15:0px;--f-letterSpacing-16:0px;--f-letterSpacing-true:0px;--f-size-1:11px;--f-size-2:12px;--f-size-3:13px;--f-size-4:14px;--f-size-5:16px;--f-size-6:18px;--f-size-7:20px;--f-size-8:23px;--f-size-9:30px;--f-size-10:46px;--f-size-11:55px;--f-size-12:62px;--f-size-13:72px;--f-size-14:92px;--f-size-15:114px;--f-size-16:134px;--f-size-true:14px}
:root .font_mono, :root .t_lang-mono-default .font_mono {--f-family:"ui-monospace", "SFMono-Regular", "SF Mono", Menlo, Consolas, "Liberation Mono", monospace;--f-size-1:11px;--f-size-2:12px;--f-size-3:13px;--f-size-4:14px;--f-size-5:16px;--f-size-6:18px;--f-size-7:20px;--f-size-8:22px;--f-size-9:30px;--f-size-10:42px;--f-size-11:52px;--f-size-12:62px;--f-size-13:72px;--f-size-14:92px;--f-size-15:114px;--f-size-16:124px;--f-lineHeight-1:16.5px;--f-lineHeight-2:18px;--f-lineHeight-3:19.5px;--f-lineHeight-4:21px;--f-lineHeight-5:24px;--f-lineHeight-6:27px;--f-lineHeight-7:30px;--f-lineHeight-8:33px;--f-lineHeight-9:45px;--f-lineHeight-10:63px;--f-lineHeight-11:78px;--f-lineHeight-12:93px;--f-lineHeight-13:108px;--f-lineHeight-14:138px;--f-lineHeight-15:171px;--f-lineHeight-16:186px;--f-weight-1:500;--f-weight-2:500;--f-weight-3:500;--f-weight-4:500;--f-weight-5:500;--f-weight-6:500;--f-weight-7:500;--f-weight-8:500;--f-weight-9:500;--f-weight-10:500;--f-weight-11:500;--f-weight-12:500;--f-weight-13:500;--f-weight-14:500;--f-weight-15:500;--f-weight-16:500;--f-letterSpacing-1:0px;--f-letterSpacing-2:0px;--f-letterSpacing-3:0px;--f-letterSpacing-4:0px;--f-letterSpacing-5:0px;--f-letterSpacing-6:0px;--f-letterSpacing-7:0px;--f-letterSpacing-8:0px;--f-letterSpacing-9:0px;--f-letterSpacing-10:0px;--f-letterSpacing-11:0px;--f-letterSpacing-12:0px;--f-letterSpacing-13:0px;--f-letterSpacing-14:0px;--f-letterSpacing-15:0px;--f-letterSpacing-16:0px}
:root .font_silkscreen, :root .t_lang-silkscreen-default .font_silkscreen {--f-family:Silkscreen, Fira Code, Monaco, Consolas, Ubuntu Mono, monospace;--f-size-1:11px;--f-size-2:12px;--f-size-3:13px;--f-size-4:14px;--f-size-5:15px;--f-size-6:16px;--f-size-7:18px;--f-size-8:21px;--f-size-9:28px;--f-size-10:42px;--f-size-11:52px;--f-size-12:62px;--f-size-13:72px;--f-size-14:92px;--f-size-15:114px;--f-size-16:124px;--f-lineHeight-1:19px;--f-lineHeight-2:20px;--f-lineHeight-3:22px;--f-lineHeight-4:23px;--f-lineHeight-5:24px;--f-lineHeight-6:25px;--f-lineHeight-7:28px;--f-lineHeight-8:31px;--f-lineHeight-9:40px;--f-lineHeight-10:56px;--f-lineHeight-11:68px;--f-lineHeight-12:80px;--f-lineHeight-13:92px;--f-lineHeight-14:116px;--f-lineHeight-15:143px;--f-lineHeight-16:155px;--f-weight-1:300;--f-weight-2:300;--f-weight-3:300;--f-weight-4:300;--f-weight-5:300;--f-weight-6:300;--f-weight-7:300;--f-weight-8:300;--f-weight-9:300;--f-weight-10:300;--f-weight-11:300;--f-weight-12:300;--f-weight-13:300;--f-weight-14:300;--f-weight-15:300;--f-weight-16:300;--f-letterSpacing-1:1px;--f-letterSpacing-2:1px;--f-letterSpacing-3:1px;--f-letterSpacing-4:1px;--f-letterSpacing-5:3px;--f-letterSpacing-6:3px;--f-letterSpacing-7:3px;--f-letterSpacing-8:3px;--f-letterSpacing-9:-2px;--f-letterSpacing-10:-3px;--f-letterSpacing-11:-3px;--f-letterSpacing-12:-4px;--f-letterSpacing-13:-4px;--f-letterSpacing-14:-4px;--f-letterSpacing-15:-4px;--f-letterSpacing-16:-4px}
  :root.t_dark .t_light , :root.t_dark .t_light .t_dark .t_light , :root.t_light, :root.t_light , :root.t_light .t_dark .t_light , .tm_xxt {--accentBackground:var(--c-blue4Light);--accentColor:var(--c-blue4Dark);--background0:var(--c-white0);--background025:var(--c-white025);--background05:var(--c-white05);--background075:var(--c-white075);--color1:var(--c-black12);--color2:var(--c-white2);--color3:var(--c-white3);--color4:var(--c-white4);--color5:var(--c-white5);--color6:var(--c-white6);--color7:var(--c-white7);--color8:var(--c-white8);--color9:var(--c-gray9Light);--color10:var(--c-white10);--color11:var(--c-white11);--color12:var(--c-gray12Light);--color0:var(--c-black0);--color025:var(--c-black025);--color05:var(--c-black05);--color075:var(--c-black075);--background:#FFFFFF;--backgroundHover:var(--c-white075);--backgroundPress:var(--c-white2);--backgroundFocus:var(--c-white2);--borderColor:var(--c-white4);--borderColorHover:var(--c-white3);--borderColorPress:var(--c-white5);--borderColorFocus:var(--c-white4);--color:#000000;--colorHover:var(--c-white11);--colorPress:var(--c-gray12Light);--colorFocus:var(--c-white11);--colorTransparent:var(--c-black0);--placeholderColor:var(--c-gray9Light);--outlineColor:var(--c-black025);--blue1:var(--c-blue1Light);--blue2:var(--c-blue2Light);--blue3:var(--c-blue3Light);--blue4:var(--c-blue4Light);--blue5:var(--c-blue5Light);--blue6:var(--c-blue6Light);--blue7:var(--c-blue7Light);--blue8:var(--c-blue8Light);--blue9:var(--c-blue9Dark);--blue10:var(--c-blue10Light);--blue11:var(--c-blue11Light);--blue12:var(--c-blue12Light);--gray1:var(--c-gray1Light);--gray2:var(--c-gray2Light);--gray3:var(--c-gray3Light);--gray4:var(--c-gray12Dark);--gray5:var(--c-gray5Light);--gray6:var(--c-gray6Light);--gray7:var(--c-gray7Light);--gray8:var(--c-gray8Light);--gray9:var(--c-gray9Light);--gray10:var(--c-gray10Light);--gray11:var(--c-gray11Light);--gray12:var(--c-gray12Light);--green1:var(--c-green1Light);--green2:var(--c-green2Light);--green3:var(--c-green3Light);--green4:var(--c-green4Light);--green5:var(--c-green5Light);--green6:var(--c-green6Light);--green7:var(--c-green7Light);--green8:var(--c-green8Light);--green9:var(--c-green9Dark);--green10:var(--c-green10Light);--green11:var(--c-green11Light);--green12:var(--c-green12Light);--orange1:var(--c-orange1Light);--orange2:var(--c-orange2Light);--orange3:var(--c-orange3Light);--orange4:var(--c-orange4Light);--orange5:var(--c-orange5Light);--orange6:var(--c-orange6Light);--orange7:var(--c-orange7Light);--orange8:var(--c-orange8Light);--orange9:var(--c-orange9Dark);--orange10:var(--c-orange10Light);--orange11:var(--c-orange11Light);--orange12:var(--c-orange12Light);--pink1:var(--c-pink1Light);--pink2:var(--c-pink2Light);--pink3:var(--c-pink3Light);--pink4:var(--c-pink4Light);--pink5:var(--c-pink5Light);--pink6:var(--c-pink6Light);--pink7:var(--c-pink7Light);--pink8:var(--c-pink8Light);--pink9:var(--c-pink9Dark);--pink10:var(--c-pink10Light);--pink11:var(--c-pink11Light);--pink12:var(--c-pink12Light);--purple1:var(--c-purple1Light);--purple2:var(--c-purple2Light);--purple3:var(--c-purple3Light);--purple4:var(--c-purple4Light);--purple5:var(--c-purple5Light);--purple6:var(--c-purple6Light);--purple7:var(--c-purple7Light);--purple8:var(--c-purple8Light);--purple9:var(--c-purple9Dark);--purple10:var(--c-purple10Light);--purple11:var(--c-purple11Light);--purple12:var(--c-purple12Light);--red1:var(--c-red1Light);--red2:var(--c-red2Light);--red3:var(--c-red3Light);--red4:var(--c-red4Light);--red5:var(--c-red5Light);--red6:var(--c-red6Light);--red7:var(--c-red7Light);--red8:var(--c-red8Light);--red9:var(--c-red9Dark);--red10:var(--c-red10Light);--red11:var(--c-red11Light);--red12:var(--c-red12Light);--yellow1:var(--c-yellow1Light);--yellow2:var(--c-yellow2Light);--yellow3:var(--c-yellow3Light);--yellow4:var(--c-yellow4Light);--yellow5:var(--c-yellow5Light);--yellow6:var(--c-yellow6Light);--yellow7:var(--c-yellow7Light);--yellow8:var(--c-yellow8Light);--yellow9:var(--c-yellow9Dark);--yellow10:var(--c-yellow10Light);--yellow11:var(--c-yellow11Light);--yellow12:var(--c-yellow12Light);--shadowColor:rgba(0,0,0,0.085);--shadowColorHover:rgba(0,0,0,0.085);--shadowColorPress:rgba(0,0,0,0.04);--shadowColorFocus:rgba(0,0,0,0.04);--windowBackground:#F2F2F4;--grey1:#656874;--grey2:#ACAFBA;--grey3:#DADCE7;--primary:#C43F2D;--borderHighlight:#E59F17;--backgroundHighlight:#FFF5D2;--success:#02B875;--warning:#F0A600;--error:#E76049;--red:#E76049;--yellow:#FFCE00;--green:#02B875;--purple:#311C66;--black:#000000;--white:#FFFFFF;--grey:#ACAFBB;--textGrey:#646875;--accentGrey:#D9DCE8;}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    :root, .t_dark .t_light {--accentBackground:var(--c-blue4Light);--accentColor:var(--c-blue4Dark);--background0:var(--c-white0);--background025:var(--c-white025);--background05:var(--c-white05);--background075:var(--c-white075);--color1:var(--c-black12);--color2:var(--c-white2);--color3:var(--c-white3);--color4:var(--c-white4);--color5:var(--c-white5);--color6:var(--c-white6);--color7:var(--c-white7);--color8:var(--c-white8);--color9:var(--c-gray9Light);--color10:var(--c-white10);--color11:var(--c-white11);--color12:var(--c-gray12Light);--color0:var(--c-black0);--color025:var(--c-black025);--color05:var(--c-black05);--color075:var(--c-black075);--background:#FFFFFF;--backgroundHover:var(--c-white075);--backgroundPress:var(--c-white2);--backgroundFocus:var(--c-white2);--borderColor:var(--c-white4);--borderColorHover:var(--c-white3);--borderColorPress:var(--c-white5);--borderColorFocus:var(--c-white4);--color:#000000;--colorHover:var(--c-white11);--colorPress:var(--c-gray12Light);--colorFocus:var(--c-white11);--colorTransparent:var(--c-black0);--placeholderColor:var(--c-gray9Light);--outlineColor:var(--c-black025);--blue1:var(--c-blue1Light);--blue2:var(--c-blue2Light);--blue3:var(--c-blue3Light);--blue4:var(--c-blue4Light);--blue5:var(--c-blue5Light);--blue6:var(--c-blue6Light);--blue7:var(--c-blue7Light);--blue8:var(--c-blue8Light);--blue9:var(--c-blue9Dark);--blue10:var(--c-blue10Light);--blue11:var(--c-blue11Light);--blue12:var(--c-blue12Light);--gray1:var(--c-gray1Light);--gray2:var(--c-gray2Light);--gray3:var(--c-gray3Light);--gray4:var(--c-gray12Dark);--gray5:var(--c-gray5Light);--gray6:var(--c-gray6Light);--gray7:var(--c-gray7Light);--gray8:var(--c-gray8Light);--gray9:var(--c-gray9Light);--gray10:var(--c-gray10Light);--gray11:var(--c-gray11Light);--gray12:var(--c-gray12Light);--green1:var(--c-green1Light);--green2:var(--c-green2Light);--green3:var(--c-green3Light);--green4:var(--c-green4Light);--green5:var(--c-green5Light);--green6:var(--c-green6Light);--green7:var(--c-green7Light);--green8:var(--c-green8Light);--green9:var(--c-green9Dark);--green10:var(--c-green10Light);--green11:var(--c-green11Light);--green12:var(--c-green12Light);--orange1:var(--c-orange1Light);--orange2:var(--c-orange2Light);--orange3:var(--c-orange3Light);--orange4:var(--c-orange4Light);--orange5:var(--c-orange5Light);--orange6:var(--c-orange6Light);--orange7:var(--c-orange7Light);--orange8:var(--c-orange8Light);--orange9:var(--c-orange9Dark);--orange10:var(--c-orange10Light);--orange11:var(--c-orange11Light);--orange12:var(--c-orange12Light);--pink1:var(--c-pink1Light);--pink2:var(--c-pink2Light);--pink3:var(--c-pink3Light);--pink4:var(--c-pink4Light);--pink5:var(--c-pink5Light);--pink6:var(--c-pink6Light);--pink7:var(--c-pink7Light);--pink8:var(--c-pink8Light);--pink9:var(--c-pink9Dark);--pink10:var(--c-pink10Light);--pink11:var(--c-pink11Light);--pink12:var(--c-pink12Light);--purple1:var(--c-purple1Light);--purple2:var(--c-purple2Light);--purple3:var(--c-purple3Light);--purple4:var(--c-purple4Light);--purple5:var(--c-purple5Light);--purple6:var(--c-purple6Light);--purple7:var(--c-purple7Light);--purple8:var(--c-purple8Light);--purple9:var(--c-purple9Dark);--purple10:var(--c-purple10Light);--purple11:var(--c-purple11Light);--purple12:var(--c-purple12Light);--red1:var(--c-red1Light);--red2:var(--c-red2Light);--red3:var(--c-red3Light);--red4:var(--c-red4Light);--red5:var(--c-red5Light);--red6:var(--c-red6Light);--red7:var(--c-red7Light);--red8:var(--c-red8Light);--red9:var(--c-red9Dark);--red10:var(--c-red10Light);--red11:var(--c-red11Light);--red12:var(--c-red12Light);--yellow1:var(--c-yellow1Light);--yellow2:var(--c-yellow2Light);--yellow3:var(--c-yellow3Light);--yellow4:var(--c-yellow4Light);--yellow5:var(--c-yellow5Light);--yellow6:var(--c-yellow6Light);--yellow7:var(--c-yellow7Light);--yellow8:var(--c-yellow8Light);--yellow9:var(--c-yellow9Dark);--yellow10:var(--c-yellow10Light);--yellow11:var(--c-yellow11Light);--yellow12:var(--c-yellow12Light);--shadowColor:rgba(0,0,0,0.085);--shadowColorHover:rgba(0,0,0,0.085);--shadowColorPress:rgba(0,0,0,0.04);--shadowColorFocus:rgba(0,0,0,0.04);--windowBackground:#F2F2F4;--grey1:#656874;--grey2:#ACAFBA;--grey3:#DADCE7;--primary:#C43F2D;--borderHighlight:#E59F17;--backgroundHighlight:#FFF5D2;--success:#02B875;--warning:#F0A600;--error:#E76049;--red:#E76049;--yellow:#FFCE00;--green:#02B875;--purple:#311C66;--black:#000000;--white:#FFFFFF;--grey:#ACAFBB;--textGrey:#646875;--accentGrey:#D9DCE8;}
  }
.t_light ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark, :root.t_dark , :root.t_dark .t_light .t_dark , :root.t_light .t_dark , :root.t_light .t_dark .t_light .t_dark , .tm_xxt {--accentBackground:var(--c-blue4Dark);--accentColor:var(--c-blue4Light);--background0:var(--c-black0);--background025:var(--c-black025);--background05:var(--c-black05);--background075:var(--c-black075);--color1:var(--c-black1);--color2:var(--c-black2);--color3:var(--c-black3);--color4:var(--c-black4);--color5:var(--c-black5);--color6:var(--c-black6);--color7:var(--c-black7);--color8:var(--c-black8);--color9:var(--c-black9);--color10:var(--c-black10);--color11:var(--c-black11);--color12:var(--c-black12);--color0:var(--c-white0);--color025:var(--c-white025);--color05:var(--c-white05);--color075:var(--c-white075);--background:#FFFFFF;--backgroundHover:var(--c-black2);--backgroundPress:var(--c-black075);--backgroundFocus:var(--c-black075);--borderColor:var(--c-black4);--borderColorHover:var(--c-black5);--borderColorPress:var(--c-black3);--borderColorFocus:var(--c-black4);--color:#000000;--colorHover:var(--c-black11);--colorPress:var(--c-black12);--colorFocus:var(--c-black11);--colorTransparent:var(--c-white0);--placeholderColor:var(--c-black9);--outlineColor:var(--c-white025);--blue1:var(--c-blue1Dark);--blue2:var(--c-blue2Dark);--blue3:var(--c-blue3Dark);--blue4:var(--c-blue4Dark);--blue5:var(--c-blue5Dark);--blue6:var(--c-blue6Dark);--blue7:var(--c-blue7Dark);--blue8:var(--c-blue8Dark);--blue9:var(--c-blue9Dark);--blue10:var(--c-blue10Dark);--blue11:var(--c-blue11Dark);--blue12:var(--c-blue12Dark);--gray1:var(--c-gray1Dark);--gray2:var(--c-gray2Dark);--gray3:var(--c-gray3Dark);--gray4:var(--c-gray4Dark);--gray5:var(--c-gray5Dark);--gray6:var(--c-gray6Dark);--gray7:var(--c-gray7Dark);--gray8:var(--c-gray8Dark);--gray9:var(--c-gray9Dark);--gray10:var(--c-gray10Dark);--gray11:var(--c-gray11Dark);--gray12:var(--c-gray12Dark);--green1:var(--c-green1Dark);--green2:var(--c-green2Dark);--green3:var(--c-green3Dark);--green4:var(--c-green4Dark);--green5:var(--c-green5Dark);--green6:var(--c-green6Dark);--green7:var(--c-green7Dark);--green8:var(--c-green8Dark);--green9:var(--c-green9Dark);--green10:var(--c-green10Dark);--green11:var(--c-green11Dark);--green12:var(--c-green12Dark);--orange1:var(--c-orange1Dark);--orange2:var(--c-orange2Dark);--orange3:var(--c-orange3Dark);--orange4:var(--c-orange4Dark);--orange5:var(--c-orange5Dark);--orange6:var(--c-orange6Dark);--orange7:var(--c-orange7Dark);--orange8:var(--c-orange8Dark);--orange9:var(--c-orange9Dark);--orange10:var(--c-orange10Dark);--orange11:var(--c-orange11Dark);--orange12:var(--c-orange12Dark);--pink1:var(--c-pink1Dark);--pink2:var(--c-pink2Dark);--pink3:var(--c-pink3Dark);--pink4:var(--c-pink4Dark);--pink5:var(--c-pink5Dark);--pink6:var(--c-pink6Dark);--pink7:var(--c-pink7Dark);--pink8:var(--c-pink8Dark);--pink9:var(--c-pink9Dark);--pink10:var(--c-pink10Dark);--pink11:var(--c-pink11Dark);--pink12:var(--c-pink12Dark);--purple1:var(--c-purple1Dark);--purple2:var(--c-purple2Dark);--purple3:var(--c-purple3Dark);--purple4:var(--c-purple4Dark);--purple5:var(--c-purple5Dark);--purple6:var(--c-purple6Dark);--purple7:var(--c-purple7Dark);--purple8:var(--c-purple8Dark);--purple9:var(--c-purple9Dark);--purple10:var(--c-purple10Dark);--purple11:var(--c-purple11Dark);--purple12:var(--c-purple12Dark);--red1:var(--c-red1Dark);--red2:var(--c-red2Dark);--red3:var(--c-red3Dark);--red4:var(--c-red4Dark);--red5:var(--c-red5Dark);--red6:var(--c-red6Dark);--red7:var(--c-red7Dark);--red8:var(--c-red8Dark);--red9:var(--c-red9Dark);--red10:var(--c-red10Dark);--red11:var(--c-red11Dark);--red12:var(--c-red12Dark);--yellow1:var(--c-yellow1Dark);--yellow2:var(--c-yellow2Dark);--yellow3:var(--c-yellow3Dark);--yellow4:var(--c-yellow4Dark);--yellow5:var(--c-yellow5Dark);--yellow6:var(--c-yellow6Dark);--yellow7:var(--c-yellow7Dark);--yellow8:var(--c-yellow8Dark);--yellow9:var(--c-yellow9Dark);--yellow10:var(--c-yellow10Dark);--yellow11:var(--c-yellow11Dark);--yellow12:var(--c-yellow12Dark);--shadowColor:rgba(0,0,0,0.3);--shadowColorHover:rgba(0,0,0,0.3);--shadowColorPress:rgba(0,0,0,0.2);--shadowColorFocus:rgba(0,0,0,0.2);--windowBackground:#F2F2F4;--grey1:#656874;--grey2:#ACAFBA;--grey3:#DADCE7;--primary:#C43F2D;--borderHighlight:#E59F17;--backgroundHighlight:#FFF5D2;--success:#02B875;--warning:#F0A600;--error:#E76049;--red:#E76049;--yellow:#FFCE00;--green:#02B875;--purple:#311C66;--black:#000000;--white:#FFFFFF;--grey:#ACAFBB;--textGrey:#646875;--accentGrey:#D9DCE8;}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    :root, .t_light .t_dark {--accentBackground:var(--c-blue4Dark);--accentColor:var(--c-blue4Light);--background0:var(--c-black0);--background025:var(--c-black025);--background05:var(--c-black05);--background075:var(--c-black075);--color1:var(--c-black1);--color2:var(--c-black2);--color3:var(--c-black3);--color4:var(--c-black4);--color5:var(--c-black5);--color6:var(--c-black6);--color7:var(--c-black7);--color8:var(--c-black8);--color9:var(--c-black9);--color10:var(--c-black10);--color11:var(--c-black11);--color12:var(--c-black12);--color0:var(--c-white0);--color025:var(--c-white025);--color05:var(--c-white05);--color075:var(--c-white075);--background:#FFFFFF;--backgroundHover:var(--c-black2);--backgroundPress:var(--c-black075);--backgroundFocus:var(--c-black075);--borderColor:var(--c-black4);--borderColorHover:var(--c-black5);--borderColorPress:var(--c-black3);--borderColorFocus:var(--c-black4);--color:#000000;--colorHover:var(--c-black11);--colorPress:var(--c-black12);--colorFocus:var(--c-black11);--colorTransparent:var(--c-white0);--placeholderColor:var(--c-black9);--outlineColor:var(--c-white025);--blue1:var(--c-blue1Dark);--blue2:var(--c-blue2Dark);--blue3:var(--c-blue3Dark);--blue4:var(--c-blue4Dark);--blue5:var(--c-blue5Dark);--blue6:var(--c-blue6Dark);--blue7:var(--c-blue7Dark);--blue8:var(--c-blue8Dark);--blue9:var(--c-blue9Dark);--blue10:var(--c-blue10Dark);--blue11:var(--c-blue11Dark);--blue12:var(--c-blue12Dark);--gray1:var(--c-gray1Dark);--gray2:var(--c-gray2Dark);--gray3:var(--c-gray3Dark);--gray4:var(--c-gray4Dark);--gray5:var(--c-gray5Dark);--gray6:var(--c-gray6Dark);--gray7:var(--c-gray7Dark);--gray8:var(--c-gray8Dark);--gray9:var(--c-gray9Dark);--gray10:var(--c-gray10Dark);--gray11:var(--c-gray11Dark);--gray12:var(--c-gray12Dark);--green1:var(--c-green1Dark);--green2:var(--c-green2Dark);--green3:var(--c-green3Dark);--green4:var(--c-green4Dark);--green5:var(--c-green5Dark);--green6:var(--c-green6Dark);--green7:var(--c-green7Dark);--green8:var(--c-green8Dark);--green9:var(--c-green9Dark);--green10:var(--c-green10Dark);--green11:var(--c-green11Dark);--green12:var(--c-green12Dark);--orange1:var(--c-orange1Dark);--orange2:var(--c-orange2Dark);--orange3:var(--c-orange3Dark);--orange4:var(--c-orange4Dark);--orange5:var(--c-orange5Dark);--orange6:var(--c-orange6Dark);--orange7:var(--c-orange7Dark);--orange8:var(--c-orange8Dark);--orange9:var(--c-orange9Dark);--orange10:var(--c-orange10Dark);--orange11:var(--c-orange11Dark);--orange12:var(--c-orange12Dark);--pink1:var(--c-pink1Dark);--pink2:var(--c-pink2Dark);--pink3:var(--c-pink3Dark);--pink4:var(--c-pink4Dark);--pink5:var(--c-pink5Dark);--pink6:var(--c-pink6Dark);--pink7:var(--c-pink7Dark);--pink8:var(--c-pink8Dark);--pink9:var(--c-pink9Dark);--pink10:var(--c-pink10Dark);--pink11:var(--c-pink11Dark);--pink12:var(--c-pink12Dark);--purple1:var(--c-purple1Dark);--purple2:var(--c-purple2Dark);--purple3:var(--c-purple3Dark);--purple4:var(--c-purple4Dark);--purple5:var(--c-purple5Dark);--purple6:var(--c-purple6Dark);--purple7:var(--c-purple7Dark);--purple8:var(--c-purple8Dark);--purple9:var(--c-purple9Dark);--purple10:var(--c-purple10Dark);--purple11:var(--c-purple11Dark);--purple12:var(--c-purple12Dark);--red1:var(--c-red1Dark);--red2:var(--c-red2Dark);--red3:var(--c-red3Dark);--red4:var(--c-red4Dark);--red5:var(--c-red5Dark);--red6:var(--c-red6Dark);--red7:var(--c-red7Dark);--red8:var(--c-red8Dark);--red9:var(--c-red9Dark);--red10:var(--c-red10Dark);--red11:var(--c-red11Dark);--red12:var(--c-red12Dark);--yellow1:var(--c-yellow1Dark);--yellow2:var(--c-yellow2Dark);--yellow3:var(--c-yellow3Dark);--yellow4:var(--c-yellow4Dark);--yellow5:var(--c-yellow5Dark);--yellow6:var(--c-yellow6Dark);--yellow7:var(--c-yellow7Dark);--yellow8:var(--c-yellow8Dark);--yellow9:var(--c-yellow9Dark);--yellow10:var(--c-yellow10Dark);--yellow11:var(--c-yellow11Dark);--yellow12:var(--c-yellow12Dark);--shadowColor:rgba(0,0,0,0.3);--shadowColorHover:rgba(0,0,0,0.3);--shadowColorPress:rgba(0,0,0,0.2);--shadowColorFocus:rgba(0,0,0,0.2);--windowBackground:#F2F2F4;--grey1:#656874;--grey2:#ACAFBA;--grey3:#DADCE7;--primary:#C43F2D;--borderHighlight:#E59F17;--backgroundHighlight:#FFF5D2;--success:#02B875;--warning:#F0A600;--error:#E76049;--red:#E76049;--yellow:#FFCE00;--green:#02B875;--purple:#311C66;--black:#000000;--white:#FFFFFF;--grey:#ACAFBB;--textGrey:#646875;--accentGrey:#D9DCE8;}
  }
.t_dark ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_light .t_orange, :root.t_dark .t_light .t_orange, :root.t_light .t_dark .t_light .t_orange, :root.t_light .t_orange, .tm_xxt {--accentBackground:var(--c-pink1Light);--accentColor:var(--c-pink12Light);--background0:hsla(24, 70.0%, 99.0%, 0);--background025:hsla(24, 70.0%, 99.0%, 0.25);--background05:hsla(24, 70.0%, 99.0%, 0.5);--background075:hsla(24, 70.0%, 99.0%, 0.75);--color1:var(--c-orange1Light);--color2:var(--c-orange2Light);--color3:var(--c-orange3Light);--color4:var(--c-orange4Light);--color5:var(--c-orange5Light);--color6:var(--c-orange6Light);--color7:var(--c-orange7Light);--color8:var(--c-orange8Light);--color9:var(--c-orange9Dark);--color10:var(--c-orange10Light);--color11:var(--c-orange11Light);--color12:var(--c-orange12Light);--color0:hsla(24, 94.0%, 50.0%, 0);--color025:hsla(24, 94.0%, 50.0%, 0.25);--color05:hsla(24, 94.0%, 50.0%, 0.5);--color075:hsla(24, 94.0%, 50.0%, 0.75);--background:var(--c-orange1Light);--backgroundHover:hsla(24, 70.0%, 99.0%, 0.75);--backgroundPress:var(--c-orange2Light);--backgroundFocus:var(--c-orange2Light);--borderColor:var(--c-orange4Light);--borderColorHover:var(--c-orange3Light);--borderColorPress:var(--c-orange5Light);--borderColorFocus:var(--c-orange4Light);--color:var(--c-orange12Light);--colorHover:var(--c-orange11Light);--colorPress:var(--c-orange12Light);--colorFocus:var(--c-orange11Light);--colorTransparent:hsla(24, 94.0%, 50.0%, 0);--placeholderColor:var(--c-orange9Dark);--outlineColor:hsla(24, 94.0%, 50.0%, 0.25);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_orange, .t_orange {--accentBackground:var(--c-pink1Light);--accentColor:var(--c-pink12Light);--background0:hsla(24, 70.0%, 99.0%, 0);--background025:hsla(24, 70.0%, 99.0%, 0.25);--background05:hsla(24, 70.0%, 99.0%, 0.5);--background075:hsla(24, 70.0%, 99.0%, 0.75);--color1:var(--c-orange1Light);--color2:var(--c-orange2Light);--color3:var(--c-orange3Light);--color4:var(--c-orange4Light);--color5:var(--c-orange5Light);--color6:var(--c-orange6Light);--color7:var(--c-orange7Light);--color8:var(--c-orange8Light);--color9:var(--c-orange9Dark);--color10:var(--c-orange10Light);--color11:var(--c-orange11Light);--color12:var(--c-orange12Light);--color0:hsla(24, 94.0%, 50.0%, 0);--color025:hsla(24, 94.0%, 50.0%, 0.25);--color05:hsla(24, 94.0%, 50.0%, 0.5);--color075:hsla(24, 94.0%, 50.0%, 0.75);--background:var(--c-orange1Light);--backgroundHover:hsla(24, 70.0%, 99.0%, 0.75);--backgroundPress:var(--c-orange2Light);--backgroundFocus:var(--c-orange2Light);--borderColor:var(--c-orange4Light);--borderColorHover:var(--c-orange3Light);--borderColorPress:var(--c-orange5Light);--borderColorFocus:var(--c-orange4Light);--color:var(--c-orange12Light);--colorHover:var(--c-orange11Light);--colorPress:var(--c-orange12Light);--colorFocus:var(--c-orange11Light);--colorTransparent:hsla(24, 94.0%, 50.0%, 0);--placeholderColor:var(--c-orange9Dark);--outlineColor:hsla(24, 94.0%, 50.0%, 0.25);}
  }
.t_light_orange ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_light .t_yellow, :root.t_dark .t_light .t_yellow, :root.t_light .t_dark .t_light .t_yellow, :root.t_light .t_yellow, .tm_xxt {--accentBackground:var(--c-blue1Light);--accentColor:var(--c-blue12Light);--background0:hsla(60, 54.0%, 98.5%, 0);--background025:hsla(60, 54.0%, 98.5%, 0.25);--background05:hsla(60, 54.0%, 98.5%, 0.5);--background075:hsla(60, 54.0%, 98.5%, 0.75);--color1:var(--c-yellow1Light);--color2:var(--c-yellow2Light);--color3:var(--c-yellow3Light);--color4:var(--c-yellow4Light);--color5:var(--c-yellow5Light);--color6:var(--c-yellow6Light);--color7:var(--c-yellow7Light);--color8:var(--c-yellow8Light);--color9:var(--c-yellow9Dark);--color10:var(--c-yellow10Light);--color11:var(--c-yellow11Light);--color12:var(--c-yellow12Light);--color0:hsla(53, 92.0%, 50.0%, 0);--color025:hsla(53, 92.0%, 50.0%, 0.25);--color05:hsla(53, 92.0%, 50.0%, 0.5);--color075:hsla(53, 92.0%, 50.0%, 0.75);--background:var(--c-yellow1Light);--backgroundHover:hsla(60, 54.0%, 98.5%, 0.75);--backgroundPress:var(--c-yellow2Light);--backgroundFocus:var(--c-yellow2Light);--borderColor:var(--c-yellow4Light);--borderColorHover:var(--c-yellow3Light);--borderColorPress:var(--c-yellow5Light);--borderColorFocus:var(--c-yellow4Light);--color:var(--c-yellow12Light);--colorHover:var(--c-yellow11Light);--colorPress:var(--c-yellow12Light);--colorFocus:var(--c-yellow11Light);--colorTransparent:hsla(53, 92.0%, 50.0%, 0);--placeholderColor:var(--c-yellow9Dark);--outlineColor:hsla(53, 92.0%, 50.0%, 0.25);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_yellow, .t_yellow {--accentBackground:var(--c-blue1Light);--accentColor:var(--c-blue12Light);--background0:hsla(60, 54.0%, 98.5%, 0);--background025:hsla(60, 54.0%, 98.5%, 0.25);--background05:hsla(60, 54.0%, 98.5%, 0.5);--background075:hsla(60, 54.0%, 98.5%, 0.75);--color1:var(--c-yellow1Light);--color2:var(--c-yellow2Light);--color3:var(--c-yellow3Light);--color4:var(--c-yellow4Light);--color5:var(--c-yellow5Light);--color6:var(--c-yellow6Light);--color7:var(--c-yellow7Light);--color8:var(--c-yellow8Light);--color9:var(--c-yellow9Dark);--color10:var(--c-yellow10Light);--color11:var(--c-yellow11Light);--color12:var(--c-yellow12Light);--color0:hsla(53, 92.0%, 50.0%, 0);--color025:hsla(53, 92.0%, 50.0%, 0.25);--color05:hsla(53, 92.0%, 50.0%, 0.5);--color075:hsla(53, 92.0%, 50.0%, 0.75);--background:var(--c-yellow1Light);--backgroundHover:hsla(60, 54.0%, 98.5%, 0.75);--backgroundPress:var(--c-yellow2Light);--backgroundFocus:var(--c-yellow2Light);--borderColor:var(--c-yellow4Light);--borderColorHover:var(--c-yellow3Light);--borderColorPress:var(--c-yellow5Light);--borderColorFocus:var(--c-yellow4Light);--color:var(--c-yellow12Light);--colorHover:var(--c-yellow11Light);--colorPress:var(--c-yellow12Light);--colorFocus:var(--c-yellow11Light);--colorTransparent:hsla(53, 92.0%, 50.0%, 0);--placeholderColor:var(--c-yellow9Dark);--outlineColor:hsla(53, 92.0%, 50.0%, 0.25);}
  }
.t_light_yellow ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_light .t_green, :root.t_dark .t_light .t_green, :root.t_light .t_dark .t_light .t_green, :root.t_light .t_green, .tm_xxt {--accentBackground:var(--c-orange1Light);--accentColor:var(--c-orange12Light);--background0:hsla(136, 50.0%, 98.9%, 0);--background025:hsla(136, 50.0%, 98.9%, 0.25);--background05:hsla(136, 50.0%, 98.9%, 0.5);--background075:hsla(136, 50.0%, 98.9%, 0.75);--color1:var(--c-green1Light);--color2:var(--c-green2Light);--color3:var(--c-green3Light);--color4:var(--c-green4Light);--color5:var(--c-green5Light);--color6:var(--c-green6Light);--color7:var(--c-green7Light);--color8:var(--c-green8Light);--color9:var(--c-green9Dark);--color10:var(--c-green10Light);--color11:var(--c-green11Light);--color12:var(--c-green12Light);--color0:hsla(151, 55.0%, 41.5%, 0);--color025:hsla(151, 55.0%, 41.5%, 0.25);--color05:hsla(151, 55.0%, 41.5%, 0.5);--color075:hsla(151, 55.0%, 41.5%, 0.75);--background:var(--c-green1Light);--backgroundHover:hsla(136, 50.0%, 98.9%, 0.75);--backgroundPress:var(--c-green2Light);--backgroundFocus:var(--c-green2Light);--borderColor:var(--c-green4Light);--borderColorHover:var(--c-green3Light);--borderColorPress:var(--c-green5Light);--borderColorFocus:var(--c-green4Light);--color:var(--c-green12Light);--colorHover:var(--c-green11Light);--colorPress:var(--c-green12Light);--colorFocus:var(--c-green11Light);--colorTransparent:hsla(151, 55.0%, 41.5%, 0);--placeholderColor:var(--c-green9Dark);--outlineColor:hsla(151, 55.0%, 41.5%, 0.25);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_green, .t_green {--accentBackground:var(--c-orange1Light);--accentColor:var(--c-orange12Light);--background0:hsla(136, 50.0%, 98.9%, 0);--background025:hsla(136, 50.0%, 98.9%, 0.25);--background05:hsla(136, 50.0%, 98.9%, 0.5);--background075:hsla(136, 50.0%, 98.9%, 0.75);--color1:var(--c-green1Light);--color2:var(--c-green2Light);--color3:var(--c-green3Light);--color4:var(--c-green4Light);--color5:var(--c-green5Light);--color6:var(--c-green6Light);--color7:var(--c-green7Light);--color8:var(--c-green8Light);--color9:var(--c-green9Dark);--color10:var(--c-green10Light);--color11:var(--c-green11Light);--color12:var(--c-green12Light);--color0:hsla(151, 55.0%, 41.5%, 0);--color025:hsla(151, 55.0%, 41.5%, 0.25);--color05:hsla(151, 55.0%, 41.5%, 0.5);--color075:hsla(151, 55.0%, 41.5%, 0.75);--background:var(--c-green1Light);--backgroundHover:hsla(136, 50.0%, 98.9%, 0.75);--backgroundPress:var(--c-green2Light);--backgroundFocus:var(--c-green2Light);--borderColor:var(--c-green4Light);--borderColorHover:var(--c-green3Light);--borderColorPress:var(--c-green5Light);--borderColorFocus:var(--c-green4Light);--color:var(--c-green12Light);--colorHover:var(--c-green11Light);--colorPress:var(--c-green12Light);--colorFocus:var(--c-green11Light);--colorTransparent:hsla(151, 55.0%, 41.5%, 0);--placeholderColor:var(--c-green9Dark);--outlineColor:hsla(151, 55.0%, 41.5%, 0.25);}
  }
.t_light_green ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_blue, :root.t_dark .t_light .t_dark .t_light .t_blue, :root.t_light .t_blue, :root.t_light .t_dark .t_light .t_blue, .tm_xxt {--accentBackground:var(--c-gray1Light);--accentColor:var(--c-gray12Light);--background0:hsla(206, 100%, 99.2%, 0);--background025:hsla(206, 100%, 99.2%, 0.25);--background05:hsla(206, 100%, 99.2%, 0.5);--background075:hsla(206, 100%, 99.2%, 0.75);--color1:var(--c-blue1Light);--color2:var(--c-blue2Light);--color3:var(--c-blue3Light);--color4:var(--c-blue4Light);--color5:var(--c-blue5Light);--color6:var(--c-blue6Light);--color7:var(--c-blue7Light);--color8:var(--c-blue8Light);--color9:var(--c-blue9Dark);--color10:var(--c-blue10Light);--color11:var(--c-blue11Light);--color12:var(--c-blue12Light);--color0:hsla(206, 100%, 50.0%, 0);--color025:hsla(206, 100%, 50.0%, 0.25);--color05:hsla(206, 100%, 50.0%, 0.5);--color075:hsla(206, 100%, 50.0%, 0.75);--background:var(--c-blue1Light);--backgroundHover:hsla(206, 100%, 99.2%, 0.75);--backgroundPress:var(--c-blue2Light);--backgroundFocus:var(--c-blue2Light);--borderColor:var(--c-blue4Light);--borderColorHover:var(--c-blue3Light);--borderColorPress:var(--c-blue5Light);--borderColorFocus:var(--c-blue4Light);--color:var(--c-blue12Light);--colorHover:var(--c-blue11Light);--colorPress:var(--c-blue12Light);--colorFocus:var(--c-blue11Light);--colorTransparent:hsla(206, 100%, 50.0%, 0);--placeholderColor:var(--c-blue9Dark);--outlineColor:hsla(206, 100%, 50.0%, 0.25);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_blue, .t_dark .t_light .t_blue {--accentBackground:var(--c-gray1Light);--accentColor:var(--c-gray12Light);--background0:hsla(206, 100%, 99.2%, 0);--background025:hsla(206, 100%, 99.2%, 0.25);--background05:hsla(206, 100%, 99.2%, 0.5);--background075:hsla(206, 100%, 99.2%, 0.75);--color1:var(--c-blue1Light);--color2:var(--c-blue2Light);--color3:var(--c-blue3Light);--color4:var(--c-blue4Light);--color5:var(--c-blue5Light);--color6:var(--c-blue6Light);--color7:var(--c-blue7Light);--color8:var(--c-blue8Light);--color9:var(--c-blue9Dark);--color10:var(--c-blue10Light);--color11:var(--c-blue11Light);--color12:var(--c-blue12Light);--color0:hsla(206, 100%, 50.0%, 0);--color025:hsla(206, 100%, 50.0%, 0.25);--color05:hsla(206, 100%, 50.0%, 0.5);--color075:hsla(206, 100%, 50.0%, 0.75);--background:var(--c-blue1Light);--backgroundHover:hsla(206, 100%, 99.2%, 0.75);--backgroundPress:var(--c-blue2Light);--backgroundFocus:var(--c-blue2Light);--borderColor:var(--c-blue4Light);--borderColorHover:var(--c-blue3Light);--borderColorPress:var(--c-blue5Light);--borderColorFocus:var(--c-blue4Light);--color:var(--c-blue12Light);--colorHover:var(--c-blue11Light);--colorPress:var(--c-blue12Light);--colorFocus:var(--c-blue11Light);--colorTransparent:hsla(206, 100%, 50.0%, 0);--placeholderColor:var(--c-blue9Dark);--outlineColor:hsla(206, 100%, 50.0%, 0.25);}
  }
.t_light_blue ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_light .t_purple, :root.t_dark .t_light .t_purple, :root.t_light .t_dark .t_light .t_purple, :root.t_light .t_purple, .tm_xxt {--accentBackground:var(--c-red1Light);--accentColor:var(--c-red12Light);--background0:hsla(280, 65.0%, 99.4%, 0);--background025:hsla(280, 65.0%, 99.4%, 0.25);--background05:hsla(280, 65.0%, 99.4%, 0.5);--background075:hsla(280, 65.0%, 99.4%, 0.75);--color1:var(--c-purple1Light);--color2:var(--c-purple2Light);--color3:var(--c-purple3Light);--color4:var(--c-purple4Light);--color5:var(--c-purple5Light);--color6:var(--c-purple6Light);--color7:var(--c-purple7Light);--color8:var(--c-purple8Light);--color9:var(--c-purple9Dark);--color10:var(--c-purple10Light);--color11:var(--c-purple11Light);--color12:var(--c-purple12Light);--color0:hsla(272, 51.0%, 54.0%, 0);--color025:hsla(272, 51.0%, 54.0%, 0.25);--color05:hsla(272, 51.0%, 54.0%, 0.5);--color075:hsla(272, 51.0%, 54.0%, 0.75);--background:var(--c-purple1Light);--backgroundHover:hsla(280, 65.0%, 99.4%, 0.75);--backgroundPress:var(--c-purple2Light);--backgroundFocus:var(--c-purple2Light);--borderColor:var(--c-purple4Light);--borderColorHover:var(--c-purple3Light);--borderColorPress:var(--c-purple5Light);--borderColorFocus:var(--c-purple4Light);--color:var(--c-purple12Light);--colorHover:var(--c-purple11Light);--colorPress:var(--c-purple12Light);--colorFocus:var(--c-purple11Light);--colorTransparent:hsla(272, 51.0%, 54.0%, 0);--placeholderColor:var(--c-purple9Dark);--outlineColor:hsla(272, 51.0%, 54.0%, 0.25);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_purple, .t_purple {--accentBackground:var(--c-red1Light);--accentColor:var(--c-red12Light);--background0:hsla(280, 65.0%, 99.4%, 0);--background025:hsla(280, 65.0%, 99.4%, 0.25);--background05:hsla(280, 65.0%, 99.4%, 0.5);--background075:hsla(280, 65.0%, 99.4%, 0.75);--color1:var(--c-purple1Light);--color2:var(--c-purple2Light);--color3:var(--c-purple3Light);--color4:var(--c-purple4Light);--color5:var(--c-purple5Light);--color6:var(--c-purple6Light);--color7:var(--c-purple7Light);--color8:var(--c-purple8Light);--color9:var(--c-purple9Dark);--color10:var(--c-purple10Light);--color11:var(--c-purple11Light);--color12:var(--c-purple12Light);--color0:hsla(272, 51.0%, 54.0%, 0);--color025:hsla(272, 51.0%, 54.0%, 0.25);--color05:hsla(272, 51.0%, 54.0%, 0.5);--color075:hsla(272, 51.0%, 54.0%, 0.75);--background:var(--c-purple1Light);--backgroundHover:hsla(280, 65.0%, 99.4%, 0.75);--backgroundPress:var(--c-purple2Light);--backgroundFocus:var(--c-purple2Light);--borderColor:var(--c-purple4Light);--borderColorHover:var(--c-purple3Light);--borderColorPress:var(--c-purple5Light);--borderColorFocus:var(--c-purple4Light);--color:var(--c-purple12Light);--colorHover:var(--c-purple11Light);--colorPress:var(--c-purple12Light);--colorFocus:var(--c-purple11Light);--colorTransparent:hsla(272, 51.0%, 54.0%, 0);--placeholderColor:var(--c-purple9Dark);--outlineColor:hsla(272, 51.0%, 54.0%, 0.25);}
  }
.t_light_purple ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_light .t_pink, :root.t_dark .t_light .t_pink, :root.t_light .t_dark .t_light .t_pink, :root.t_light .t_pink, .tm_xxt {--accentBackground:var(--c-purple1Light);--accentColor:var(--c-purple12Light);--background0:hsla(322, 100%, 99.4%, 0);--background025:hsla(322, 100%, 99.4%, 0.25);--background05:hsla(322, 100%, 99.4%, 0.5);--background075:hsla(322, 100%, 99.4%, 0.75);--color1:var(--c-pink1Light);--color2:var(--c-pink2Light);--color3:var(--c-pink3Light);--color4:var(--c-pink4Light);--color5:var(--c-pink5Light);--color6:var(--c-pink6Light);--color7:var(--c-pink7Light);--color8:var(--c-pink8Light);--color9:var(--c-pink9Dark);--color10:var(--c-pink10Light);--color11:var(--c-pink11Light);--color12:var(--c-pink12Light);--color0:hsla(322, 65.0%, 54.5%, 0);--color025:hsla(322, 65.0%, 54.5%, 0.25);--color05:hsla(322, 65.0%, 54.5%, 0.5);--color075:hsla(322, 65.0%, 54.5%, 0.75);--background:var(--c-pink1Light);--backgroundHover:hsla(322, 100%, 99.4%, 0.75);--backgroundPress:var(--c-pink2Light);--backgroundFocus:var(--c-pink2Light);--borderColor:var(--c-pink4Light);--borderColorHover:var(--c-pink3Light);--borderColorPress:var(--c-pink5Light);--borderColorFocus:var(--c-pink4Light);--color:var(--c-pink12Light);--colorHover:var(--c-pink11Light);--colorPress:var(--c-pink12Light);--colorFocus:var(--c-pink11Light);--colorTransparent:hsla(322, 65.0%, 54.5%, 0);--placeholderColor:var(--c-pink9Dark);--outlineColor:hsla(322, 65.0%, 54.5%, 0.25);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_pink, .t_pink {--accentBackground:var(--c-purple1Light);--accentColor:var(--c-purple12Light);--background0:hsla(322, 100%, 99.4%, 0);--background025:hsla(322, 100%, 99.4%, 0.25);--background05:hsla(322, 100%, 99.4%, 0.5);--background075:hsla(322, 100%, 99.4%, 0.75);--color1:var(--c-pink1Light);--color2:var(--c-pink2Light);--color3:var(--c-pink3Light);--color4:var(--c-pink4Light);--color5:var(--c-pink5Light);--color6:var(--c-pink6Light);--color7:var(--c-pink7Light);--color8:var(--c-pink8Light);--color9:var(--c-pink9Dark);--color10:var(--c-pink10Light);--color11:var(--c-pink11Light);--color12:var(--c-pink12Light);--color0:hsla(322, 65.0%, 54.5%, 0);--color025:hsla(322, 65.0%, 54.5%, 0.25);--color05:hsla(322, 65.0%, 54.5%, 0.5);--color075:hsla(322, 65.0%, 54.5%, 0.75);--background:var(--c-pink1Light);--backgroundHover:hsla(322, 100%, 99.4%, 0.75);--backgroundPress:var(--c-pink2Light);--backgroundFocus:var(--c-pink2Light);--borderColor:var(--c-pink4Light);--borderColorHover:var(--c-pink3Light);--borderColorPress:var(--c-pink5Light);--borderColorFocus:var(--c-pink4Light);--color:var(--c-pink12Light);--colorHover:var(--c-pink11Light);--colorPress:var(--c-pink12Light);--colorFocus:var(--c-pink11Light);--colorTransparent:hsla(322, 65.0%, 54.5%, 0);--placeholderColor:var(--c-pink9Dark);--outlineColor:hsla(322, 65.0%, 54.5%, 0.25);}
  }
.t_light_pink ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_light .t_red, :root.t_dark .t_light .t_red, :root.t_light .t_dark .t_light .t_red, :root.t_light .t_red, .tm_xxt {--accentBackground:var(--c-yellow1Light);--accentColor:var(--c-yellow12Light);--background0:hsla(359, 100%, 99.4%, 0);--background025:hsla(359, 100%, 99.4%, 0.25);--background05:hsla(359, 100%, 99.4%, 0.5);--background075:hsla(359, 100%, 99.4%, 0.75);--color1:var(--c-red1Light);--color2:var(--c-red2Light);--color3:var(--c-red3Light);--color4:var(--c-red4Light);--color5:var(--c-red5Light);--color6:var(--c-red6Light);--color7:var(--c-red7Light);--color8:var(--c-red8Light);--color9:var(--c-red9Dark);--color10:var(--c-red10Light);--color11:var(--c-red11Light);--color12:var(--c-red12Light);--color0:hsla(358, 75.0%, 59.0%, 0);--color025:hsla(358, 75.0%, 59.0%, 0.25);--color05:hsla(358, 75.0%, 59.0%, 0.5);--color075:hsla(358, 75.0%, 59.0%, 0.75);--background:var(--c-red1Light);--backgroundHover:hsla(359, 100%, 99.4%, 0.75);--backgroundPress:var(--c-red2Light);--backgroundFocus:var(--c-red2Light);--borderColor:var(--c-red4Light);--borderColorHover:var(--c-red3Light);--borderColorPress:var(--c-red5Light);--borderColorFocus:var(--c-red4Light);--color:var(--c-red12Light);--colorHover:var(--c-red11Light);--colorPress:var(--c-red12Light);--colorFocus:var(--c-red11Light);--colorTransparent:hsla(358, 75.0%, 59.0%, 0);--placeholderColor:var(--c-red9Dark);--outlineColor:hsla(358, 75.0%, 59.0%, 0.25);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_red, .t_red {--accentBackground:var(--c-yellow1Light);--accentColor:var(--c-yellow12Light);--background0:hsla(359, 100%, 99.4%, 0);--background025:hsla(359, 100%, 99.4%, 0.25);--background05:hsla(359, 100%, 99.4%, 0.5);--background075:hsla(359, 100%, 99.4%, 0.75);--color1:var(--c-red1Light);--color2:var(--c-red2Light);--color3:var(--c-red3Light);--color4:var(--c-red4Light);--color5:var(--c-red5Light);--color6:var(--c-red6Light);--color7:var(--c-red7Light);--color8:var(--c-red8Light);--color9:var(--c-red9Dark);--color10:var(--c-red10Light);--color11:var(--c-red11Light);--color12:var(--c-red12Light);--color0:hsla(358, 75.0%, 59.0%, 0);--color025:hsla(358, 75.0%, 59.0%, 0.25);--color05:hsla(358, 75.0%, 59.0%, 0.5);--color075:hsla(358, 75.0%, 59.0%, 0.75);--background:var(--c-red1Light);--backgroundHover:hsla(359, 100%, 99.4%, 0.75);--backgroundPress:var(--c-red2Light);--backgroundFocus:var(--c-red2Light);--borderColor:var(--c-red4Light);--borderColorHover:var(--c-red3Light);--borderColorPress:var(--c-red5Light);--borderColorFocus:var(--c-red4Light);--color:var(--c-red12Light);--colorHover:var(--c-red11Light);--colorPress:var(--c-red12Light);--colorFocus:var(--c-red11Light);--colorTransparent:hsla(358, 75.0%, 59.0%, 0);--placeholderColor:var(--c-red9Dark);--outlineColor:hsla(358, 75.0%, 59.0%, 0.25);}
  }
.t_light_red ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_light .t_gray, :root.t_dark .t_light .t_gray, :root.t_light .t_dark .t_light .t_gray, :root.t_light .t_gray, .tm_xxt {--accentBackground:var(--c-green1Light);--accentColor:var(--c-green12Light);--background0:hsla(0, 0%, 99.0%, 0);--background025:hsla(0, 0%, 99.0%, 0.25);--background05:hsla(0, 0%, 99.0%, 0.5);--background075:hsla(0, 0%, 99.0%, 0.75);--color1:var(--c-gray1Light);--color2:var(--c-gray2Light);--color3:var(--c-gray3Light);--color4:var(--c-gray12Dark);--color5:var(--c-gray5Light);--color6:var(--c-gray6Light);--color7:var(--c-gray7Light);--color8:var(--c-gray8Light);--color9:var(--c-gray9Light);--color10:var(--c-gray10Light);--color11:var(--c-gray11Light);--color12:var(--c-gray12Light);--color0:hsla(0, 0%, 56.1%, 0);--color025:hsla(0, 0%, 56.1%, 0.25);--color05:hsla(0, 0%, 56.1%, 0.5);--color075:hsla(0, 0%, 56.1%, 0.75);--background:var(--c-gray1Light);--backgroundHover:hsla(0, 0%, 99.0%, 0.75);--backgroundPress:var(--c-gray2Light);--backgroundFocus:var(--c-gray2Light);--borderColor:var(--c-gray12Dark);--borderColorHover:var(--c-gray3Light);--borderColorPress:var(--c-gray5Light);--borderColorFocus:var(--c-gray12Dark);--color:var(--c-gray12Light);--colorHover:var(--c-gray11Light);--colorPress:var(--c-gray12Light);--colorFocus:var(--c-gray11Light);--colorTransparent:hsla(0, 0%, 56.1%, 0);--placeholderColor:var(--c-gray9Light);--outlineColor:hsla(0, 0%, 56.1%, 0.25);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_gray, .t_gray {--accentBackground:var(--c-green1Light);--accentColor:var(--c-green12Light);--background0:hsla(0, 0%, 99.0%, 0);--background025:hsla(0, 0%, 99.0%, 0.25);--background05:hsla(0, 0%, 99.0%, 0.5);--background075:hsla(0, 0%, 99.0%, 0.75);--color1:var(--c-gray1Light);--color2:var(--c-gray2Light);--color3:var(--c-gray3Light);--color4:var(--c-gray12Dark);--color5:var(--c-gray5Light);--color6:var(--c-gray6Light);--color7:var(--c-gray7Light);--color8:var(--c-gray8Light);--color9:var(--c-gray9Light);--color10:var(--c-gray10Light);--color11:var(--c-gray11Light);--color12:var(--c-gray12Light);--color0:hsla(0, 0%, 56.1%, 0);--color025:hsla(0, 0%, 56.1%, 0.25);--color05:hsla(0, 0%, 56.1%, 0.5);--color075:hsla(0, 0%, 56.1%, 0.75);--background:var(--c-gray1Light);--backgroundHover:hsla(0, 0%, 99.0%, 0.75);--backgroundPress:var(--c-gray2Light);--backgroundFocus:var(--c-gray2Light);--borderColor:var(--c-gray12Dark);--borderColorHover:var(--c-gray3Light);--borderColorPress:var(--c-gray5Light);--borderColorFocus:var(--c-gray12Dark);--color:var(--c-gray12Light);--colorHover:var(--c-gray11Light);--colorPress:var(--c-gray12Light);--colorFocus:var(--c-gray11Light);--colorTransparent:hsla(0, 0%, 56.1%, 0);--placeholderColor:var(--c-gray9Light);--outlineColor:hsla(0, 0%, 56.1%, 0.25);}
  }
.t_light_gray ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_orange, :root.t_dark .t_orange, :root.t_light .t_dark .t_light .t_dark .t_orange, :root.t_light .t_dark .t_orange, .tm_xxt {--accentBackground:var(--c-pink1Light);--accentColor:var(--c-pink12Light);--background0:hsla(30, 70.0%, 7.2%, 0);--background025:hsla(30, 70.0%, 7.2%, 0.25);--background05:hsla(30, 70.0%, 7.2%, 0.5);--background075:hsla(30, 70.0%, 7.2%, 0.75);--color1:var(--c-orange1Dark);--color2:var(--c-orange2Dark);--color3:var(--c-orange3Dark);--color4:var(--c-orange4Dark);--color5:var(--c-orange5Dark);--color6:var(--c-orange6Dark);--color7:var(--c-orange7Dark);--color8:var(--c-orange8Dark);--color9:var(--c-orange9Dark);--color10:var(--c-orange10Dark);--color11:var(--c-orange11Dark);--color12:var(--c-orange12Dark);--color0:hsla(24, 94.0%, 50.0%, 0);--color025:hsla(24, 94.0%, 50.0%, 0.25);--color05:hsla(24, 94.0%, 50.0%, 0.5);--color075:hsla(24, 94.0%, 50.0%, 0.75);--background:var(--c-orange1Dark);--backgroundHover:var(--c-orange2Dark);--backgroundPress:hsla(30, 70.0%, 7.2%, 0.75);--backgroundFocus:hsla(30, 70.0%, 7.2%, 0.75);--borderColor:var(--c-orange4Dark);--borderColorHover:var(--c-orange5Dark);--borderColorPress:var(--c-orange3Dark);--borderColorFocus:var(--c-orange4Dark);--color:var(--c-orange12Dark);--colorHover:var(--c-orange11Dark);--colorPress:var(--c-orange12Dark);--colorFocus:var(--c-orange11Dark);--colorTransparent:hsla(24, 94.0%, 50.0%, 0);--placeholderColor:var(--c-orange9Dark);--outlineColor:hsla(24, 94.0%, 50.0%, 0.25);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_orange, .t_orange {--accentBackground:var(--c-pink1Light);--accentColor:var(--c-pink12Light);--background0:hsla(30, 70.0%, 7.2%, 0);--background025:hsla(30, 70.0%, 7.2%, 0.25);--background05:hsla(30, 70.0%, 7.2%, 0.5);--background075:hsla(30, 70.0%, 7.2%, 0.75);--color1:var(--c-orange1Dark);--color2:var(--c-orange2Dark);--color3:var(--c-orange3Dark);--color4:var(--c-orange4Dark);--color5:var(--c-orange5Dark);--color6:var(--c-orange6Dark);--color7:var(--c-orange7Dark);--color8:var(--c-orange8Dark);--color9:var(--c-orange9Dark);--color10:var(--c-orange10Dark);--color11:var(--c-orange11Dark);--color12:var(--c-orange12Dark);--color0:hsla(24, 94.0%, 50.0%, 0);--color025:hsla(24, 94.0%, 50.0%, 0.25);--color05:hsla(24, 94.0%, 50.0%, 0.5);--color075:hsla(24, 94.0%, 50.0%, 0.75);--background:var(--c-orange1Dark);--backgroundHover:var(--c-orange2Dark);--backgroundPress:hsla(30, 70.0%, 7.2%, 0.75);--backgroundFocus:hsla(30, 70.0%, 7.2%, 0.75);--borderColor:var(--c-orange4Dark);--borderColorHover:var(--c-orange5Dark);--borderColorPress:var(--c-orange3Dark);--borderColorFocus:var(--c-orange4Dark);--color:var(--c-orange12Dark);--colorHover:var(--c-orange11Dark);--colorPress:var(--c-orange12Dark);--colorFocus:var(--c-orange11Dark);--colorTransparent:hsla(24, 94.0%, 50.0%, 0);--placeholderColor:var(--c-orange9Dark);--outlineColor:hsla(24, 94.0%, 50.0%, 0.25);}
  }
.t_dark_orange ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_yellow, :root.t_dark .t_yellow, :root.t_light .t_dark .t_light .t_dark .t_yellow, :root.t_light .t_dark .t_yellow, .tm_xxt {--accentBackground:var(--c-blue1Light);--accentColor:var(--c-blue12Light);--background0:hsla(45, 100%, 5.5%, 0);--background025:hsla(45, 100%, 5.5%, 0.25);--background05:hsla(45, 100%, 5.5%, 0.5);--background075:hsla(45, 100%, 5.5%, 0.75);--color1:var(--c-yellow1Dark);--color2:var(--c-yellow2Dark);--color3:var(--c-yellow3Dark);--color4:var(--c-yellow4Dark);--color5:var(--c-yellow5Dark);--color6:var(--c-yellow6Dark);--color7:var(--c-yellow7Dark);--color8:var(--c-yellow8Dark);--color9:var(--c-yellow9Dark);--color10:var(--c-yellow10Dark);--color11:var(--c-yellow11Dark);--color12:var(--c-yellow12Dark);--color0:hsla(53, 92.0%, 50.0%, 0);--color025:hsla(53, 92.0%, 50.0%, 0.25);--color05:hsla(53, 92.0%, 50.0%, 0.5);--color075:hsla(53, 92.0%, 50.0%, 0.75);--background:var(--c-yellow1Dark);--backgroundHover:var(--c-yellow2Dark);--backgroundPress:hsla(45, 100%, 5.5%, 0.75);--backgroundFocus:hsla(45, 100%, 5.5%, 0.75);--borderColor:var(--c-yellow4Dark);--borderColorHover:var(--c-yellow5Dark);--borderColorPress:var(--c-yellow3Dark);--borderColorFocus:var(--c-yellow4Dark);--color:var(--c-yellow12Dark);--colorHover:var(--c-yellow11Dark);--colorPress:var(--c-yellow12Dark);--colorFocus:var(--c-yellow11Dark);--colorTransparent:hsla(53, 92.0%, 50.0%, 0);--placeholderColor:var(--c-yellow9Dark);--outlineColor:hsla(53, 92.0%, 50.0%, 0.25);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_yellow, .t_yellow {--accentBackground:var(--c-blue1Light);--accentColor:var(--c-blue12Light);--background0:hsla(45, 100%, 5.5%, 0);--background025:hsla(45, 100%, 5.5%, 0.25);--background05:hsla(45, 100%, 5.5%, 0.5);--background075:hsla(45, 100%, 5.5%, 0.75);--color1:var(--c-yellow1Dark);--color2:var(--c-yellow2Dark);--color3:var(--c-yellow3Dark);--color4:var(--c-yellow4Dark);--color5:var(--c-yellow5Dark);--color6:var(--c-yellow6Dark);--color7:var(--c-yellow7Dark);--color8:var(--c-yellow8Dark);--color9:var(--c-yellow9Dark);--color10:var(--c-yellow10Dark);--color11:var(--c-yellow11Dark);--color12:var(--c-yellow12Dark);--color0:hsla(53, 92.0%, 50.0%, 0);--color025:hsla(53, 92.0%, 50.0%, 0.25);--color05:hsla(53, 92.0%, 50.0%, 0.5);--color075:hsla(53, 92.0%, 50.0%, 0.75);--background:var(--c-yellow1Dark);--backgroundHover:var(--c-yellow2Dark);--backgroundPress:hsla(45, 100%, 5.5%, 0.75);--backgroundFocus:hsla(45, 100%, 5.5%, 0.75);--borderColor:var(--c-yellow4Dark);--borderColorHover:var(--c-yellow5Dark);--borderColorPress:var(--c-yellow3Dark);--borderColorFocus:var(--c-yellow4Dark);--color:var(--c-yellow12Dark);--colorHover:var(--c-yellow11Dark);--colorPress:var(--c-yellow12Dark);--colorFocus:var(--c-yellow11Dark);--colorTransparent:hsla(53, 92.0%, 50.0%, 0);--placeholderColor:var(--c-yellow9Dark);--outlineColor:hsla(53, 92.0%, 50.0%, 0.25);}
  }
.t_dark_yellow ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_green, :root.t_dark .t_light .t_dark .t_green, :root.t_light .t_dark .t_green, :root.t_light .t_dark .t_light .t_dark .t_green, .tm_xxt {--accentBackground:var(--c-orange1Light);--accentColor:var(--c-orange12Light);--background0:hsla(146, 30.0%, 7.4%, 0);--background025:hsla(146, 30.0%, 7.4%, 0.25);--background05:hsla(146, 30.0%, 7.4%, 0.5);--background075:hsla(146, 30.0%, 7.4%, 0.75);--color1:var(--c-green1Dark);--color2:var(--c-green2Dark);--color3:var(--c-green3Dark);--color4:var(--c-green4Dark);--color5:var(--c-green5Dark);--color6:var(--c-green6Dark);--color7:var(--c-green7Dark);--color8:var(--c-green8Dark);--color9:var(--c-green9Dark);--color10:var(--c-green10Dark);--color11:var(--c-green11Dark);--color12:var(--c-green12Dark);--color0:hsla(151, 55.0%, 41.5%, 0);--color025:hsla(151, 55.0%, 41.5%, 0.25);--color05:hsla(151, 55.0%, 41.5%, 0.5);--color075:hsla(151, 55.0%, 41.5%, 0.75);--background:var(--c-green1Dark);--backgroundHover:var(--c-green2Dark);--backgroundPress:hsla(146, 30.0%, 7.4%, 0.75);--backgroundFocus:hsla(146, 30.0%, 7.4%, 0.75);--borderColor:var(--c-green4Dark);--borderColorHover:var(--c-green5Dark);--borderColorPress:var(--c-green3Dark);--borderColorFocus:var(--c-green4Dark);--color:var(--c-green12Dark);--colorHover:var(--c-green11Dark);--colorPress:var(--c-green12Dark);--colorFocus:var(--c-green11Dark);--colorTransparent:hsla(151, 55.0%, 41.5%, 0);--placeholderColor:var(--c-green9Dark);--outlineColor:hsla(151, 55.0%, 41.5%, 0.25);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_green, .t_light .t_dark .t_green {--accentBackground:var(--c-orange1Light);--accentColor:var(--c-orange12Light);--background0:hsla(146, 30.0%, 7.4%, 0);--background025:hsla(146, 30.0%, 7.4%, 0.25);--background05:hsla(146, 30.0%, 7.4%, 0.5);--background075:hsla(146, 30.0%, 7.4%, 0.75);--color1:var(--c-green1Dark);--color2:var(--c-green2Dark);--color3:var(--c-green3Dark);--color4:var(--c-green4Dark);--color5:var(--c-green5Dark);--color6:var(--c-green6Dark);--color7:var(--c-green7Dark);--color8:var(--c-green8Dark);--color9:var(--c-green9Dark);--color10:var(--c-green10Dark);--color11:var(--c-green11Dark);--color12:var(--c-green12Dark);--color0:hsla(151, 55.0%, 41.5%, 0);--color025:hsla(151, 55.0%, 41.5%, 0.25);--color05:hsla(151, 55.0%, 41.5%, 0.5);--color075:hsla(151, 55.0%, 41.5%, 0.75);--background:var(--c-green1Dark);--backgroundHover:var(--c-green2Dark);--backgroundPress:hsla(146, 30.0%, 7.4%, 0.75);--backgroundFocus:hsla(146, 30.0%, 7.4%, 0.75);--borderColor:var(--c-green4Dark);--borderColorHover:var(--c-green5Dark);--borderColorPress:var(--c-green3Dark);--borderColorFocus:var(--c-green4Dark);--color:var(--c-green12Dark);--colorHover:var(--c-green11Dark);--colorPress:var(--c-green12Dark);--colorFocus:var(--c-green11Dark);--colorTransparent:hsla(151, 55.0%, 41.5%, 0);--placeholderColor:var(--c-green9Dark);--outlineColor:hsla(151, 55.0%, 41.5%, 0.25);}
  }
.t_dark_green ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_blue, :root.t_dark .t_light .t_dark .t_blue, :root.t_light .t_dark .t_blue, :root.t_light .t_dark .t_light .t_dark .t_blue, .tm_xxt {--accentBackground:var(--c-gray1Light);--accentColor:var(--c-gray12Light);--background0:hsla(212, 35.0%, 9.2%, 0);--background025:hsla(212, 35.0%, 9.2%, 0.25);--background05:hsla(212, 35.0%, 9.2%, 0.5);--background075:hsla(212, 35.0%, 9.2%, 0.75);--color1:var(--c-blue1Dark);--color2:var(--c-blue2Dark);--color3:var(--c-blue3Dark);--color4:var(--c-blue4Dark);--color5:var(--c-blue5Dark);--color6:var(--c-blue6Dark);--color7:var(--c-blue7Dark);--color8:var(--c-blue8Dark);--color9:var(--c-blue9Dark);--color10:var(--c-blue10Dark);--color11:var(--c-blue11Dark);--color12:var(--c-blue12Dark);--color0:hsla(206, 100%, 50.0%, 0);--color025:hsla(206, 100%, 50.0%, 0.25);--color05:hsla(206, 100%, 50.0%, 0.5);--color075:hsla(206, 100%, 50.0%, 0.75);--background:var(--c-blue1Dark);--backgroundHover:var(--c-blue2Dark);--backgroundPress:hsla(212, 35.0%, 9.2%, 0.75);--backgroundFocus:hsla(212, 35.0%, 9.2%, 0.75);--borderColor:var(--c-blue4Dark);--borderColorHover:var(--c-blue5Dark);--borderColorPress:var(--c-blue3Dark);--borderColorFocus:var(--c-blue4Dark);--color:var(--c-blue12Dark);--colorHover:var(--c-blue11Dark);--colorPress:var(--c-blue12Dark);--colorFocus:var(--c-blue11Dark);--colorTransparent:hsla(206, 100%, 50.0%, 0);--placeholderColor:var(--c-blue9Dark);--outlineColor:hsla(206, 100%, 50.0%, 0.25);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_blue, .t_light .t_dark .t_blue {--accentBackground:var(--c-gray1Light);--accentColor:var(--c-gray12Light);--background0:hsla(212, 35.0%, 9.2%, 0);--background025:hsla(212, 35.0%, 9.2%, 0.25);--background05:hsla(212, 35.0%, 9.2%, 0.5);--background075:hsla(212, 35.0%, 9.2%, 0.75);--color1:var(--c-blue1Dark);--color2:var(--c-blue2Dark);--color3:var(--c-blue3Dark);--color4:var(--c-blue4Dark);--color5:var(--c-blue5Dark);--color6:var(--c-blue6Dark);--color7:var(--c-blue7Dark);--color8:var(--c-blue8Dark);--color9:var(--c-blue9Dark);--color10:var(--c-blue10Dark);--color11:var(--c-blue11Dark);--color12:var(--c-blue12Dark);--color0:hsla(206, 100%, 50.0%, 0);--color025:hsla(206, 100%, 50.0%, 0.25);--color05:hsla(206, 100%, 50.0%, 0.5);--color075:hsla(206, 100%, 50.0%, 0.75);--background:var(--c-blue1Dark);--backgroundHover:var(--c-blue2Dark);--backgroundPress:hsla(212, 35.0%, 9.2%, 0.75);--backgroundFocus:hsla(212, 35.0%, 9.2%, 0.75);--borderColor:var(--c-blue4Dark);--borderColorHover:var(--c-blue5Dark);--borderColorPress:var(--c-blue3Dark);--borderColorFocus:var(--c-blue4Dark);--color:var(--c-blue12Dark);--colorHover:var(--c-blue11Dark);--colorPress:var(--c-blue12Dark);--colorFocus:var(--c-blue11Dark);--colorTransparent:hsla(206, 100%, 50.0%, 0);--placeholderColor:var(--c-blue9Dark);--outlineColor:hsla(206, 100%, 50.0%, 0.25);}
  }
.t_dark_blue ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_purple, :root.t_dark .t_purple, :root.t_light .t_dark .t_light .t_dark .t_purple, :root.t_light .t_dark .t_purple, .tm_xxt {--accentBackground:var(--c-red1Light);--accentColor:var(--c-red12Light);--background0:hsla(284, 20.0%, 9.6%, 0);--background025:hsla(284, 20.0%, 9.6%, 0.25);--background05:hsla(284, 20.0%, 9.6%, 0.5);--background075:hsla(284, 20.0%, 9.6%, 0.75);--color1:var(--c-purple1Dark);--color2:var(--c-purple2Dark);--color3:var(--c-purple3Dark);--color4:var(--c-purple4Dark);--color5:var(--c-purple5Dark);--color6:var(--c-purple6Dark);--color7:var(--c-purple7Dark);--color8:var(--c-purple8Dark);--color9:var(--c-purple9Dark);--color10:var(--c-purple10Dark);--color11:var(--c-purple11Dark);--color12:var(--c-purple12Dark);--color0:hsla(272, 51.0%, 54.0%, 0);--color025:hsla(272, 51.0%, 54.0%, 0.25);--color05:hsla(272, 51.0%, 54.0%, 0.5);--color075:hsla(272, 51.0%, 54.0%, 0.75);--background:var(--c-purple1Dark);--backgroundHover:var(--c-purple2Dark);--backgroundPress:hsla(284, 20.0%, 9.6%, 0.75);--backgroundFocus:hsla(284, 20.0%, 9.6%, 0.75);--borderColor:var(--c-purple4Dark);--borderColorHover:var(--c-purple5Dark);--borderColorPress:var(--c-purple3Dark);--borderColorFocus:var(--c-purple4Dark);--color:var(--c-purple12Dark);--colorHover:var(--c-purple11Dark);--colorPress:var(--c-purple12Dark);--colorFocus:var(--c-purple11Dark);--colorTransparent:hsla(272, 51.0%, 54.0%, 0);--placeholderColor:var(--c-purple9Dark);--outlineColor:hsla(272, 51.0%, 54.0%, 0.25);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_purple, .t_purple {--accentBackground:var(--c-red1Light);--accentColor:var(--c-red12Light);--background0:hsla(284, 20.0%, 9.6%, 0);--background025:hsla(284, 20.0%, 9.6%, 0.25);--background05:hsla(284, 20.0%, 9.6%, 0.5);--background075:hsla(284, 20.0%, 9.6%, 0.75);--color1:var(--c-purple1Dark);--color2:var(--c-purple2Dark);--color3:var(--c-purple3Dark);--color4:var(--c-purple4Dark);--color5:var(--c-purple5Dark);--color6:var(--c-purple6Dark);--color7:var(--c-purple7Dark);--color8:var(--c-purple8Dark);--color9:var(--c-purple9Dark);--color10:var(--c-purple10Dark);--color11:var(--c-purple11Dark);--color12:var(--c-purple12Dark);--color0:hsla(272, 51.0%, 54.0%, 0);--color025:hsla(272, 51.0%, 54.0%, 0.25);--color05:hsla(272, 51.0%, 54.0%, 0.5);--color075:hsla(272, 51.0%, 54.0%, 0.75);--background:var(--c-purple1Dark);--backgroundHover:var(--c-purple2Dark);--backgroundPress:hsla(284, 20.0%, 9.6%, 0.75);--backgroundFocus:hsla(284, 20.0%, 9.6%, 0.75);--borderColor:var(--c-purple4Dark);--borderColorHover:var(--c-purple5Dark);--borderColorPress:var(--c-purple3Dark);--borderColorFocus:var(--c-purple4Dark);--color:var(--c-purple12Dark);--colorHover:var(--c-purple11Dark);--colorPress:var(--c-purple12Dark);--colorFocus:var(--c-purple11Dark);--colorTransparent:hsla(272, 51.0%, 54.0%, 0);--placeholderColor:var(--c-purple9Dark);--outlineColor:hsla(272, 51.0%, 54.0%, 0.25);}
  }
.t_dark_purple ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_pink, :root.t_dark .t_pink, :root.t_light .t_dark .t_light .t_dark .t_pink, :root.t_light .t_dark .t_pink, .tm_xxt {--accentBackground:var(--c-purple1Light);--accentColor:var(--c-purple12Light);--background0:hsla(318, 25.0%, 9.6%, 0);--background025:hsla(318, 25.0%, 9.6%, 0.25);--background05:hsla(318, 25.0%, 9.6%, 0.5);--background075:hsla(318, 25.0%, 9.6%, 0.75);--color1:var(--c-pink1Dark);--color2:var(--c-pink2Dark);--color3:var(--c-pink3Dark);--color4:var(--c-pink4Dark);--color5:var(--c-pink5Dark);--color6:var(--c-pink6Dark);--color7:var(--c-pink7Dark);--color8:var(--c-pink8Dark);--color9:var(--c-pink9Dark);--color10:var(--c-pink10Dark);--color11:var(--c-pink11Dark);--color12:var(--c-pink12Dark);--color0:hsla(322, 65.0%, 54.5%, 0);--color025:hsla(322, 65.0%, 54.5%, 0.25);--color05:hsla(322, 65.0%, 54.5%, 0.5);--color075:hsla(322, 65.0%, 54.5%, 0.75);--background:var(--c-pink1Dark);--backgroundHover:var(--c-pink2Dark);--backgroundPress:hsla(318, 25.0%, 9.6%, 0.75);--backgroundFocus:hsla(318, 25.0%, 9.6%, 0.75);--borderColor:var(--c-pink4Dark);--borderColorHover:var(--c-pink5Dark);--borderColorPress:var(--c-pink3Dark);--borderColorFocus:var(--c-pink4Dark);--color:var(--c-pink12Dark);--colorHover:var(--c-pink11Dark);--colorPress:var(--c-pink12Dark);--colorFocus:var(--c-pink11Dark);--colorTransparent:hsla(322, 65.0%, 54.5%, 0);--placeholderColor:var(--c-pink9Dark);--outlineColor:hsla(322, 65.0%, 54.5%, 0.25);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_pink, .t_pink {--accentBackground:var(--c-purple1Light);--accentColor:var(--c-purple12Light);--background0:hsla(318, 25.0%, 9.6%, 0);--background025:hsla(318, 25.0%, 9.6%, 0.25);--background05:hsla(318, 25.0%, 9.6%, 0.5);--background075:hsla(318, 25.0%, 9.6%, 0.75);--color1:var(--c-pink1Dark);--color2:var(--c-pink2Dark);--color3:var(--c-pink3Dark);--color4:var(--c-pink4Dark);--color5:var(--c-pink5Dark);--color6:var(--c-pink6Dark);--color7:var(--c-pink7Dark);--color8:var(--c-pink8Dark);--color9:var(--c-pink9Dark);--color10:var(--c-pink10Dark);--color11:var(--c-pink11Dark);--color12:var(--c-pink12Dark);--color0:hsla(322, 65.0%, 54.5%, 0);--color025:hsla(322, 65.0%, 54.5%, 0.25);--color05:hsla(322, 65.0%, 54.5%, 0.5);--color075:hsla(322, 65.0%, 54.5%, 0.75);--background:var(--c-pink1Dark);--backgroundHover:var(--c-pink2Dark);--backgroundPress:hsla(318, 25.0%, 9.6%, 0.75);--backgroundFocus:hsla(318, 25.0%, 9.6%, 0.75);--borderColor:var(--c-pink4Dark);--borderColorHover:var(--c-pink5Dark);--borderColorPress:var(--c-pink3Dark);--borderColorFocus:var(--c-pink4Dark);--color:var(--c-pink12Dark);--colorHover:var(--c-pink11Dark);--colorPress:var(--c-pink12Dark);--colorFocus:var(--c-pink11Dark);--colorTransparent:hsla(322, 65.0%, 54.5%, 0);--placeholderColor:var(--c-pink9Dark);--outlineColor:hsla(322, 65.0%, 54.5%, 0.25);}
  }
.t_dark_pink ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_dark .t_red, :root.t_dark .t_red, :root.t_light .t_dark .t_light .t_dark .t_red, :root.t_light .t_dark .t_red, .tm_xxt {--accentBackground:var(--c-yellow1Light);--accentColor:var(--c-yellow12Light);--background0:hsla(353, 23.0%, 9.8%, 0);--background025:hsla(353, 23.0%, 9.8%, 0.25);--background05:hsla(353, 23.0%, 9.8%, 0.5);--background075:hsla(353, 23.0%, 9.8%, 0.75);--color1:var(--c-red1Dark);--color2:var(--c-red2Dark);--color3:var(--c-red3Dark);--color4:var(--c-red4Dark);--color5:var(--c-red5Dark);--color6:var(--c-red6Dark);--color7:var(--c-red7Dark);--color8:var(--c-red8Dark);--color9:var(--c-red9Dark);--color10:var(--c-red10Dark);--color11:var(--c-red11Dark);--color12:var(--c-red12Dark);--color0:hsla(358, 75.0%, 59.0%, 0);--color025:hsla(358, 75.0%, 59.0%, 0.25);--color05:hsla(358, 75.0%, 59.0%, 0.5);--color075:hsla(358, 75.0%, 59.0%, 0.75);--background:var(--c-red1Dark);--backgroundHover:var(--c-red2Dark);--backgroundPress:hsla(353, 23.0%, 9.8%, 0.75);--backgroundFocus:hsla(353, 23.0%, 9.8%, 0.75);--borderColor:var(--c-red4Dark);--borderColorHover:var(--c-red5Dark);--borderColorPress:var(--c-red3Dark);--borderColorFocus:var(--c-red4Dark);--color:var(--c-red12Dark);--colorHover:var(--c-red11Dark);--colorPress:var(--c-red12Dark);--colorFocus:var(--c-red11Dark);--colorTransparent:hsla(358, 75.0%, 59.0%, 0);--placeholderColor:var(--c-red9Dark);--outlineColor:hsla(358, 75.0%, 59.0%, 0.25);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_red, .t_red {--accentBackground:var(--c-yellow1Light);--accentColor:var(--c-yellow12Light);--background0:hsla(353, 23.0%, 9.8%, 0);--background025:hsla(353, 23.0%, 9.8%, 0.25);--background05:hsla(353, 23.0%, 9.8%, 0.5);--background075:hsla(353, 23.0%, 9.8%, 0.75);--color1:var(--c-red1Dark);--color2:var(--c-red2Dark);--color3:var(--c-red3Dark);--color4:var(--c-red4Dark);--color5:var(--c-red5Dark);--color6:var(--c-red6Dark);--color7:var(--c-red7Dark);--color8:var(--c-red8Dark);--color9:var(--c-red9Dark);--color10:var(--c-red10Dark);--color11:var(--c-red11Dark);--color12:var(--c-red12Dark);--color0:hsla(358, 75.0%, 59.0%, 0);--color025:hsla(358, 75.0%, 59.0%, 0.25);--color05:hsla(358, 75.0%, 59.0%, 0.5);--color075:hsla(358, 75.0%, 59.0%, 0.75);--background:var(--c-red1Dark);--backgroundHover:var(--c-red2Dark);--backgroundPress:hsla(353, 23.0%, 9.8%, 0.75);--backgroundFocus:hsla(353, 23.0%, 9.8%, 0.75);--borderColor:var(--c-red4Dark);--borderColorHover:var(--c-red5Dark);--borderColorPress:var(--c-red3Dark);--borderColorFocus:var(--c-red4Dark);--color:var(--c-red12Dark);--colorHover:var(--c-red11Dark);--colorPress:var(--c-red12Dark);--colorFocus:var(--c-red11Dark);--colorTransparent:hsla(358, 75.0%, 59.0%, 0);--placeholderColor:var(--c-red9Dark);--outlineColor:hsla(358, 75.0%, 59.0%, 0.25);}
  }
.t_dark_red ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_gray, :root.t_dark .t_light .t_dark .t_gray, :root.t_light .t_dark .t_gray, :root.t_light .t_dark .t_light .t_dark .t_gray, .tm_xxt {--accentBackground:var(--c-green1Light);--accentColor:var(--c-green12Light);--background0:hsla(0, 0%, 8.5%, 0);--background025:hsla(0, 0%, 8.5%, 0.25);--background05:hsla(0, 0%, 8.5%, 0.5);--background075:hsla(0, 0%, 8.5%, 0.75);--color1:var(--c-gray1Dark);--color2:var(--c-gray2Dark);--color3:var(--c-gray3Dark);--color4:var(--c-gray4Dark);--color5:var(--c-gray5Dark);--color6:var(--c-gray6Dark);--color7:var(--c-gray7Dark);--color8:var(--c-gray8Dark);--color9:var(--c-gray9Dark);--color10:var(--c-gray10Dark);--color11:var(--c-gray11Dark);--color12:var(--c-gray12Dark);--color0:hsla(0, 0%, 43.9%, 0);--color025:hsla(0, 0%, 43.9%, 0.25);--color05:hsla(0, 0%, 43.9%, 0.5);--color075:hsla(0, 0%, 43.9%, 0.75);--background:var(--c-gray1Dark);--backgroundHover:var(--c-gray2Dark);--backgroundPress:hsla(0, 0%, 8.5%, 0.75);--backgroundFocus:hsla(0, 0%, 8.5%, 0.75);--borderColor:var(--c-gray4Dark);--borderColorHover:var(--c-gray5Dark);--borderColorPress:var(--c-gray3Dark);--borderColorFocus:var(--c-gray4Dark);--color:var(--c-gray12Dark);--colorHover:var(--c-gray11Dark);--colorPress:var(--c-gray12Dark);--colorFocus:var(--c-gray11Dark);--colorTransparent:hsla(0, 0%, 43.9%, 0);--placeholderColor:var(--c-gray9Dark);--outlineColor:hsla(0, 0%, 43.9%, 0.25);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_gray, .t_light .t_dark .t_gray {--accentBackground:var(--c-green1Light);--accentColor:var(--c-green12Light);--background0:hsla(0, 0%, 8.5%, 0);--background025:hsla(0, 0%, 8.5%, 0.25);--background05:hsla(0, 0%, 8.5%, 0.5);--background075:hsla(0, 0%, 8.5%, 0.75);--color1:var(--c-gray1Dark);--color2:var(--c-gray2Dark);--color3:var(--c-gray3Dark);--color4:var(--c-gray4Dark);--color5:var(--c-gray5Dark);--color6:var(--c-gray6Dark);--color7:var(--c-gray7Dark);--color8:var(--c-gray8Dark);--color9:var(--c-gray9Dark);--color10:var(--c-gray10Dark);--color11:var(--c-gray11Dark);--color12:var(--c-gray12Dark);--color0:hsla(0, 0%, 43.9%, 0);--color025:hsla(0, 0%, 43.9%, 0.25);--color05:hsla(0, 0%, 43.9%, 0.5);--color075:hsla(0, 0%, 43.9%, 0.75);--background:var(--c-gray1Dark);--backgroundHover:var(--c-gray2Dark);--backgroundPress:hsla(0, 0%, 8.5%, 0.75);--backgroundFocus:hsla(0, 0%, 8.5%, 0.75);--borderColor:var(--c-gray4Dark);--borderColorHover:var(--c-gray5Dark);--borderColorPress:var(--c-gray3Dark);--borderColorFocus:var(--c-gray4Dark);--color:var(--c-gray12Dark);--colorHover:var(--c-gray11Dark);--colorPress:var(--c-gray12Dark);--colorFocus:var(--c-gray11Dark);--colorTransparent:hsla(0, 0%, 43.9%, 0);--placeholderColor:var(--c-gray9Dark);--outlineColor:hsla(0, 0%, 43.9%, 0.25);}
  }
.t_dark_gray ::selection{background:var(--color5);color:var(--color11)}
:root.t_dark .t_light .t_alt1, :root.t_dark .t_light .t_dark .t_light .t_alt1, :root.t_light .t_alt1, :root.t_light .t_dark .t_light .t_alt1, .tm_xxt {--color:var(--c-white11);--colorHover:var(--c-white10);--colorPress:var(--c-white11);--colorFocus:var(--c-white10);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_alt1, .t_dark .t_light .t_alt1 {--color:var(--c-white11);--colorHover:var(--c-white10);--colorPress:var(--c-white11);--colorFocus:var(--c-white10);}
  }
:root.t_dark .t_light .t_alt2, :root.t_dark .t_light .t_dark .t_light .t_alt2, :root.t_light .t_alt2, :root.t_light .t_dark .t_light .t_alt2, .tm_xxt {--color:var(--c-white10);--colorHover:var(--c-gray9Light);--colorPress:var(--c-white10);--colorFocus:var(--c-gray9Light);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_alt2, .t_dark .t_light .t_alt2 {--color:var(--c-white10);--colorHover:var(--c-gray9Light);--colorPress:var(--c-white10);--colorFocus:var(--c-gray9Light);}
  }
:root.t_dark .t_light .t_SliderTrackActive, :root.t_dark .t_light .t_active, :root.t_dark .t_light .t_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_active, :root.t_dark .t_light .t_dark .t_light .t_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_surface3, :root.t_dark .t_light .t_surface3, :root.t_light .t_SliderTrackActive, :root.t_light .t_active, :root.t_light .t_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_SliderTrackActive, :root.t_light .t_dark .t_light .t_active, :root.t_light .t_dark .t_light .t_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_surface3, :root.t_light .t_surface3, .tm_xxt {--background:var(--c-white4);--backgroundHover:var(--c-white3);--backgroundPress:var(--c-white5);--backgroundFocus:var(--c-white5);--borderColor:var(--c-white7);--borderColorHover:var(--c-white6);--borderColorFocus:var(--c-white7);--borderColorPress:var(--c-white8);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_SliderTrackActive, .t_active, .t_active_SliderTrackActive, .t_dark .t_light .t_SliderTrackActive, .t_dark .t_light .t_active, .t_dark .t_light .t_active_SliderTrackActive, .t_dark .t_light .t_surface3, .t_surface3 {--background:var(--c-white4);--backgroundHover:var(--c-white3);--backgroundPress:var(--c-white5);--backgroundFocus:var(--c-white5);--borderColor:var(--c-white7);--borderColorHover:var(--c-white6);--borderColorFocus:var(--c-white7);--borderColorPress:var(--c-white8);}
  }
:root.t_dark .t_light .t_Card, :root.t_dark .t_light .t_ListItem, :root.t_dark .t_light .t_Progress, :root.t_dark .t_light .t_SelectTrigger, :root.t_dark .t_light .t_SliderTrack, :root.t_dark .t_light .t_TextArea, :root.t_dark .t_light .t_TooltipArrow, :root.t_dark .t_light .t_active_ListItem, :root.t_dark .t_light .t_active_Progress, :root.t_dark .t_light .t_active_SliderTrack, :root.t_dark .t_light .t_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_Card, :root.t_dark .t_light .t_dark .t_light .t_ListItem, :root.t_dark .t_light .t_dark .t_light .t_Progress, :root.t_dark .t_light .t_dark .t_light .t_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_TextArea, :root.t_dark .t_light .t_dark .t_light .t_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_active_ListItem, :root.t_dark .t_light .t_dark .t_light .t_active_Progress, :root.t_dark .t_light .t_dark .t_light .t_active_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_surface1, :root.t_dark .t_light .t_surface1, :root.t_light .t_Card, :root.t_light .t_ListItem, :root.t_light .t_Progress, :root.t_light .t_SelectTrigger, :root.t_light .t_SliderTrack, :root.t_light .t_TextArea, :root.t_light .t_TooltipArrow, :root.t_light .t_active_ListItem, :root.t_light .t_active_Progress, :root.t_light .t_active_SliderTrack, :root.t_light .t_active_TooltipArrow, :root.t_light .t_dark .t_light .t_Card, :root.t_light .t_dark .t_light .t_ListItem, :root.t_light .t_dark .t_light .t_Progress, :root.t_light .t_dark .t_light .t_SelectTrigger, :root.t_light .t_dark .t_light .t_SliderTrack, :root.t_light .t_dark .t_light .t_TextArea, :root.t_light .t_dark .t_light .t_TooltipArrow, :root.t_light .t_dark .t_light .t_active_ListItem, :root.t_light .t_dark .t_light .t_active_Progress, :root.t_light .t_dark .t_light .t_active_SliderTrack, :root.t_light .t_dark .t_light .t_active_TooltipArrow, :root.t_light .t_dark .t_light .t_surface1, :root.t_light .t_surface1, .tm_xxt {--background:var(--c-white2);--backgroundHover:var(--c-black12);--backgroundPress:var(--c-white3);--backgroundFocus:var(--c-white3);--borderColor:var(--c-white5);--borderColorHover:var(--c-white4);--borderColorFocus:var(--c-white5);--borderColorPress:var(--c-white6);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_Card, .t_ListItem, .t_Progress, .t_SelectTrigger, .t_SliderTrack, .t_TextArea, .t_TooltipArrow, .t_active_ListItem, .t_active_Progress, .t_active_SliderTrack, .t_active_TooltipArrow, .t_dark .t_light .t_Card, .t_dark .t_light .t_ListItem, .t_dark .t_light .t_Progress, .t_dark .t_light .t_SelectTrigger, .t_dark .t_light .t_SliderTrack, .t_dark .t_light .t_TextArea, .t_dark .t_light .t_TooltipArrow, .t_dark .t_light .t_active_ListItem, .t_dark .t_light .t_active_Progress, .t_dark .t_light .t_active_SliderTrack, .t_dark .t_light .t_active_TooltipArrow, .t_dark .t_light .t_surface1, .t_surface1 {--background:var(--c-white2);--backgroundHover:var(--c-black12);--backgroundPress:var(--c-white3);--backgroundFocus:var(--c-white3);--borderColor:var(--c-white5);--borderColorHover:var(--c-white4);--borderColorFocus:var(--c-white5);--borderColorPress:var(--c-white6);}
  }
:root.t_dark .t_light .t_Checkbox, :root.t_dark .t_light .t_RadioGroupItem, :root.t_dark .t_light .t_Switch, :root.t_dark .t_light .t_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_Switch, :root.t_dark .t_light .t_dark .t_light .t_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_surface2, :root.t_dark .t_light .t_surface2, :root.t_light .t_Checkbox, :root.t_light .t_RadioGroupItem, :root.t_light .t_Switch, :root.t_light .t_TooltipContent, :root.t_light .t_dark .t_light .t_Checkbox, :root.t_light .t_dark .t_light .t_RadioGroupItem, :root.t_light .t_dark .t_light .t_Switch, :root.t_light .t_dark .t_light .t_TooltipContent, :root.t_light .t_dark .t_light .t_surface2, :root.t_light .t_surface2, .tm_xxt {--background:var(--c-white3);--backgroundHover:var(--c-white2);--backgroundPress:var(--c-white4);--backgroundFocus:var(--c-white4);--borderColor:var(--c-white6);--borderColorHover:var(--c-white5);--borderColorFocus:var(--c-white6);--borderColorPress:var(--c-white7);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_Checkbox, .t_RadioGroupItem, .t_Switch, .t_TooltipContent, .t_dark .t_light .t_Checkbox, .t_dark .t_light .t_RadioGroupItem, .t_dark .t_light .t_Switch, .t_dark .t_light .t_TooltipContent, .t_dark .t_light .t_surface2, .t_surface2 {--background:var(--c-white3);--backgroundHover:var(--c-white2);--backgroundPress:var(--c-white4);--backgroundFocus:var(--c-white4);--borderColor:var(--c-white6);--borderColorHover:var(--c-white5);--borderColorFocus:var(--c-white6);--borderColorPress:var(--c-white7);}
  }
:root.t_dark .t_light .t_active_Button, :root.t_dark .t_light .t_active_Card, :root.t_dark .t_light .t_active_Checkbox, :root.t_dark .t_light .t_active_Input, :root.t_dark .t_light .t_active_RadioGroupItem, :root.t_dark .t_light .t_active_SelectTrigger, :root.t_dark .t_light .t_active_Switch, :root.t_dark .t_light .t_active_TextArea, :root.t_dark .t_light .t_active_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_active_Button, :root.t_dark .t_light .t_dark .t_light .t_active_Card, :root.t_dark .t_light .t_dark .t_light .t_active_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_active_Input, :root.t_dark .t_light .t_dark .t_light .t_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_active_Switch, :root.t_dark .t_light .t_dark .t_light .t_active_TextArea, :root.t_dark .t_light .t_dark .t_light .t_active_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_surface4, :root.t_dark .t_light .t_surface4, :root.t_light .t_active_Button, :root.t_light .t_active_Card, :root.t_light .t_active_Checkbox, :root.t_light .t_active_Input, :root.t_light .t_active_RadioGroupItem, :root.t_light .t_active_SelectTrigger, :root.t_light .t_active_Switch, :root.t_light .t_active_TextArea, :root.t_light .t_active_TooltipContent, :root.t_light .t_dark .t_light .t_active_Button, :root.t_light .t_dark .t_light .t_active_Card, :root.t_light .t_dark .t_light .t_active_Checkbox, :root.t_light .t_dark .t_light .t_active_Input, :root.t_light .t_dark .t_light .t_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_active_SelectTrigger, :root.t_light .t_dark .t_light .t_active_Switch, :root.t_light .t_dark .t_light .t_active_TextArea, :root.t_light .t_dark .t_light .t_active_TooltipContent, :root.t_light .t_dark .t_light .t_surface4, :root.t_light .t_surface4, .tm_xxt {--background:var(--c-white6);--backgroundHover:var(--c-white6);--backgroundPress:var(--c-white7);--backgroundFocus:var(--c-white7);--borderColor:var(--c-white6);--borderColorHover:var(--c-white6);--borderColorFocus:var(--c-white7);--borderColorPress:var(--c-white7);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_active_Button, .t_active_Card, .t_active_Checkbox, .t_active_Input, .t_active_RadioGroupItem, .t_active_SelectTrigger, .t_active_Switch, .t_active_TextArea, .t_active_TooltipContent, .t_dark .t_light .t_active_Button, .t_dark .t_light .t_active_Card, .t_dark .t_light .t_active_Checkbox, .t_dark .t_light .t_active_Input, .t_dark .t_light .t_active_RadioGroupItem, .t_dark .t_light .t_active_SelectTrigger, .t_dark .t_light .t_active_Switch, .t_dark .t_light .t_active_TextArea, .t_dark .t_light .t_active_TooltipContent, .t_dark .t_light .t_surface4, .t_surface4 {--background:var(--c-white6);--backgroundHover:var(--c-white6);--backgroundPress:var(--c-white7);--backgroundFocus:var(--c-white7);--borderColor:var(--c-white6);--borderColorHover:var(--c-white6);--borderColorFocus:var(--c-white7);--borderColorPress:var(--c-white7);}
  }
:root.t_dark .t_alt1, :root.t_dark .t_light .t_dark .t_alt1, :root.t_light .t_dark .t_alt1, :root.t_light .t_dark .t_light .t_dark .t_alt1, .tm_xxt {--color:var(--c-black11);--colorHover:var(--c-black10);--colorPress:var(--c-black11);--colorFocus:var(--c-black10);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_alt1, .t_light .t_dark .t_alt1 {--color:var(--c-black11);--colorHover:var(--c-black10);--colorPress:var(--c-black11);--colorFocus:var(--c-black10);}
  }
:root.t_dark .t_alt2, :root.t_dark .t_light .t_dark .t_alt2, :root.t_light .t_dark .t_alt2, :root.t_light .t_dark .t_light .t_dark .t_alt2, .tm_xxt {--color:var(--c-black10);--colorHover:var(--c-black9);--colorPress:var(--c-black10);--colorFocus:var(--c-black9);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_alt2, .t_light .t_dark .t_alt2 {--color:var(--c-black10);--colorHover:var(--c-black9);--colorPress:var(--c-black10);--colorFocus:var(--c-black9);}
  }
:root.t_dark .t_SliderTrackActive, :root.t_dark .t_active, :root.t_dark .t_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_SliderTrackActive, :root.t_dark .t_light .t_dark .t_active, :root.t_dark .t_light .t_dark .t_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_surface3, :root.t_dark .t_surface3, :root.t_light .t_dark .t_SliderTrackActive, :root.t_light .t_dark .t_active, :root.t_light .t_dark .t_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_active, :root.t_light .t_dark .t_light .t_dark .t_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_surface3, :root.t_light .t_dark .t_surface3, .tm_xxt {--background:var(--c-black4);--backgroundHover:var(--c-black5);--backgroundPress:var(--c-black3);--backgroundFocus:var(--c-black3);--borderColor:var(--c-black7);--borderColorHover:var(--c-black8);--borderColorFocus:var(--c-black7);--borderColorPress:var(--c-black6);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_SliderTrackActive, .t_active, .t_active_SliderTrackActive, .t_light .t_dark .t_SliderTrackActive, .t_light .t_dark .t_active, .t_light .t_dark .t_active_SliderTrackActive, .t_light .t_dark .t_surface3, .t_surface3 {--background:var(--c-black4);--backgroundHover:var(--c-black5);--backgroundPress:var(--c-black3);--backgroundFocus:var(--c-black3);--borderColor:var(--c-black7);--borderColorHover:var(--c-black8);--borderColorFocus:var(--c-black7);--borderColorPress:var(--c-black6);}
  }
:root.t_dark .t_Card, :root.t_dark .t_ListItem, :root.t_dark .t_Progress, :root.t_dark .t_SelectTrigger, :root.t_dark .t_SliderTrack, :root.t_dark .t_TextArea, :root.t_dark .t_TooltipArrow, :root.t_dark .t_active_ListItem, :root.t_dark .t_active_Progress, :root.t_dark .t_active_SliderTrack, :root.t_dark .t_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_Card, :root.t_dark .t_light .t_dark .t_ListItem, :root.t_dark .t_light .t_dark .t_Progress, :root.t_dark .t_light .t_dark .t_SelectTrigger, :root.t_dark .t_light .t_dark .t_SliderTrack, :root.t_dark .t_light .t_dark .t_TextArea, :root.t_dark .t_light .t_dark .t_TooltipArrow, :root.t_dark .t_light .t_dark .t_active_ListItem, :root.t_dark .t_light .t_dark .t_active_Progress, :root.t_dark .t_light .t_dark .t_active_SliderTrack, :root.t_dark .t_light .t_dark .t_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_surface1, :root.t_dark .t_surface1, :root.t_light .t_dark .t_Card, :root.t_light .t_dark .t_ListItem, :root.t_light .t_dark .t_Progress, :root.t_light .t_dark .t_SelectTrigger, :root.t_light .t_dark .t_SliderTrack, :root.t_light .t_dark .t_TextArea, :root.t_light .t_dark .t_TooltipArrow, :root.t_light .t_dark .t_active_ListItem, :root.t_light .t_dark .t_active_Progress, :root.t_light .t_dark .t_active_SliderTrack, :root.t_light .t_dark .t_active_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_Card, :root.t_light .t_dark .t_light .t_dark .t_ListItem, :root.t_light .t_dark .t_light .t_dark .t_Progress, :root.t_light .t_dark .t_light .t_dark .t_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_TextArea, :root.t_light .t_dark .t_light .t_dark .t_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_active_ListItem, :root.t_light .t_dark .t_light .t_dark .t_active_Progress, :root.t_light .t_dark .t_light .t_dark .t_active_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_active_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_surface1, :root.t_light .t_dark .t_surface1, .tm_xxt {--background:var(--c-black2);--backgroundHover:var(--c-black3);--backgroundPress:var(--c-black1);--backgroundFocus:var(--c-black1);--borderColor:var(--c-black5);--borderColorHover:var(--c-black6);--borderColorFocus:var(--c-black5);--borderColorPress:var(--c-black4);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_Card, .t_ListItem, .t_Progress, .t_SelectTrigger, .t_SliderTrack, .t_TextArea, .t_TooltipArrow, .t_active_ListItem, .t_active_Progress, .t_active_SliderTrack, .t_active_TooltipArrow, .t_light .t_dark .t_Card, .t_light .t_dark .t_ListItem, .t_light .t_dark .t_Progress, .t_light .t_dark .t_SelectTrigger, .t_light .t_dark .t_SliderTrack, .t_light .t_dark .t_TextArea, .t_light .t_dark .t_TooltipArrow, .t_light .t_dark .t_active_ListItem, .t_light .t_dark .t_active_Progress, .t_light .t_dark .t_active_SliderTrack, .t_light .t_dark .t_active_TooltipArrow, .t_light .t_dark .t_surface1, .t_surface1 {--background:var(--c-black2);--backgroundHover:var(--c-black3);--backgroundPress:var(--c-black1);--backgroundFocus:var(--c-black1);--borderColor:var(--c-black5);--borderColorHover:var(--c-black6);--borderColorFocus:var(--c-black5);--borderColorPress:var(--c-black4);}
  }
:root.t_dark .t_Checkbox, :root.t_dark .t_RadioGroupItem, :root.t_dark .t_Switch, :root.t_dark .t_TooltipContent, :root.t_dark .t_light .t_dark .t_Checkbox, :root.t_dark .t_light .t_dark .t_RadioGroupItem, :root.t_dark .t_light .t_dark .t_Switch, :root.t_dark .t_light .t_dark .t_TooltipContent, :root.t_dark .t_light .t_dark .t_surface2, :root.t_dark .t_surface2, :root.t_light .t_dark .t_Checkbox, :root.t_light .t_dark .t_RadioGroupItem, :root.t_light .t_dark .t_Switch, :root.t_light .t_dark .t_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_Switch, :root.t_light .t_dark .t_light .t_dark .t_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_surface2, :root.t_light .t_dark .t_surface2, .tm_xxt {--background:var(--c-black3);--backgroundHover:var(--c-black4);--backgroundPress:var(--c-black2);--backgroundFocus:var(--c-black2);--borderColor:var(--c-black6);--borderColorHover:var(--c-black7);--borderColorFocus:var(--c-black6);--borderColorPress:var(--c-black5);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_Checkbox, .t_RadioGroupItem, .t_Switch, .t_TooltipContent, .t_light .t_dark .t_Checkbox, .t_light .t_dark .t_RadioGroupItem, .t_light .t_dark .t_Switch, .t_light .t_dark .t_TooltipContent, .t_light .t_dark .t_surface2, .t_surface2 {--background:var(--c-black3);--backgroundHover:var(--c-black4);--backgroundPress:var(--c-black2);--backgroundFocus:var(--c-black2);--borderColor:var(--c-black6);--borderColorHover:var(--c-black7);--borderColorFocus:var(--c-black6);--borderColorPress:var(--c-black5);}
  }
:root.t_dark .t_active_Button, :root.t_dark .t_active_Card, :root.t_dark .t_active_Checkbox, :root.t_dark .t_active_Input, :root.t_dark .t_active_RadioGroupItem, :root.t_dark .t_active_SelectTrigger, :root.t_dark .t_active_Switch, :root.t_dark .t_active_TextArea, :root.t_dark .t_active_TooltipContent, :root.t_dark .t_light .t_dark .t_active_Button, :root.t_dark .t_light .t_dark .t_active_Card, :root.t_dark .t_light .t_dark .t_active_Checkbox, :root.t_dark .t_light .t_dark .t_active_Input, :root.t_dark .t_light .t_dark .t_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_active_Switch, :root.t_dark .t_light .t_dark .t_active_TextArea, :root.t_dark .t_light .t_dark .t_active_TooltipContent, :root.t_dark .t_light .t_dark .t_surface4, :root.t_dark .t_surface4, :root.t_light .t_dark .t_active_Button, :root.t_light .t_dark .t_active_Card, :root.t_light .t_dark .t_active_Checkbox, :root.t_light .t_dark .t_active_Input, :root.t_light .t_dark .t_active_RadioGroupItem, :root.t_light .t_dark .t_active_SelectTrigger, :root.t_light .t_dark .t_active_Switch, :root.t_light .t_dark .t_active_TextArea, :root.t_light .t_dark .t_active_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_active_Button, :root.t_light .t_dark .t_light .t_dark .t_active_Card, :root.t_light .t_dark .t_light .t_dark .t_active_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_active_Input, :root.t_light .t_dark .t_light .t_dark .t_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_active_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_active_Switch, :root.t_light .t_dark .t_light .t_dark .t_active_TextArea, :root.t_light .t_dark .t_light .t_dark .t_active_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_surface4, :root.t_light .t_dark .t_surface4, .tm_xxt {--background:var(--c-black6);--backgroundHover:var(--c-black6);--backgroundPress:var(--c-black5);--backgroundFocus:var(--c-black5);--borderColor:var(--c-black6);--borderColorHover:var(--c-black6);--borderColorFocus:var(--c-black5);--borderColorPress:var(--c-black5);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_active_Button, .t_active_Card, .t_active_Checkbox, .t_active_Input, .t_active_RadioGroupItem, .t_active_SelectTrigger, .t_active_Switch, .t_active_TextArea, .t_active_TooltipContent, .t_light .t_dark .t_active_Button, .t_light .t_dark .t_active_Card, .t_light .t_dark .t_active_Checkbox, .t_light .t_dark .t_active_Input, .t_light .t_dark .t_active_RadioGroupItem, .t_light .t_dark .t_active_SelectTrigger, .t_light .t_dark .t_active_Switch, .t_light .t_dark .t_active_TextArea, .t_light .t_dark .t_active_TooltipContent, .t_light .t_dark .t_surface4, .t_surface4 {--background:var(--c-black6);--backgroundHover:var(--c-black6);--backgroundPress:var(--c-black5);--backgroundFocus:var(--c-black5);--borderColor:var(--c-black6);--borderColorHover:var(--c-black6);--borderColorFocus:var(--c-black5);--borderColorPress:var(--c-black5);}
  }
:root.t_dark .t_light .t_dark .t_light .t_orange_alt1, :root.t_dark .t_light .t_orange_alt1, :root.t_light .t_dark .t_light .t_orange_alt1, :root.t_light .t_orange_alt1, .tm_xxt {--color:var(--c-orange11Light);--colorHover:var(--c-orange10Light);--colorPress:var(--c-orange11Light);--colorFocus:var(--c-orange10Light);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_orange_alt1, .t_orange_alt1 {--color:var(--c-orange11Light);--colorHover:var(--c-orange10Light);--colorPress:var(--c-orange11Light);--colorFocus:var(--c-orange10Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_orange_alt2, :root.t_dark .t_light .t_orange_alt2, :root.t_light .t_dark .t_light .t_orange_alt2, :root.t_light .t_orange_alt2, .tm_xxt {--color:var(--c-orange10Light);--colorHover:var(--c-orange9Dark);--colorPress:var(--c-orange10Light);--colorFocus:var(--c-orange9Dark);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_orange_alt2, .t_orange_alt2 {--color:var(--c-orange10Light);--colorHover:var(--c-orange9Dark);--colorPress:var(--c-orange10Light);--colorFocus:var(--c-orange9Dark);}
  }
:root.t_dark .t_light .t_dark .t_light .t_orange_Button, :root.t_dark .t_light .t_dark .t_light .t_orange_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_orange_active, :root.t_dark .t_light .t_dark .t_light .t_orange_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_orange_surface3, :root.t_dark .t_light .t_orange_Button, :root.t_dark .t_light .t_orange_SliderTrackActive, :root.t_dark .t_light .t_orange_active, :root.t_dark .t_light .t_orange_active_SliderTrackActive, :root.t_dark .t_light .t_orange_surface3, :root.t_light .t_dark .t_light .t_orange_Button, :root.t_light .t_dark .t_light .t_orange_SliderTrackActive, :root.t_light .t_dark .t_light .t_orange_active, :root.t_light .t_dark .t_light .t_orange_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_orange_surface3, :root.t_light .t_orange_Button, :root.t_light .t_orange_SliderTrackActive, :root.t_light .t_orange_active, :root.t_light .t_orange_active_SliderTrackActive, :root.t_light .t_orange_surface3, .tm_xxt {--background:var(--c-orange4Light);--backgroundHover:var(--c-orange3Light);--backgroundPress:var(--c-orange5Light);--backgroundFocus:var(--c-orange5Light);--borderColor:var(--c-orange7Light);--borderColorHover:var(--c-orange6Light);--borderColorFocus:var(--c-orange7Light);--borderColorPress:var(--c-orange8Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_orange_Button, .t_dark .t_light .t_orange_SliderTrackActive, .t_dark .t_light .t_orange_active, .t_dark .t_light .t_orange_active_SliderTrackActive, .t_dark .t_light .t_orange_surface3, .t_orange_Button, .t_orange_SliderTrackActive, .t_orange_active, .t_orange_active_SliderTrackActive, .t_orange_surface3 {--background:var(--c-orange4Light);--backgroundHover:var(--c-orange3Light);--backgroundPress:var(--c-orange5Light);--backgroundFocus:var(--c-orange5Light);--borderColor:var(--c-orange7Light);--borderColorHover:var(--c-orange6Light);--borderColorFocus:var(--c-orange7Light);--borderColorPress:var(--c-orange8Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_orange_Card, :root.t_dark .t_light .t_dark .t_light .t_orange_Input, :root.t_dark .t_light .t_dark .t_light .t_orange_ListItem, :root.t_dark .t_light .t_dark .t_light .t_orange_Progress, :root.t_dark .t_light .t_dark .t_light .t_orange_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_orange_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_orange_TextArea, :root.t_dark .t_light .t_dark .t_light .t_orange_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_orange_active_ListItem, :root.t_dark .t_light .t_dark .t_light .t_orange_active_Progress, :root.t_dark .t_light .t_dark .t_light .t_orange_active_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_orange_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_orange_surface1, :root.t_dark .t_light .t_orange_Card, :root.t_dark .t_light .t_orange_Input, :root.t_dark .t_light .t_orange_ListItem, :root.t_dark .t_light .t_orange_Progress, :root.t_dark .t_light .t_orange_SelectTrigger, :root.t_dark .t_light .t_orange_SliderTrack, :root.t_dark .t_light .t_orange_TextArea, :root.t_dark .t_light .t_orange_TooltipArrow, :root.t_dark .t_light .t_orange_active_ListItem, :root.t_dark .t_light .t_orange_active_Progress, :root.t_dark .t_light .t_orange_active_SliderTrack, :root.t_dark .t_light .t_orange_active_TooltipArrow, :root.t_dark .t_light .t_orange_surface1, :root.t_light .t_dark .t_light .t_orange_Card, :root.t_light .t_dark .t_light .t_orange_Input, :root.t_light .t_dark .t_light .t_orange_ListItem, :root.t_light .t_dark .t_light .t_orange_Progress, :root.t_light .t_dark .t_light .t_orange_SelectTrigger, :root.t_light .t_dark .t_light .t_orange_SliderTrack, :root.t_light .t_dark .t_light .t_orange_TextArea, :root.t_light .t_dark .t_light .t_orange_TooltipArrow, :root.t_light .t_dark .t_light .t_orange_active_ListItem, :root.t_light .t_dark .t_light .t_orange_active_Progress, :root.t_light .t_dark .t_light .t_orange_active_SliderTrack, :root.t_light .t_dark .t_light .t_orange_active_TooltipArrow, :root.t_light .t_dark .t_light .t_orange_surface1, :root.t_light .t_orange_Card, :root.t_light .t_orange_Input, :root.t_light .t_orange_ListItem, :root.t_light .t_orange_Progress, :root.t_light .t_orange_SelectTrigger, :root.t_light .t_orange_SliderTrack, :root.t_light .t_orange_TextArea, :root.t_light .t_orange_TooltipArrow, :root.t_light .t_orange_active_ListItem, :root.t_light .t_orange_active_Progress, :root.t_light .t_orange_active_SliderTrack, :root.t_light .t_orange_active_TooltipArrow, :root.t_light .t_orange_surface1, .tm_xxt {--background:var(--c-orange2Light);--backgroundHover:var(--c-orange1Light);--backgroundPress:var(--c-orange3Light);--backgroundFocus:var(--c-orange3Light);--borderColor:var(--c-orange5Light);--borderColorHover:var(--c-orange4Light);--borderColorFocus:var(--c-orange5Light);--borderColorPress:var(--c-orange6Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_orange_Card, .t_dark .t_light .t_orange_Input, .t_dark .t_light .t_orange_ListItem, .t_dark .t_light .t_orange_Progress, .t_dark .t_light .t_orange_SelectTrigger, .t_dark .t_light .t_orange_SliderTrack, .t_dark .t_light .t_orange_TextArea, .t_dark .t_light .t_orange_TooltipArrow, .t_dark .t_light .t_orange_active_ListItem, .t_dark .t_light .t_orange_active_Progress, .t_dark .t_light .t_orange_active_SliderTrack, .t_dark .t_light .t_orange_active_TooltipArrow, .t_dark .t_light .t_orange_surface1, .t_orange_Card, .t_orange_Input, .t_orange_ListItem, .t_orange_Progress, .t_orange_SelectTrigger, .t_orange_SliderTrack, .t_orange_TextArea, .t_orange_TooltipArrow, .t_orange_active_ListItem, .t_orange_active_Progress, .t_orange_active_SliderTrack, .t_orange_active_TooltipArrow, .t_orange_surface1 {--background:var(--c-orange2Light);--backgroundHover:var(--c-orange1Light);--backgroundPress:var(--c-orange3Light);--backgroundFocus:var(--c-orange3Light);--borderColor:var(--c-orange5Light);--borderColorHover:var(--c-orange4Light);--borderColorFocus:var(--c-orange5Light);--borderColorPress:var(--c-orange6Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_orange_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_orange_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_orange_Switch, :root.t_dark .t_light .t_dark .t_light .t_orange_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_orange_surface2, :root.t_dark .t_light .t_orange_Checkbox, :root.t_dark .t_light .t_orange_RadioGroupItem, :root.t_dark .t_light .t_orange_Switch, :root.t_dark .t_light .t_orange_TooltipContent, :root.t_dark .t_light .t_orange_surface2, :root.t_light .t_dark .t_light .t_orange_Checkbox, :root.t_light .t_dark .t_light .t_orange_RadioGroupItem, :root.t_light .t_dark .t_light .t_orange_Switch, :root.t_light .t_dark .t_light .t_orange_TooltipContent, :root.t_light .t_dark .t_light .t_orange_surface2, :root.t_light .t_orange_Checkbox, :root.t_light .t_orange_RadioGroupItem, :root.t_light .t_orange_Switch, :root.t_light .t_orange_TooltipContent, :root.t_light .t_orange_surface2, .tm_xxt {--background:var(--c-orange3Light);--backgroundHover:var(--c-orange2Light);--backgroundPress:var(--c-orange4Light);--backgroundFocus:var(--c-orange4Light);--borderColor:var(--c-orange6Light);--borderColorHover:var(--c-orange5Light);--borderColorFocus:var(--c-orange6Light);--borderColorPress:var(--c-orange7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_orange_Checkbox, .t_dark .t_light .t_orange_RadioGroupItem, .t_dark .t_light .t_orange_Switch, .t_dark .t_light .t_orange_TooltipContent, .t_dark .t_light .t_orange_surface2, .t_orange_Checkbox, .t_orange_RadioGroupItem, .t_orange_Switch, .t_orange_TooltipContent, .t_orange_surface2 {--background:var(--c-orange3Light);--backgroundHover:var(--c-orange2Light);--backgroundPress:var(--c-orange4Light);--backgroundFocus:var(--c-orange4Light);--borderColor:var(--c-orange6Light);--borderColorHover:var(--c-orange5Light);--borderColorFocus:var(--c-orange6Light);--borderColorPress:var(--c-orange7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_orange_active_Button, :root.t_dark .t_light .t_dark .t_light .t_orange_active_Card, :root.t_dark .t_light .t_dark .t_light .t_orange_active_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_orange_active_Input, :root.t_dark .t_light .t_dark .t_light .t_orange_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_orange_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_orange_active_Switch, :root.t_dark .t_light .t_dark .t_light .t_orange_active_TextArea, :root.t_dark .t_light .t_dark .t_light .t_orange_active_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_orange_surface4, :root.t_dark .t_light .t_orange_active_Button, :root.t_dark .t_light .t_orange_active_Card, :root.t_dark .t_light .t_orange_active_Checkbox, :root.t_dark .t_light .t_orange_active_Input, :root.t_dark .t_light .t_orange_active_RadioGroupItem, :root.t_dark .t_light .t_orange_active_SelectTrigger, :root.t_dark .t_light .t_orange_active_Switch, :root.t_dark .t_light .t_orange_active_TextArea, :root.t_dark .t_light .t_orange_active_TooltipContent, :root.t_dark .t_light .t_orange_surface4, :root.t_light .t_dark .t_light .t_orange_active_Button, :root.t_light .t_dark .t_light .t_orange_active_Card, :root.t_light .t_dark .t_light .t_orange_active_Checkbox, :root.t_light .t_dark .t_light .t_orange_active_Input, :root.t_light .t_dark .t_light .t_orange_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_orange_active_SelectTrigger, :root.t_light .t_dark .t_light .t_orange_active_Switch, :root.t_light .t_dark .t_light .t_orange_active_TextArea, :root.t_light .t_dark .t_light .t_orange_active_TooltipContent, :root.t_light .t_dark .t_light .t_orange_surface4, :root.t_light .t_orange_active_Button, :root.t_light .t_orange_active_Card, :root.t_light .t_orange_active_Checkbox, :root.t_light .t_orange_active_Input, :root.t_light .t_orange_active_RadioGroupItem, :root.t_light .t_orange_active_SelectTrigger, :root.t_light .t_orange_active_Switch, :root.t_light .t_orange_active_TextArea, :root.t_light .t_orange_active_TooltipContent, :root.t_light .t_orange_surface4, .tm_xxt {--background:var(--c-orange6Light);--backgroundHover:var(--c-orange6Light);--backgroundPress:var(--c-orange7Light);--backgroundFocus:var(--c-orange7Light);--borderColor:var(--c-orange6Light);--borderColorHover:var(--c-orange6Light);--borderColorFocus:var(--c-orange7Light);--borderColorPress:var(--c-orange7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_orange_active_Button, .t_dark .t_light .t_orange_active_Card, .t_dark .t_light .t_orange_active_Checkbox, .t_dark .t_light .t_orange_active_Input, .t_dark .t_light .t_orange_active_RadioGroupItem, .t_dark .t_light .t_orange_active_SelectTrigger, .t_dark .t_light .t_orange_active_Switch, .t_dark .t_light .t_orange_active_TextArea, .t_dark .t_light .t_orange_active_TooltipContent, .t_dark .t_light .t_orange_surface4, .t_orange_active_Button, .t_orange_active_Card, .t_orange_active_Checkbox, .t_orange_active_Input, .t_orange_active_RadioGroupItem, .t_orange_active_SelectTrigger, .t_orange_active_Switch, .t_orange_active_TextArea, .t_orange_active_TooltipContent, .t_orange_surface4 {--background:var(--c-orange6Light);--backgroundHover:var(--c-orange6Light);--backgroundPress:var(--c-orange7Light);--backgroundFocus:var(--c-orange7Light);--borderColor:var(--c-orange6Light);--borderColorHover:var(--c-orange6Light);--borderColorFocus:var(--c-orange7Light);--borderColorPress:var(--c-orange7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_yellow_alt1, :root.t_dark .t_light .t_yellow_alt1, :root.t_light .t_dark .t_light .t_yellow_alt1, :root.t_light .t_yellow_alt1, .tm_xxt {--color:var(--c-yellow11Light);--colorHover:var(--c-yellow10Light);--colorPress:var(--c-yellow11Light);--colorFocus:var(--c-yellow10Light);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_yellow_alt1, .t_yellow_alt1 {--color:var(--c-yellow11Light);--colorHover:var(--c-yellow10Light);--colorPress:var(--c-yellow11Light);--colorFocus:var(--c-yellow10Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_yellow_alt2, :root.t_dark .t_light .t_yellow_alt2, :root.t_light .t_dark .t_light .t_yellow_alt2, :root.t_light .t_yellow_alt2, .tm_xxt {--color:var(--c-yellow10Light);--colorHover:var(--c-yellow9Dark);--colorPress:var(--c-yellow10Light);--colorFocus:var(--c-yellow9Dark);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_yellow_alt2, .t_yellow_alt2 {--color:var(--c-yellow10Light);--colorHover:var(--c-yellow9Dark);--colorPress:var(--c-yellow10Light);--colorFocus:var(--c-yellow9Dark);}
  }
:root.t_dark .t_light .t_dark .t_light .t_yellow_Button, :root.t_dark .t_light .t_dark .t_light .t_yellow_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_yellow_active, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_yellow_surface3, :root.t_dark .t_light .t_yellow_Button, :root.t_dark .t_light .t_yellow_SliderTrackActive, :root.t_dark .t_light .t_yellow_active, :root.t_dark .t_light .t_yellow_active_SliderTrackActive, :root.t_dark .t_light .t_yellow_surface3, :root.t_light .t_dark .t_light .t_yellow_Button, :root.t_light .t_dark .t_light .t_yellow_SliderTrackActive, :root.t_light .t_dark .t_light .t_yellow_active, :root.t_light .t_dark .t_light .t_yellow_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_yellow_surface3, :root.t_light .t_yellow_Button, :root.t_light .t_yellow_SliderTrackActive, :root.t_light .t_yellow_active, :root.t_light .t_yellow_active_SliderTrackActive, :root.t_light .t_yellow_surface3, .tm_xxt {--background:var(--c-yellow4Light);--backgroundHover:var(--c-yellow3Light);--backgroundPress:var(--c-yellow5Light);--backgroundFocus:var(--c-yellow5Light);--borderColor:var(--c-yellow7Light);--borderColorHover:var(--c-yellow6Light);--borderColorFocus:var(--c-yellow7Light);--borderColorPress:var(--c-yellow8Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_yellow_Button, .t_dark .t_light .t_yellow_SliderTrackActive, .t_dark .t_light .t_yellow_active, .t_dark .t_light .t_yellow_active_SliderTrackActive, .t_dark .t_light .t_yellow_surface3, .t_yellow_Button, .t_yellow_SliderTrackActive, .t_yellow_active, .t_yellow_active_SliderTrackActive, .t_yellow_surface3 {--background:var(--c-yellow4Light);--backgroundHover:var(--c-yellow3Light);--backgroundPress:var(--c-yellow5Light);--backgroundFocus:var(--c-yellow5Light);--borderColor:var(--c-yellow7Light);--borderColorHover:var(--c-yellow6Light);--borderColorFocus:var(--c-yellow7Light);--borderColorPress:var(--c-yellow8Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_yellow_Card, :root.t_dark .t_light .t_dark .t_light .t_yellow_Input, :root.t_dark .t_light .t_dark .t_light .t_yellow_ListItem, :root.t_dark .t_light .t_dark .t_light .t_yellow_Progress, :root.t_dark .t_light .t_dark .t_light .t_yellow_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_yellow_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_yellow_TextArea, :root.t_dark .t_light .t_dark .t_light .t_yellow_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_ListItem, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_Progress, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_yellow_surface1, :root.t_dark .t_light .t_yellow_Card, :root.t_dark .t_light .t_yellow_Input, :root.t_dark .t_light .t_yellow_ListItem, :root.t_dark .t_light .t_yellow_Progress, :root.t_dark .t_light .t_yellow_SelectTrigger, :root.t_dark .t_light .t_yellow_SliderTrack, :root.t_dark .t_light .t_yellow_TextArea, :root.t_dark .t_light .t_yellow_TooltipArrow, :root.t_dark .t_light .t_yellow_active_ListItem, :root.t_dark .t_light .t_yellow_active_Progress, :root.t_dark .t_light .t_yellow_active_SliderTrack, :root.t_dark .t_light .t_yellow_active_TooltipArrow, :root.t_dark .t_light .t_yellow_surface1, :root.t_light .t_dark .t_light .t_yellow_Card, :root.t_light .t_dark .t_light .t_yellow_Input, :root.t_light .t_dark .t_light .t_yellow_ListItem, :root.t_light .t_dark .t_light .t_yellow_Progress, :root.t_light .t_dark .t_light .t_yellow_SelectTrigger, :root.t_light .t_dark .t_light .t_yellow_SliderTrack, :root.t_light .t_dark .t_light .t_yellow_TextArea, :root.t_light .t_dark .t_light .t_yellow_TooltipArrow, :root.t_light .t_dark .t_light .t_yellow_active_ListItem, :root.t_light .t_dark .t_light .t_yellow_active_Progress, :root.t_light .t_dark .t_light .t_yellow_active_SliderTrack, :root.t_light .t_dark .t_light .t_yellow_active_TooltipArrow, :root.t_light .t_dark .t_light .t_yellow_surface1, :root.t_light .t_yellow_Card, :root.t_light .t_yellow_Input, :root.t_light .t_yellow_ListItem, :root.t_light .t_yellow_Progress, :root.t_light .t_yellow_SelectTrigger, :root.t_light .t_yellow_SliderTrack, :root.t_light .t_yellow_TextArea, :root.t_light .t_yellow_TooltipArrow, :root.t_light .t_yellow_active_ListItem, :root.t_light .t_yellow_active_Progress, :root.t_light .t_yellow_active_SliderTrack, :root.t_light .t_yellow_active_TooltipArrow, :root.t_light .t_yellow_surface1, .tm_xxt {--background:var(--c-yellow2Light);--backgroundHover:var(--c-yellow1Light);--backgroundPress:var(--c-yellow3Light);--backgroundFocus:var(--c-yellow3Light);--borderColor:var(--c-yellow5Light);--borderColorHover:var(--c-yellow4Light);--borderColorFocus:var(--c-yellow5Light);--borderColorPress:var(--c-yellow6Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_yellow_Card, .t_dark .t_light .t_yellow_Input, .t_dark .t_light .t_yellow_ListItem, .t_dark .t_light .t_yellow_Progress, .t_dark .t_light .t_yellow_SelectTrigger, .t_dark .t_light .t_yellow_SliderTrack, .t_dark .t_light .t_yellow_TextArea, .t_dark .t_light .t_yellow_TooltipArrow, .t_dark .t_light .t_yellow_active_ListItem, .t_dark .t_light .t_yellow_active_Progress, .t_dark .t_light .t_yellow_active_SliderTrack, .t_dark .t_light .t_yellow_active_TooltipArrow, .t_dark .t_light .t_yellow_surface1, .t_yellow_Card, .t_yellow_Input, .t_yellow_ListItem, .t_yellow_Progress, .t_yellow_SelectTrigger, .t_yellow_SliderTrack, .t_yellow_TextArea, .t_yellow_TooltipArrow, .t_yellow_active_ListItem, .t_yellow_active_Progress, .t_yellow_active_SliderTrack, .t_yellow_active_TooltipArrow, .t_yellow_surface1 {--background:var(--c-yellow2Light);--backgroundHover:var(--c-yellow1Light);--backgroundPress:var(--c-yellow3Light);--backgroundFocus:var(--c-yellow3Light);--borderColor:var(--c-yellow5Light);--borderColorHover:var(--c-yellow4Light);--borderColorFocus:var(--c-yellow5Light);--borderColorPress:var(--c-yellow6Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_yellow_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_yellow_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_yellow_Switch, :root.t_dark .t_light .t_dark .t_light .t_yellow_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_yellow_surface2, :root.t_dark .t_light .t_yellow_Checkbox, :root.t_dark .t_light .t_yellow_RadioGroupItem, :root.t_dark .t_light .t_yellow_Switch, :root.t_dark .t_light .t_yellow_TooltipContent, :root.t_dark .t_light .t_yellow_surface2, :root.t_light .t_dark .t_light .t_yellow_Checkbox, :root.t_light .t_dark .t_light .t_yellow_RadioGroupItem, :root.t_light .t_dark .t_light .t_yellow_Switch, :root.t_light .t_dark .t_light .t_yellow_TooltipContent, :root.t_light .t_dark .t_light .t_yellow_surface2, :root.t_light .t_yellow_Checkbox, :root.t_light .t_yellow_RadioGroupItem, :root.t_light .t_yellow_Switch, :root.t_light .t_yellow_TooltipContent, :root.t_light .t_yellow_surface2, .tm_xxt {--background:var(--c-yellow3Light);--backgroundHover:var(--c-yellow2Light);--backgroundPress:var(--c-yellow4Light);--backgroundFocus:var(--c-yellow4Light);--borderColor:var(--c-yellow6Light);--borderColorHover:var(--c-yellow5Light);--borderColorFocus:var(--c-yellow6Light);--borderColorPress:var(--c-yellow7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_yellow_Checkbox, .t_dark .t_light .t_yellow_RadioGroupItem, .t_dark .t_light .t_yellow_Switch, .t_dark .t_light .t_yellow_TooltipContent, .t_dark .t_light .t_yellow_surface2, .t_yellow_Checkbox, .t_yellow_RadioGroupItem, .t_yellow_Switch, .t_yellow_TooltipContent, .t_yellow_surface2 {--background:var(--c-yellow3Light);--backgroundHover:var(--c-yellow2Light);--backgroundPress:var(--c-yellow4Light);--backgroundFocus:var(--c-yellow4Light);--borderColor:var(--c-yellow6Light);--borderColorHover:var(--c-yellow5Light);--borderColorFocus:var(--c-yellow6Light);--borderColorPress:var(--c-yellow7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_yellow_active_Button, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_Card, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_Input, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_Switch, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_TextArea, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_yellow_surface4, :root.t_dark .t_light .t_yellow_active_Button, :root.t_dark .t_light .t_yellow_active_Card, :root.t_dark .t_light .t_yellow_active_Checkbox, :root.t_dark .t_light .t_yellow_active_Input, :root.t_dark .t_light .t_yellow_active_RadioGroupItem, :root.t_dark .t_light .t_yellow_active_SelectTrigger, :root.t_dark .t_light .t_yellow_active_Switch, :root.t_dark .t_light .t_yellow_active_TextArea, :root.t_dark .t_light .t_yellow_active_TooltipContent, :root.t_dark .t_light .t_yellow_surface4, :root.t_light .t_dark .t_light .t_yellow_active_Button, :root.t_light .t_dark .t_light .t_yellow_active_Card, :root.t_light .t_dark .t_light .t_yellow_active_Checkbox, :root.t_light .t_dark .t_light .t_yellow_active_Input, :root.t_light .t_dark .t_light .t_yellow_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_yellow_active_SelectTrigger, :root.t_light .t_dark .t_light .t_yellow_active_Switch, :root.t_light .t_dark .t_light .t_yellow_active_TextArea, :root.t_light .t_dark .t_light .t_yellow_active_TooltipContent, :root.t_light .t_dark .t_light .t_yellow_surface4, :root.t_light .t_yellow_active_Button, :root.t_light .t_yellow_active_Card, :root.t_light .t_yellow_active_Checkbox, :root.t_light .t_yellow_active_Input, :root.t_light .t_yellow_active_RadioGroupItem, :root.t_light .t_yellow_active_SelectTrigger, :root.t_light .t_yellow_active_Switch, :root.t_light .t_yellow_active_TextArea, :root.t_light .t_yellow_active_TooltipContent, :root.t_light .t_yellow_surface4, .tm_xxt {--background:var(--c-yellow6Light);--backgroundHover:var(--c-yellow6Light);--backgroundPress:var(--c-yellow7Light);--backgroundFocus:var(--c-yellow7Light);--borderColor:var(--c-yellow6Light);--borderColorHover:var(--c-yellow6Light);--borderColorFocus:var(--c-yellow7Light);--borderColorPress:var(--c-yellow7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_yellow_active_Button, .t_dark .t_light .t_yellow_active_Card, .t_dark .t_light .t_yellow_active_Checkbox, .t_dark .t_light .t_yellow_active_Input, .t_dark .t_light .t_yellow_active_RadioGroupItem, .t_dark .t_light .t_yellow_active_SelectTrigger, .t_dark .t_light .t_yellow_active_Switch, .t_dark .t_light .t_yellow_active_TextArea, .t_dark .t_light .t_yellow_active_TooltipContent, .t_dark .t_light .t_yellow_surface4, .t_yellow_active_Button, .t_yellow_active_Card, .t_yellow_active_Checkbox, .t_yellow_active_Input, .t_yellow_active_RadioGroupItem, .t_yellow_active_SelectTrigger, .t_yellow_active_Switch, .t_yellow_active_TextArea, .t_yellow_active_TooltipContent, .t_yellow_surface4 {--background:var(--c-yellow6Light);--backgroundHover:var(--c-yellow6Light);--backgroundPress:var(--c-yellow7Light);--backgroundFocus:var(--c-yellow7Light);--borderColor:var(--c-yellow6Light);--borderColorHover:var(--c-yellow6Light);--borderColorFocus:var(--c-yellow7Light);--borderColorPress:var(--c-yellow7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_green_alt1, :root.t_dark .t_light .t_green_alt1, :root.t_light .t_dark .t_light .t_green_alt1, :root.t_light .t_green_alt1, .tm_xxt {--color:var(--c-green11Light);--colorHover:var(--c-green10Light);--colorPress:var(--c-green11Light);--colorFocus:var(--c-green10Light);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_green_alt1, .t_green_alt1 {--color:var(--c-green11Light);--colorHover:var(--c-green10Light);--colorPress:var(--c-green11Light);--colorFocus:var(--c-green10Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_green_alt2, :root.t_dark .t_light .t_green_alt2, :root.t_light .t_dark .t_light .t_green_alt2, :root.t_light .t_green_alt2, .tm_xxt {--color:var(--c-green10Light);--colorHover:var(--c-green9Dark);--colorPress:var(--c-green10Light);--colorFocus:var(--c-green9Dark);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_green_alt2, .t_green_alt2 {--color:var(--c-green10Light);--colorHover:var(--c-green9Dark);--colorPress:var(--c-green10Light);--colorFocus:var(--c-green9Dark);}
  }
:root.t_dark .t_light .t_dark .t_light .t_green_Button, :root.t_dark .t_light .t_dark .t_light .t_green_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_green_active, :root.t_dark .t_light .t_dark .t_light .t_green_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_green_surface3, :root.t_dark .t_light .t_green_Button, :root.t_dark .t_light .t_green_SliderTrackActive, :root.t_dark .t_light .t_green_active, :root.t_dark .t_light .t_green_active_SliderTrackActive, :root.t_dark .t_light .t_green_surface3, :root.t_light .t_dark .t_light .t_green_Button, :root.t_light .t_dark .t_light .t_green_SliderTrackActive, :root.t_light .t_dark .t_light .t_green_active, :root.t_light .t_dark .t_light .t_green_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_green_surface3, :root.t_light .t_green_Button, :root.t_light .t_green_SliderTrackActive, :root.t_light .t_green_active, :root.t_light .t_green_active_SliderTrackActive, :root.t_light .t_green_surface3, .tm_xxt {--background:var(--c-green4Light);--backgroundHover:var(--c-green3Light);--backgroundPress:var(--c-green5Light);--backgroundFocus:var(--c-green5Light);--borderColor:var(--c-green7Light);--borderColorHover:var(--c-green6Light);--borderColorFocus:var(--c-green7Light);--borderColorPress:var(--c-green8Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_green_Button, .t_dark .t_light .t_green_SliderTrackActive, .t_dark .t_light .t_green_active, .t_dark .t_light .t_green_active_SliderTrackActive, .t_dark .t_light .t_green_surface3, .t_green_Button, .t_green_SliderTrackActive, .t_green_active, .t_green_active_SliderTrackActive, .t_green_surface3 {--background:var(--c-green4Light);--backgroundHover:var(--c-green3Light);--backgroundPress:var(--c-green5Light);--backgroundFocus:var(--c-green5Light);--borderColor:var(--c-green7Light);--borderColorHover:var(--c-green6Light);--borderColorFocus:var(--c-green7Light);--borderColorPress:var(--c-green8Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_green_Card, :root.t_dark .t_light .t_dark .t_light .t_green_Input, :root.t_dark .t_light .t_dark .t_light .t_green_ListItem, :root.t_dark .t_light .t_dark .t_light .t_green_Progress, :root.t_dark .t_light .t_dark .t_light .t_green_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_green_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_green_TextArea, :root.t_dark .t_light .t_dark .t_light .t_green_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_green_active_ListItem, :root.t_dark .t_light .t_dark .t_light .t_green_active_Progress, :root.t_dark .t_light .t_dark .t_light .t_green_active_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_green_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_green_surface1, :root.t_dark .t_light .t_green_Card, :root.t_dark .t_light .t_green_Input, :root.t_dark .t_light .t_green_ListItem, :root.t_dark .t_light .t_green_Progress, :root.t_dark .t_light .t_green_SelectTrigger, :root.t_dark .t_light .t_green_SliderTrack, :root.t_dark .t_light .t_green_TextArea, :root.t_dark .t_light .t_green_TooltipArrow, :root.t_dark .t_light .t_green_active_ListItem, :root.t_dark .t_light .t_green_active_Progress, :root.t_dark .t_light .t_green_active_SliderTrack, :root.t_dark .t_light .t_green_active_TooltipArrow, :root.t_dark .t_light .t_green_surface1, :root.t_light .t_dark .t_light .t_green_Card, :root.t_light .t_dark .t_light .t_green_Input, :root.t_light .t_dark .t_light .t_green_ListItem, :root.t_light .t_dark .t_light .t_green_Progress, :root.t_light .t_dark .t_light .t_green_SelectTrigger, :root.t_light .t_dark .t_light .t_green_SliderTrack, :root.t_light .t_dark .t_light .t_green_TextArea, :root.t_light .t_dark .t_light .t_green_TooltipArrow, :root.t_light .t_dark .t_light .t_green_active_ListItem, :root.t_light .t_dark .t_light .t_green_active_Progress, :root.t_light .t_dark .t_light .t_green_active_SliderTrack, :root.t_light .t_dark .t_light .t_green_active_TooltipArrow, :root.t_light .t_dark .t_light .t_green_surface1, :root.t_light .t_green_Card, :root.t_light .t_green_Input, :root.t_light .t_green_ListItem, :root.t_light .t_green_Progress, :root.t_light .t_green_SelectTrigger, :root.t_light .t_green_SliderTrack, :root.t_light .t_green_TextArea, :root.t_light .t_green_TooltipArrow, :root.t_light .t_green_active_ListItem, :root.t_light .t_green_active_Progress, :root.t_light .t_green_active_SliderTrack, :root.t_light .t_green_active_TooltipArrow, :root.t_light .t_green_surface1, .tm_xxt {--background:var(--c-green2Light);--backgroundHover:var(--c-green1Light);--backgroundPress:var(--c-green3Light);--backgroundFocus:var(--c-green3Light);--borderColor:var(--c-green5Light);--borderColorHover:var(--c-green4Light);--borderColorFocus:var(--c-green5Light);--borderColorPress:var(--c-green6Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_green_Card, .t_dark .t_light .t_green_Input, .t_dark .t_light .t_green_ListItem, .t_dark .t_light .t_green_Progress, .t_dark .t_light .t_green_SelectTrigger, .t_dark .t_light .t_green_SliderTrack, .t_dark .t_light .t_green_TextArea, .t_dark .t_light .t_green_TooltipArrow, .t_dark .t_light .t_green_active_ListItem, .t_dark .t_light .t_green_active_Progress, .t_dark .t_light .t_green_active_SliderTrack, .t_dark .t_light .t_green_active_TooltipArrow, .t_dark .t_light .t_green_surface1, .t_green_Card, .t_green_Input, .t_green_ListItem, .t_green_Progress, .t_green_SelectTrigger, .t_green_SliderTrack, .t_green_TextArea, .t_green_TooltipArrow, .t_green_active_ListItem, .t_green_active_Progress, .t_green_active_SliderTrack, .t_green_active_TooltipArrow, .t_green_surface1 {--background:var(--c-green2Light);--backgroundHover:var(--c-green1Light);--backgroundPress:var(--c-green3Light);--backgroundFocus:var(--c-green3Light);--borderColor:var(--c-green5Light);--borderColorHover:var(--c-green4Light);--borderColorFocus:var(--c-green5Light);--borderColorPress:var(--c-green6Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_green_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_green_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_green_Switch, :root.t_dark .t_light .t_dark .t_light .t_green_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_green_surface2, :root.t_dark .t_light .t_green_Checkbox, :root.t_dark .t_light .t_green_RadioGroupItem, :root.t_dark .t_light .t_green_Switch, :root.t_dark .t_light .t_green_TooltipContent, :root.t_dark .t_light .t_green_surface2, :root.t_light .t_dark .t_light .t_green_Checkbox, :root.t_light .t_dark .t_light .t_green_RadioGroupItem, :root.t_light .t_dark .t_light .t_green_Switch, :root.t_light .t_dark .t_light .t_green_TooltipContent, :root.t_light .t_dark .t_light .t_green_surface2, :root.t_light .t_green_Checkbox, :root.t_light .t_green_RadioGroupItem, :root.t_light .t_green_Switch, :root.t_light .t_green_TooltipContent, :root.t_light .t_green_surface2, .tm_xxt {--background:var(--c-green3Light);--backgroundHover:var(--c-green2Light);--backgroundPress:var(--c-green4Light);--backgroundFocus:var(--c-green4Light);--borderColor:var(--c-green6Light);--borderColorHover:var(--c-green5Light);--borderColorFocus:var(--c-green6Light);--borderColorPress:var(--c-green7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_green_Checkbox, .t_dark .t_light .t_green_RadioGroupItem, .t_dark .t_light .t_green_Switch, .t_dark .t_light .t_green_TooltipContent, .t_dark .t_light .t_green_surface2, .t_green_Checkbox, .t_green_RadioGroupItem, .t_green_Switch, .t_green_TooltipContent, .t_green_surface2 {--background:var(--c-green3Light);--backgroundHover:var(--c-green2Light);--backgroundPress:var(--c-green4Light);--backgroundFocus:var(--c-green4Light);--borderColor:var(--c-green6Light);--borderColorHover:var(--c-green5Light);--borderColorFocus:var(--c-green6Light);--borderColorPress:var(--c-green7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_green_active_Button, :root.t_dark .t_light .t_dark .t_light .t_green_active_Card, :root.t_dark .t_light .t_dark .t_light .t_green_active_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_green_active_Input, :root.t_dark .t_light .t_dark .t_light .t_green_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_green_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_green_active_Switch, :root.t_dark .t_light .t_dark .t_light .t_green_active_TextArea, :root.t_dark .t_light .t_dark .t_light .t_green_active_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_green_surface4, :root.t_dark .t_light .t_green_active_Button, :root.t_dark .t_light .t_green_active_Card, :root.t_dark .t_light .t_green_active_Checkbox, :root.t_dark .t_light .t_green_active_Input, :root.t_dark .t_light .t_green_active_RadioGroupItem, :root.t_dark .t_light .t_green_active_SelectTrigger, :root.t_dark .t_light .t_green_active_Switch, :root.t_dark .t_light .t_green_active_TextArea, :root.t_dark .t_light .t_green_active_TooltipContent, :root.t_dark .t_light .t_green_surface4, :root.t_light .t_dark .t_light .t_green_active_Button, :root.t_light .t_dark .t_light .t_green_active_Card, :root.t_light .t_dark .t_light .t_green_active_Checkbox, :root.t_light .t_dark .t_light .t_green_active_Input, :root.t_light .t_dark .t_light .t_green_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_green_active_SelectTrigger, :root.t_light .t_dark .t_light .t_green_active_Switch, :root.t_light .t_dark .t_light .t_green_active_TextArea, :root.t_light .t_dark .t_light .t_green_active_TooltipContent, :root.t_light .t_dark .t_light .t_green_surface4, :root.t_light .t_green_active_Button, :root.t_light .t_green_active_Card, :root.t_light .t_green_active_Checkbox, :root.t_light .t_green_active_Input, :root.t_light .t_green_active_RadioGroupItem, :root.t_light .t_green_active_SelectTrigger, :root.t_light .t_green_active_Switch, :root.t_light .t_green_active_TextArea, :root.t_light .t_green_active_TooltipContent, :root.t_light .t_green_surface4, .tm_xxt {--background:var(--c-green6Light);--backgroundHover:var(--c-green6Light);--backgroundPress:var(--c-green7Light);--backgroundFocus:var(--c-green7Light);--borderColor:var(--c-green6Light);--borderColorHover:var(--c-green6Light);--borderColorFocus:var(--c-green7Light);--borderColorPress:var(--c-green7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_green_active_Button, .t_dark .t_light .t_green_active_Card, .t_dark .t_light .t_green_active_Checkbox, .t_dark .t_light .t_green_active_Input, .t_dark .t_light .t_green_active_RadioGroupItem, .t_dark .t_light .t_green_active_SelectTrigger, .t_dark .t_light .t_green_active_Switch, .t_dark .t_light .t_green_active_TextArea, .t_dark .t_light .t_green_active_TooltipContent, .t_dark .t_light .t_green_surface4, .t_green_active_Button, .t_green_active_Card, .t_green_active_Checkbox, .t_green_active_Input, .t_green_active_RadioGroupItem, .t_green_active_SelectTrigger, .t_green_active_Switch, .t_green_active_TextArea, .t_green_active_TooltipContent, .t_green_surface4 {--background:var(--c-green6Light);--backgroundHover:var(--c-green6Light);--backgroundPress:var(--c-green7Light);--backgroundFocus:var(--c-green7Light);--borderColor:var(--c-green6Light);--borderColorHover:var(--c-green6Light);--borderColorFocus:var(--c-green7Light);--borderColorPress:var(--c-green7Light);}
  }
:root.t_dark .t_light .t_blue_alt1, :root.t_dark .t_light .t_dark .t_light .t_blue_alt1, :root.t_light .t_blue_alt1, :root.t_light .t_dark .t_light .t_blue_alt1, .tm_xxt {--color:var(--c-blue11Light);--colorHover:var(--c-blue10Light);--colorPress:var(--c-blue11Light);--colorFocus:var(--c-blue10Light);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_blue_alt1, .t_dark .t_light .t_blue_alt1 {--color:var(--c-blue11Light);--colorHover:var(--c-blue10Light);--colorPress:var(--c-blue11Light);--colorFocus:var(--c-blue10Light);}
  }
:root.t_dark .t_light .t_blue_alt2, :root.t_dark .t_light .t_dark .t_light .t_blue_alt2, :root.t_light .t_blue_alt2, :root.t_light .t_dark .t_light .t_blue_alt2, .tm_xxt {--color:var(--c-blue10Light);--colorHover:var(--c-blue9Dark);--colorPress:var(--c-blue10Light);--colorFocus:var(--c-blue9Dark);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_blue_alt2, .t_dark .t_light .t_blue_alt2 {--color:var(--c-blue10Light);--colorHover:var(--c-blue9Dark);--colorPress:var(--c-blue10Light);--colorFocus:var(--c-blue9Dark);}
  }
:root.t_dark .t_light .t_blue_Button, :root.t_dark .t_light .t_blue_SliderTrackActive, :root.t_dark .t_light .t_blue_active, :root.t_dark .t_light .t_blue_active_SliderTrackActive, :root.t_dark .t_light .t_blue_surface3, :root.t_dark .t_light .t_dark .t_light .t_blue_Button, :root.t_dark .t_light .t_dark .t_light .t_blue_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_blue_active, :root.t_dark .t_light .t_dark .t_light .t_blue_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_blue_surface3, :root.t_light .t_blue_Button, :root.t_light .t_blue_SliderTrackActive, :root.t_light .t_blue_active, :root.t_light .t_blue_active_SliderTrackActive, :root.t_light .t_blue_surface3, :root.t_light .t_dark .t_light .t_blue_Button, :root.t_light .t_dark .t_light .t_blue_SliderTrackActive, :root.t_light .t_dark .t_light .t_blue_active, :root.t_light .t_dark .t_light .t_blue_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_blue_surface3, .tm_xxt {--background:var(--c-blue4Light);--backgroundHover:var(--c-blue3Light);--backgroundPress:var(--c-blue5Light);--backgroundFocus:var(--c-blue5Light);--borderColor:var(--c-blue7Light);--borderColorHover:var(--c-blue6Light);--borderColorFocus:var(--c-blue7Light);--borderColorPress:var(--c-blue8Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_blue_Button, .t_blue_SliderTrackActive, .t_blue_active, .t_blue_active_SliderTrackActive, .t_blue_surface3, .t_dark .t_light .t_blue_Button, .t_dark .t_light .t_blue_SliderTrackActive, .t_dark .t_light .t_blue_active, .t_dark .t_light .t_blue_active_SliderTrackActive, .t_dark .t_light .t_blue_surface3 {--background:var(--c-blue4Light);--backgroundHover:var(--c-blue3Light);--backgroundPress:var(--c-blue5Light);--backgroundFocus:var(--c-blue5Light);--borderColor:var(--c-blue7Light);--borderColorHover:var(--c-blue6Light);--borderColorFocus:var(--c-blue7Light);--borderColorPress:var(--c-blue8Light);}
  }
:root.t_dark .t_light .t_blue_Card, :root.t_dark .t_light .t_blue_Input, :root.t_dark .t_light .t_blue_ListItem, :root.t_dark .t_light .t_blue_Progress, :root.t_dark .t_light .t_blue_SelectTrigger, :root.t_dark .t_light .t_blue_SliderTrack, :root.t_dark .t_light .t_blue_TextArea, :root.t_dark .t_light .t_blue_TooltipArrow, :root.t_dark .t_light .t_blue_active_ListItem, :root.t_dark .t_light .t_blue_active_Progress, :root.t_dark .t_light .t_blue_active_SliderTrack, :root.t_dark .t_light .t_blue_active_TooltipArrow, :root.t_dark .t_light .t_blue_surface1, :root.t_dark .t_light .t_dark .t_light .t_blue_Card, :root.t_dark .t_light .t_dark .t_light .t_blue_Input, :root.t_dark .t_light .t_dark .t_light .t_blue_ListItem, :root.t_dark .t_light .t_dark .t_light .t_blue_Progress, :root.t_dark .t_light .t_dark .t_light .t_blue_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_blue_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_blue_TextArea, :root.t_dark .t_light .t_dark .t_light .t_blue_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_blue_active_ListItem, :root.t_dark .t_light .t_dark .t_light .t_blue_active_Progress, :root.t_dark .t_light .t_dark .t_light .t_blue_active_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_blue_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_blue_surface1, :root.t_light .t_blue_Card, :root.t_light .t_blue_Input, :root.t_light .t_blue_ListItem, :root.t_light .t_blue_Progress, :root.t_light .t_blue_SelectTrigger, :root.t_light .t_blue_SliderTrack, :root.t_light .t_blue_TextArea, :root.t_light .t_blue_TooltipArrow, :root.t_light .t_blue_active_ListItem, :root.t_light .t_blue_active_Progress, :root.t_light .t_blue_active_SliderTrack, :root.t_light .t_blue_active_TooltipArrow, :root.t_light .t_blue_surface1, :root.t_light .t_dark .t_light .t_blue_Card, :root.t_light .t_dark .t_light .t_blue_Input, :root.t_light .t_dark .t_light .t_blue_ListItem, :root.t_light .t_dark .t_light .t_blue_Progress, :root.t_light .t_dark .t_light .t_blue_SelectTrigger, :root.t_light .t_dark .t_light .t_blue_SliderTrack, :root.t_light .t_dark .t_light .t_blue_TextArea, :root.t_light .t_dark .t_light .t_blue_TooltipArrow, :root.t_light .t_dark .t_light .t_blue_active_ListItem, :root.t_light .t_dark .t_light .t_blue_active_Progress, :root.t_light .t_dark .t_light .t_blue_active_SliderTrack, :root.t_light .t_dark .t_light .t_blue_active_TooltipArrow, :root.t_light .t_dark .t_light .t_blue_surface1, .tm_xxt {--background:var(--c-blue2Light);--backgroundHover:var(--c-blue1Light);--backgroundPress:var(--c-blue3Light);--backgroundFocus:var(--c-blue3Light);--borderColor:var(--c-blue5Light);--borderColorHover:var(--c-blue4Light);--borderColorFocus:var(--c-blue5Light);--borderColorPress:var(--c-blue6Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_blue_Card, .t_blue_Input, .t_blue_ListItem, .t_blue_Progress, .t_blue_SelectTrigger, .t_blue_SliderTrack, .t_blue_TextArea, .t_blue_TooltipArrow, .t_blue_active_ListItem, .t_blue_active_Progress, .t_blue_active_SliderTrack, .t_blue_active_TooltipArrow, .t_blue_surface1, .t_dark .t_light .t_blue_Card, .t_dark .t_light .t_blue_Input, .t_dark .t_light .t_blue_ListItem, .t_dark .t_light .t_blue_Progress, .t_dark .t_light .t_blue_SelectTrigger, .t_dark .t_light .t_blue_SliderTrack, .t_dark .t_light .t_blue_TextArea, .t_dark .t_light .t_blue_TooltipArrow, .t_dark .t_light .t_blue_active_ListItem, .t_dark .t_light .t_blue_active_Progress, .t_dark .t_light .t_blue_active_SliderTrack, .t_dark .t_light .t_blue_active_TooltipArrow, .t_dark .t_light .t_blue_surface1 {--background:var(--c-blue2Light);--backgroundHover:var(--c-blue1Light);--backgroundPress:var(--c-blue3Light);--backgroundFocus:var(--c-blue3Light);--borderColor:var(--c-blue5Light);--borderColorHover:var(--c-blue4Light);--borderColorFocus:var(--c-blue5Light);--borderColorPress:var(--c-blue6Light);}
  }
:root.t_dark .t_light .t_blue_Checkbox, :root.t_dark .t_light .t_blue_RadioGroupItem, :root.t_dark .t_light .t_blue_Switch, :root.t_dark .t_light .t_blue_TooltipContent, :root.t_dark .t_light .t_blue_surface2, :root.t_dark .t_light .t_dark .t_light .t_blue_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_blue_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_blue_Switch, :root.t_dark .t_light .t_dark .t_light .t_blue_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_blue_surface2, :root.t_light .t_blue_Checkbox, :root.t_light .t_blue_RadioGroupItem, :root.t_light .t_blue_Switch, :root.t_light .t_blue_TooltipContent, :root.t_light .t_blue_surface2, :root.t_light .t_dark .t_light .t_blue_Checkbox, :root.t_light .t_dark .t_light .t_blue_RadioGroupItem, :root.t_light .t_dark .t_light .t_blue_Switch, :root.t_light .t_dark .t_light .t_blue_TooltipContent, :root.t_light .t_dark .t_light .t_blue_surface2, .tm_xxt {--background:var(--c-blue3Light);--backgroundHover:var(--c-blue2Light);--backgroundPress:var(--c-blue4Light);--backgroundFocus:var(--c-blue4Light);--borderColor:var(--c-blue6Light);--borderColorHover:var(--c-blue5Light);--borderColorFocus:var(--c-blue6Light);--borderColorPress:var(--c-blue7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_blue_Checkbox, .t_blue_RadioGroupItem, .t_blue_Switch, .t_blue_TooltipContent, .t_blue_surface2, .t_dark .t_light .t_blue_Checkbox, .t_dark .t_light .t_blue_RadioGroupItem, .t_dark .t_light .t_blue_Switch, .t_dark .t_light .t_blue_TooltipContent, .t_dark .t_light .t_blue_surface2 {--background:var(--c-blue3Light);--backgroundHover:var(--c-blue2Light);--backgroundPress:var(--c-blue4Light);--backgroundFocus:var(--c-blue4Light);--borderColor:var(--c-blue6Light);--borderColorHover:var(--c-blue5Light);--borderColorFocus:var(--c-blue6Light);--borderColorPress:var(--c-blue7Light);}
  }
:root.t_dark .t_light .t_blue_active_Button, :root.t_dark .t_light .t_blue_active_Card, :root.t_dark .t_light .t_blue_active_Checkbox, :root.t_dark .t_light .t_blue_active_Input, :root.t_dark .t_light .t_blue_active_RadioGroupItem, :root.t_dark .t_light .t_blue_active_SelectTrigger, :root.t_dark .t_light .t_blue_active_Switch, :root.t_dark .t_light .t_blue_active_TextArea, :root.t_dark .t_light .t_blue_active_TooltipContent, :root.t_dark .t_light .t_blue_surface4, :root.t_dark .t_light .t_dark .t_light .t_blue_active_Button, :root.t_dark .t_light .t_dark .t_light .t_blue_active_Card, :root.t_dark .t_light .t_dark .t_light .t_blue_active_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_blue_active_Input, :root.t_dark .t_light .t_dark .t_light .t_blue_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_blue_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_blue_active_Switch, :root.t_dark .t_light .t_dark .t_light .t_blue_active_TextArea, :root.t_dark .t_light .t_dark .t_light .t_blue_active_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_blue_surface4, :root.t_light .t_blue_active_Button, :root.t_light .t_blue_active_Card, :root.t_light .t_blue_active_Checkbox, :root.t_light .t_blue_active_Input, :root.t_light .t_blue_active_RadioGroupItem, :root.t_light .t_blue_active_SelectTrigger, :root.t_light .t_blue_active_Switch, :root.t_light .t_blue_active_TextArea, :root.t_light .t_blue_active_TooltipContent, :root.t_light .t_blue_surface4, :root.t_light .t_dark .t_light .t_blue_active_Button, :root.t_light .t_dark .t_light .t_blue_active_Card, :root.t_light .t_dark .t_light .t_blue_active_Checkbox, :root.t_light .t_dark .t_light .t_blue_active_Input, :root.t_light .t_dark .t_light .t_blue_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_blue_active_SelectTrigger, :root.t_light .t_dark .t_light .t_blue_active_Switch, :root.t_light .t_dark .t_light .t_blue_active_TextArea, :root.t_light .t_dark .t_light .t_blue_active_TooltipContent, :root.t_light .t_dark .t_light .t_blue_surface4, .tm_xxt {--background:var(--c-blue6Light);--backgroundHover:var(--c-blue6Light);--backgroundPress:var(--c-blue7Light);--backgroundFocus:var(--c-blue7Light);--borderColor:var(--c-blue6Light);--borderColorHover:var(--c-blue6Light);--borderColorFocus:var(--c-blue7Light);--borderColorPress:var(--c-blue7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_blue_active_Button, .t_blue_active_Card, .t_blue_active_Checkbox, .t_blue_active_Input, .t_blue_active_RadioGroupItem, .t_blue_active_SelectTrigger, .t_blue_active_Switch, .t_blue_active_TextArea, .t_blue_active_TooltipContent, .t_blue_surface4, .t_dark .t_light .t_blue_active_Button, .t_dark .t_light .t_blue_active_Card, .t_dark .t_light .t_blue_active_Checkbox, .t_dark .t_light .t_blue_active_Input, .t_dark .t_light .t_blue_active_RadioGroupItem, .t_dark .t_light .t_blue_active_SelectTrigger, .t_dark .t_light .t_blue_active_Switch, .t_dark .t_light .t_blue_active_TextArea, .t_dark .t_light .t_blue_active_TooltipContent, .t_dark .t_light .t_blue_surface4 {--background:var(--c-blue6Light);--backgroundHover:var(--c-blue6Light);--backgroundPress:var(--c-blue7Light);--backgroundFocus:var(--c-blue7Light);--borderColor:var(--c-blue6Light);--borderColorHover:var(--c-blue6Light);--borderColorFocus:var(--c-blue7Light);--borderColorPress:var(--c-blue7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_purple_alt1, :root.t_dark .t_light .t_purple_alt1, :root.t_light .t_dark .t_light .t_purple_alt1, :root.t_light .t_purple_alt1, .tm_xxt {--color:var(--c-purple11Light);--colorHover:var(--c-purple10Light);--colorPress:var(--c-purple11Light);--colorFocus:var(--c-purple10Light);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_purple_alt1, .t_purple_alt1 {--color:var(--c-purple11Light);--colorHover:var(--c-purple10Light);--colorPress:var(--c-purple11Light);--colorFocus:var(--c-purple10Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_purple_alt2, :root.t_dark .t_light .t_purple_alt2, :root.t_light .t_dark .t_light .t_purple_alt2, :root.t_light .t_purple_alt2, .tm_xxt {--color:var(--c-purple10Light);--colorHover:var(--c-purple9Dark);--colorPress:var(--c-purple10Light);--colorFocus:var(--c-purple9Dark);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_purple_alt2, .t_purple_alt2 {--color:var(--c-purple10Light);--colorHover:var(--c-purple9Dark);--colorPress:var(--c-purple10Light);--colorFocus:var(--c-purple9Dark);}
  }
:root.t_dark .t_light .t_dark .t_light .t_purple_Button, :root.t_dark .t_light .t_dark .t_light .t_purple_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_purple_active, :root.t_dark .t_light .t_dark .t_light .t_purple_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_purple_surface3, :root.t_dark .t_light .t_purple_Button, :root.t_dark .t_light .t_purple_SliderTrackActive, :root.t_dark .t_light .t_purple_active, :root.t_dark .t_light .t_purple_active_SliderTrackActive, :root.t_dark .t_light .t_purple_surface3, :root.t_light .t_dark .t_light .t_purple_Button, :root.t_light .t_dark .t_light .t_purple_SliderTrackActive, :root.t_light .t_dark .t_light .t_purple_active, :root.t_light .t_dark .t_light .t_purple_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_purple_surface3, :root.t_light .t_purple_Button, :root.t_light .t_purple_SliderTrackActive, :root.t_light .t_purple_active, :root.t_light .t_purple_active_SliderTrackActive, :root.t_light .t_purple_surface3, .tm_xxt {--background:var(--c-purple4Light);--backgroundHover:var(--c-purple3Light);--backgroundPress:var(--c-purple5Light);--backgroundFocus:var(--c-purple5Light);--borderColor:var(--c-purple7Light);--borderColorHover:var(--c-purple6Light);--borderColorFocus:var(--c-purple7Light);--borderColorPress:var(--c-purple8Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_purple_Button, .t_dark .t_light .t_purple_SliderTrackActive, .t_dark .t_light .t_purple_active, .t_dark .t_light .t_purple_active_SliderTrackActive, .t_dark .t_light .t_purple_surface3, .t_purple_Button, .t_purple_SliderTrackActive, .t_purple_active, .t_purple_active_SliderTrackActive, .t_purple_surface3 {--background:var(--c-purple4Light);--backgroundHover:var(--c-purple3Light);--backgroundPress:var(--c-purple5Light);--backgroundFocus:var(--c-purple5Light);--borderColor:var(--c-purple7Light);--borderColorHover:var(--c-purple6Light);--borderColorFocus:var(--c-purple7Light);--borderColorPress:var(--c-purple8Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_purple_Card, :root.t_dark .t_light .t_dark .t_light .t_purple_Input, :root.t_dark .t_light .t_dark .t_light .t_purple_ListItem, :root.t_dark .t_light .t_dark .t_light .t_purple_Progress, :root.t_dark .t_light .t_dark .t_light .t_purple_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_purple_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_purple_TextArea, :root.t_dark .t_light .t_dark .t_light .t_purple_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_purple_active_ListItem, :root.t_dark .t_light .t_dark .t_light .t_purple_active_Progress, :root.t_dark .t_light .t_dark .t_light .t_purple_active_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_purple_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_purple_surface1, :root.t_dark .t_light .t_purple_Card, :root.t_dark .t_light .t_purple_Input, :root.t_dark .t_light .t_purple_ListItem, :root.t_dark .t_light .t_purple_Progress, :root.t_dark .t_light .t_purple_SelectTrigger, :root.t_dark .t_light .t_purple_SliderTrack, :root.t_dark .t_light .t_purple_TextArea, :root.t_dark .t_light .t_purple_TooltipArrow, :root.t_dark .t_light .t_purple_active_ListItem, :root.t_dark .t_light .t_purple_active_Progress, :root.t_dark .t_light .t_purple_active_SliderTrack, :root.t_dark .t_light .t_purple_active_TooltipArrow, :root.t_dark .t_light .t_purple_surface1, :root.t_light .t_dark .t_light .t_purple_Card, :root.t_light .t_dark .t_light .t_purple_Input, :root.t_light .t_dark .t_light .t_purple_ListItem, :root.t_light .t_dark .t_light .t_purple_Progress, :root.t_light .t_dark .t_light .t_purple_SelectTrigger, :root.t_light .t_dark .t_light .t_purple_SliderTrack, :root.t_light .t_dark .t_light .t_purple_TextArea, :root.t_light .t_dark .t_light .t_purple_TooltipArrow, :root.t_light .t_dark .t_light .t_purple_active_ListItem, :root.t_light .t_dark .t_light .t_purple_active_Progress, :root.t_light .t_dark .t_light .t_purple_active_SliderTrack, :root.t_light .t_dark .t_light .t_purple_active_TooltipArrow, :root.t_light .t_dark .t_light .t_purple_surface1, :root.t_light .t_purple_Card, :root.t_light .t_purple_Input, :root.t_light .t_purple_ListItem, :root.t_light .t_purple_Progress, :root.t_light .t_purple_SelectTrigger, :root.t_light .t_purple_SliderTrack, :root.t_light .t_purple_TextArea, :root.t_light .t_purple_TooltipArrow, :root.t_light .t_purple_active_ListItem, :root.t_light .t_purple_active_Progress, :root.t_light .t_purple_active_SliderTrack, :root.t_light .t_purple_active_TooltipArrow, :root.t_light .t_purple_surface1, .tm_xxt {--background:var(--c-purple2Light);--backgroundHover:var(--c-purple1Light);--backgroundPress:var(--c-purple3Light);--backgroundFocus:var(--c-purple3Light);--borderColor:var(--c-purple5Light);--borderColorHover:var(--c-purple4Light);--borderColorFocus:var(--c-purple5Light);--borderColorPress:var(--c-purple6Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_purple_Card, .t_dark .t_light .t_purple_Input, .t_dark .t_light .t_purple_ListItem, .t_dark .t_light .t_purple_Progress, .t_dark .t_light .t_purple_SelectTrigger, .t_dark .t_light .t_purple_SliderTrack, .t_dark .t_light .t_purple_TextArea, .t_dark .t_light .t_purple_TooltipArrow, .t_dark .t_light .t_purple_active_ListItem, .t_dark .t_light .t_purple_active_Progress, .t_dark .t_light .t_purple_active_SliderTrack, .t_dark .t_light .t_purple_active_TooltipArrow, .t_dark .t_light .t_purple_surface1, .t_purple_Card, .t_purple_Input, .t_purple_ListItem, .t_purple_Progress, .t_purple_SelectTrigger, .t_purple_SliderTrack, .t_purple_TextArea, .t_purple_TooltipArrow, .t_purple_active_ListItem, .t_purple_active_Progress, .t_purple_active_SliderTrack, .t_purple_active_TooltipArrow, .t_purple_surface1 {--background:var(--c-purple2Light);--backgroundHover:var(--c-purple1Light);--backgroundPress:var(--c-purple3Light);--backgroundFocus:var(--c-purple3Light);--borderColor:var(--c-purple5Light);--borderColorHover:var(--c-purple4Light);--borderColorFocus:var(--c-purple5Light);--borderColorPress:var(--c-purple6Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_purple_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_purple_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_purple_Switch, :root.t_dark .t_light .t_dark .t_light .t_purple_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_purple_surface2, :root.t_dark .t_light .t_purple_Checkbox, :root.t_dark .t_light .t_purple_RadioGroupItem, :root.t_dark .t_light .t_purple_Switch, :root.t_dark .t_light .t_purple_TooltipContent, :root.t_dark .t_light .t_purple_surface2, :root.t_light .t_dark .t_light .t_purple_Checkbox, :root.t_light .t_dark .t_light .t_purple_RadioGroupItem, :root.t_light .t_dark .t_light .t_purple_Switch, :root.t_light .t_dark .t_light .t_purple_TooltipContent, :root.t_light .t_dark .t_light .t_purple_surface2, :root.t_light .t_purple_Checkbox, :root.t_light .t_purple_RadioGroupItem, :root.t_light .t_purple_Switch, :root.t_light .t_purple_TooltipContent, :root.t_light .t_purple_surface2, .tm_xxt {--background:var(--c-purple3Light);--backgroundHover:var(--c-purple2Light);--backgroundPress:var(--c-purple4Light);--backgroundFocus:var(--c-purple4Light);--borderColor:var(--c-purple6Light);--borderColorHover:var(--c-purple5Light);--borderColorFocus:var(--c-purple6Light);--borderColorPress:var(--c-purple7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_purple_Checkbox, .t_dark .t_light .t_purple_RadioGroupItem, .t_dark .t_light .t_purple_Switch, .t_dark .t_light .t_purple_TooltipContent, .t_dark .t_light .t_purple_surface2, .t_purple_Checkbox, .t_purple_RadioGroupItem, .t_purple_Switch, .t_purple_TooltipContent, .t_purple_surface2 {--background:var(--c-purple3Light);--backgroundHover:var(--c-purple2Light);--backgroundPress:var(--c-purple4Light);--backgroundFocus:var(--c-purple4Light);--borderColor:var(--c-purple6Light);--borderColorHover:var(--c-purple5Light);--borderColorFocus:var(--c-purple6Light);--borderColorPress:var(--c-purple7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_purple_active_Button, :root.t_dark .t_light .t_dark .t_light .t_purple_active_Card, :root.t_dark .t_light .t_dark .t_light .t_purple_active_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_purple_active_Input, :root.t_dark .t_light .t_dark .t_light .t_purple_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_purple_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_purple_active_Switch, :root.t_dark .t_light .t_dark .t_light .t_purple_active_TextArea, :root.t_dark .t_light .t_dark .t_light .t_purple_active_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_purple_surface4, :root.t_dark .t_light .t_purple_active_Button, :root.t_dark .t_light .t_purple_active_Card, :root.t_dark .t_light .t_purple_active_Checkbox, :root.t_dark .t_light .t_purple_active_Input, :root.t_dark .t_light .t_purple_active_RadioGroupItem, :root.t_dark .t_light .t_purple_active_SelectTrigger, :root.t_dark .t_light .t_purple_active_Switch, :root.t_dark .t_light .t_purple_active_TextArea, :root.t_dark .t_light .t_purple_active_TooltipContent, :root.t_dark .t_light .t_purple_surface4, :root.t_light .t_dark .t_light .t_purple_active_Button, :root.t_light .t_dark .t_light .t_purple_active_Card, :root.t_light .t_dark .t_light .t_purple_active_Checkbox, :root.t_light .t_dark .t_light .t_purple_active_Input, :root.t_light .t_dark .t_light .t_purple_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_purple_active_SelectTrigger, :root.t_light .t_dark .t_light .t_purple_active_Switch, :root.t_light .t_dark .t_light .t_purple_active_TextArea, :root.t_light .t_dark .t_light .t_purple_active_TooltipContent, :root.t_light .t_dark .t_light .t_purple_surface4, :root.t_light .t_purple_active_Button, :root.t_light .t_purple_active_Card, :root.t_light .t_purple_active_Checkbox, :root.t_light .t_purple_active_Input, :root.t_light .t_purple_active_RadioGroupItem, :root.t_light .t_purple_active_SelectTrigger, :root.t_light .t_purple_active_Switch, :root.t_light .t_purple_active_TextArea, :root.t_light .t_purple_active_TooltipContent, :root.t_light .t_purple_surface4, .tm_xxt {--background:var(--c-purple6Light);--backgroundHover:var(--c-purple6Light);--backgroundPress:var(--c-purple7Light);--backgroundFocus:var(--c-purple7Light);--borderColor:var(--c-purple6Light);--borderColorHover:var(--c-purple6Light);--borderColorFocus:var(--c-purple7Light);--borderColorPress:var(--c-purple7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_purple_active_Button, .t_dark .t_light .t_purple_active_Card, .t_dark .t_light .t_purple_active_Checkbox, .t_dark .t_light .t_purple_active_Input, .t_dark .t_light .t_purple_active_RadioGroupItem, .t_dark .t_light .t_purple_active_SelectTrigger, .t_dark .t_light .t_purple_active_Switch, .t_dark .t_light .t_purple_active_TextArea, .t_dark .t_light .t_purple_active_TooltipContent, .t_dark .t_light .t_purple_surface4, .t_purple_active_Button, .t_purple_active_Card, .t_purple_active_Checkbox, .t_purple_active_Input, .t_purple_active_RadioGroupItem, .t_purple_active_SelectTrigger, .t_purple_active_Switch, .t_purple_active_TextArea, .t_purple_active_TooltipContent, .t_purple_surface4 {--background:var(--c-purple6Light);--backgroundHover:var(--c-purple6Light);--backgroundPress:var(--c-purple7Light);--backgroundFocus:var(--c-purple7Light);--borderColor:var(--c-purple6Light);--borderColorHover:var(--c-purple6Light);--borderColorFocus:var(--c-purple7Light);--borderColorPress:var(--c-purple7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_pink_alt1, :root.t_dark .t_light .t_pink_alt1, :root.t_light .t_dark .t_light .t_pink_alt1, :root.t_light .t_pink_alt1, .tm_xxt {--color:var(--c-pink11Light);--colorHover:var(--c-pink10Light);--colorPress:var(--c-pink11Light);--colorFocus:var(--c-pink10Light);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_pink_alt1, .t_pink_alt1 {--color:var(--c-pink11Light);--colorHover:var(--c-pink10Light);--colorPress:var(--c-pink11Light);--colorFocus:var(--c-pink10Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_pink_alt2, :root.t_dark .t_light .t_pink_alt2, :root.t_light .t_dark .t_light .t_pink_alt2, :root.t_light .t_pink_alt2, .tm_xxt {--color:var(--c-pink10Light);--colorHover:var(--c-pink9Dark);--colorPress:var(--c-pink10Light);--colorFocus:var(--c-pink9Dark);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_pink_alt2, .t_pink_alt2 {--color:var(--c-pink10Light);--colorHover:var(--c-pink9Dark);--colorPress:var(--c-pink10Light);--colorFocus:var(--c-pink9Dark);}
  }
:root.t_dark .t_light .t_dark .t_light .t_pink_Button, :root.t_dark .t_light .t_dark .t_light .t_pink_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_pink_active, :root.t_dark .t_light .t_dark .t_light .t_pink_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_pink_surface3, :root.t_dark .t_light .t_pink_Button, :root.t_dark .t_light .t_pink_SliderTrackActive, :root.t_dark .t_light .t_pink_active, :root.t_dark .t_light .t_pink_active_SliderTrackActive, :root.t_dark .t_light .t_pink_surface3, :root.t_light .t_dark .t_light .t_pink_Button, :root.t_light .t_dark .t_light .t_pink_SliderTrackActive, :root.t_light .t_dark .t_light .t_pink_active, :root.t_light .t_dark .t_light .t_pink_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_pink_surface3, :root.t_light .t_pink_Button, :root.t_light .t_pink_SliderTrackActive, :root.t_light .t_pink_active, :root.t_light .t_pink_active_SliderTrackActive, :root.t_light .t_pink_surface3, .tm_xxt {--background:var(--c-pink4Light);--backgroundHover:var(--c-pink3Light);--backgroundPress:var(--c-pink5Light);--backgroundFocus:var(--c-pink5Light);--borderColor:var(--c-pink7Light);--borderColorHover:var(--c-pink6Light);--borderColorFocus:var(--c-pink7Light);--borderColorPress:var(--c-pink8Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_pink_Button, .t_dark .t_light .t_pink_SliderTrackActive, .t_dark .t_light .t_pink_active, .t_dark .t_light .t_pink_active_SliderTrackActive, .t_dark .t_light .t_pink_surface3, .t_pink_Button, .t_pink_SliderTrackActive, .t_pink_active, .t_pink_active_SliderTrackActive, .t_pink_surface3 {--background:var(--c-pink4Light);--backgroundHover:var(--c-pink3Light);--backgroundPress:var(--c-pink5Light);--backgroundFocus:var(--c-pink5Light);--borderColor:var(--c-pink7Light);--borderColorHover:var(--c-pink6Light);--borderColorFocus:var(--c-pink7Light);--borderColorPress:var(--c-pink8Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_pink_Card, :root.t_dark .t_light .t_dark .t_light .t_pink_Input, :root.t_dark .t_light .t_dark .t_light .t_pink_ListItem, :root.t_dark .t_light .t_dark .t_light .t_pink_Progress, :root.t_dark .t_light .t_dark .t_light .t_pink_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_pink_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_pink_TextArea, :root.t_dark .t_light .t_dark .t_light .t_pink_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_pink_active_ListItem, :root.t_dark .t_light .t_dark .t_light .t_pink_active_Progress, :root.t_dark .t_light .t_dark .t_light .t_pink_active_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_pink_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_pink_surface1, :root.t_dark .t_light .t_pink_Card, :root.t_dark .t_light .t_pink_Input, :root.t_dark .t_light .t_pink_ListItem, :root.t_dark .t_light .t_pink_Progress, :root.t_dark .t_light .t_pink_SelectTrigger, :root.t_dark .t_light .t_pink_SliderTrack, :root.t_dark .t_light .t_pink_TextArea, :root.t_dark .t_light .t_pink_TooltipArrow, :root.t_dark .t_light .t_pink_active_ListItem, :root.t_dark .t_light .t_pink_active_Progress, :root.t_dark .t_light .t_pink_active_SliderTrack, :root.t_dark .t_light .t_pink_active_TooltipArrow, :root.t_dark .t_light .t_pink_surface1, :root.t_light .t_dark .t_light .t_pink_Card, :root.t_light .t_dark .t_light .t_pink_Input, :root.t_light .t_dark .t_light .t_pink_ListItem, :root.t_light .t_dark .t_light .t_pink_Progress, :root.t_light .t_dark .t_light .t_pink_SelectTrigger, :root.t_light .t_dark .t_light .t_pink_SliderTrack, :root.t_light .t_dark .t_light .t_pink_TextArea, :root.t_light .t_dark .t_light .t_pink_TooltipArrow, :root.t_light .t_dark .t_light .t_pink_active_ListItem, :root.t_light .t_dark .t_light .t_pink_active_Progress, :root.t_light .t_dark .t_light .t_pink_active_SliderTrack, :root.t_light .t_dark .t_light .t_pink_active_TooltipArrow, :root.t_light .t_dark .t_light .t_pink_surface1, :root.t_light .t_pink_Card, :root.t_light .t_pink_Input, :root.t_light .t_pink_ListItem, :root.t_light .t_pink_Progress, :root.t_light .t_pink_SelectTrigger, :root.t_light .t_pink_SliderTrack, :root.t_light .t_pink_TextArea, :root.t_light .t_pink_TooltipArrow, :root.t_light .t_pink_active_ListItem, :root.t_light .t_pink_active_Progress, :root.t_light .t_pink_active_SliderTrack, :root.t_light .t_pink_active_TooltipArrow, :root.t_light .t_pink_surface1, .tm_xxt {--background:var(--c-pink2Light);--backgroundHover:var(--c-pink1Light);--backgroundPress:var(--c-pink3Light);--backgroundFocus:var(--c-pink3Light);--borderColor:var(--c-pink5Light);--borderColorHover:var(--c-pink4Light);--borderColorFocus:var(--c-pink5Light);--borderColorPress:var(--c-pink6Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_pink_Card, .t_dark .t_light .t_pink_Input, .t_dark .t_light .t_pink_ListItem, .t_dark .t_light .t_pink_Progress, .t_dark .t_light .t_pink_SelectTrigger, .t_dark .t_light .t_pink_SliderTrack, .t_dark .t_light .t_pink_TextArea, .t_dark .t_light .t_pink_TooltipArrow, .t_dark .t_light .t_pink_active_ListItem, .t_dark .t_light .t_pink_active_Progress, .t_dark .t_light .t_pink_active_SliderTrack, .t_dark .t_light .t_pink_active_TooltipArrow, .t_dark .t_light .t_pink_surface1, .t_pink_Card, .t_pink_Input, .t_pink_ListItem, .t_pink_Progress, .t_pink_SelectTrigger, .t_pink_SliderTrack, .t_pink_TextArea, .t_pink_TooltipArrow, .t_pink_active_ListItem, .t_pink_active_Progress, .t_pink_active_SliderTrack, .t_pink_active_TooltipArrow, .t_pink_surface1 {--background:var(--c-pink2Light);--backgroundHover:var(--c-pink1Light);--backgroundPress:var(--c-pink3Light);--backgroundFocus:var(--c-pink3Light);--borderColor:var(--c-pink5Light);--borderColorHover:var(--c-pink4Light);--borderColorFocus:var(--c-pink5Light);--borderColorPress:var(--c-pink6Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_pink_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_pink_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_pink_Switch, :root.t_dark .t_light .t_dark .t_light .t_pink_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_pink_surface2, :root.t_dark .t_light .t_pink_Checkbox, :root.t_dark .t_light .t_pink_RadioGroupItem, :root.t_dark .t_light .t_pink_Switch, :root.t_dark .t_light .t_pink_TooltipContent, :root.t_dark .t_light .t_pink_surface2, :root.t_light .t_dark .t_light .t_pink_Checkbox, :root.t_light .t_dark .t_light .t_pink_RadioGroupItem, :root.t_light .t_dark .t_light .t_pink_Switch, :root.t_light .t_dark .t_light .t_pink_TooltipContent, :root.t_light .t_dark .t_light .t_pink_surface2, :root.t_light .t_pink_Checkbox, :root.t_light .t_pink_RadioGroupItem, :root.t_light .t_pink_Switch, :root.t_light .t_pink_TooltipContent, :root.t_light .t_pink_surface2, .tm_xxt {--background:var(--c-pink3Light);--backgroundHover:var(--c-pink2Light);--backgroundPress:var(--c-pink4Light);--backgroundFocus:var(--c-pink4Light);--borderColor:var(--c-pink6Light);--borderColorHover:var(--c-pink5Light);--borderColorFocus:var(--c-pink6Light);--borderColorPress:var(--c-pink7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_pink_Checkbox, .t_dark .t_light .t_pink_RadioGroupItem, .t_dark .t_light .t_pink_Switch, .t_dark .t_light .t_pink_TooltipContent, .t_dark .t_light .t_pink_surface2, .t_pink_Checkbox, .t_pink_RadioGroupItem, .t_pink_Switch, .t_pink_TooltipContent, .t_pink_surface2 {--background:var(--c-pink3Light);--backgroundHover:var(--c-pink2Light);--backgroundPress:var(--c-pink4Light);--backgroundFocus:var(--c-pink4Light);--borderColor:var(--c-pink6Light);--borderColorHover:var(--c-pink5Light);--borderColorFocus:var(--c-pink6Light);--borderColorPress:var(--c-pink7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_pink_active_Button, :root.t_dark .t_light .t_dark .t_light .t_pink_active_Card, :root.t_dark .t_light .t_dark .t_light .t_pink_active_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_pink_active_Input, :root.t_dark .t_light .t_dark .t_light .t_pink_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_pink_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_pink_active_Switch, :root.t_dark .t_light .t_dark .t_light .t_pink_active_TextArea, :root.t_dark .t_light .t_dark .t_light .t_pink_active_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_pink_surface4, :root.t_dark .t_light .t_pink_active_Button, :root.t_dark .t_light .t_pink_active_Card, :root.t_dark .t_light .t_pink_active_Checkbox, :root.t_dark .t_light .t_pink_active_Input, :root.t_dark .t_light .t_pink_active_RadioGroupItem, :root.t_dark .t_light .t_pink_active_SelectTrigger, :root.t_dark .t_light .t_pink_active_Switch, :root.t_dark .t_light .t_pink_active_TextArea, :root.t_dark .t_light .t_pink_active_TooltipContent, :root.t_dark .t_light .t_pink_surface4, :root.t_light .t_dark .t_light .t_pink_active_Button, :root.t_light .t_dark .t_light .t_pink_active_Card, :root.t_light .t_dark .t_light .t_pink_active_Checkbox, :root.t_light .t_dark .t_light .t_pink_active_Input, :root.t_light .t_dark .t_light .t_pink_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_pink_active_SelectTrigger, :root.t_light .t_dark .t_light .t_pink_active_Switch, :root.t_light .t_dark .t_light .t_pink_active_TextArea, :root.t_light .t_dark .t_light .t_pink_active_TooltipContent, :root.t_light .t_dark .t_light .t_pink_surface4, :root.t_light .t_pink_active_Button, :root.t_light .t_pink_active_Card, :root.t_light .t_pink_active_Checkbox, :root.t_light .t_pink_active_Input, :root.t_light .t_pink_active_RadioGroupItem, :root.t_light .t_pink_active_SelectTrigger, :root.t_light .t_pink_active_Switch, :root.t_light .t_pink_active_TextArea, :root.t_light .t_pink_active_TooltipContent, :root.t_light .t_pink_surface4, .tm_xxt {--background:var(--c-pink6Light);--backgroundHover:var(--c-pink6Light);--backgroundPress:var(--c-pink7Light);--backgroundFocus:var(--c-pink7Light);--borderColor:var(--c-pink6Light);--borderColorHover:var(--c-pink6Light);--borderColorFocus:var(--c-pink7Light);--borderColorPress:var(--c-pink7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_pink_active_Button, .t_dark .t_light .t_pink_active_Card, .t_dark .t_light .t_pink_active_Checkbox, .t_dark .t_light .t_pink_active_Input, .t_dark .t_light .t_pink_active_RadioGroupItem, .t_dark .t_light .t_pink_active_SelectTrigger, .t_dark .t_light .t_pink_active_Switch, .t_dark .t_light .t_pink_active_TextArea, .t_dark .t_light .t_pink_active_TooltipContent, .t_dark .t_light .t_pink_surface4, .t_pink_active_Button, .t_pink_active_Card, .t_pink_active_Checkbox, .t_pink_active_Input, .t_pink_active_RadioGroupItem, .t_pink_active_SelectTrigger, .t_pink_active_Switch, .t_pink_active_TextArea, .t_pink_active_TooltipContent, .t_pink_surface4 {--background:var(--c-pink6Light);--backgroundHover:var(--c-pink6Light);--backgroundPress:var(--c-pink7Light);--backgroundFocus:var(--c-pink7Light);--borderColor:var(--c-pink6Light);--borderColorHover:var(--c-pink6Light);--borderColorFocus:var(--c-pink7Light);--borderColorPress:var(--c-pink7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_red_alt1, :root.t_dark .t_light .t_red_alt1, :root.t_light .t_dark .t_light .t_red_alt1, :root.t_light .t_red_alt1, .tm_xxt {--color:var(--c-red11Light);--colorHover:var(--c-red10Light);--colorPress:var(--c-red11Light);--colorFocus:var(--c-red10Light);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_red_alt1, .t_red_alt1 {--color:var(--c-red11Light);--colorHover:var(--c-red10Light);--colorPress:var(--c-red11Light);--colorFocus:var(--c-red10Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_red_alt2, :root.t_dark .t_light .t_red_alt2, :root.t_light .t_dark .t_light .t_red_alt2, :root.t_light .t_red_alt2, .tm_xxt {--color:var(--c-red10Light);--colorHover:var(--c-red9Dark);--colorPress:var(--c-red10Light);--colorFocus:var(--c-red9Dark);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_red_alt2, .t_red_alt2 {--color:var(--c-red10Light);--colorHover:var(--c-red9Dark);--colorPress:var(--c-red10Light);--colorFocus:var(--c-red9Dark);}
  }
:root.t_dark .t_light .t_dark .t_light .t_red_Button, :root.t_dark .t_light .t_dark .t_light .t_red_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_red_active, :root.t_dark .t_light .t_dark .t_light .t_red_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_red_surface3, :root.t_dark .t_light .t_red_Button, :root.t_dark .t_light .t_red_SliderTrackActive, :root.t_dark .t_light .t_red_active, :root.t_dark .t_light .t_red_active_SliderTrackActive, :root.t_dark .t_light .t_red_surface3, :root.t_light .t_dark .t_light .t_red_Button, :root.t_light .t_dark .t_light .t_red_SliderTrackActive, :root.t_light .t_dark .t_light .t_red_active, :root.t_light .t_dark .t_light .t_red_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_red_surface3, :root.t_light .t_red_Button, :root.t_light .t_red_SliderTrackActive, :root.t_light .t_red_active, :root.t_light .t_red_active_SliderTrackActive, :root.t_light .t_red_surface3, .tm_xxt {--background:var(--c-red4Light);--backgroundHover:var(--c-red3Light);--backgroundPress:var(--c-red5Light);--backgroundFocus:var(--c-red5Light);--borderColor:var(--c-red7Light);--borderColorHover:var(--c-red6Light);--borderColorFocus:var(--c-red7Light);--borderColorPress:var(--c-red8Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_red_Button, .t_dark .t_light .t_red_SliderTrackActive, .t_dark .t_light .t_red_active, .t_dark .t_light .t_red_active_SliderTrackActive, .t_dark .t_light .t_red_surface3, .t_red_Button, .t_red_SliderTrackActive, .t_red_active, .t_red_active_SliderTrackActive, .t_red_surface3 {--background:var(--c-red4Light);--backgroundHover:var(--c-red3Light);--backgroundPress:var(--c-red5Light);--backgroundFocus:var(--c-red5Light);--borderColor:var(--c-red7Light);--borderColorHover:var(--c-red6Light);--borderColorFocus:var(--c-red7Light);--borderColorPress:var(--c-red8Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_red_Card, :root.t_dark .t_light .t_dark .t_light .t_red_Input, :root.t_dark .t_light .t_dark .t_light .t_red_ListItem, :root.t_dark .t_light .t_dark .t_light .t_red_Progress, :root.t_dark .t_light .t_dark .t_light .t_red_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_red_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_red_TextArea, :root.t_dark .t_light .t_dark .t_light .t_red_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_red_active_ListItem, :root.t_dark .t_light .t_dark .t_light .t_red_active_Progress, :root.t_dark .t_light .t_dark .t_light .t_red_active_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_red_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_red_surface1, :root.t_dark .t_light .t_red_Card, :root.t_dark .t_light .t_red_Input, :root.t_dark .t_light .t_red_ListItem, :root.t_dark .t_light .t_red_Progress, :root.t_dark .t_light .t_red_SelectTrigger, :root.t_dark .t_light .t_red_SliderTrack, :root.t_dark .t_light .t_red_TextArea, :root.t_dark .t_light .t_red_TooltipArrow, :root.t_dark .t_light .t_red_active_ListItem, :root.t_dark .t_light .t_red_active_Progress, :root.t_dark .t_light .t_red_active_SliderTrack, :root.t_dark .t_light .t_red_active_TooltipArrow, :root.t_dark .t_light .t_red_surface1, :root.t_light .t_dark .t_light .t_red_Card, :root.t_light .t_dark .t_light .t_red_Input, :root.t_light .t_dark .t_light .t_red_ListItem, :root.t_light .t_dark .t_light .t_red_Progress, :root.t_light .t_dark .t_light .t_red_SelectTrigger, :root.t_light .t_dark .t_light .t_red_SliderTrack, :root.t_light .t_dark .t_light .t_red_TextArea, :root.t_light .t_dark .t_light .t_red_TooltipArrow, :root.t_light .t_dark .t_light .t_red_active_ListItem, :root.t_light .t_dark .t_light .t_red_active_Progress, :root.t_light .t_dark .t_light .t_red_active_SliderTrack, :root.t_light .t_dark .t_light .t_red_active_TooltipArrow, :root.t_light .t_dark .t_light .t_red_surface1, :root.t_light .t_red_Card, :root.t_light .t_red_Input, :root.t_light .t_red_ListItem, :root.t_light .t_red_Progress, :root.t_light .t_red_SelectTrigger, :root.t_light .t_red_SliderTrack, :root.t_light .t_red_TextArea, :root.t_light .t_red_TooltipArrow, :root.t_light .t_red_active_ListItem, :root.t_light .t_red_active_Progress, :root.t_light .t_red_active_SliderTrack, :root.t_light .t_red_active_TooltipArrow, :root.t_light .t_red_surface1, .tm_xxt {--background:var(--c-red2Light);--backgroundHover:var(--c-red1Light);--backgroundPress:var(--c-red3Light);--backgroundFocus:var(--c-red3Light);--borderColor:var(--c-red5Light);--borderColorHover:var(--c-red4Light);--borderColorFocus:var(--c-red5Light);--borderColorPress:var(--c-red6Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_red_Card, .t_dark .t_light .t_red_Input, .t_dark .t_light .t_red_ListItem, .t_dark .t_light .t_red_Progress, .t_dark .t_light .t_red_SelectTrigger, .t_dark .t_light .t_red_SliderTrack, .t_dark .t_light .t_red_TextArea, .t_dark .t_light .t_red_TooltipArrow, .t_dark .t_light .t_red_active_ListItem, .t_dark .t_light .t_red_active_Progress, .t_dark .t_light .t_red_active_SliderTrack, .t_dark .t_light .t_red_active_TooltipArrow, .t_dark .t_light .t_red_surface1, .t_red_Card, .t_red_Input, .t_red_ListItem, .t_red_Progress, .t_red_SelectTrigger, .t_red_SliderTrack, .t_red_TextArea, .t_red_TooltipArrow, .t_red_active_ListItem, .t_red_active_Progress, .t_red_active_SliderTrack, .t_red_active_TooltipArrow, .t_red_surface1 {--background:var(--c-red2Light);--backgroundHover:var(--c-red1Light);--backgroundPress:var(--c-red3Light);--backgroundFocus:var(--c-red3Light);--borderColor:var(--c-red5Light);--borderColorHover:var(--c-red4Light);--borderColorFocus:var(--c-red5Light);--borderColorPress:var(--c-red6Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_red_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_red_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_red_Switch, :root.t_dark .t_light .t_dark .t_light .t_red_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_red_surface2, :root.t_dark .t_light .t_red_Checkbox, :root.t_dark .t_light .t_red_RadioGroupItem, :root.t_dark .t_light .t_red_Switch, :root.t_dark .t_light .t_red_TooltipContent, :root.t_dark .t_light .t_red_surface2, :root.t_light .t_dark .t_light .t_red_Checkbox, :root.t_light .t_dark .t_light .t_red_RadioGroupItem, :root.t_light .t_dark .t_light .t_red_Switch, :root.t_light .t_dark .t_light .t_red_TooltipContent, :root.t_light .t_dark .t_light .t_red_surface2, :root.t_light .t_red_Checkbox, :root.t_light .t_red_RadioGroupItem, :root.t_light .t_red_Switch, :root.t_light .t_red_TooltipContent, :root.t_light .t_red_surface2, .tm_xxt {--background:var(--c-red3Light);--backgroundHover:var(--c-red2Light);--backgroundPress:var(--c-red4Light);--backgroundFocus:var(--c-red4Light);--borderColor:var(--c-red6Light);--borderColorHover:var(--c-red5Light);--borderColorFocus:var(--c-red6Light);--borderColorPress:var(--c-red7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_red_Checkbox, .t_dark .t_light .t_red_RadioGroupItem, .t_dark .t_light .t_red_Switch, .t_dark .t_light .t_red_TooltipContent, .t_dark .t_light .t_red_surface2, .t_red_Checkbox, .t_red_RadioGroupItem, .t_red_Switch, .t_red_TooltipContent, .t_red_surface2 {--background:var(--c-red3Light);--backgroundHover:var(--c-red2Light);--backgroundPress:var(--c-red4Light);--backgroundFocus:var(--c-red4Light);--borderColor:var(--c-red6Light);--borderColorHover:var(--c-red5Light);--borderColorFocus:var(--c-red6Light);--borderColorPress:var(--c-red7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_red_active_Button, :root.t_dark .t_light .t_dark .t_light .t_red_active_Card, :root.t_dark .t_light .t_dark .t_light .t_red_active_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_red_active_Input, :root.t_dark .t_light .t_dark .t_light .t_red_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_red_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_red_active_Switch, :root.t_dark .t_light .t_dark .t_light .t_red_active_TextArea, :root.t_dark .t_light .t_dark .t_light .t_red_active_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_red_surface4, :root.t_dark .t_light .t_red_active_Button, :root.t_dark .t_light .t_red_active_Card, :root.t_dark .t_light .t_red_active_Checkbox, :root.t_dark .t_light .t_red_active_Input, :root.t_dark .t_light .t_red_active_RadioGroupItem, :root.t_dark .t_light .t_red_active_SelectTrigger, :root.t_dark .t_light .t_red_active_Switch, :root.t_dark .t_light .t_red_active_TextArea, :root.t_dark .t_light .t_red_active_TooltipContent, :root.t_dark .t_light .t_red_surface4, :root.t_light .t_dark .t_light .t_red_active_Button, :root.t_light .t_dark .t_light .t_red_active_Card, :root.t_light .t_dark .t_light .t_red_active_Checkbox, :root.t_light .t_dark .t_light .t_red_active_Input, :root.t_light .t_dark .t_light .t_red_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_red_active_SelectTrigger, :root.t_light .t_dark .t_light .t_red_active_Switch, :root.t_light .t_dark .t_light .t_red_active_TextArea, :root.t_light .t_dark .t_light .t_red_active_TooltipContent, :root.t_light .t_dark .t_light .t_red_surface4, :root.t_light .t_red_active_Button, :root.t_light .t_red_active_Card, :root.t_light .t_red_active_Checkbox, :root.t_light .t_red_active_Input, :root.t_light .t_red_active_RadioGroupItem, :root.t_light .t_red_active_SelectTrigger, :root.t_light .t_red_active_Switch, :root.t_light .t_red_active_TextArea, :root.t_light .t_red_active_TooltipContent, :root.t_light .t_red_surface4, .tm_xxt {--background:var(--c-red6Light);--backgroundHover:var(--c-red6Light);--backgroundPress:var(--c-red7Light);--backgroundFocus:var(--c-red7Light);--borderColor:var(--c-red6Light);--borderColorHover:var(--c-red6Light);--borderColorFocus:var(--c-red7Light);--borderColorPress:var(--c-red7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_red_active_Button, .t_dark .t_light .t_red_active_Card, .t_dark .t_light .t_red_active_Checkbox, .t_dark .t_light .t_red_active_Input, .t_dark .t_light .t_red_active_RadioGroupItem, .t_dark .t_light .t_red_active_SelectTrigger, .t_dark .t_light .t_red_active_Switch, .t_dark .t_light .t_red_active_TextArea, .t_dark .t_light .t_red_active_TooltipContent, .t_dark .t_light .t_red_surface4, .t_red_active_Button, .t_red_active_Card, .t_red_active_Checkbox, .t_red_active_Input, .t_red_active_RadioGroupItem, .t_red_active_SelectTrigger, .t_red_active_Switch, .t_red_active_TextArea, .t_red_active_TooltipContent, .t_red_surface4 {--background:var(--c-red6Light);--backgroundHover:var(--c-red6Light);--backgroundPress:var(--c-red7Light);--backgroundFocus:var(--c-red7Light);--borderColor:var(--c-red6Light);--borderColorHover:var(--c-red6Light);--borderColorFocus:var(--c-red7Light);--borderColorPress:var(--c-red7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_gray_alt1, :root.t_dark .t_light .t_gray_alt1, :root.t_light .t_dark .t_light .t_gray_alt1, :root.t_light .t_gray_alt1, .tm_xxt {--color:var(--c-gray11Light);--colorHover:var(--c-gray10Light);--colorPress:var(--c-gray11Light);--colorFocus:var(--c-gray10Light);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_gray_alt1, .t_gray_alt1 {--color:var(--c-gray11Light);--colorHover:var(--c-gray10Light);--colorPress:var(--c-gray11Light);--colorFocus:var(--c-gray10Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_gray_alt2, :root.t_dark .t_light .t_gray_alt2, :root.t_light .t_dark .t_light .t_gray_alt2, :root.t_light .t_gray_alt2, .tm_xxt {--color:var(--c-gray10Light);--colorHover:var(--c-gray9Light);--colorPress:var(--c-gray10Light);--colorFocus:var(--c-gray9Light);}
@media(prefers-color-scheme:light){
    body{color:var(--color)}
    .t_dark .t_light .t_gray_alt2, .t_gray_alt2 {--color:var(--c-gray10Light);--colorHover:var(--c-gray9Light);--colorPress:var(--c-gray10Light);--colorFocus:var(--c-gray9Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_gray_Button, :root.t_dark .t_light .t_dark .t_light .t_gray_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_gray_active, :root.t_dark .t_light .t_dark .t_light .t_gray_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_light .t_gray_surface3, :root.t_dark .t_light .t_gray_Button, :root.t_dark .t_light .t_gray_SliderTrackActive, :root.t_dark .t_light .t_gray_active, :root.t_dark .t_light .t_gray_active_SliderTrackActive, :root.t_dark .t_light .t_gray_surface3, :root.t_light .t_dark .t_light .t_gray_Button, :root.t_light .t_dark .t_light .t_gray_SliderTrackActive, :root.t_light .t_dark .t_light .t_gray_active, :root.t_light .t_dark .t_light .t_gray_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_gray_surface3, :root.t_light .t_gray_Button, :root.t_light .t_gray_SliderTrackActive, :root.t_light .t_gray_active, :root.t_light .t_gray_active_SliderTrackActive, :root.t_light .t_gray_surface3, .tm_xxt {--background:var(--c-gray12Dark);--backgroundHover:var(--c-gray3Light);--backgroundPress:var(--c-gray5Light);--backgroundFocus:var(--c-gray5Light);--borderColor:var(--c-gray7Light);--borderColorHover:var(--c-gray6Light);--borderColorFocus:var(--c-gray7Light);--borderColorPress:var(--c-gray8Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_gray_Button, .t_dark .t_light .t_gray_SliderTrackActive, .t_dark .t_light .t_gray_active, .t_dark .t_light .t_gray_active_SliderTrackActive, .t_dark .t_light .t_gray_surface3, .t_gray_Button, .t_gray_SliderTrackActive, .t_gray_active, .t_gray_active_SliderTrackActive, .t_gray_surface3 {--background:var(--c-gray12Dark);--backgroundHover:var(--c-gray3Light);--backgroundPress:var(--c-gray5Light);--backgroundFocus:var(--c-gray5Light);--borderColor:var(--c-gray7Light);--borderColorHover:var(--c-gray6Light);--borderColorFocus:var(--c-gray7Light);--borderColorPress:var(--c-gray8Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_gray_Card, :root.t_dark .t_light .t_dark .t_light .t_gray_Input, :root.t_dark .t_light .t_dark .t_light .t_gray_ListItem, :root.t_dark .t_light .t_dark .t_light .t_gray_Progress, :root.t_dark .t_light .t_dark .t_light .t_gray_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_gray_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_gray_TextArea, :root.t_dark .t_light .t_dark .t_light .t_gray_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_gray_active_ListItem, :root.t_dark .t_light .t_dark .t_light .t_gray_active_Progress, :root.t_dark .t_light .t_dark .t_light .t_gray_active_SliderTrack, :root.t_dark .t_light .t_dark .t_light .t_gray_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_light .t_gray_surface1, :root.t_dark .t_light .t_gray_Card, :root.t_dark .t_light .t_gray_Input, :root.t_dark .t_light .t_gray_ListItem, :root.t_dark .t_light .t_gray_Progress, :root.t_dark .t_light .t_gray_SelectTrigger, :root.t_dark .t_light .t_gray_SliderTrack, :root.t_dark .t_light .t_gray_TextArea, :root.t_dark .t_light .t_gray_TooltipArrow, :root.t_dark .t_light .t_gray_active_ListItem, :root.t_dark .t_light .t_gray_active_Progress, :root.t_dark .t_light .t_gray_active_SliderTrack, :root.t_dark .t_light .t_gray_active_TooltipArrow, :root.t_dark .t_light .t_gray_surface1, :root.t_light .t_dark .t_light .t_gray_Card, :root.t_light .t_dark .t_light .t_gray_Input, :root.t_light .t_dark .t_light .t_gray_ListItem, :root.t_light .t_dark .t_light .t_gray_Progress, :root.t_light .t_dark .t_light .t_gray_SelectTrigger, :root.t_light .t_dark .t_light .t_gray_SliderTrack, :root.t_light .t_dark .t_light .t_gray_TextArea, :root.t_light .t_dark .t_light .t_gray_TooltipArrow, :root.t_light .t_dark .t_light .t_gray_active_ListItem, :root.t_light .t_dark .t_light .t_gray_active_Progress, :root.t_light .t_dark .t_light .t_gray_active_SliderTrack, :root.t_light .t_dark .t_light .t_gray_active_TooltipArrow, :root.t_light .t_dark .t_light .t_gray_surface1, :root.t_light .t_gray_Card, :root.t_light .t_gray_Input, :root.t_light .t_gray_ListItem, :root.t_light .t_gray_Progress, :root.t_light .t_gray_SelectTrigger, :root.t_light .t_gray_SliderTrack, :root.t_light .t_gray_TextArea, :root.t_light .t_gray_TooltipArrow, :root.t_light .t_gray_active_ListItem, :root.t_light .t_gray_active_Progress, :root.t_light .t_gray_active_SliderTrack, :root.t_light .t_gray_active_TooltipArrow, :root.t_light .t_gray_surface1, .tm_xxt {--background:var(--c-gray2Light);--backgroundHover:var(--c-gray1Light);--backgroundPress:var(--c-gray3Light);--backgroundFocus:var(--c-gray3Light);--borderColor:var(--c-gray5Light);--borderColorHover:var(--c-gray12Dark);--borderColorFocus:var(--c-gray5Light);--borderColorPress:var(--c-gray6Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_gray_Card, .t_dark .t_light .t_gray_Input, .t_dark .t_light .t_gray_ListItem, .t_dark .t_light .t_gray_Progress, .t_dark .t_light .t_gray_SelectTrigger, .t_dark .t_light .t_gray_SliderTrack, .t_dark .t_light .t_gray_TextArea, .t_dark .t_light .t_gray_TooltipArrow, .t_dark .t_light .t_gray_active_ListItem, .t_dark .t_light .t_gray_active_Progress, .t_dark .t_light .t_gray_active_SliderTrack, .t_dark .t_light .t_gray_active_TooltipArrow, .t_dark .t_light .t_gray_surface1, .t_gray_Card, .t_gray_Input, .t_gray_ListItem, .t_gray_Progress, .t_gray_SelectTrigger, .t_gray_SliderTrack, .t_gray_TextArea, .t_gray_TooltipArrow, .t_gray_active_ListItem, .t_gray_active_Progress, .t_gray_active_SliderTrack, .t_gray_active_TooltipArrow, .t_gray_surface1 {--background:var(--c-gray2Light);--backgroundHover:var(--c-gray1Light);--backgroundPress:var(--c-gray3Light);--backgroundFocus:var(--c-gray3Light);--borderColor:var(--c-gray5Light);--borderColorHover:var(--c-gray12Dark);--borderColorFocus:var(--c-gray5Light);--borderColorPress:var(--c-gray6Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_gray_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_gray_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_gray_Switch, :root.t_dark .t_light .t_dark .t_light .t_gray_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_gray_surface2, :root.t_dark .t_light .t_gray_Checkbox, :root.t_dark .t_light .t_gray_RadioGroupItem, :root.t_dark .t_light .t_gray_Switch, :root.t_dark .t_light .t_gray_TooltipContent, :root.t_dark .t_light .t_gray_surface2, :root.t_light .t_dark .t_light .t_gray_Checkbox, :root.t_light .t_dark .t_light .t_gray_RadioGroupItem, :root.t_light .t_dark .t_light .t_gray_Switch, :root.t_light .t_dark .t_light .t_gray_TooltipContent, :root.t_light .t_dark .t_light .t_gray_surface2, :root.t_light .t_gray_Checkbox, :root.t_light .t_gray_RadioGroupItem, :root.t_light .t_gray_Switch, :root.t_light .t_gray_TooltipContent, :root.t_light .t_gray_surface2, .tm_xxt {--background:var(--c-gray3Light);--backgroundHover:var(--c-gray2Light);--backgroundPress:var(--c-gray12Dark);--backgroundFocus:var(--c-gray12Dark);--borderColor:var(--c-gray6Light);--borderColorHover:var(--c-gray5Light);--borderColorFocus:var(--c-gray6Light);--borderColorPress:var(--c-gray7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_gray_Checkbox, .t_dark .t_light .t_gray_RadioGroupItem, .t_dark .t_light .t_gray_Switch, .t_dark .t_light .t_gray_TooltipContent, .t_dark .t_light .t_gray_surface2, .t_gray_Checkbox, .t_gray_RadioGroupItem, .t_gray_Switch, .t_gray_TooltipContent, .t_gray_surface2 {--background:var(--c-gray3Light);--backgroundHover:var(--c-gray2Light);--backgroundPress:var(--c-gray12Dark);--backgroundFocus:var(--c-gray12Dark);--borderColor:var(--c-gray6Light);--borderColorHover:var(--c-gray5Light);--borderColorFocus:var(--c-gray6Light);--borderColorPress:var(--c-gray7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_gray_active_Button, :root.t_dark .t_light .t_dark .t_light .t_gray_active_Card, :root.t_dark .t_light .t_dark .t_light .t_gray_active_Checkbox, :root.t_dark .t_light .t_dark .t_light .t_gray_active_Input, :root.t_dark .t_light .t_dark .t_light .t_gray_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_light .t_gray_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_light .t_gray_active_Switch, :root.t_dark .t_light .t_dark .t_light .t_gray_active_TextArea, :root.t_dark .t_light .t_dark .t_light .t_gray_active_TooltipContent, :root.t_dark .t_light .t_dark .t_light .t_gray_surface4, :root.t_dark .t_light .t_gray_active_Button, :root.t_dark .t_light .t_gray_active_Card, :root.t_dark .t_light .t_gray_active_Checkbox, :root.t_dark .t_light .t_gray_active_Input, :root.t_dark .t_light .t_gray_active_RadioGroupItem, :root.t_dark .t_light .t_gray_active_SelectTrigger, :root.t_dark .t_light .t_gray_active_Switch, :root.t_dark .t_light .t_gray_active_TextArea, :root.t_dark .t_light .t_gray_active_TooltipContent, :root.t_dark .t_light .t_gray_surface4, :root.t_light .t_dark .t_light .t_gray_active_Button, :root.t_light .t_dark .t_light .t_gray_active_Card, :root.t_light .t_dark .t_light .t_gray_active_Checkbox, :root.t_light .t_dark .t_light .t_gray_active_Input, :root.t_light .t_dark .t_light .t_gray_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_gray_active_SelectTrigger, :root.t_light .t_dark .t_light .t_gray_active_Switch, :root.t_light .t_dark .t_light .t_gray_active_TextArea, :root.t_light .t_dark .t_light .t_gray_active_TooltipContent, :root.t_light .t_dark .t_light .t_gray_surface4, :root.t_light .t_gray_active_Button, :root.t_light .t_gray_active_Card, :root.t_light .t_gray_active_Checkbox, :root.t_light .t_gray_active_Input, :root.t_light .t_gray_active_RadioGroupItem, :root.t_light .t_gray_active_SelectTrigger, :root.t_light .t_gray_active_Switch, :root.t_light .t_gray_active_TextArea, :root.t_light .t_gray_active_TooltipContent, :root.t_light .t_gray_surface4, .tm_xxt {--background:var(--c-gray6Light);--backgroundHover:var(--c-gray6Light);--backgroundPress:var(--c-gray7Light);--backgroundFocus:var(--c-gray7Light);--borderColor:var(--c-gray6Light);--borderColorHover:var(--c-gray6Light);--borderColorFocus:var(--c-gray7Light);--borderColorPress:var(--c-gray7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_dark .t_light .t_gray_active_Button, .t_dark .t_light .t_gray_active_Card, .t_dark .t_light .t_gray_active_Checkbox, .t_dark .t_light .t_gray_active_Input, .t_dark .t_light .t_gray_active_RadioGroupItem, .t_dark .t_light .t_gray_active_SelectTrigger, .t_dark .t_light .t_gray_active_Switch, .t_dark .t_light .t_gray_active_TextArea, .t_dark .t_light .t_gray_active_TooltipContent, .t_dark .t_light .t_gray_surface4, .t_gray_active_Button, .t_gray_active_Card, .t_gray_active_Checkbox, .t_gray_active_Input, .t_gray_active_RadioGroupItem, .t_gray_active_SelectTrigger, .t_gray_active_Switch, .t_gray_active_TextArea, .t_gray_active_TooltipContent, .t_gray_surface4 {--background:var(--c-gray6Light);--backgroundHover:var(--c-gray6Light);--backgroundPress:var(--c-gray7Light);--backgroundFocus:var(--c-gray7Light);--borderColor:var(--c-gray6Light);--borderColorHover:var(--c-gray6Light);--borderColorFocus:var(--c-gray7Light);--borderColorPress:var(--c-gray7Light);}
  }
:root.t_dark .t_light .t_dark .t_orange_alt1, :root.t_dark .t_orange_alt1, :root.t_light .t_dark .t_light .t_dark .t_orange_alt1, :root.t_light .t_dark .t_orange_alt1, .tm_xxt {--color:var(--c-orange11Dark);--colorHover:var(--c-orange10Dark);--colorPress:var(--c-orange11Dark);--colorFocus:var(--c-orange10Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_light .t_dark .t_orange_alt1, .t_orange_alt1 {--color:var(--c-orange11Dark);--colorHover:var(--c-orange10Dark);--colorPress:var(--c-orange11Dark);--colorFocus:var(--c-orange10Dark);}
  }
:root.t_dark .t_light .t_dark .t_orange_alt2, :root.t_dark .t_orange_alt2, :root.t_light .t_dark .t_light .t_dark .t_orange_alt2, :root.t_light .t_dark .t_orange_alt2, .tm_xxt {--color:var(--c-orange10Dark);--colorHover:var(--c-orange9Dark);--colorPress:var(--c-orange10Dark);--colorFocus:var(--c-orange9Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_light .t_dark .t_orange_alt2, .t_orange_alt2 {--color:var(--c-orange10Dark);--colorHover:var(--c-orange9Dark);--colorPress:var(--c-orange10Dark);--colorFocus:var(--c-orange9Dark);}
  }
:root.t_dark .t_light .t_dark .t_orange_Button, :root.t_dark .t_light .t_dark .t_orange_SliderTrackActive, :root.t_dark .t_light .t_dark .t_orange_active, :root.t_dark .t_light .t_dark .t_orange_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_orange_surface3, :root.t_dark .t_orange_Button, :root.t_dark .t_orange_SliderTrackActive, :root.t_dark .t_orange_active, :root.t_dark .t_orange_active_SliderTrackActive, :root.t_dark .t_orange_surface3, :root.t_light .t_dark .t_light .t_dark .t_orange_Button, :root.t_light .t_dark .t_light .t_dark .t_orange_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_orange_active, :root.t_light .t_dark .t_light .t_dark .t_orange_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_orange_surface3, :root.t_light .t_dark .t_orange_Button, :root.t_light .t_dark .t_orange_SliderTrackActive, :root.t_light .t_dark .t_orange_active, :root.t_light .t_dark .t_orange_active_SliderTrackActive, :root.t_light .t_dark .t_orange_surface3, .tm_xxt {--background:var(--c-orange4Dark);--backgroundHover:var(--c-orange5Dark);--backgroundPress:var(--c-orange3Dark);--backgroundFocus:var(--c-orange3Dark);--borderColor:var(--c-orange7Dark);--borderColorHover:var(--c-orange8Dark);--borderColorFocus:var(--c-orange7Dark);--borderColorPress:var(--c-orange6Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_orange_Button, .t_light .t_dark .t_orange_SliderTrackActive, .t_light .t_dark .t_orange_active, .t_light .t_dark .t_orange_active_SliderTrackActive, .t_light .t_dark .t_orange_surface3, .t_orange_Button, .t_orange_SliderTrackActive, .t_orange_active, .t_orange_active_SliderTrackActive, .t_orange_surface3 {--background:var(--c-orange4Dark);--backgroundHover:var(--c-orange5Dark);--backgroundPress:var(--c-orange3Dark);--backgroundFocus:var(--c-orange3Dark);--borderColor:var(--c-orange7Dark);--borderColorHover:var(--c-orange8Dark);--borderColorFocus:var(--c-orange7Dark);--borderColorPress:var(--c-orange6Dark);}
  }
:root.t_dark .t_light .t_dark .t_orange_Card, :root.t_dark .t_light .t_dark .t_orange_Input, :root.t_dark .t_light .t_dark .t_orange_ListItem, :root.t_dark .t_light .t_dark .t_orange_Progress, :root.t_dark .t_light .t_dark .t_orange_SelectTrigger, :root.t_dark .t_light .t_dark .t_orange_SliderTrack, :root.t_dark .t_light .t_dark .t_orange_TextArea, :root.t_dark .t_light .t_dark .t_orange_TooltipArrow, :root.t_dark .t_light .t_dark .t_orange_active_ListItem, :root.t_dark .t_light .t_dark .t_orange_active_Progress, :root.t_dark .t_light .t_dark .t_orange_active_SliderTrack, :root.t_dark .t_light .t_dark .t_orange_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_orange_surface1, :root.t_dark .t_orange_Card, :root.t_dark .t_orange_Input, :root.t_dark .t_orange_ListItem, :root.t_dark .t_orange_Progress, :root.t_dark .t_orange_SelectTrigger, :root.t_dark .t_orange_SliderTrack, :root.t_dark .t_orange_TextArea, :root.t_dark .t_orange_TooltipArrow, :root.t_dark .t_orange_active_ListItem, :root.t_dark .t_orange_active_Progress, :root.t_dark .t_orange_active_SliderTrack, :root.t_dark .t_orange_active_TooltipArrow, :root.t_dark .t_orange_surface1, :root.t_light .t_dark .t_light .t_dark .t_orange_Card, :root.t_light .t_dark .t_light .t_dark .t_orange_Input, :root.t_light .t_dark .t_light .t_dark .t_orange_ListItem, :root.t_light .t_dark .t_light .t_dark .t_orange_Progress, :root.t_light .t_dark .t_light .t_dark .t_orange_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_orange_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_orange_TextArea, :root.t_light .t_dark .t_light .t_dark .t_orange_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_orange_active_ListItem, :root.t_light .t_dark .t_light .t_dark .t_orange_active_Progress, :root.t_light .t_dark .t_light .t_dark .t_orange_active_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_orange_active_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_orange_surface1, :root.t_light .t_dark .t_orange_Card, :root.t_light .t_dark .t_orange_Input, :root.t_light .t_dark .t_orange_ListItem, :root.t_light .t_dark .t_orange_Progress, :root.t_light .t_dark .t_orange_SelectTrigger, :root.t_light .t_dark .t_orange_SliderTrack, :root.t_light .t_dark .t_orange_TextArea, :root.t_light .t_dark .t_orange_TooltipArrow, :root.t_light .t_dark .t_orange_active_ListItem, :root.t_light .t_dark .t_orange_active_Progress, :root.t_light .t_dark .t_orange_active_SliderTrack, :root.t_light .t_dark .t_orange_active_TooltipArrow, :root.t_light .t_dark .t_orange_surface1, .tm_xxt {--background:var(--c-orange2Dark);--backgroundHover:var(--c-orange3Dark);--backgroundPress:var(--c-orange1Dark);--backgroundFocus:var(--c-orange1Dark);--borderColor:var(--c-orange5Dark);--borderColorHover:var(--c-orange6Dark);--borderColorFocus:var(--c-orange5Dark);--borderColorPress:var(--c-orange4Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_orange_Card, .t_light .t_dark .t_orange_Input, .t_light .t_dark .t_orange_ListItem, .t_light .t_dark .t_orange_Progress, .t_light .t_dark .t_orange_SelectTrigger, .t_light .t_dark .t_orange_SliderTrack, .t_light .t_dark .t_orange_TextArea, .t_light .t_dark .t_orange_TooltipArrow, .t_light .t_dark .t_orange_active_ListItem, .t_light .t_dark .t_orange_active_Progress, .t_light .t_dark .t_orange_active_SliderTrack, .t_light .t_dark .t_orange_active_TooltipArrow, .t_light .t_dark .t_orange_surface1, .t_orange_Card, .t_orange_Input, .t_orange_ListItem, .t_orange_Progress, .t_orange_SelectTrigger, .t_orange_SliderTrack, .t_orange_TextArea, .t_orange_TooltipArrow, .t_orange_active_ListItem, .t_orange_active_Progress, .t_orange_active_SliderTrack, .t_orange_active_TooltipArrow, .t_orange_surface1 {--background:var(--c-orange2Dark);--backgroundHover:var(--c-orange3Dark);--backgroundPress:var(--c-orange1Dark);--backgroundFocus:var(--c-orange1Dark);--borderColor:var(--c-orange5Dark);--borderColorHover:var(--c-orange6Dark);--borderColorFocus:var(--c-orange5Dark);--borderColorPress:var(--c-orange4Dark);}
  }
:root.t_dark .t_light .t_dark .t_orange_Checkbox, :root.t_dark .t_light .t_dark .t_orange_RadioGroupItem, :root.t_dark .t_light .t_dark .t_orange_Switch, :root.t_dark .t_light .t_dark .t_orange_TooltipContent, :root.t_dark .t_light .t_dark .t_orange_surface2, :root.t_dark .t_orange_Checkbox, :root.t_dark .t_orange_RadioGroupItem, :root.t_dark .t_orange_Switch, :root.t_dark .t_orange_TooltipContent, :root.t_dark .t_orange_surface2, :root.t_light .t_dark .t_light .t_dark .t_orange_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_orange_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_orange_Switch, :root.t_light .t_dark .t_light .t_dark .t_orange_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_orange_surface2, :root.t_light .t_dark .t_orange_Checkbox, :root.t_light .t_dark .t_orange_RadioGroupItem, :root.t_light .t_dark .t_orange_Switch, :root.t_light .t_dark .t_orange_TooltipContent, :root.t_light .t_dark .t_orange_surface2, .tm_xxt {--background:var(--c-orange3Dark);--backgroundHover:var(--c-orange4Dark);--backgroundPress:var(--c-orange2Dark);--backgroundFocus:var(--c-orange2Dark);--borderColor:var(--c-orange6Dark);--borderColorHover:var(--c-orange7Dark);--borderColorFocus:var(--c-orange6Dark);--borderColorPress:var(--c-orange5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_orange_Checkbox, .t_light .t_dark .t_orange_RadioGroupItem, .t_light .t_dark .t_orange_Switch, .t_light .t_dark .t_orange_TooltipContent, .t_light .t_dark .t_orange_surface2, .t_orange_Checkbox, .t_orange_RadioGroupItem, .t_orange_Switch, .t_orange_TooltipContent, .t_orange_surface2 {--background:var(--c-orange3Dark);--backgroundHover:var(--c-orange4Dark);--backgroundPress:var(--c-orange2Dark);--backgroundFocus:var(--c-orange2Dark);--borderColor:var(--c-orange6Dark);--borderColorHover:var(--c-orange7Dark);--borderColorFocus:var(--c-orange6Dark);--borderColorPress:var(--c-orange5Dark);}
  }
:root.t_dark .t_light .t_dark .t_orange_active_Button, :root.t_dark .t_light .t_dark .t_orange_active_Card, :root.t_dark .t_light .t_dark .t_orange_active_Checkbox, :root.t_dark .t_light .t_dark .t_orange_active_Input, :root.t_dark .t_light .t_dark .t_orange_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_orange_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_orange_active_Switch, :root.t_dark .t_light .t_dark .t_orange_active_TextArea, :root.t_dark .t_light .t_dark .t_orange_active_TooltipContent, :root.t_dark .t_light .t_dark .t_orange_surface4, :root.t_dark .t_orange_active_Button, :root.t_dark .t_orange_active_Card, :root.t_dark .t_orange_active_Checkbox, :root.t_dark .t_orange_active_Input, :root.t_dark .t_orange_active_RadioGroupItem, :root.t_dark .t_orange_active_SelectTrigger, :root.t_dark .t_orange_active_Switch, :root.t_dark .t_orange_active_TextArea, :root.t_dark .t_orange_active_TooltipContent, :root.t_dark .t_orange_surface4, :root.t_light .t_dark .t_light .t_dark .t_orange_active_Button, :root.t_light .t_dark .t_light .t_dark .t_orange_active_Card, :root.t_light .t_dark .t_light .t_dark .t_orange_active_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_orange_active_Input, :root.t_light .t_dark .t_light .t_dark .t_orange_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_orange_active_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_orange_active_Switch, :root.t_light .t_dark .t_light .t_dark .t_orange_active_TextArea, :root.t_light .t_dark .t_light .t_dark .t_orange_active_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_orange_surface4, :root.t_light .t_dark .t_orange_active_Button, :root.t_light .t_dark .t_orange_active_Card, :root.t_light .t_dark .t_orange_active_Checkbox, :root.t_light .t_dark .t_orange_active_Input, :root.t_light .t_dark .t_orange_active_RadioGroupItem, :root.t_light .t_dark .t_orange_active_SelectTrigger, :root.t_light .t_dark .t_orange_active_Switch, :root.t_light .t_dark .t_orange_active_TextArea, :root.t_light .t_dark .t_orange_active_TooltipContent, :root.t_light .t_dark .t_orange_surface4, .tm_xxt {--background:var(--c-orange6Dark);--backgroundHover:var(--c-orange6Dark);--backgroundPress:var(--c-orange5Dark);--backgroundFocus:var(--c-orange5Dark);--borderColor:var(--c-orange6Dark);--borderColorHover:var(--c-orange6Dark);--borderColorFocus:var(--c-orange5Dark);--borderColorPress:var(--c-orange5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_orange_active_Button, .t_light .t_dark .t_orange_active_Card, .t_light .t_dark .t_orange_active_Checkbox, .t_light .t_dark .t_orange_active_Input, .t_light .t_dark .t_orange_active_RadioGroupItem, .t_light .t_dark .t_orange_active_SelectTrigger, .t_light .t_dark .t_orange_active_Switch, .t_light .t_dark .t_orange_active_TextArea, .t_light .t_dark .t_orange_active_TooltipContent, .t_light .t_dark .t_orange_surface4, .t_orange_active_Button, .t_orange_active_Card, .t_orange_active_Checkbox, .t_orange_active_Input, .t_orange_active_RadioGroupItem, .t_orange_active_SelectTrigger, .t_orange_active_Switch, .t_orange_active_TextArea, .t_orange_active_TooltipContent, .t_orange_surface4 {--background:var(--c-orange6Dark);--backgroundHover:var(--c-orange6Dark);--backgroundPress:var(--c-orange5Dark);--backgroundFocus:var(--c-orange5Dark);--borderColor:var(--c-orange6Dark);--borderColorHover:var(--c-orange6Dark);--borderColorFocus:var(--c-orange5Dark);--borderColorPress:var(--c-orange5Dark);}
  }
:root.t_dark .t_light .t_dark .t_yellow_alt1, :root.t_dark .t_yellow_alt1, :root.t_light .t_dark .t_light .t_dark .t_yellow_alt1, :root.t_light .t_dark .t_yellow_alt1, .tm_xxt {--color:var(--c-yellow11Dark);--colorHover:var(--c-yellow10Dark);--colorPress:var(--c-yellow11Dark);--colorFocus:var(--c-yellow10Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_light .t_dark .t_yellow_alt1, .t_yellow_alt1 {--color:var(--c-yellow11Dark);--colorHover:var(--c-yellow10Dark);--colorPress:var(--c-yellow11Dark);--colorFocus:var(--c-yellow10Dark);}
  }
:root.t_dark .t_light .t_dark .t_yellow_alt2, :root.t_dark .t_yellow_alt2, :root.t_light .t_dark .t_light .t_dark .t_yellow_alt2, :root.t_light .t_dark .t_yellow_alt2, .tm_xxt {--color:var(--c-yellow10Dark);--colorHover:var(--c-yellow9Dark);--colorPress:var(--c-yellow10Dark);--colorFocus:var(--c-yellow9Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_light .t_dark .t_yellow_alt2, .t_yellow_alt2 {--color:var(--c-yellow10Dark);--colorHover:var(--c-yellow9Dark);--colorPress:var(--c-yellow10Dark);--colorFocus:var(--c-yellow9Dark);}
  }
:root.t_dark .t_light .t_dark .t_yellow_Button, :root.t_dark .t_light .t_dark .t_yellow_SliderTrackActive, :root.t_dark .t_light .t_dark .t_yellow_active, :root.t_dark .t_light .t_dark .t_yellow_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_yellow_surface3, :root.t_dark .t_yellow_Button, :root.t_dark .t_yellow_SliderTrackActive, :root.t_dark .t_yellow_active, :root.t_dark .t_yellow_active_SliderTrackActive, :root.t_dark .t_yellow_surface3, :root.t_light .t_dark .t_light .t_dark .t_yellow_Button, :root.t_light .t_dark .t_light .t_dark .t_yellow_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_yellow_active, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_yellow_surface3, :root.t_light .t_dark .t_yellow_Button, :root.t_light .t_dark .t_yellow_SliderTrackActive, :root.t_light .t_dark .t_yellow_active, :root.t_light .t_dark .t_yellow_active_SliderTrackActive, :root.t_light .t_dark .t_yellow_surface3, .tm_xxt {--background:var(--c-yellow4Dark);--backgroundHover:var(--c-yellow5Dark);--backgroundPress:var(--c-yellow3Dark);--backgroundFocus:var(--c-yellow3Dark);--borderColor:var(--c-yellow7Dark);--borderColorHover:var(--c-yellow8Dark);--borderColorFocus:var(--c-yellow7Dark);--borderColorPress:var(--c-yellow6Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_yellow_Button, .t_light .t_dark .t_yellow_SliderTrackActive, .t_light .t_dark .t_yellow_active, .t_light .t_dark .t_yellow_active_SliderTrackActive, .t_light .t_dark .t_yellow_surface3, .t_yellow_Button, .t_yellow_SliderTrackActive, .t_yellow_active, .t_yellow_active_SliderTrackActive, .t_yellow_surface3 {--background:var(--c-yellow4Dark);--backgroundHover:var(--c-yellow5Dark);--backgroundPress:var(--c-yellow3Dark);--backgroundFocus:var(--c-yellow3Dark);--borderColor:var(--c-yellow7Dark);--borderColorHover:var(--c-yellow8Dark);--borderColorFocus:var(--c-yellow7Dark);--borderColorPress:var(--c-yellow6Dark);}
  }
:root.t_dark .t_light .t_dark .t_yellow_Card, :root.t_dark .t_light .t_dark .t_yellow_Input, :root.t_dark .t_light .t_dark .t_yellow_ListItem, :root.t_dark .t_light .t_dark .t_yellow_Progress, :root.t_dark .t_light .t_dark .t_yellow_SelectTrigger, :root.t_dark .t_light .t_dark .t_yellow_SliderTrack, :root.t_dark .t_light .t_dark .t_yellow_TextArea, :root.t_dark .t_light .t_dark .t_yellow_TooltipArrow, :root.t_dark .t_light .t_dark .t_yellow_active_ListItem, :root.t_dark .t_light .t_dark .t_yellow_active_Progress, :root.t_dark .t_light .t_dark .t_yellow_active_SliderTrack, :root.t_dark .t_light .t_dark .t_yellow_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_yellow_surface1, :root.t_dark .t_yellow_Card, :root.t_dark .t_yellow_Input, :root.t_dark .t_yellow_ListItem, :root.t_dark .t_yellow_Progress, :root.t_dark .t_yellow_SelectTrigger, :root.t_dark .t_yellow_SliderTrack, :root.t_dark .t_yellow_TextArea, :root.t_dark .t_yellow_TooltipArrow, :root.t_dark .t_yellow_active_ListItem, :root.t_dark .t_yellow_active_Progress, :root.t_dark .t_yellow_active_SliderTrack, :root.t_dark .t_yellow_active_TooltipArrow, :root.t_dark .t_yellow_surface1, :root.t_light .t_dark .t_light .t_dark .t_yellow_Card, :root.t_light .t_dark .t_light .t_dark .t_yellow_Input, :root.t_light .t_dark .t_light .t_dark .t_yellow_ListItem, :root.t_light .t_dark .t_light .t_dark .t_yellow_Progress, :root.t_light .t_dark .t_light .t_dark .t_yellow_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_yellow_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_yellow_TextArea, :root.t_light .t_dark .t_light .t_dark .t_yellow_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_ListItem, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_Progress, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_yellow_surface1, :root.t_light .t_dark .t_yellow_Card, :root.t_light .t_dark .t_yellow_Input, :root.t_light .t_dark .t_yellow_ListItem, :root.t_light .t_dark .t_yellow_Progress, :root.t_light .t_dark .t_yellow_SelectTrigger, :root.t_light .t_dark .t_yellow_SliderTrack, :root.t_light .t_dark .t_yellow_TextArea, :root.t_light .t_dark .t_yellow_TooltipArrow, :root.t_light .t_dark .t_yellow_active_ListItem, :root.t_light .t_dark .t_yellow_active_Progress, :root.t_light .t_dark .t_yellow_active_SliderTrack, :root.t_light .t_dark .t_yellow_active_TooltipArrow, :root.t_light .t_dark .t_yellow_surface1, .tm_xxt {--background:var(--c-yellow2Dark);--backgroundHover:var(--c-yellow3Dark);--backgroundPress:var(--c-yellow1Dark);--backgroundFocus:var(--c-yellow1Dark);--borderColor:var(--c-yellow5Dark);--borderColorHover:var(--c-yellow6Dark);--borderColorFocus:var(--c-yellow5Dark);--borderColorPress:var(--c-yellow4Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_yellow_Card, .t_light .t_dark .t_yellow_Input, .t_light .t_dark .t_yellow_ListItem, .t_light .t_dark .t_yellow_Progress, .t_light .t_dark .t_yellow_SelectTrigger, .t_light .t_dark .t_yellow_SliderTrack, .t_light .t_dark .t_yellow_TextArea, .t_light .t_dark .t_yellow_TooltipArrow, .t_light .t_dark .t_yellow_active_ListItem, .t_light .t_dark .t_yellow_active_Progress, .t_light .t_dark .t_yellow_active_SliderTrack, .t_light .t_dark .t_yellow_active_TooltipArrow, .t_light .t_dark .t_yellow_surface1, .t_yellow_Card, .t_yellow_Input, .t_yellow_ListItem, .t_yellow_Progress, .t_yellow_SelectTrigger, .t_yellow_SliderTrack, .t_yellow_TextArea, .t_yellow_TooltipArrow, .t_yellow_active_ListItem, .t_yellow_active_Progress, .t_yellow_active_SliderTrack, .t_yellow_active_TooltipArrow, .t_yellow_surface1 {--background:var(--c-yellow2Dark);--backgroundHover:var(--c-yellow3Dark);--backgroundPress:var(--c-yellow1Dark);--backgroundFocus:var(--c-yellow1Dark);--borderColor:var(--c-yellow5Dark);--borderColorHover:var(--c-yellow6Dark);--borderColorFocus:var(--c-yellow5Dark);--borderColorPress:var(--c-yellow4Dark);}
  }
:root.t_dark .t_light .t_dark .t_yellow_Checkbox, :root.t_dark .t_light .t_dark .t_yellow_RadioGroupItem, :root.t_dark .t_light .t_dark .t_yellow_Switch, :root.t_dark .t_light .t_dark .t_yellow_TooltipContent, :root.t_dark .t_light .t_dark .t_yellow_surface2, :root.t_dark .t_yellow_Checkbox, :root.t_dark .t_yellow_RadioGroupItem, :root.t_dark .t_yellow_Switch, :root.t_dark .t_yellow_TooltipContent, :root.t_dark .t_yellow_surface2, :root.t_light .t_dark .t_light .t_dark .t_yellow_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_yellow_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_yellow_Switch, :root.t_light .t_dark .t_light .t_dark .t_yellow_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_yellow_surface2, :root.t_light .t_dark .t_yellow_Checkbox, :root.t_light .t_dark .t_yellow_RadioGroupItem, :root.t_light .t_dark .t_yellow_Switch, :root.t_light .t_dark .t_yellow_TooltipContent, :root.t_light .t_dark .t_yellow_surface2, .tm_xxt {--background:var(--c-yellow3Dark);--backgroundHover:var(--c-yellow4Dark);--backgroundPress:var(--c-yellow2Dark);--backgroundFocus:var(--c-yellow2Dark);--borderColor:var(--c-yellow6Dark);--borderColorHover:var(--c-yellow7Dark);--borderColorFocus:var(--c-yellow6Dark);--borderColorPress:var(--c-yellow5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_yellow_Checkbox, .t_light .t_dark .t_yellow_RadioGroupItem, .t_light .t_dark .t_yellow_Switch, .t_light .t_dark .t_yellow_TooltipContent, .t_light .t_dark .t_yellow_surface2, .t_yellow_Checkbox, .t_yellow_RadioGroupItem, .t_yellow_Switch, .t_yellow_TooltipContent, .t_yellow_surface2 {--background:var(--c-yellow3Dark);--backgroundHover:var(--c-yellow4Dark);--backgroundPress:var(--c-yellow2Dark);--backgroundFocus:var(--c-yellow2Dark);--borderColor:var(--c-yellow6Dark);--borderColorHover:var(--c-yellow7Dark);--borderColorFocus:var(--c-yellow6Dark);--borderColorPress:var(--c-yellow5Dark);}
  }
:root.t_dark .t_light .t_dark .t_yellow_active_Button, :root.t_dark .t_light .t_dark .t_yellow_active_Card, :root.t_dark .t_light .t_dark .t_yellow_active_Checkbox, :root.t_dark .t_light .t_dark .t_yellow_active_Input, :root.t_dark .t_light .t_dark .t_yellow_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_yellow_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_yellow_active_Switch, :root.t_dark .t_light .t_dark .t_yellow_active_TextArea, :root.t_dark .t_light .t_dark .t_yellow_active_TooltipContent, :root.t_dark .t_light .t_dark .t_yellow_surface4, :root.t_dark .t_yellow_active_Button, :root.t_dark .t_yellow_active_Card, :root.t_dark .t_yellow_active_Checkbox, :root.t_dark .t_yellow_active_Input, :root.t_dark .t_yellow_active_RadioGroupItem, :root.t_dark .t_yellow_active_SelectTrigger, :root.t_dark .t_yellow_active_Switch, :root.t_dark .t_yellow_active_TextArea, :root.t_dark .t_yellow_active_TooltipContent, :root.t_dark .t_yellow_surface4, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_Button, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_Card, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_Input, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_Switch, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_TextArea, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_yellow_surface4, :root.t_light .t_dark .t_yellow_active_Button, :root.t_light .t_dark .t_yellow_active_Card, :root.t_light .t_dark .t_yellow_active_Checkbox, :root.t_light .t_dark .t_yellow_active_Input, :root.t_light .t_dark .t_yellow_active_RadioGroupItem, :root.t_light .t_dark .t_yellow_active_SelectTrigger, :root.t_light .t_dark .t_yellow_active_Switch, :root.t_light .t_dark .t_yellow_active_TextArea, :root.t_light .t_dark .t_yellow_active_TooltipContent, :root.t_light .t_dark .t_yellow_surface4, .tm_xxt {--background:var(--c-yellow6Dark);--backgroundHover:var(--c-yellow6Dark);--backgroundPress:var(--c-yellow5Dark);--backgroundFocus:var(--c-yellow5Dark);--borderColor:var(--c-yellow6Dark);--borderColorHover:var(--c-yellow6Dark);--borderColorFocus:var(--c-yellow5Dark);--borderColorPress:var(--c-yellow5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_yellow_active_Button, .t_light .t_dark .t_yellow_active_Card, .t_light .t_dark .t_yellow_active_Checkbox, .t_light .t_dark .t_yellow_active_Input, .t_light .t_dark .t_yellow_active_RadioGroupItem, .t_light .t_dark .t_yellow_active_SelectTrigger, .t_light .t_dark .t_yellow_active_Switch, .t_light .t_dark .t_yellow_active_TextArea, .t_light .t_dark .t_yellow_active_TooltipContent, .t_light .t_dark .t_yellow_surface4, .t_yellow_active_Button, .t_yellow_active_Card, .t_yellow_active_Checkbox, .t_yellow_active_Input, .t_yellow_active_RadioGroupItem, .t_yellow_active_SelectTrigger, .t_yellow_active_Switch, .t_yellow_active_TextArea, .t_yellow_active_TooltipContent, .t_yellow_surface4 {--background:var(--c-yellow6Dark);--backgroundHover:var(--c-yellow6Dark);--backgroundPress:var(--c-yellow5Dark);--backgroundFocus:var(--c-yellow5Dark);--borderColor:var(--c-yellow6Dark);--borderColorHover:var(--c-yellow6Dark);--borderColorFocus:var(--c-yellow5Dark);--borderColorPress:var(--c-yellow5Dark);}
  }
:root.t_dark .t_green_alt1, :root.t_dark .t_light .t_dark .t_green_alt1, :root.t_light .t_dark .t_green_alt1, :root.t_light .t_dark .t_light .t_dark .t_green_alt1, .tm_xxt {--color:var(--c-green11Dark);--colorHover:var(--c-green10Dark);--colorPress:var(--c-green11Dark);--colorFocus:var(--c-green10Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_green_alt1, .t_light .t_dark .t_green_alt1 {--color:var(--c-green11Dark);--colorHover:var(--c-green10Dark);--colorPress:var(--c-green11Dark);--colorFocus:var(--c-green10Dark);}
  }
:root.t_dark .t_green_alt2, :root.t_dark .t_light .t_dark .t_green_alt2, :root.t_light .t_dark .t_green_alt2, :root.t_light .t_dark .t_light .t_dark .t_green_alt2, .tm_xxt {--color:var(--c-green10Dark);--colorHover:var(--c-green9Dark);--colorPress:var(--c-green10Dark);--colorFocus:var(--c-green9Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_green_alt2, .t_light .t_dark .t_green_alt2 {--color:var(--c-green10Dark);--colorHover:var(--c-green9Dark);--colorPress:var(--c-green10Dark);--colorFocus:var(--c-green9Dark);}
  }
:root.t_dark .t_green_Button, :root.t_dark .t_green_SliderTrackActive, :root.t_dark .t_green_active, :root.t_dark .t_green_active_SliderTrackActive, :root.t_dark .t_green_surface3, :root.t_dark .t_light .t_dark .t_green_Button, :root.t_dark .t_light .t_dark .t_green_SliderTrackActive, :root.t_dark .t_light .t_dark .t_green_active, :root.t_dark .t_light .t_dark .t_green_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_green_surface3, :root.t_light .t_dark .t_green_Button, :root.t_light .t_dark .t_green_SliderTrackActive, :root.t_light .t_dark .t_green_active, :root.t_light .t_dark .t_green_active_SliderTrackActive, :root.t_light .t_dark .t_green_surface3, :root.t_light .t_dark .t_light .t_dark .t_green_Button, :root.t_light .t_dark .t_light .t_dark .t_green_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_green_active, :root.t_light .t_dark .t_light .t_dark .t_green_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_green_surface3, .tm_xxt {--background:var(--c-green4Dark);--backgroundHover:var(--c-green5Dark);--backgroundPress:var(--c-green3Dark);--backgroundFocus:var(--c-green3Dark);--borderColor:var(--c-green7Dark);--borderColorHover:var(--c-green8Dark);--borderColorFocus:var(--c-green7Dark);--borderColorPress:var(--c-green6Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_green_Button, .t_green_SliderTrackActive, .t_green_active, .t_green_active_SliderTrackActive, .t_green_surface3, .t_light .t_dark .t_green_Button, .t_light .t_dark .t_green_SliderTrackActive, .t_light .t_dark .t_green_active, .t_light .t_dark .t_green_active_SliderTrackActive, .t_light .t_dark .t_green_surface3 {--background:var(--c-green4Dark);--backgroundHover:var(--c-green5Dark);--backgroundPress:var(--c-green3Dark);--backgroundFocus:var(--c-green3Dark);--borderColor:var(--c-green7Dark);--borderColorHover:var(--c-green8Dark);--borderColorFocus:var(--c-green7Dark);--borderColorPress:var(--c-green6Dark);}
  }
:root.t_dark .t_green_Card, :root.t_dark .t_green_Input, :root.t_dark .t_green_ListItem, :root.t_dark .t_green_Progress, :root.t_dark .t_green_SelectTrigger, :root.t_dark .t_green_SliderTrack, :root.t_dark .t_green_TextArea, :root.t_dark .t_green_TooltipArrow, :root.t_dark .t_green_active_ListItem, :root.t_dark .t_green_active_Progress, :root.t_dark .t_green_active_SliderTrack, :root.t_dark .t_green_active_TooltipArrow, :root.t_dark .t_green_surface1, :root.t_dark .t_light .t_dark .t_green_Card, :root.t_dark .t_light .t_dark .t_green_Input, :root.t_dark .t_light .t_dark .t_green_ListItem, :root.t_dark .t_light .t_dark .t_green_Progress, :root.t_dark .t_light .t_dark .t_green_SelectTrigger, :root.t_dark .t_light .t_dark .t_green_SliderTrack, :root.t_dark .t_light .t_dark .t_green_TextArea, :root.t_dark .t_light .t_dark .t_green_TooltipArrow, :root.t_dark .t_light .t_dark .t_green_active_ListItem, :root.t_dark .t_light .t_dark .t_green_active_Progress, :root.t_dark .t_light .t_dark .t_green_active_SliderTrack, :root.t_dark .t_light .t_dark .t_green_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_green_surface1, :root.t_light .t_dark .t_green_Card, :root.t_light .t_dark .t_green_Input, :root.t_light .t_dark .t_green_ListItem, :root.t_light .t_dark .t_green_Progress, :root.t_light .t_dark .t_green_SelectTrigger, :root.t_light .t_dark .t_green_SliderTrack, :root.t_light .t_dark .t_green_TextArea, :root.t_light .t_dark .t_green_TooltipArrow, :root.t_light .t_dark .t_green_active_ListItem, :root.t_light .t_dark .t_green_active_Progress, :root.t_light .t_dark .t_green_active_SliderTrack, :root.t_light .t_dark .t_green_active_TooltipArrow, :root.t_light .t_dark .t_green_surface1, :root.t_light .t_dark .t_light .t_dark .t_green_Card, :root.t_light .t_dark .t_light .t_dark .t_green_Input, :root.t_light .t_dark .t_light .t_dark .t_green_ListItem, :root.t_light .t_dark .t_light .t_dark .t_green_Progress, :root.t_light .t_dark .t_light .t_dark .t_green_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_green_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_green_TextArea, :root.t_light .t_dark .t_light .t_dark .t_green_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_green_active_ListItem, :root.t_light .t_dark .t_light .t_dark .t_green_active_Progress, :root.t_light .t_dark .t_light .t_dark .t_green_active_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_green_active_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_green_surface1, .tm_xxt {--background:var(--c-green2Dark);--backgroundHover:var(--c-green3Dark);--backgroundPress:var(--c-green1Dark);--backgroundFocus:var(--c-green1Dark);--borderColor:var(--c-green5Dark);--borderColorHover:var(--c-green6Dark);--borderColorFocus:var(--c-green5Dark);--borderColorPress:var(--c-green4Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_green_Card, .t_green_Input, .t_green_ListItem, .t_green_Progress, .t_green_SelectTrigger, .t_green_SliderTrack, .t_green_TextArea, .t_green_TooltipArrow, .t_green_active_ListItem, .t_green_active_Progress, .t_green_active_SliderTrack, .t_green_active_TooltipArrow, .t_green_surface1, .t_light .t_dark .t_green_Card, .t_light .t_dark .t_green_Input, .t_light .t_dark .t_green_ListItem, .t_light .t_dark .t_green_Progress, .t_light .t_dark .t_green_SelectTrigger, .t_light .t_dark .t_green_SliderTrack, .t_light .t_dark .t_green_TextArea, .t_light .t_dark .t_green_TooltipArrow, .t_light .t_dark .t_green_active_ListItem, .t_light .t_dark .t_green_active_Progress, .t_light .t_dark .t_green_active_SliderTrack, .t_light .t_dark .t_green_active_TooltipArrow, .t_light .t_dark .t_green_surface1 {--background:var(--c-green2Dark);--backgroundHover:var(--c-green3Dark);--backgroundPress:var(--c-green1Dark);--backgroundFocus:var(--c-green1Dark);--borderColor:var(--c-green5Dark);--borderColorHover:var(--c-green6Dark);--borderColorFocus:var(--c-green5Dark);--borderColorPress:var(--c-green4Dark);}
  }
:root.t_dark .t_green_Checkbox, :root.t_dark .t_green_RadioGroupItem, :root.t_dark .t_green_Switch, :root.t_dark .t_green_TooltipContent, :root.t_dark .t_green_surface2, :root.t_dark .t_light .t_dark .t_green_Checkbox, :root.t_dark .t_light .t_dark .t_green_RadioGroupItem, :root.t_dark .t_light .t_dark .t_green_Switch, :root.t_dark .t_light .t_dark .t_green_TooltipContent, :root.t_dark .t_light .t_dark .t_green_surface2, :root.t_light .t_dark .t_green_Checkbox, :root.t_light .t_dark .t_green_RadioGroupItem, :root.t_light .t_dark .t_green_Switch, :root.t_light .t_dark .t_green_TooltipContent, :root.t_light .t_dark .t_green_surface2, :root.t_light .t_dark .t_light .t_dark .t_green_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_green_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_green_Switch, :root.t_light .t_dark .t_light .t_dark .t_green_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_green_surface2, .tm_xxt {--background:var(--c-green3Dark);--backgroundHover:var(--c-green4Dark);--backgroundPress:var(--c-green2Dark);--backgroundFocus:var(--c-green2Dark);--borderColor:var(--c-green6Dark);--borderColorHover:var(--c-green7Dark);--borderColorFocus:var(--c-green6Dark);--borderColorPress:var(--c-green5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_green_Checkbox, .t_green_RadioGroupItem, .t_green_Switch, .t_green_TooltipContent, .t_green_surface2, .t_light .t_dark .t_green_Checkbox, .t_light .t_dark .t_green_RadioGroupItem, .t_light .t_dark .t_green_Switch, .t_light .t_dark .t_green_TooltipContent, .t_light .t_dark .t_green_surface2 {--background:var(--c-green3Dark);--backgroundHover:var(--c-green4Dark);--backgroundPress:var(--c-green2Dark);--backgroundFocus:var(--c-green2Dark);--borderColor:var(--c-green6Dark);--borderColorHover:var(--c-green7Dark);--borderColorFocus:var(--c-green6Dark);--borderColorPress:var(--c-green5Dark);}
  }
:root.t_dark .t_green_active_Button, :root.t_dark .t_green_active_Card, :root.t_dark .t_green_active_Checkbox, :root.t_dark .t_green_active_Input, :root.t_dark .t_green_active_RadioGroupItem, :root.t_dark .t_green_active_SelectTrigger, :root.t_dark .t_green_active_Switch, :root.t_dark .t_green_active_TextArea, :root.t_dark .t_green_active_TooltipContent, :root.t_dark .t_green_surface4, :root.t_dark .t_light .t_dark .t_green_active_Button, :root.t_dark .t_light .t_dark .t_green_active_Card, :root.t_dark .t_light .t_dark .t_green_active_Checkbox, :root.t_dark .t_light .t_dark .t_green_active_Input, :root.t_dark .t_light .t_dark .t_green_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_green_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_green_active_Switch, :root.t_dark .t_light .t_dark .t_green_active_TextArea, :root.t_dark .t_light .t_dark .t_green_active_TooltipContent, :root.t_dark .t_light .t_dark .t_green_surface4, :root.t_light .t_dark .t_green_active_Button, :root.t_light .t_dark .t_green_active_Card, :root.t_light .t_dark .t_green_active_Checkbox, :root.t_light .t_dark .t_green_active_Input, :root.t_light .t_dark .t_green_active_RadioGroupItem, :root.t_light .t_dark .t_green_active_SelectTrigger, :root.t_light .t_dark .t_green_active_Switch, :root.t_light .t_dark .t_green_active_TextArea, :root.t_light .t_dark .t_green_active_TooltipContent, :root.t_light .t_dark .t_green_surface4, :root.t_light .t_dark .t_light .t_dark .t_green_active_Button, :root.t_light .t_dark .t_light .t_dark .t_green_active_Card, :root.t_light .t_dark .t_light .t_dark .t_green_active_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_green_active_Input, :root.t_light .t_dark .t_light .t_dark .t_green_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_green_active_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_green_active_Switch, :root.t_light .t_dark .t_light .t_dark .t_green_active_TextArea, :root.t_light .t_dark .t_light .t_dark .t_green_active_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_green_surface4, .tm_xxt {--background:var(--c-green6Dark);--backgroundHover:var(--c-green6Dark);--backgroundPress:var(--c-green5Dark);--backgroundFocus:var(--c-green5Dark);--borderColor:var(--c-green6Dark);--borderColorHover:var(--c-green6Dark);--borderColorFocus:var(--c-green5Dark);--borderColorPress:var(--c-green5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_green_active_Button, .t_green_active_Card, .t_green_active_Checkbox, .t_green_active_Input, .t_green_active_RadioGroupItem, .t_green_active_SelectTrigger, .t_green_active_Switch, .t_green_active_TextArea, .t_green_active_TooltipContent, .t_green_surface4, .t_light .t_dark .t_green_active_Button, .t_light .t_dark .t_green_active_Card, .t_light .t_dark .t_green_active_Checkbox, .t_light .t_dark .t_green_active_Input, .t_light .t_dark .t_green_active_RadioGroupItem, .t_light .t_dark .t_green_active_SelectTrigger, .t_light .t_dark .t_green_active_Switch, .t_light .t_dark .t_green_active_TextArea, .t_light .t_dark .t_green_active_TooltipContent, .t_light .t_dark .t_green_surface4 {--background:var(--c-green6Dark);--backgroundHover:var(--c-green6Dark);--backgroundPress:var(--c-green5Dark);--backgroundFocus:var(--c-green5Dark);--borderColor:var(--c-green6Dark);--borderColorHover:var(--c-green6Dark);--borderColorFocus:var(--c-green5Dark);--borderColorPress:var(--c-green5Dark);}
  }
:root.t_dark .t_blue_alt1, :root.t_dark .t_light .t_dark .t_blue_alt1, :root.t_light .t_dark .t_blue_alt1, :root.t_light .t_dark .t_light .t_dark .t_blue_alt1, .tm_xxt {--color:var(--c-blue11Dark);--colorHover:var(--c-blue10Dark);--colorPress:var(--c-blue11Dark);--colorFocus:var(--c-blue10Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_blue_alt1, .t_light .t_dark .t_blue_alt1 {--color:var(--c-blue11Dark);--colorHover:var(--c-blue10Dark);--colorPress:var(--c-blue11Dark);--colorFocus:var(--c-blue10Dark);}
  }
:root.t_dark .t_blue_alt2, :root.t_dark .t_light .t_dark .t_blue_alt2, :root.t_light .t_dark .t_blue_alt2, :root.t_light .t_dark .t_light .t_dark .t_blue_alt2, .tm_xxt {--color:var(--c-blue10Dark);--colorHover:var(--c-blue9Dark);--colorPress:var(--c-blue10Dark);--colorFocus:var(--c-blue9Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_blue_alt2, .t_light .t_dark .t_blue_alt2 {--color:var(--c-blue10Dark);--colorHover:var(--c-blue9Dark);--colorPress:var(--c-blue10Dark);--colorFocus:var(--c-blue9Dark);}
  }
:root.t_dark .t_blue_Button, :root.t_dark .t_blue_SliderTrackActive, :root.t_dark .t_blue_active, :root.t_dark .t_blue_active_SliderTrackActive, :root.t_dark .t_blue_surface3, :root.t_dark .t_light .t_dark .t_blue_Button, :root.t_dark .t_light .t_dark .t_blue_SliderTrackActive, :root.t_dark .t_light .t_dark .t_blue_active, :root.t_dark .t_light .t_dark .t_blue_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_blue_surface3, :root.t_light .t_dark .t_blue_Button, :root.t_light .t_dark .t_blue_SliderTrackActive, :root.t_light .t_dark .t_blue_active, :root.t_light .t_dark .t_blue_active_SliderTrackActive, :root.t_light .t_dark .t_blue_surface3, :root.t_light .t_dark .t_light .t_dark .t_blue_Button, :root.t_light .t_dark .t_light .t_dark .t_blue_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_blue_active, :root.t_light .t_dark .t_light .t_dark .t_blue_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_blue_surface3, .tm_xxt {--background:var(--c-blue4Dark);--backgroundHover:var(--c-blue5Dark);--backgroundPress:var(--c-blue3Dark);--backgroundFocus:var(--c-blue3Dark);--borderColor:var(--c-blue7Dark);--borderColorHover:var(--c-blue8Dark);--borderColorFocus:var(--c-blue7Dark);--borderColorPress:var(--c-blue6Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_blue_Button, .t_blue_SliderTrackActive, .t_blue_active, .t_blue_active_SliderTrackActive, .t_blue_surface3, .t_light .t_dark .t_blue_Button, .t_light .t_dark .t_blue_SliderTrackActive, .t_light .t_dark .t_blue_active, .t_light .t_dark .t_blue_active_SliderTrackActive, .t_light .t_dark .t_blue_surface3 {--background:var(--c-blue4Dark);--backgroundHover:var(--c-blue5Dark);--backgroundPress:var(--c-blue3Dark);--backgroundFocus:var(--c-blue3Dark);--borderColor:var(--c-blue7Dark);--borderColorHover:var(--c-blue8Dark);--borderColorFocus:var(--c-blue7Dark);--borderColorPress:var(--c-blue6Dark);}
  }
:root.t_dark .t_blue_Card, :root.t_dark .t_blue_Input, :root.t_dark .t_blue_ListItem, :root.t_dark .t_blue_Progress, :root.t_dark .t_blue_SelectTrigger, :root.t_dark .t_blue_SliderTrack, :root.t_dark .t_blue_TextArea, :root.t_dark .t_blue_TooltipArrow, :root.t_dark .t_blue_active_ListItem, :root.t_dark .t_blue_active_Progress, :root.t_dark .t_blue_active_SliderTrack, :root.t_dark .t_blue_active_TooltipArrow, :root.t_dark .t_blue_surface1, :root.t_dark .t_light .t_dark .t_blue_Card, :root.t_dark .t_light .t_dark .t_blue_Input, :root.t_dark .t_light .t_dark .t_blue_ListItem, :root.t_dark .t_light .t_dark .t_blue_Progress, :root.t_dark .t_light .t_dark .t_blue_SelectTrigger, :root.t_dark .t_light .t_dark .t_blue_SliderTrack, :root.t_dark .t_light .t_dark .t_blue_TextArea, :root.t_dark .t_light .t_dark .t_blue_TooltipArrow, :root.t_dark .t_light .t_dark .t_blue_active_ListItem, :root.t_dark .t_light .t_dark .t_blue_active_Progress, :root.t_dark .t_light .t_dark .t_blue_active_SliderTrack, :root.t_dark .t_light .t_dark .t_blue_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_blue_surface1, :root.t_light .t_dark .t_blue_Card, :root.t_light .t_dark .t_blue_Input, :root.t_light .t_dark .t_blue_ListItem, :root.t_light .t_dark .t_blue_Progress, :root.t_light .t_dark .t_blue_SelectTrigger, :root.t_light .t_dark .t_blue_SliderTrack, :root.t_light .t_dark .t_blue_TextArea, :root.t_light .t_dark .t_blue_TooltipArrow, :root.t_light .t_dark .t_blue_active_ListItem, :root.t_light .t_dark .t_blue_active_Progress, :root.t_light .t_dark .t_blue_active_SliderTrack, :root.t_light .t_dark .t_blue_active_TooltipArrow, :root.t_light .t_dark .t_blue_surface1, :root.t_light .t_dark .t_light .t_dark .t_blue_Card, :root.t_light .t_dark .t_light .t_dark .t_blue_Input, :root.t_light .t_dark .t_light .t_dark .t_blue_ListItem, :root.t_light .t_dark .t_light .t_dark .t_blue_Progress, :root.t_light .t_dark .t_light .t_dark .t_blue_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_blue_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_blue_TextArea, :root.t_light .t_dark .t_light .t_dark .t_blue_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_blue_active_ListItem, :root.t_light .t_dark .t_light .t_dark .t_blue_active_Progress, :root.t_light .t_dark .t_light .t_dark .t_blue_active_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_blue_active_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_blue_surface1, .tm_xxt {--background:var(--c-blue2Dark);--backgroundHover:var(--c-blue3Dark);--backgroundPress:var(--c-blue1Dark);--backgroundFocus:var(--c-blue1Dark);--borderColor:var(--c-blue5Dark);--borderColorHover:var(--c-blue6Dark);--borderColorFocus:var(--c-blue5Dark);--borderColorPress:var(--c-blue4Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_blue_Card, .t_blue_Input, .t_blue_ListItem, .t_blue_Progress, .t_blue_SelectTrigger, .t_blue_SliderTrack, .t_blue_TextArea, .t_blue_TooltipArrow, .t_blue_active_ListItem, .t_blue_active_Progress, .t_blue_active_SliderTrack, .t_blue_active_TooltipArrow, .t_blue_surface1, .t_light .t_dark .t_blue_Card, .t_light .t_dark .t_blue_Input, .t_light .t_dark .t_blue_ListItem, .t_light .t_dark .t_blue_Progress, .t_light .t_dark .t_blue_SelectTrigger, .t_light .t_dark .t_blue_SliderTrack, .t_light .t_dark .t_blue_TextArea, .t_light .t_dark .t_blue_TooltipArrow, .t_light .t_dark .t_blue_active_ListItem, .t_light .t_dark .t_blue_active_Progress, .t_light .t_dark .t_blue_active_SliderTrack, .t_light .t_dark .t_blue_active_TooltipArrow, .t_light .t_dark .t_blue_surface1 {--background:var(--c-blue2Dark);--backgroundHover:var(--c-blue3Dark);--backgroundPress:var(--c-blue1Dark);--backgroundFocus:var(--c-blue1Dark);--borderColor:var(--c-blue5Dark);--borderColorHover:var(--c-blue6Dark);--borderColorFocus:var(--c-blue5Dark);--borderColorPress:var(--c-blue4Dark);}
  }
:root.t_dark .t_blue_Checkbox, :root.t_dark .t_blue_RadioGroupItem, :root.t_dark .t_blue_Switch, :root.t_dark .t_blue_TooltipContent, :root.t_dark .t_blue_surface2, :root.t_dark .t_light .t_dark .t_blue_Checkbox, :root.t_dark .t_light .t_dark .t_blue_RadioGroupItem, :root.t_dark .t_light .t_dark .t_blue_Switch, :root.t_dark .t_light .t_dark .t_blue_TooltipContent, :root.t_dark .t_light .t_dark .t_blue_surface2, :root.t_light .t_dark .t_blue_Checkbox, :root.t_light .t_dark .t_blue_RadioGroupItem, :root.t_light .t_dark .t_blue_Switch, :root.t_light .t_dark .t_blue_TooltipContent, :root.t_light .t_dark .t_blue_surface2, :root.t_light .t_dark .t_light .t_dark .t_blue_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_blue_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_blue_Switch, :root.t_light .t_dark .t_light .t_dark .t_blue_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_blue_surface2, .tm_xxt {--background:var(--c-blue3Dark);--backgroundHover:var(--c-blue4Dark);--backgroundPress:var(--c-blue2Dark);--backgroundFocus:var(--c-blue2Dark);--borderColor:var(--c-blue6Dark);--borderColorHover:var(--c-blue7Dark);--borderColorFocus:var(--c-blue6Dark);--borderColorPress:var(--c-blue5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_blue_Checkbox, .t_blue_RadioGroupItem, .t_blue_Switch, .t_blue_TooltipContent, .t_blue_surface2, .t_light .t_dark .t_blue_Checkbox, .t_light .t_dark .t_blue_RadioGroupItem, .t_light .t_dark .t_blue_Switch, .t_light .t_dark .t_blue_TooltipContent, .t_light .t_dark .t_blue_surface2 {--background:var(--c-blue3Dark);--backgroundHover:var(--c-blue4Dark);--backgroundPress:var(--c-blue2Dark);--backgroundFocus:var(--c-blue2Dark);--borderColor:var(--c-blue6Dark);--borderColorHover:var(--c-blue7Dark);--borderColorFocus:var(--c-blue6Dark);--borderColorPress:var(--c-blue5Dark);}
  }
:root.t_dark .t_blue_active_Button, :root.t_dark .t_blue_active_Card, :root.t_dark .t_blue_active_Checkbox, :root.t_dark .t_blue_active_Input, :root.t_dark .t_blue_active_RadioGroupItem, :root.t_dark .t_blue_active_SelectTrigger, :root.t_dark .t_blue_active_Switch, :root.t_dark .t_blue_active_TextArea, :root.t_dark .t_blue_active_TooltipContent, :root.t_dark .t_blue_surface4, :root.t_dark .t_light .t_dark .t_blue_active_Button, :root.t_dark .t_light .t_dark .t_blue_active_Card, :root.t_dark .t_light .t_dark .t_blue_active_Checkbox, :root.t_dark .t_light .t_dark .t_blue_active_Input, :root.t_dark .t_light .t_dark .t_blue_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_blue_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_blue_active_Switch, :root.t_dark .t_light .t_dark .t_blue_active_TextArea, :root.t_dark .t_light .t_dark .t_blue_active_TooltipContent, :root.t_dark .t_light .t_dark .t_blue_surface4, :root.t_light .t_dark .t_blue_active_Button, :root.t_light .t_dark .t_blue_active_Card, :root.t_light .t_dark .t_blue_active_Checkbox, :root.t_light .t_dark .t_blue_active_Input, :root.t_light .t_dark .t_blue_active_RadioGroupItem, :root.t_light .t_dark .t_blue_active_SelectTrigger, :root.t_light .t_dark .t_blue_active_Switch, :root.t_light .t_dark .t_blue_active_TextArea, :root.t_light .t_dark .t_blue_active_TooltipContent, :root.t_light .t_dark .t_blue_surface4, :root.t_light .t_dark .t_light .t_dark .t_blue_active_Button, :root.t_light .t_dark .t_light .t_dark .t_blue_active_Card, :root.t_light .t_dark .t_light .t_dark .t_blue_active_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_blue_active_Input, :root.t_light .t_dark .t_light .t_dark .t_blue_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_blue_active_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_blue_active_Switch, :root.t_light .t_dark .t_light .t_dark .t_blue_active_TextArea, :root.t_light .t_dark .t_light .t_dark .t_blue_active_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_blue_surface4, .tm_xxt {--background:var(--c-blue6Dark);--backgroundHover:var(--c-blue6Dark);--backgroundPress:var(--c-blue5Dark);--backgroundFocus:var(--c-blue5Dark);--borderColor:var(--c-blue6Dark);--borderColorHover:var(--c-blue6Dark);--borderColorFocus:var(--c-blue5Dark);--borderColorPress:var(--c-blue5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_blue_active_Button, .t_blue_active_Card, .t_blue_active_Checkbox, .t_blue_active_Input, .t_blue_active_RadioGroupItem, .t_blue_active_SelectTrigger, .t_blue_active_Switch, .t_blue_active_TextArea, .t_blue_active_TooltipContent, .t_blue_surface4, .t_light .t_dark .t_blue_active_Button, .t_light .t_dark .t_blue_active_Card, .t_light .t_dark .t_blue_active_Checkbox, .t_light .t_dark .t_blue_active_Input, .t_light .t_dark .t_blue_active_RadioGroupItem, .t_light .t_dark .t_blue_active_SelectTrigger, .t_light .t_dark .t_blue_active_Switch, .t_light .t_dark .t_blue_active_TextArea, .t_light .t_dark .t_blue_active_TooltipContent, .t_light .t_dark .t_blue_surface4 {--background:var(--c-blue6Dark);--backgroundHover:var(--c-blue6Dark);--backgroundPress:var(--c-blue5Dark);--backgroundFocus:var(--c-blue5Dark);--borderColor:var(--c-blue6Dark);--borderColorHover:var(--c-blue6Dark);--borderColorFocus:var(--c-blue5Dark);--borderColorPress:var(--c-blue5Dark);}
  }
:root.t_dark .t_light .t_dark .t_purple_alt1, :root.t_dark .t_purple_alt1, :root.t_light .t_dark .t_light .t_dark .t_purple_alt1, :root.t_light .t_dark .t_purple_alt1, .tm_xxt {--color:var(--c-purple11Dark);--colorHover:var(--c-purple10Dark);--colorPress:var(--c-purple11Dark);--colorFocus:var(--c-purple10Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_light .t_dark .t_purple_alt1, .t_purple_alt1 {--color:var(--c-purple11Dark);--colorHover:var(--c-purple10Dark);--colorPress:var(--c-purple11Dark);--colorFocus:var(--c-purple10Dark);}
  }
:root.t_dark .t_light .t_dark .t_purple_alt2, :root.t_dark .t_purple_alt2, :root.t_light .t_dark .t_light .t_dark .t_purple_alt2, :root.t_light .t_dark .t_purple_alt2, .tm_xxt {--color:var(--c-purple10Dark);--colorHover:var(--c-purple9Dark);--colorPress:var(--c-purple10Dark);--colorFocus:var(--c-purple9Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_light .t_dark .t_purple_alt2, .t_purple_alt2 {--color:var(--c-purple10Dark);--colorHover:var(--c-purple9Dark);--colorPress:var(--c-purple10Dark);--colorFocus:var(--c-purple9Dark);}
  }
:root.t_dark .t_light .t_dark .t_purple_Button, :root.t_dark .t_light .t_dark .t_purple_SliderTrackActive, :root.t_dark .t_light .t_dark .t_purple_active, :root.t_dark .t_light .t_dark .t_purple_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_purple_surface3, :root.t_dark .t_purple_Button, :root.t_dark .t_purple_SliderTrackActive, :root.t_dark .t_purple_active, :root.t_dark .t_purple_active_SliderTrackActive, :root.t_dark .t_purple_surface3, :root.t_light .t_dark .t_light .t_dark .t_purple_Button, :root.t_light .t_dark .t_light .t_dark .t_purple_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_purple_active, :root.t_light .t_dark .t_light .t_dark .t_purple_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_purple_surface3, :root.t_light .t_dark .t_purple_Button, :root.t_light .t_dark .t_purple_SliderTrackActive, :root.t_light .t_dark .t_purple_active, :root.t_light .t_dark .t_purple_active_SliderTrackActive, :root.t_light .t_dark .t_purple_surface3, .tm_xxt {--background:var(--c-purple4Dark);--backgroundHover:var(--c-purple5Dark);--backgroundPress:var(--c-purple3Dark);--backgroundFocus:var(--c-purple3Dark);--borderColor:var(--c-purple7Dark);--borderColorHover:var(--c-purple8Dark);--borderColorFocus:var(--c-purple7Dark);--borderColorPress:var(--c-purple6Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_purple_Button, .t_light .t_dark .t_purple_SliderTrackActive, .t_light .t_dark .t_purple_active, .t_light .t_dark .t_purple_active_SliderTrackActive, .t_light .t_dark .t_purple_surface3, .t_purple_Button, .t_purple_SliderTrackActive, .t_purple_active, .t_purple_active_SliderTrackActive, .t_purple_surface3 {--background:var(--c-purple4Dark);--backgroundHover:var(--c-purple5Dark);--backgroundPress:var(--c-purple3Dark);--backgroundFocus:var(--c-purple3Dark);--borderColor:var(--c-purple7Dark);--borderColorHover:var(--c-purple8Dark);--borderColorFocus:var(--c-purple7Dark);--borderColorPress:var(--c-purple6Dark);}
  }
:root.t_dark .t_light .t_dark .t_purple_Card, :root.t_dark .t_light .t_dark .t_purple_Input, :root.t_dark .t_light .t_dark .t_purple_ListItem, :root.t_dark .t_light .t_dark .t_purple_Progress, :root.t_dark .t_light .t_dark .t_purple_SelectTrigger, :root.t_dark .t_light .t_dark .t_purple_SliderTrack, :root.t_dark .t_light .t_dark .t_purple_TextArea, :root.t_dark .t_light .t_dark .t_purple_TooltipArrow, :root.t_dark .t_light .t_dark .t_purple_active_ListItem, :root.t_dark .t_light .t_dark .t_purple_active_Progress, :root.t_dark .t_light .t_dark .t_purple_active_SliderTrack, :root.t_dark .t_light .t_dark .t_purple_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_purple_surface1, :root.t_dark .t_purple_Card, :root.t_dark .t_purple_Input, :root.t_dark .t_purple_ListItem, :root.t_dark .t_purple_Progress, :root.t_dark .t_purple_SelectTrigger, :root.t_dark .t_purple_SliderTrack, :root.t_dark .t_purple_TextArea, :root.t_dark .t_purple_TooltipArrow, :root.t_dark .t_purple_active_ListItem, :root.t_dark .t_purple_active_Progress, :root.t_dark .t_purple_active_SliderTrack, :root.t_dark .t_purple_active_TooltipArrow, :root.t_dark .t_purple_surface1, :root.t_light .t_dark .t_light .t_dark .t_purple_Card, :root.t_light .t_dark .t_light .t_dark .t_purple_Input, :root.t_light .t_dark .t_light .t_dark .t_purple_ListItem, :root.t_light .t_dark .t_light .t_dark .t_purple_Progress, :root.t_light .t_dark .t_light .t_dark .t_purple_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_purple_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_purple_TextArea, :root.t_light .t_dark .t_light .t_dark .t_purple_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_purple_active_ListItem, :root.t_light .t_dark .t_light .t_dark .t_purple_active_Progress, :root.t_light .t_dark .t_light .t_dark .t_purple_active_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_purple_active_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_purple_surface1, :root.t_light .t_dark .t_purple_Card, :root.t_light .t_dark .t_purple_Input, :root.t_light .t_dark .t_purple_ListItem, :root.t_light .t_dark .t_purple_Progress, :root.t_light .t_dark .t_purple_SelectTrigger, :root.t_light .t_dark .t_purple_SliderTrack, :root.t_light .t_dark .t_purple_TextArea, :root.t_light .t_dark .t_purple_TooltipArrow, :root.t_light .t_dark .t_purple_active_ListItem, :root.t_light .t_dark .t_purple_active_Progress, :root.t_light .t_dark .t_purple_active_SliderTrack, :root.t_light .t_dark .t_purple_active_TooltipArrow, :root.t_light .t_dark .t_purple_surface1, .tm_xxt {--background:var(--c-purple2Dark);--backgroundHover:var(--c-purple3Dark);--backgroundPress:var(--c-purple1Dark);--backgroundFocus:var(--c-purple1Dark);--borderColor:var(--c-purple5Dark);--borderColorHover:var(--c-purple6Dark);--borderColorFocus:var(--c-purple5Dark);--borderColorPress:var(--c-purple4Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_purple_Card, .t_light .t_dark .t_purple_Input, .t_light .t_dark .t_purple_ListItem, .t_light .t_dark .t_purple_Progress, .t_light .t_dark .t_purple_SelectTrigger, .t_light .t_dark .t_purple_SliderTrack, .t_light .t_dark .t_purple_TextArea, .t_light .t_dark .t_purple_TooltipArrow, .t_light .t_dark .t_purple_active_ListItem, .t_light .t_dark .t_purple_active_Progress, .t_light .t_dark .t_purple_active_SliderTrack, .t_light .t_dark .t_purple_active_TooltipArrow, .t_light .t_dark .t_purple_surface1, .t_purple_Card, .t_purple_Input, .t_purple_ListItem, .t_purple_Progress, .t_purple_SelectTrigger, .t_purple_SliderTrack, .t_purple_TextArea, .t_purple_TooltipArrow, .t_purple_active_ListItem, .t_purple_active_Progress, .t_purple_active_SliderTrack, .t_purple_active_TooltipArrow, .t_purple_surface1 {--background:var(--c-purple2Dark);--backgroundHover:var(--c-purple3Dark);--backgroundPress:var(--c-purple1Dark);--backgroundFocus:var(--c-purple1Dark);--borderColor:var(--c-purple5Dark);--borderColorHover:var(--c-purple6Dark);--borderColorFocus:var(--c-purple5Dark);--borderColorPress:var(--c-purple4Dark);}
  }
:root.t_dark .t_light .t_dark .t_purple_Checkbox, :root.t_dark .t_light .t_dark .t_purple_RadioGroupItem, :root.t_dark .t_light .t_dark .t_purple_Switch, :root.t_dark .t_light .t_dark .t_purple_TooltipContent, :root.t_dark .t_light .t_dark .t_purple_surface2, :root.t_dark .t_purple_Checkbox, :root.t_dark .t_purple_RadioGroupItem, :root.t_dark .t_purple_Switch, :root.t_dark .t_purple_TooltipContent, :root.t_dark .t_purple_surface2, :root.t_light .t_dark .t_light .t_dark .t_purple_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_purple_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_purple_Switch, :root.t_light .t_dark .t_light .t_dark .t_purple_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_purple_surface2, :root.t_light .t_dark .t_purple_Checkbox, :root.t_light .t_dark .t_purple_RadioGroupItem, :root.t_light .t_dark .t_purple_Switch, :root.t_light .t_dark .t_purple_TooltipContent, :root.t_light .t_dark .t_purple_surface2, .tm_xxt {--background:var(--c-purple3Dark);--backgroundHover:var(--c-purple4Dark);--backgroundPress:var(--c-purple2Dark);--backgroundFocus:var(--c-purple2Dark);--borderColor:var(--c-purple6Dark);--borderColorHover:var(--c-purple7Dark);--borderColorFocus:var(--c-purple6Dark);--borderColorPress:var(--c-purple5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_purple_Checkbox, .t_light .t_dark .t_purple_RadioGroupItem, .t_light .t_dark .t_purple_Switch, .t_light .t_dark .t_purple_TooltipContent, .t_light .t_dark .t_purple_surface2, .t_purple_Checkbox, .t_purple_RadioGroupItem, .t_purple_Switch, .t_purple_TooltipContent, .t_purple_surface2 {--background:var(--c-purple3Dark);--backgroundHover:var(--c-purple4Dark);--backgroundPress:var(--c-purple2Dark);--backgroundFocus:var(--c-purple2Dark);--borderColor:var(--c-purple6Dark);--borderColorHover:var(--c-purple7Dark);--borderColorFocus:var(--c-purple6Dark);--borderColorPress:var(--c-purple5Dark);}
  }
:root.t_dark .t_light .t_dark .t_purple_active_Button, :root.t_dark .t_light .t_dark .t_purple_active_Card, :root.t_dark .t_light .t_dark .t_purple_active_Checkbox, :root.t_dark .t_light .t_dark .t_purple_active_Input, :root.t_dark .t_light .t_dark .t_purple_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_purple_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_purple_active_Switch, :root.t_dark .t_light .t_dark .t_purple_active_TextArea, :root.t_dark .t_light .t_dark .t_purple_active_TooltipContent, :root.t_dark .t_light .t_dark .t_purple_surface4, :root.t_dark .t_purple_active_Button, :root.t_dark .t_purple_active_Card, :root.t_dark .t_purple_active_Checkbox, :root.t_dark .t_purple_active_Input, :root.t_dark .t_purple_active_RadioGroupItem, :root.t_dark .t_purple_active_SelectTrigger, :root.t_dark .t_purple_active_Switch, :root.t_dark .t_purple_active_TextArea, :root.t_dark .t_purple_active_TooltipContent, :root.t_dark .t_purple_surface4, :root.t_light .t_dark .t_light .t_dark .t_purple_active_Button, :root.t_light .t_dark .t_light .t_dark .t_purple_active_Card, :root.t_light .t_dark .t_light .t_dark .t_purple_active_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_purple_active_Input, :root.t_light .t_dark .t_light .t_dark .t_purple_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_purple_active_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_purple_active_Switch, :root.t_light .t_dark .t_light .t_dark .t_purple_active_TextArea, :root.t_light .t_dark .t_light .t_dark .t_purple_active_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_purple_surface4, :root.t_light .t_dark .t_purple_active_Button, :root.t_light .t_dark .t_purple_active_Card, :root.t_light .t_dark .t_purple_active_Checkbox, :root.t_light .t_dark .t_purple_active_Input, :root.t_light .t_dark .t_purple_active_RadioGroupItem, :root.t_light .t_dark .t_purple_active_SelectTrigger, :root.t_light .t_dark .t_purple_active_Switch, :root.t_light .t_dark .t_purple_active_TextArea, :root.t_light .t_dark .t_purple_active_TooltipContent, :root.t_light .t_dark .t_purple_surface4, .tm_xxt {--background:var(--c-purple6Dark);--backgroundHover:var(--c-purple6Dark);--backgroundPress:var(--c-purple5Dark);--backgroundFocus:var(--c-purple5Dark);--borderColor:var(--c-purple6Dark);--borderColorHover:var(--c-purple6Dark);--borderColorFocus:var(--c-purple5Dark);--borderColorPress:var(--c-purple5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_purple_active_Button, .t_light .t_dark .t_purple_active_Card, .t_light .t_dark .t_purple_active_Checkbox, .t_light .t_dark .t_purple_active_Input, .t_light .t_dark .t_purple_active_RadioGroupItem, .t_light .t_dark .t_purple_active_SelectTrigger, .t_light .t_dark .t_purple_active_Switch, .t_light .t_dark .t_purple_active_TextArea, .t_light .t_dark .t_purple_active_TooltipContent, .t_light .t_dark .t_purple_surface4, .t_purple_active_Button, .t_purple_active_Card, .t_purple_active_Checkbox, .t_purple_active_Input, .t_purple_active_RadioGroupItem, .t_purple_active_SelectTrigger, .t_purple_active_Switch, .t_purple_active_TextArea, .t_purple_active_TooltipContent, .t_purple_surface4 {--background:var(--c-purple6Dark);--backgroundHover:var(--c-purple6Dark);--backgroundPress:var(--c-purple5Dark);--backgroundFocus:var(--c-purple5Dark);--borderColor:var(--c-purple6Dark);--borderColorHover:var(--c-purple6Dark);--borderColorFocus:var(--c-purple5Dark);--borderColorPress:var(--c-purple5Dark);}
  }
:root.t_dark .t_light .t_dark .t_pink_alt1, :root.t_dark .t_pink_alt1, :root.t_light .t_dark .t_light .t_dark .t_pink_alt1, :root.t_light .t_dark .t_pink_alt1, .tm_xxt {--color:var(--c-pink11Dark);--colorHover:var(--c-pink10Dark);--colorPress:var(--c-pink11Dark);--colorFocus:var(--c-pink10Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_light .t_dark .t_pink_alt1, .t_pink_alt1 {--color:var(--c-pink11Dark);--colorHover:var(--c-pink10Dark);--colorPress:var(--c-pink11Dark);--colorFocus:var(--c-pink10Dark);}
  }
:root.t_dark .t_light .t_dark .t_pink_alt2, :root.t_dark .t_pink_alt2, :root.t_light .t_dark .t_light .t_dark .t_pink_alt2, :root.t_light .t_dark .t_pink_alt2, .tm_xxt {--color:var(--c-pink10Dark);--colorHover:var(--c-pink9Dark);--colorPress:var(--c-pink10Dark);--colorFocus:var(--c-pink9Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_light .t_dark .t_pink_alt2, .t_pink_alt2 {--color:var(--c-pink10Dark);--colorHover:var(--c-pink9Dark);--colorPress:var(--c-pink10Dark);--colorFocus:var(--c-pink9Dark);}
  }
:root.t_dark .t_light .t_dark .t_pink_Button, :root.t_dark .t_light .t_dark .t_pink_SliderTrackActive, :root.t_dark .t_light .t_dark .t_pink_active, :root.t_dark .t_light .t_dark .t_pink_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_pink_surface3, :root.t_dark .t_pink_Button, :root.t_dark .t_pink_SliderTrackActive, :root.t_dark .t_pink_active, :root.t_dark .t_pink_active_SliderTrackActive, :root.t_dark .t_pink_surface3, :root.t_light .t_dark .t_light .t_dark .t_pink_Button, :root.t_light .t_dark .t_light .t_dark .t_pink_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_pink_active, :root.t_light .t_dark .t_light .t_dark .t_pink_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_pink_surface3, :root.t_light .t_dark .t_pink_Button, :root.t_light .t_dark .t_pink_SliderTrackActive, :root.t_light .t_dark .t_pink_active, :root.t_light .t_dark .t_pink_active_SliderTrackActive, :root.t_light .t_dark .t_pink_surface3, .tm_xxt {--background:var(--c-pink4Dark);--backgroundHover:var(--c-pink5Dark);--backgroundPress:var(--c-pink3Dark);--backgroundFocus:var(--c-pink3Dark);--borderColor:var(--c-pink7Dark);--borderColorHover:var(--c-pink8Dark);--borderColorFocus:var(--c-pink7Dark);--borderColorPress:var(--c-pink6Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_pink_Button, .t_light .t_dark .t_pink_SliderTrackActive, .t_light .t_dark .t_pink_active, .t_light .t_dark .t_pink_active_SliderTrackActive, .t_light .t_dark .t_pink_surface3, .t_pink_Button, .t_pink_SliderTrackActive, .t_pink_active, .t_pink_active_SliderTrackActive, .t_pink_surface3 {--background:var(--c-pink4Dark);--backgroundHover:var(--c-pink5Dark);--backgroundPress:var(--c-pink3Dark);--backgroundFocus:var(--c-pink3Dark);--borderColor:var(--c-pink7Dark);--borderColorHover:var(--c-pink8Dark);--borderColorFocus:var(--c-pink7Dark);--borderColorPress:var(--c-pink6Dark);}
  }
:root.t_dark .t_light .t_dark .t_pink_Card, :root.t_dark .t_light .t_dark .t_pink_Input, :root.t_dark .t_light .t_dark .t_pink_ListItem, :root.t_dark .t_light .t_dark .t_pink_Progress, :root.t_dark .t_light .t_dark .t_pink_SelectTrigger, :root.t_dark .t_light .t_dark .t_pink_SliderTrack, :root.t_dark .t_light .t_dark .t_pink_TextArea, :root.t_dark .t_light .t_dark .t_pink_TooltipArrow, :root.t_dark .t_light .t_dark .t_pink_active_ListItem, :root.t_dark .t_light .t_dark .t_pink_active_Progress, :root.t_dark .t_light .t_dark .t_pink_active_SliderTrack, :root.t_dark .t_light .t_dark .t_pink_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_pink_surface1, :root.t_dark .t_pink_Card, :root.t_dark .t_pink_Input, :root.t_dark .t_pink_ListItem, :root.t_dark .t_pink_Progress, :root.t_dark .t_pink_SelectTrigger, :root.t_dark .t_pink_SliderTrack, :root.t_dark .t_pink_TextArea, :root.t_dark .t_pink_TooltipArrow, :root.t_dark .t_pink_active_ListItem, :root.t_dark .t_pink_active_Progress, :root.t_dark .t_pink_active_SliderTrack, :root.t_dark .t_pink_active_TooltipArrow, :root.t_dark .t_pink_surface1, :root.t_light .t_dark .t_light .t_dark .t_pink_Card, :root.t_light .t_dark .t_light .t_dark .t_pink_Input, :root.t_light .t_dark .t_light .t_dark .t_pink_ListItem, :root.t_light .t_dark .t_light .t_dark .t_pink_Progress, :root.t_light .t_dark .t_light .t_dark .t_pink_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_pink_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_pink_TextArea, :root.t_light .t_dark .t_light .t_dark .t_pink_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_pink_active_ListItem, :root.t_light .t_dark .t_light .t_dark .t_pink_active_Progress, :root.t_light .t_dark .t_light .t_dark .t_pink_active_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_pink_active_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_pink_surface1, :root.t_light .t_dark .t_pink_Card, :root.t_light .t_dark .t_pink_Input, :root.t_light .t_dark .t_pink_ListItem, :root.t_light .t_dark .t_pink_Progress, :root.t_light .t_dark .t_pink_SelectTrigger, :root.t_light .t_dark .t_pink_SliderTrack, :root.t_light .t_dark .t_pink_TextArea, :root.t_light .t_dark .t_pink_TooltipArrow, :root.t_light .t_dark .t_pink_active_ListItem, :root.t_light .t_dark .t_pink_active_Progress, :root.t_light .t_dark .t_pink_active_SliderTrack, :root.t_light .t_dark .t_pink_active_TooltipArrow, :root.t_light .t_dark .t_pink_surface1, .tm_xxt {--background:var(--c-pink2Dark);--backgroundHover:var(--c-pink3Dark);--backgroundPress:var(--c-pink1Dark);--backgroundFocus:var(--c-pink1Dark);--borderColor:var(--c-pink5Dark);--borderColorHover:var(--c-pink6Dark);--borderColorFocus:var(--c-pink5Dark);--borderColorPress:var(--c-pink4Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_pink_Card, .t_light .t_dark .t_pink_Input, .t_light .t_dark .t_pink_ListItem, .t_light .t_dark .t_pink_Progress, .t_light .t_dark .t_pink_SelectTrigger, .t_light .t_dark .t_pink_SliderTrack, .t_light .t_dark .t_pink_TextArea, .t_light .t_dark .t_pink_TooltipArrow, .t_light .t_dark .t_pink_active_ListItem, .t_light .t_dark .t_pink_active_Progress, .t_light .t_dark .t_pink_active_SliderTrack, .t_light .t_dark .t_pink_active_TooltipArrow, .t_light .t_dark .t_pink_surface1, .t_pink_Card, .t_pink_Input, .t_pink_ListItem, .t_pink_Progress, .t_pink_SelectTrigger, .t_pink_SliderTrack, .t_pink_TextArea, .t_pink_TooltipArrow, .t_pink_active_ListItem, .t_pink_active_Progress, .t_pink_active_SliderTrack, .t_pink_active_TooltipArrow, .t_pink_surface1 {--background:var(--c-pink2Dark);--backgroundHover:var(--c-pink3Dark);--backgroundPress:var(--c-pink1Dark);--backgroundFocus:var(--c-pink1Dark);--borderColor:var(--c-pink5Dark);--borderColorHover:var(--c-pink6Dark);--borderColorFocus:var(--c-pink5Dark);--borderColorPress:var(--c-pink4Dark);}
  }
:root.t_dark .t_light .t_dark .t_pink_Checkbox, :root.t_dark .t_light .t_dark .t_pink_RadioGroupItem, :root.t_dark .t_light .t_dark .t_pink_Switch, :root.t_dark .t_light .t_dark .t_pink_TooltipContent, :root.t_dark .t_light .t_dark .t_pink_surface2, :root.t_dark .t_pink_Checkbox, :root.t_dark .t_pink_RadioGroupItem, :root.t_dark .t_pink_Switch, :root.t_dark .t_pink_TooltipContent, :root.t_dark .t_pink_surface2, :root.t_light .t_dark .t_light .t_dark .t_pink_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_pink_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_pink_Switch, :root.t_light .t_dark .t_light .t_dark .t_pink_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_pink_surface2, :root.t_light .t_dark .t_pink_Checkbox, :root.t_light .t_dark .t_pink_RadioGroupItem, :root.t_light .t_dark .t_pink_Switch, :root.t_light .t_dark .t_pink_TooltipContent, :root.t_light .t_dark .t_pink_surface2, .tm_xxt {--background:var(--c-pink3Dark);--backgroundHover:var(--c-pink4Dark);--backgroundPress:var(--c-pink2Dark);--backgroundFocus:var(--c-pink2Dark);--borderColor:var(--c-pink6Dark);--borderColorHover:var(--c-pink7Dark);--borderColorFocus:var(--c-pink6Dark);--borderColorPress:var(--c-pink5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_pink_Checkbox, .t_light .t_dark .t_pink_RadioGroupItem, .t_light .t_dark .t_pink_Switch, .t_light .t_dark .t_pink_TooltipContent, .t_light .t_dark .t_pink_surface2, .t_pink_Checkbox, .t_pink_RadioGroupItem, .t_pink_Switch, .t_pink_TooltipContent, .t_pink_surface2 {--background:var(--c-pink3Dark);--backgroundHover:var(--c-pink4Dark);--backgroundPress:var(--c-pink2Dark);--backgroundFocus:var(--c-pink2Dark);--borderColor:var(--c-pink6Dark);--borderColorHover:var(--c-pink7Dark);--borderColorFocus:var(--c-pink6Dark);--borderColorPress:var(--c-pink5Dark);}
  }
:root.t_dark .t_light .t_dark .t_pink_active_Button, :root.t_dark .t_light .t_dark .t_pink_active_Card, :root.t_dark .t_light .t_dark .t_pink_active_Checkbox, :root.t_dark .t_light .t_dark .t_pink_active_Input, :root.t_dark .t_light .t_dark .t_pink_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_pink_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_pink_active_Switch, :root.t_dark .t_light .t_dark .t_pink_active_TextArea, :root.t_dark .t_light .t_dark .t_pink_active_TooltipContent, :root.t_dark .t_light .t_dark .t_pink_surface4, :root.t_dark .t_pink_active_Button, :root.t_dark .t_pink_active_Card, :root.t_dark .t_pink_active_Checkbox, :root.t_dark .t_pink_active_Input, :root.t_dark .t_pink_active_RadioGroupItem, :root.t_dark .t_pink_active_SelectTrigger, :root.t_dark .t_pink_active_Switch, :root.t_dark .t_pink_active_TextArea, :root.t_dark .t_pink_active_TooltipContent, :root.t_dark .t_pink_surface4, :root.t_light .t_dark .t_light .t_dark .t_pink_active_Button, :root.t_light .t_dark .t_light .t_dark .t_pink_active_Card, :root.t_light .t_dark .t_light .t_dark .t_pink_active_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_pink_active_Input, :root.t_light .t_dark .t_light .t_dark .t_pink_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_pink_active_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_pink_active_Switch, :root.t_light .t_dark .t_light .t_dark .t_pink_active_TextArea, :root.t_light .t_dark .t_light .t_dark .t_pink_active_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_pink_surface4, :root.t_light .t_dark .t_pink_active_Button, :root.t_light .t_dark .t_pink_active_Card, :root.t_light .t_dark .t_pink_active_Checkbox, :root.t_light .t_dark .t_pink_active_Input, :root.t_light .t_dark .t_pink_active_RadioGroupItem, :root.t_light .t_dark .t_pink_active_SelectTrigger, :root.t_light .t_dark .t_pink_active_Switch, :root.t_light .t_dark .t_pink_active_TextArea, :root.t_light .t_dark .t_pink_active_TooltipContent, :root.t_light .t_dark .t_pink_surface4, .tm_xxt {--background:var(--c-pink6Dark);--backgroundHover:var(--c-pink6Dark);--backgroundPress:var(--c-pink5Dark);--backgroundFocus:var(--c-pink5Dark);--borderColor:var(--c-pink6Dark);--borderColorHover:var(--c-pink6Dark);--borderColorFocus:var(--c-pink5Dark);--borderColorPress:var(--c-pink5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_pink_active_Button, .t_light .t_dark .t_pink_active_Card, .t_light .t_dark .t_pink_active_Checkbox, .t_light .t_dark .t_pink_active_Input, .t_light .t_dark .t_pink_active_RadioGroupItem, .t_light .t_dark .t_pink_active_SelectTrigger, .t_light .t_dark .t_pink_active_Switch, .t_light .t_dark .t_pink_active_TextArea, .t_light .t_dark .t_pink_active_TooltipContent, .t_light .t_dark .t_pink_surface4, .t_pink_active_Button, .t_pink_active_Card, .t_pink_active_Checkbox, .t_pink_active_Input, .t_pink_active_RadioGroupItem, .t_pink_active_SelectTrigger, .t_pink_active_Switch, .t_pink_active_TextArea, .t_pink_active_TooltipContent, .t_pink_surface4 {--background:var(--c-pink6Dark);--backgroundHover:var(--c-pink6Dark);--backgroundPress:var(--c-pink5Dark);--backgroundFocus:var(--c-pink5Dark);--borderColor:var(--c-pink6Dark);--borderColorHover:var(--c-pink6Dark);--borderColorFocus:var(--c-pink5Dark);--borderColorPress:var(--c-pink5Dark);}
  }
:root.t_dark .t_light .t_dark .t_red_alt1, :root.t_dark .t_red_alt1, :root.t_light .t_dark .t_light .t_dark .t_red_alt1, :root.t_light .t_dark .t_red_alt1, .tm_xxt {--color:var(--c-red11Dark);--colorHover:var(--c-red10Dark);--colorPress:var(--c-red11Dark);--colorFocus:var(--c-red10Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_light .t_dark .t_red_alt1, .t_red_alt1 {--color:var(--c-red11Dark);--colorHover:var(--c-red10Dark);--colorPress:var(--c-red11Dark);--colorFocus:var(--c-red10Dark);}
  }
:root.t_dark .t_light .t_dark .t_red_alt2, :root.t_dark .t_red_alt2, :root.t_light .t_dark .t_light .t_dark .t_red_alt2, :root.t_light .t_dark .t_red_alt2, .tm_xxt {--color:var(--c-red10Dark);--colorHover:var(--c-red9Dark);--colorPress:var(--c-red10Dark);--colorFocus:var(--c-red9Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_light .t_dark .t_red_alt2, .t_red_alt2 {--color:var(--c-red10Dark);--colorHover:var(--c-red9Dark);--colorPress:var(--c-red10Dark);--colorFocus:var(--c-red9Dark);}
  }
:root.t_dark .t_light .t_dark .t_red_Button, :root.t_dark .t_light .t_dark .t_red_SliderTrackActive, :root.t_dark .t_light .t_dark .t_red_active, :root.t_dark .t_light .t_dark .t_red_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_red_surface3, :root.t_dark .t_red_Button, :root.t_dark .t_red_SliderTrackActive, :root.t_dark .t_red_active, :root.t_dark .t_red_active_SliderTrackActive, :root.t_dark .t_red_surface3, :root.t_light .t_dark .t_light .t_dark .t_red_Button, :root.t_light .t_dark .t_light .t_dark .t_red_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_red_active, :root.t_light .t_dark .t_light .t_dark .t_red_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_red_surface3, :root.t_light .t_dark .t_red_Button, :root.t_light .t_dark .t_red_SliderTrackActive, :root.t_light .t_dark .t_red_active, :root.t_light .t_dark .t_red_active_SliderTrackActive, :root.t_light .t_dark .t_red_surface3, .tm_xxt {--background:var(--c-red4Dark);--backgroundHover:var(--c-red5Dark);--backgroundPress:var(--c-red3Dark);--backgroundFocus:var(--c-red3Dark);--borderColor:var(--c-red7Dark);--borderColorHover:var(--c-red8Dark);--borderColorFocus:var(--c-red7Dark);--borderColorPress:var(--c-red6Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_red_Button, .t_light .t_dark .t_red_SliderTrackActive, .t_light .t_dark .t_red_active, .t_light .t_dark .t_red_active_SliderTrackActive, .t_light .t_dark .t_red_surface3, .t_red_Button, .t_red_SliderTrackActive, .t_red_active, .t_red_active_SliderTrackActive, .t_red_surface3 {--background:var(--c-red4Dark);--backgroundHover:var(--c-red5Dark);--backgroundPress:var(--c-red3Dark);--backgroundFocus:var(--c-red3Dark);--borderColor:var(--c-red7Dark);--borderColorHover:var(--c-red8Dark);--borderColorFocus:var(--c-red7Dark);--borderColorPress:var(--c-red6Dark);}
  }
:root.t_dark .t_light .t_dark .t_red_Card, :root.t_dark .t_light .t_dark .t_red_Input, :root.t_dark .t_light .t_dark .t_red_ListItem, :root.t_dark .t_light .t_dark .t_red_Progress, :root.t_dark .t_light .t_dark .t_red_SelectTrigger, :root.t_dark .t_light .t_dark .t_red_SliderTrack, :root.t_dark .t_light .t_dark .t_red_TextArea, :root.t_dark .t_light .t_dark .t_red_TooltipArrow, :root.t_dark .t_light .t_dark .t_red_active_ListItem, :root.t_dark .t_light .t_dark .t_red_active_Progress, :root.t_dark .t_light .t_dark .t_red_active_SliderTrack, :root.t_dark .t_light .t_dark .t_red_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_red_surface1, :root.t_dark .t_red_Card, :root.t_dark .t_red_Input, :root.t_dark .t_red_ListItem, :root.t_dark .t_red_Progress, :root.t_dark .t_red_SelectTrigger, :root.t_dark .t_red_SliderTrack, :root.t_dark .t_red_TextArea, :root.t_dark .t_red_TooltipArrow, :root.t_dark .t_red_active_ListItem, :root.t_dark .t_red_active_Progress, :root.t_dark .t_red_active_SliderTrack, :root.t_dark .t_red_active_TooltipArrow, :root.t_dark .t_red_surface1, :root.t_light .t_dark .t_light .t_dark .t_red_Card, :root.t_light .t_dark .t_light .t_dark .t_red_Input, :root.t_light .t_dark .t_light .t_dark .t_red_ListItem, :root.t_light .t_dark .t_light .t_dark .t_red_Progress, :root.t_light .t_dark .t_light .t_dark .t_red_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_red_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_red_TextArea, :root.t_light .t_dark .t_light .t_dark .t_red_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_red_active_ListItem, :root.t_light .t_dark .t_light .t_dark .t_red_active_Progress, :root.t_light .t_dark .t_light .t_dark .t_red_active_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_red_active_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_red_surface1, :root.t_light .t_dark .t_red_Card, :root.t_light .t_dark .t_red_Input, :root.t_light .t_dark .t_red_ListItem, :root.t_light .t_dark .t_red_Progress, :root.t_light .t_dark .t_red_SelectTrigger, :root.t_light .t_dark .t_red_SliderTrack, :root.t_light .t_dark .t_red_TextArea, :root.t_light .t_dark .t_red_TooltipArrow, :root.t_light .t_dark .t_red_active_ListItem, :root.t_light .t_dark .t_red_active_Progress, :root.t_light .t_dark .t_red_active_SliderTrack, :root.t_light .t_dark .t_red_active_TooltipArrow, :root.t_light .t_dark .t_red_surface1, .tm_xxt {--background:var(--c-red2Dark);--backgroundHover:var(--c-red3Dark);--backgroundPress:var(--c-red1Dark);--backgroundFocus:var(--c-red1Dark);--borderColor:var(--c-red5Dark);--borderColorHover:var(--c-red6Dark);--borderColorFocus:var(--c-red5Dark);--borderColorPress:var(--c-red4Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_red_Card, .t_light .t_dark .t_red_Input, .t_light .t_dark .t_red_ListItem, .t_light .t_dark .t_red_Progress, .t_light .t_dark .t_red_SelectTrigger, .t_light .t_dark .t_red_SliderTrack, .t_light .t_dark .t_red_TextArea, .t_light .t_dark .t_red_TooltipArrow, .t_light .t_dark .t_red_active_ListItem, .t_light .t_dark .t_red_active_Progress, .t_light .t_dark .t_red_active_SliderTrack, .t_light .t_dark .t_red_active_TooltipArrow, .t_light .t_dark .t_red_surface1, .t_red_Card, .t_red_Input, .t_red_ListItem, .t_red_Progress, .t_red_SelectTrigger, .t_red_SliderTrack, .t_red_TextArea, .t_red_TooltipArrow, .t_red_active_ListItem, .t_red_active_Progress, .t_red_active_SliderTrack, .t_red_active_TooltipArrow, .t_red_surface1 {--background:var(--c-red2Dark);--backgroundHover:var(--c-red3Dark);--backgroundPress:var(--c-red1Dark);--backgroundFocus:var(--c-red1Dark);--borderColor:var(--c-red5Dark);--borderColorHover:var(--c-red6Dark);--borderColorFocus:var(--c-red5Dark);--borderColorPress:var(--c-red4Dark);}
  }
:root.t_dark .t_light .t_dark .t_red_Checkbox, :root.t_dark .t_light .t_dark .t_red_RadioGroupItem, :root.t_dark .t_light .t_dark .t_red_Switch, :root.t_dark .t_light .t_dark .t_red_TooltipContent, :root.t_dark .t_light .t_dark .t_red_surface2, :root.t_dark .t_red_Checkbox, :root.t_dark .t_red_RadioGroupItem, :root.t_dark .t_red_Switch, :root.t_dark .t_red_TooltipContent, :root.t_dark .t_red_surface2, :root.t_light .t_dark .t_light .t_dark .t_red_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_red_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_red_Switch, :root.t_light .t_dark .t_light .t_dark .t_red_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_red_surface2, :root.t_light .t_dark .t_red_Checkbox, :root.t_light .t_dark .t_red_RadioGroupItem, :root.t_light .t_dark .t_red_Switch, :root.t_light .t_dark .t_red_TooltipContent, :root.t_light .t_dark .t_red_surface2, .tm_xxt {--background:var(--c-red3Dark);--backgroundHover:var(--c-red4Dark);--backgroundPress:var(--c-red2Dark);--backgroundFocus:var(--c-red2Dark);--borderColor:var(--c-red6Dark);--borderColorHover:var(--c-red7Dark);--borderColorFocus:var(--c-red6Dark);--borderColorPress:var(--c-red5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_red_Checkbox, .t_light .t_dark .t_red_RadioGroupItem, .t_light .t_dark .t_red_Switch, .t_light .t_dark .t_red_TooltipContent, .t_light .t_dark .t_red_surface2, .t_red_Checkbox, .t_red_RadioGroupItem, .t_red_Switch, .t_red_TooltipContent, .t_red_surface2 {--background:var(--c-red3Dark);--backgroundHover:var(--c-red4Dark);--backgroundPress:var(--c-red2Dark);--backgroundFocus:var(--c-red2Dark);--borderColor:var(--c-red6Dark);--borderColorHover:var(--c-red7Dark);--borderColorFocus:var(--c-red6Dark);--borderColorPress:var(--c-red5Dark);}
  }
:root.t_dark .t_light .t_dark .t_red_active_Button, :root.t_dark .t_light .t_dark .t_red_active_Card, :root.t_dark .t_light .t_dark .t_red_active_Checkbox, :root.t_dark .t_light .t_dark .t_red_active_Input, :root.t_dark .t_light .t_dark .t_red_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_red_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_red_active_Switch, :root.t_dark .t_light .t_dark .t_red_active_TextArea, :root.t_dark .t_light .t_dark .t_red_active_TooltipContent, :root.t_dark .t_light .t_dark .t_red_surface4, :root.t_dark .t_red_active_Button, :root.t_dark .t_red_active_Card, :root.t_dark .t_red_active_Checkbox, :root.t_dark .t_red_active_Input, :root.t_dark .t_red_active_RadioGroupItem, :root.t_dark .t_red_active_SelectTrigger, :root.t_dark .t_red_active_Switch, :root.t_dark .t_red_active_TextArea, :root.t_dark .t_red_active_TooltipContent, :root.t_dark .t_red_surface4, :root.t_light .t_dark .t_light .t_dark .t_red_active_Button, :root.t_light .t_dark .t_light .t_dark .t_red_active_Card, :root.t_light .t_dark .t_light .t_dark .t_red_active_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_red_active_Input, :root.t_light .t_dark .t_light .t_dark .t_red_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_red_active_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_red_active_Switch, :root.t_light .t_dark .t_light .t_dark .t_red_active_TextArea, :root.t_light .t_dark .t_light .t_dark .t_red_active_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_red_surface4, :root.t_light .t_dark .t_red_active_Button, :root.t_light .t_dark .t_red_active_Card, :root.t_light .t_dark .t_red_active_Checkbox, :root.t_light .t_dark .t_red_active_Input, :root.t_light .t_dark .t_red_active_RadioGroupItem, :root.t_light .t_dark .t_red_active_SelectTrigger, :root.t_light .t_dark .t_red_active_Switch, :root.t_light .t_dark .t_red_active_TextArea, :root.t_light .t_dark .t_red_active_TooltipContent, :root.t_light .t_dark .t_red_surface4, .tm_xxt {--background:var(--c-red6Dark);--backgroundHover:var(--c-red6Dark);--backgroundPress:var(--c-red5Dark);--backgroundFocus:var(--c-red5Dark);--borderColor:var(--c-red6Dark);--borderColorHover:var(--c-red6Dark);--borderColorFocus:var(--c-red5Dark);--borderColorPress:var(--c-red5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_light .t_dark .t_red_active_Button, .t_light .t_dark .t_red_active_Card, .t_light .t_dark .t_red_active_Checkbox, .t_light .t_dark .t_red_active_Input, .t_light .t_dark .t_red_active_RadioGroupItem, .t_light .t_dark .t_red_active_SelectTrigger, .t_light .t_dark .t_red_active_Switch, .t_light .t_dark .t_red_active_TextArea, .t_light .t_dark .t_red_active_TooltipContent, .t_light .t_dark .t_red_surface4, .t_red_active_Button, .t_red_active_Card, .t_red_active_Checkbox, .t_red_active_Input, .t_red_active_RadioGroupItem, .t_red_active_SelectTrigger, .t_red_active_Switch, .t_red_active_TextArea, .t_red_active_TooltipContent, .t_red_surface4 {--background:var(--c-red6Dark);--backgroundHover:var(--c-red6Dark);--backgroundPress:var(--c-red5Dark);--backgroundFocus:var(--c-red5Dark);--borderColor:var(--c-red6Dark);--borderColorHover:var(--c-red6Dark);--borderColorFocus:var(--c-red5Dark);--borderColorPress:var(--c-red5Dark);}
  }
:root.t_dark .t_gray_alt1, :root.t_dark .t_light .t_dark .t_gray_alt1, :root.t_light .t_dark .t_gray_alt1, :root.t_light .t_dark .t_light .t_dark .t_gray_alt1, .tm_xxt {--color:var(--c-gray11Dark);--colorHover:var(--c-gray10Dark);--colorPress:var(--c-gray11Dark);--colorFocus:var(--c-gray10Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_gray_alt1, .t_light .t_dark .t_gray_alt1 {--color:var(--c-gray11Dark);--colorHover:var(--c-gray10Dark);--colorPress:var(--c-gray11Dark);--colorFocus:var(--c-gray10Dark);}
  }
:root.t_dark .t_gray_alt2, :root.t_dark .t_light .t_dark .t_gray_alt2, :root.t_light .t_dark .t_gray_alt2, :root.t_light .t_dark .t_light .t_dark .t_gray_alt2, .tm_xxt {--color:var(--c-gray10Dark);--colorHover:var(--c-gray9Dark);--colorPress:var(--c-gray10Dark);--colorFocus:var(--c-gray9Dark);}
@media(prefers-color-scheme:dark){
    body{color:var(--color)}
    .t_gray_alt2, .t_light .t_dark .t_gray_alt2 {--color:var(--c-gray10Dark);--colorHover:var(--c-gray9Dark);--colorPress:var(--c-gray10Dark);--colorFocus:var(--c-gray9Dark);}
  }
:root.t_dark .t_gray_Button, :root.t_dark .t_gray_SliderTrackActive, :root.t_dark .t_gray_active, :root.t_dark .t_gray_active_SliderTrackActive, :root.t_dark .t_gray_surface3, :root.t_dark .t_light .t_dark .t_gray_Button, :root.t_dark .t_light .t_dark .t_gray_SliderTrackActive, :root.t_dark .t_light .t_dark .t_gray_active, :root.t_dark .t_light .t_dark .t_gray_active_SliderTrackActive, :root.t_dark .t_light .t_dark .t_gray_surface3, :root.t_light .t_dark .t_gray_Button, :root.t_light .t_dark .t_gray_SliderTrackActive, :root.t_light .t_dark .t_gray_active, :root.t_light .t_dark .t_gray_active_SliderTrackActive, :root.t_light .t_dark .t_gray_surface3, :root.t_light .t_dark .t_light .t_dark .t_gray_Button, :root.t_light .t_dark .t_light .t_dark .t_gray_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_gray_active, :root.t_light .t_dark .t_light .t_dark .t_gray_active_SliderTrackActive, :root.t_light .t_dark .t_light .t_dark .t_gray_surface3, .tm_xxt {--background:var(--c-gray4Dark);--backgroundHover:var(--c-gray5Dark);--backgroundPress:var(--c-gray3Dark);--backgroundFocus:var(--c-gray3Dark);--borderColor:var(--c-gray7Dark);--borderColorHover:var(--c-gray8Dark);--borderColorFocus:var(--c-gray7Dark);--borderColorPress:var(--c-gray6Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_gray_Button, .t_gray_SliderTrackActive, .t_gray_active, .t_gray_active_SliderTrackActive, .t_gray_surface3, .t_light .t_dark .t_gray_Button, .t_light .t_dark .t_gray_SliderTrackActive, .t_light .t_dark .t_gray_active, .t_light .t_dark .t_gray_active_SliderTrackActive, .t_light .t_dark .t_gray_surface3 {--background:var(--c-gray4Dark);--backgroundHover:var(--c-gray5Dark);--backgroundPress:var(--c-gray3Dark);--backgroundFocus:var(--c-gray3Dark);--borderColor:var(--c-gray7Dark);--borderColorHover:var(--c-gray8Dark);--borderColorFocus:var(--c-gray7Dark);--borderColorPress:var(--c-gray6Dark);}
  }
:root.t_dark .t_gray_Card, :root.t_dark .t_gray_Input, :root.t_dark .t_gray_ListItem, :root.t_dark .t_gray_Progress, :root.t_dark .t_gray_SelectTrigger, :root.t_dark .t_gray_SliderTrack, :root.t_dark .t_gray_TextArea, :root.t_dark .t_gray_TooltipArrow, :root.t_dark .t_gray_active_ListItem, :root.t_dark .t_gray_active_Progress, :root.t_dark .t_gray_active_SliderTrack, :root.t_dark .t_gray_active_TooltipArrow, :root.t_dark .t_gray_surface1, :root.t_dark .t_light .t_dark .t_gray_Card, :root.t_dark .t_light .t_dark .t_gray_Input, :root.t_dark .t_light .t_dark .t_gray_ListItem, :root.t_dark .t_light .t_dark .t_gray_Progress, :root.t_dark .t_light .t_dark .t_gray_SelectTrigger, :root.t_dark .t_light .t_dark .t_gray_SliderTrack, :root.t_dark .t_light .t_dark .t_gray_TextArea, :root.t_dark .t_light .t_dark .t_gray_TooltipArrow, :root.t_dark .t_light .t_dark .t_gray_active_ListItem, :root.t_dark .t_light .t_dark .t_gray_active_Progress, :root.t_dark .t_light .t_dark .t_gray_active_SliderTrack, :root.t_dark .t_light .t_dark .t_gray_active_TooltipArrow, :root.t_dark .t_light .t_dark .t_gray_surface1, :root.t_light .t_dark .t_gray_Card, :root.t_light .t_dark .t_gray_Input, :root.t_light .t_dark .t_gray_ListItem, :root.t_light .t_dark .t_gray_Progress, :root.t_light .t_dark .t_gray_SelectTrigger, :root.t_light .t_dark .t_gray_SliderTrack, :root.t_light .t_dark .t_gray_TextArea, :root.t_light .t_dark .t_gray_TooltipArrow, :root.t_light .t_dark .t_gray_active_ListItem, :root.t_light .t_dark .t_gray_active_Progress, :root.t_light .t_dark .t_gray_active_SliderTrack, :root.t_light .t_dark .t_gray_active_TooltipArrow, :root.t_light .t_dark .t_gray_surface1, :root.t_light .t_dark .t_light .t_dark .t_gray_Card, :root.t_light .t_dark .t_light .t_dark .t_gray_Input, :root.t_light .t_dark .t_light .t_dark .t_gray_ListItem, :root.t_light .t_dark .t_light .t_dark .t_gray_Progress, :root.t_light .t_dark .t_light .t_dark .t_gray_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_gray_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_gray_TextArea, :root.t_light .t_dark .t_light .t_dark .t_gray_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_gray_active_ListItem, :root.t_light .t_dark .t_light .t_dark .t_gray_active_Progress, :root.t_light .t_dark .t_light .t_dark .t_gray_active_SliderTrack, :root.t_light .t_dark .t_light .t_dark .t_gray_active_TooltipArrow, :root.t_light .t_dark .t_light .t_dark .t_gray_surface1, .tm_xxt {--background:var(--c-gray2Dark);--backgroundHover:var(--c-gray3Dark);--backgroundPress:var(--c-gray1Dark);--backgroundFocus:var(--c-gray1Dark);--borderColor:var(--c-gray5Dark);--borderColorHover:var(--c-gray6Dark);--borderColorFocus:var(--c-gray5Dark);--borderColorPress:var(--c-gray4Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_gray_Card, .t_gray_Input, .t_gray_ListItem, .t_gray_Progress, .t_gray_SelectTrigger, .t_gray_SliderTrack, .t_gray_TextArea, .t_gray_TooltipArrow, .t_gray_active_ListItem, .t_gray_active_Progress, .t_gray_active_SliderTrack, .t_gray_active_TooltipArrow, .t_gray_surface1, .t_light .t_dark .t_gray_Card, .t_light .t_dark .t_gray_Input, .t_light .t_dark .t_gray_ListItem, .t_light .t_dark .t_gray_Progress, .t_light .t_dark .t_gray_SelectTrigger, .t_light .t_dark .t_gray_SliderTrack, .t_light .t_dark .t_gray_TextArea, .t_light .t_dark .t_gray_TooltipArrow, .t_light .t_dark .t_gray_active_ListItem, .t_light .t_dark .t_gray_active_Progress, .t_light .t_dark .t_gray_active_SliderTrack, .t_light .t_dark .t_gray_active_TooltipArrow, .t_light .t_dark .t_gray_surface1 {--background:var(--c-gray2Dark);--backgroundHover:var(--c-gray3Dark);--backgroundPress:var(--c-gray1Dark);--backgroundFocus:var(--c-gray1Dark);--borderColor:var(--c-gray5Dark);--borderColorHover:var(--c-gray6Dark);--borderColorFocus:var(--c-gray5Dark);--borderColorPress:var(--c-gray4Dark);}
  }
:root.t_dark .t_gray_Checkbox, :root.t_dark .t_gray_RadioGroupItem, :root.t_dark .t_gray_Switch, :root.t_dark .t_gray_TooltipContent, :root.t_dark .t_gray_surface2, :root.t_dark .t_light .t_dark .t_gray_Checkbox, :root.t_dark .t_light .t_dark .t_gray_RadioGroupItem, :root.t_dark .t_light .t_dark .t_gray_Switch, :root.t_dark .t_light .t_dark .t_gray_TooltipContent, :root.t_dark .t_light .t_dark .t_gray_surface2, :root.t_light .t_dark .t_gray_Checkbox, :root.t_light .t_dark .t_gray_RadioGroupItem, :root.t_light .t_dark .t_gray_Switch, :root.t_light .t_dark .t_gray_TooltipContent, :root.t_light .t_dark .t_gray_surface2, :root.t_light .t_dark .t_light .t_dark .t_gray_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_gray_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_gray_Switch, :root.t_light .t_dark .t_light .t_dark .t_gray_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_gray_surface2, .tm_xxt {--background:var(--c-gray3Dark);--backgroundHover:var(--c-gray4Dark);--backgroundPress:var(--c-gray2Dark);--backgroundFocus:var(--c-gray2Dark);--borderColor:var(--c-gray6Dark);--borderColorHover:var(--c-gray7Dark);--borderColorFocus:var(--c-gray6Dark);--borderColorPress:var(--c-gray5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_gray_Checkbox, .t_gray_RadioGroupItem, .t_gray_Switch, .t_gray_TooltipContent, .t_gray_surface2, .t_light .t_dark .t_gray_Checkbox, .t_light .t_dark .t_gray_RadioGroupItem, .t_light .t_dark .t_gray_Switch, .t_light .t_dark .t_gray_TooltipContent, .t_light .t_dark .t_gray_surface2 {--background:var(--c-gray3Dark);--backgroundHover:var(--c-gray4Dark);--backgroundPress:var(--c-gray2Dark);--backgroundFocus:var(--c-gray2Dark);--borderColor:var(--c-gray6Dark);--borderColorHover:var(--c-gray7Dark);--borderColorFocus:var(--c-gray6Dark);--borderColorPress:var(--c-gray5Dark);}
  }
:root.t_dark .t_gray_active_Button, :root.t_dark .t_gray_active_Card, :root.t_dark .t_gray_active_Checkbox, :root.t_dark .t_gray_active_Input, :root.t_dark .t_gray_active_RadioGroupItem, :root.t_dark .t_gray_active_SelectTrigger, :root.t_dark .t_gray_active_Switch, :root.t_dark .t_gray_active_TextArea, :root.t_dark .t_gray_active_TooltipContent, :root.t_dark .t_gray_surface4, :root.t_dark .t_light .t_dark .t_gray_active_Button, :root.t_dark .t_light .t_dark .t_gray_active_Card, :root.t_dark .t_light .t_dark .t_gray_active_Checkbox, :root.t_dark .t_light .t_dark .t_gray_active_Input, :root.t_dark .t_light .t_dark .t_gray_active_RadioGroupItem, :root.t_dark .t_light .t_dark .t_gray_active_SelectTrigger, :root.t_dark .t_light .t_dark .t_gray_active_Switch, :root.t_dark .t_light .t_dark .t_gray_active_TextArea, :root.t_dark .t_light .t_dark .t_gray_active_TooltipContent, :root.t_dark .t_light .t_dark .t_gray_surface4, :root.t_light .t_dark .t_gray_active_Button, :root.t_light .t_dark .t_gray_active_Card, :root.t_light .t_dark .t_gray_active_Checkbox, :root.t_light .t_dark .t_gray_active_Input, :root.t_light .t_dark .t_gray_active_RadioGroupItem, :root.t_light .t_dark .t_gray_active_SelectTrigger, :root.t_light .t_dark .t_gray_active_Switch, :root.t_light .t_dark .t_gray_active_TextArea, :root.t_light .t_dark .t_gray_active_TooltipContent, :root.t_light .t_dark .t_gray_surface4, :root.t_light .t_dark .t_light .t_dark .t_gray_active_Button, :root.t_light .t_dark .t_light .t_dark .t_gray_active_Card, :root.t_light .t_dark .t_light .t_dark .t_gray_active_Checkbox, :root.t_light .t_dark .t_light .t_dark .t_gray_active_Input, :root.t_light .t_dark .t_light .t_dark .t_gray_active_RadioGroupItem, :root.t_light .t_dark .t_light .t_dark .t_gray_active_SelectTrigger, :root.t_light .t_dark .t_light .t_dark .t_gray_active_Switch, :root.t_light .t_dark .t_light .t_dark .t_gray_active_TextArea, :root.t_light .t_dark .t_light .t_dark .t_gray_active_TooltipContent, :root.t_light .t_dark .t_light .t_dark .t_gray_surface4, .tm_xxt {--background:var(--c-gray6Dark);--backgroundHover:var(--c-gray6Dark);--backgroundPress:var(--c-gray5Dark);--backgroundFocus:var(--c-gray5Dark);--borderColor:var(--c-gray6Dark);--borderColorHover:var(--c-gray6Dark);--borderColorFocus:var(--c-gray5Dark);--borderColorPress:var(--c-gray5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_gray_active_Button, .t_gray_active_Card, .t_gray_active_Checkbox, .t_gray_active_Input, .t_gray_active_RadioGroupItem, .t_gray_active_SelectTrigger, .t_gray_active_Switch, .t_gray_active_TextArea, .t_gray_active_TooltipContent, .t_gray_surface4, .t_light .t_dark .t_gray_active_Button, .t_light .t_dark .t_gray_active_Card, .t_light .t_dark .t_gray_active_Checkbox, .t_light .t_dark .t_gray_active_Input, .t_light .t_dark .t_gray_active_RadioGroupItem, .t_light .t_dark .t_gray_active_SelectTrigger, .t_light .t_dark .t_gray_active_Switch, .t_light .t_dark .t_gray_active_TextArea, .t_light .t_dark .t_gray_active_TooltipContent, .t_light .t_dark .t_gray_surface4 {--background:var(--c-gray6Dark);--backgroundHover:var(--c-gray6Dark);--backgroundPress:var(--c-gray5Dark);--backgroundFocus:var(--c-gray5Dark);--borderColor:var(--c-gray6Dark);--borderColorHover:var(--c-gray6Dark);--borderColorFocus:var(--c-gray5Dark);--borderColorPress:var(--c-gray5Dark);}
  }
:root.t_dark .t_light .t_Button, :root.t_dark .t_light .t_Input, :root.t_dark .t_light .t_dark .t_light .t_Button, :root.t_dark .t_light .t_dark .t_light .t_Input, :root.t_light .t_Button, :root.t_light .t_Input, :root.t_light .t_dark .t_light .t_Button, :root.t_light .t_dark .t_light .t_Input, .tm_xxt {--background:#FFFFFF;--color:#000000;}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_Button, .t_Input, .t_dark .t_light .t_Button, .t_dark .t_light .t_Input {--background:#FFFFFF;--color:#000000;}
  }
:root.t_dark .t_light .t_ProgressIndicator, :root.t_dark .t_light .t_SliderThumb, :root.t_dark .t_light .t_SwitchThumb, :root.t_dark .t_light .t_Tooltip, :root.t_dark .t_light .t_dark .t_light .t_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_Tooltip, :root.t_light .t_ProgressIndicator, :root.t_light .t_SliderThumb, :root.t_light .t_SwitchThumb, :root.t_light .t_Tooltip, :root.t_light .t_dark .t_light .t_ProgressIndicator, :root.t_light .t_dark .t_light .t_SliderThumb, :root.t_light .t_dark .t_light .t_SwitchThumb, :root.t_light .t_dark .t_light .t_Tooltip, .tm_xxt {--color:var(--c-white2);--colorHover:var(--c-black12);--colorPress:var(--c-white3);--colorFocus:var(--c-white3);--background:var(--c-gray12Light);--backgroundHover:var(--c-white11);--backgroundPress:var(--c-gray12Light);--backgroundFocus:var(--c-white11);--borderColor:var(--c-white10);--borderColorHover:var(--c-gray9Light);--borderColorFocus:var(--c-white8);--borderColorPress:var(--c-white7);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_ProgressIndicator, .t_SliderThumb, .t_SwitchThumb, .t_Tooltip, .t_dark .t_light .t_ProgressIndicator, .t_dark .t_light .t_SliderThumb, .t_dark .t_light .t_SwitchThumb, .t_dark .t_light .t_Tooltip {--color:var(--c-white2);--colorHover:var(--c-black12);--colorPress:var(--c-white3);--colorFocus:var(--c-white3);--background:var(--c-gray12Light);--backgroundHover:var(--c-white11);--backgroundPress:var(--c-gray12Light);--backgroundFocus:var(--c-white11);--borderColor:var(--c-white10);--borderColorHover:var(--c-gray9Light);--borderColorFocus:var(--c-white8);--borderColorPress:var(--c-white7);}
  }
:root.t_dark .t_light .t_DialogOverlay, :root.t_dark .t_light .t_ModalOverlay, :root.t_dark .t_light .t_SheetOverlay, :root.t_dark .t_light .t_active_DialogOverlay, :root.t_dark .t_light .t_active_ModalOverlay, :root.t_dark .t_light .t_active_SheetOverlay, :root.t_dark .t_light .t_blue_DialogOverlay, :root.t_dark .t_light .t_blue_ModalOverlay, :root.t_dark .t_light .t_blue_SheetOverlay, :root.t_dark .t_light .t_blue_active_DialogOverlay, :root.t_dark .t_light .t_blue_active_ModalOverlay, :root.t_dark .t_light .t_blue_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_blue_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_blue_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_blue_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_blue_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_blue_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_blue_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_gray_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_gray_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_gray_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_gray_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_gray_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_gray_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_green_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_green_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_green_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_green_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_green_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_green_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_orange_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_orange_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_orange_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_orange_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_orange_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_orange_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_pink_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_pink_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_pink_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_pink_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_pink_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_pink_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_purple_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_purple_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_purple_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_purple_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_purple_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_purple_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_red_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_red_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_red_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_red_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_red_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_red_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_yellow_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_yellow_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_yellow_SheetOverlay, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_SheetOverlay, :root.t_dark .t_light .t_gray_DialogOverlay, :root.t_dark .t_light .t_gray_ModalOverlay, :root.t_dark .t_light .t_gray_SheetOverlay, :root.t_dark .t_light .t_gray_active_DialogOverlay, :root.t_dark .t_light .t_gray_active_ModalOverlay, :root.t_dark .t_light .t_gray_active_SheetOverlay, :root.t_dark .t_light .t_green_DialogOverlay, :root.t_dark .t_light .t_green_ModalOverlay, :root.t_dark .t_light .t_green_SheetOverlay, :root.t_dark .t_light .t_green_active_DialogOverlay, :root.t_dark .t_light .t_green_active_ModalOverlay, :root.t_dark .t_light .t_green_active_SheetOverlay, :root.t_dark .t_light .t_orange_DialogOverlay, :root.t_dark .t_light .t_orange_ModalOverlay, :root.t_dark .t_light .t_orange_SheetOverlay, :root.t_dark .t_light .t_orange_active_DialogOverlay, :root.t_dark .t_light .t_orange_active_ModalOverlay, :root.t_dark .t_light .t_orange_active_SheetOverlay, :root.t_dark .t_light .t_pink_DialogOverlay, :root.t_dark .t_light .t_pink_ModalOverlay, :root.t_dark .t_light .t_pink_SheetOverlay, :root.t_dark .t_light .t_pink_active_DialogOverlay, :root.t_dark .t_light .t_pink_active_ModalOverlay, :root.t_dark .t_light .t_pink_active_SheetOverlay, :root.t_dark .t_light .t_purple_DialogOverlay, :root.t_dark .t_light .t_purple_ModalOverlay, :root.t_dark .t_light .t_purple_SheetOverlay, :root.t_dark .t_light .t_purple_active_DialogOverlay, :root.t_dark .t_light .t_purple_active_ModalOverlay, :root.t_dark .t_light .t_purple_active_SheetOverlay, :root.t_dark .t_light .t_red_DialogOverlay, :root.t_dark .t_light .t_red_ModalOverlay, :root.t_dark .t_light .t_red_SheetOverlay, :root.t_dark .t_light .t_red_active_DialogOverlay, :root.t_dark .t_light .t_red_active_ModalOverlay, :root.t_dark .t_light .t_red_active_SheetOverlay, :root.t_dark .t_light .t_yellow_DialogOverlay, :root.t_dark .t_light .t_yellow_ModalOverlay, :root.t_dark .t_light .t_yellow_SheetOverlay, :root.t_dark .t_light .t_yellow_active_DialogOverlay, :root.t_dark .t_light .t_yellow_active_ModalOverlay, :root.t_dark .t_light .t_yellow_active_SheetOverlay, :root.t_light .t_DialogOverlay, :root.t_light .t_ModalOverlay, :root.t_light .t_SheetOverlay, :root.t_light .t_active_DialogOverlay, :root.t_light .t_active_ModalOverlay, :root.t_light .t_active_SheetOverlay, :root.t_light .t_blue_DialogOverlay, :root.t_light .t_blue_ModalOverlay, :root.t_light .t_blue_SheetOverlay, :root.t_light .t_blue_active_DialogOverlay, :root.t_light .t_blue_active_ModalOverlay, :root.t_light .t_blue_active_SheetOverlay, :root.t_light .t_dark .t_light .t_DialogOverlay, :root.t_light .t_dark .t_light .t_ModalOverlay, :root.t_light .t_dark .t_light .t_SheetOverlay, :root.t_light .t_dark .t_light .t_active_DialogOverlay, :root.t_light .t_dark .t_light .t_active_ModalOverlay, :root.t_light .t_dark .t_light .t_active_SheetOverlay, :root.t_light .t_dark .t_light .t_blue_DialogOverlay, :root.t_light .t_dark .t_light .t_blue_ModalOverlay, :root.t_light .t_dark .t_light .t_blue_SheetOverlay, :root.t_light .t_dark .t_light .t_blue_active_DialogOverlay, :root.t_light .t_dark .t_light .t_blue_active_ModalOverlay, :root.t_light .t_dark .t_light .t_blue_active_SheetOverlay, :root.t_light .t_dark .t_light .t_gray_DialogOverlay, :root.t_light .t_dark .t_light .t_gray_ModalOverlay, :root.t_light .t_dark .t_light .t_gray_SheetOverlay, :root.t_light .t_dark .t_light .t_gray_active_DialogOverlay, :root.t_light .t_dark .t_light .t_gray_active_ModalOverlay, :root.t_light .t_dark .t_light .t_gray_active_SheetOverlay, :root.t_light .t_dark .t_light .t_green_DialogOverlay, :root.t_light .t_dark .t_light .t_green_ModalOverlay, :root.t_light .t_dark .t_light .t_green_SheetOverlay, :root.t_light .t_dark .t_light .t_green_active_DialogOverlay, :root.t_light .t_dark .t_light .t_green_active_ModalOverlay, :root.t_light .t_dark .t_light .t_green_active_SheetOverlay, :root.t_light .t_dark .t_light .t_orange_DialogOverlay, :root.t_light .t_dark .t_light .t_orange_ModalOverlay, :root.t_light .t_dark .t_light .t_orange_SheetOverlay, :root.t_light .t_dark .t_light .t_orange_active_DialogOverlay, :root.t_light .t_dark .t_light .t_orange_active_ModalOverlay, :root.t_light .t_dark .t_light .t_orange_active_SheetOverlay, :root.t_light .t_dark .t_light .t_pink_DialogOverlay, :root.t_light .t_dark .t_light .t_pink_ModalOverlay, :root.t_light .t_dark .t_light .t_pink_SheetOverlay, :root.t_light .t_dark .t_light .t_pink_active_DialogOverlay, :root.t_light .t_dark .t_light .t_pink_active_ModalOverlay, :root.t_light .t_dark .t_light .t_pink_active_SheetOverlay, :root.t_light .t_dark .t_light .t_purple_DialogOverlay, :root.t_light .t_dark .t_light .t_purple_ModalOverlay, :root.t_light .t_dark .t_light .t_purple_SheetOverlay, :root.t_light .t_dark .t_light .t_purple_active_DialogOverlay, :root.t_light .t_dark .t_light .t_purple_active_ModalOverlay, :root.t_light .t_dark .t_light .t_purple_active_SheetOverlay, :root.t_light .t_dark .t_light .t_red_DialogOverlay, :root.t_light .t_dark .t_light .t_red_ModalOverlay, :root.t_light .t_dark .t_light .t_red_SheetOverlay, :root.t_light .t_dark .t_light .t_red_active_DialogOverlay, :root.t_light .t_dark .t_light .t_red_active_ModalOverlay, :root.t_light .t_dark .t_light .t_red_active_SheetOverlay, :root.t_light .t_dark .t_light .t_yellow_DialogOverlay, :root.t_light .t_dark .t_light .t_yellow_ModalOverlay, :root.t_light .t_dark .t_light .t_yellow_SheetOverlay, :root.t_light .t_dark .t_light .t_yellow_active_DialogOverlay, :root.t_light .t_dark .t_light .t_yellow_active_ModalOverlay, :root.t_light .t_dark .t_light .t_yellow_active_SheetOverlay, :root.t_light .t_gray_DialogOverlay, :root.t_light .t_gray_ModalOverlay, :root.t_light .t_gray_SheetOverlay, :root.t_light .t_gray_active_DialogOverlay, :root.t_light .t_gray_active_ModalOverlay, :root.t_light .t_gray_active_SheetOverlay, :root.t_light .t_green_DialogOverlay, :root.t_light .t_green_ModalOverlay, :root.t_light .t_green_SheetOverlay, :root.t_light .t_green_active_DialogOverlay, :root.t_light .t_green_active_ModalOverlay, :root.t_light .t_green_active_SheetOverlay, :root.t_light .t_orange_DialogOverlay, :root.t_light .t_orange_ModalOverlay, :root.t_light .t_orange_SheetOverlay, :root.t_light .t_orange_active_DialogOverlay, :root.t_light .t_orange_active_ModalOverlay, :root.t_light .t_orange_active_SheetOverlay, :root.t_light .t_pink_DialogOverlay, :root.t_light .t_pink_ModalOverlay, :root.t_light .t_pink_SheetOverlay, :root.t_light .t_pink_active_DialogOverlay, :root.t_light .t_pink_active_ModalOverlay, :root.t_light .t_pink_active_SheetOverlay, :root.t_light .t_purple_DialogOverlay, :root.t_light .t_purple_ModalOverlay, :root.t_light .t_purple_SheetOverlay, :root.t_light .t_purple_active_DialogOverlay, :root.t_light .t_purple_active_ModalOverlay, :root.t_light .t_purple_active_SheetOverlay, :root.t_light .t_red_DialogOverlay, :root.t_light .t_red_ModalOverlay, :root.t_light .t_red_SheetOverlay, :root.t_light .t_red_active_DialogOverlay, :root.t_light .t_red_active_ModalOverlay, :root.t_light .t_red_active_SheetOverlay, :root.t_light .t_yellow_DialogOverlay, :root.t_light .t_yellow_ModalOverlay, :root.t_light .t_yellow_SheetOverlay, :root.t_light .t_yellow_active_DialogOverlay, :root.t_light .t_yellow_active_ModalOverlay, :root.t_light .t_yellow_active_SheetOverlay, .tm_xxt {--background:rgba(0,0,0,0.5);}
@media(prefers-color-scheme:light){
    body{background:var(--background);}
    .t_DialogOverlay, .t_ModalOverlay, .t_SheetOverlay, .t_active_DialogOverlay, .t_active_ModalOverlay, .t_active_SheetOverlay, .t_blue_DialogOverlay, .t_blue_ModalOverlay, .t_blue_SheetOverlay, .t_blue_active_DialogOverlay, .t_blue_active_ModalOverlay, .t_blue_active_SheetOverlay, .t_dark .t_light .t_DialogOverlay, .t_dark .t_light .t_ModalOverlay, .t_dark .t_light .t_SheetOverlay, .t_dark .t_light .t_active_DialogOverlay, .t_dark .t_light .t_active_ModalOverlay, .t_dark .t_light .t_active_SheetOverlay, .t_dark .t_light .t_blue_DialogOverlay, .t_dark .t_light .t_blue_ModalOverlay, .t_dark .t_light .t_blue_SheetOverlay, .t_dark .t_light .t_blue_active_DialogOverlay, .t_dark .t_light .t_blue_active_ModalOverlay, .t_dark .t_light .t_blue_active_SheetOverlay, .t_dark .t_light .t_gray_DialogOverlay, .t_dark .t_light .t_gray_ModalOverlay, .t_dark .t_light .t_gray_SheetOverlay, .t_dark .t_light .t_gray_active_DialogOverlay, .t_dark .t_light .t_gray_active_ModalOverlay, .t_dark .t_light .t_gray_active_SheetOverlay, .t_dark .t_light .t_green_DialogOverlay, .t_dark .t_light .t_green_ModalOverlay, .t_dark .t_light .t_green_SheetOverlay, .t_dark .t_light .t_green_active_DialogOverlay, .t_dark .t_light .t_green_active_ModalOverlay, .t_dark .t_light .t_green_active_SheetOverlay, .t_dark .t_light .t_orange_DialogOverlay, .t_dark .t_light .t_orange_ModalOverlay, .t_dark .t_light .t_orange_SheetOverlay, .t_dark .t_light .t_orange_active_DialogOverlay, .t_dark .t_light .t_orange_active_ModalOverlay, .t_dark .t_light .t_orange_active_SheetOverlay, .t_dark .t_light .t_pink_DialogOverlay, .t_dark .t_light .t_pink_ModalOverlay, .t_dark .t_light .t_pink_SheetOverlay, .t_dark .t_light .t_pink_active_DialogOverlay, .t_dark .t_light .t_pink_active_ModalOverlay, .t_dark .t_light .t_pink_active_SheetOverlay, .t_dark .t_light .t_purple_DialogOverlay, .t_dark .t_light .t_purple_ModalOverlay, .t_dark .t_light .t_purple_SheetOverlay, .t_dark .t_light .t_purple_active_DialogOverlay, .t_dark .t_light .t_purple_active_ModalOverlay, .t_dark .t_light .t_purple_active_SheetOverlay, .t_dark .t_light .t_red_DialogOverlay, .t_dark .t_light .t_red_ModalOverlay, .t_dark .t_light .t_red_SheetOverlay, .t_dark .t_light .t_red_active_DialogOverlay, .t_dark .t_light .t_red_active_ModalOverlay, .t_dark .t_light .t_red_active_SheetOverlay, .t_dark .t_light .t_yellow_DialogOverlay, .t_dark .t_light .t_yellow_ModalOverlay, .t_dark .t_light .t_yellow_SheetOverlay, .t_dark .t_light .t_yellow_active_DialogOverlay, .t_dark .t_light .t_yellow_active_ModalOverlay, .t_dark .t_light .t_yellow_active_SheetOverlay, .t_gray_DialogOverlay, .t_gray_ModalOverlay, .t_gray_SheetOverlay, .t_gray_active_DialogOverlay, .t_gray_active_ModalOverlay, .t_gray_active_SheetOverlay, .t_green_DialogOverlay, .t_green_ModalOverlay, .t_green_SheetOverlay, .t_green_active_DialogOverlay, .t_green_active_ModalOverlay, .t_green_active_SheetOverlay, .t_orange_DialogOverlay, .t_orange_ModalOverlay, .t_orange_SheetOverlay, .t_orange_active_DialogOverlay, .t_orange_active_ModalOverlay, .t_orange_active_SheetOverlay, .t_pink_DialogOverlay, .t_pink_ModalOverlay, .t_pink_SheetOverlay, .t_pink_active_DialogOverlay, .t_pink_active_ModalOverlay, .t_pink_active_SheetOverlay, .t_purple_DialogOverlay, .t_purple_ModalOverlay, .t_purple_SheetOverlay, .t_purple_active_DialogOverlay, .t_purple_active_ModalOverlay, .t_purple_active_SheetOverlay, .t_red_DialogOverlay, .t_red_ModalOverlay, .t_red_SheetOverlay, .t_red_active_DialogOverlay, .t_red_active_ModalOverlay, .t_red_active_SheetOverlay, .t_yellow_DialogOverlay, .t_yellow_ModalOverlay, .t_yellow_SheetOverlay, .t_yellow_active_DialogOverlay, .t_yellow_active_ModalOverlay, .t_yellow_active_SheetOverlay {--background:rgba(0,0,0,0.5);}
  }
:root.t_dark .t_Button, :root.t_dark .t_Input, :root.t_dark .t_light .t_dark .t_Button, :root.t_dark .t_light .t_dark .t_Input, :root.t_light .t_dark .t_Button, :root.t_light .t_dark .t_Input, :root.t_light .t_dark .t_light .t_dark .t_Button, :root.t_light .t_dark .t_light .t_dark .t_Input, .tm_xxt {--background:#000000;--color:#FFFFFF;}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_Button, .t_Input, .t_light .t_dark .t_Button, .t_light .t_dark .t_Input {--background:#000000;--color:#FFFFFF;}
  }
:root.t_dark .t_ProgressIndicator, :root.t_dark .t_SliderThumb, :root.t_dark .t_SwitchThumb, :root.t_dark .t_Tooltip, :root.t_dark .t_light .t_dark .t_ProgressIndicator, :root.t_dark .t_light .t_dark .t_SliderThumb, :root.t_dark .t_light .t_dark .t_SwitchThumb, :root.t_dark .t_light .t_dark .t_Tooltip, :root.t_light .t_dark .t_ProgressIndicator, :root.t_light .t_dark .t_SliderThumb, :root.t_light .t_dark .t_SwitchThumb, :root.t_light .t_dark .t_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_Tooltip, .tm_xxt {--color:var(--c-black2);--colorHover:var(--c-black3);--colorPress:var(--c-black1);--colorFocus:var(--c-black1);--background:var(--c-black12);--backgroundHover:var(--c-black11);--backgroundPress:var(--c-black12);--backgroundFocus:var(--c-black11);--borderColor:var(--c-black10);--borderColorHover:var(--c-black9);--borderColorFocus:var(--c-black8);--borderColorPress:var(--c-black7);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_ProgressIndicator, .t_SliderThumb, .t_SwitchThumb, .t_Tooltip, .t_light .t_dark .t_ProgressIndicator, .t_light .t_dark .t_SliderThumb, .t_light .t_dark .t_SwitchThumb, .t_light .t_dark .t_Tooltip {--color:var(--c-black2);--colorHover:var(--c-black3);--colorPress:var(--c-black1);--colorFocus:var(--c-black1);--background:var(--c-black12);--backgroundHover:var(--c-black11);--backgroundPress:var(--c-black12);--backgroundFocus:var(--c-black11);--borderColor:var(--c-black10);--borderColorHover:var(--c-black9);--borderColorFocus:var(--c-black8);--borderColorPress:var(--c-black7);}
  }
:root.t_dark .t_DialogOverlay, :root.t_dark .t_ModalOverlay, :root.t_dark .t_SheetOverlay, :root.t_dark .t_active_DialogOverlay, :root.t_dark .t_active_ModalOverlay, :root.t_dark .t_active_SheetOverlay, :root.t_dark .t_blue_DialogOverlay, :root.t_dark .t_blue_ModalOverlay, :root.t_dark .t_blue_SheetOverlay, :root.t_dark .t_blue_active_DialogOverlay, :root.t_dark .t_blue_active_ModalOverlay, :root.t_dark .t_blue_active_SheetOverlay, :root.t_dark .t_gray_DialogOverlay, :root.t_dark .t_gray_ModalOverlay, :root.t_dark .t_gray_SheetOverlay, :root.t_dark .t_gray_active_DialogOverlay, :root.t_dark .t_gray_active_ModalOverlay, :root.t_dark .t_gray_active_SheetOverlay, :root.t_dark .t_green_DialogOverlay, :root.t_dark .t_green_ModalOverlay, :root.t_dark .t_green_SheetOverlay, :root.t_dark .t_green_active_DialogOverlay, :root.t_dark .t_green_active_ModalOverlay, :root.t_dark .t_green_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_DialogOverlay, :root.t_dark .t_light .t_dark .t_ModalOverlay, :root.t_dark .t_light .t_dark .t_SheetOverlay, :root.t_dark .t_light .t_dark .t_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_blue_DialogOverlay, :root.t_dark .t_light .t_dark .t_blue_ModalOverlay, :root.t_dark .t_light .t_dark .t_blue_SheetOverlay, :root.t_dark .t_light .t_dark .t_blue_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_blue_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_blue_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_gray_DialogOverlay, :root.t_dark .t_light .t_dark .t_gray_ModalOverlay, :root.t_dark .t_light .t_dark .t_gray_SheetOverlay, :root.t_dark .t_light .t_dark .t_gray_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_gray_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_gray_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_green_DialogOverlay, :root.t_dark .t_light .t_dark .t_green_ModalOverlay, :root.t_dark .t_light .t_dark .t_green_SheetOverlay, :root.t_dark .t_light .t_dark .t_green_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_green_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_green_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_orange_DialogOverlay, :root.t_dark .t_light .t_dark .t_orange_ModalOverlay, :root.t_dark .t_light .t_dark .t_orange_SheetOverlay, :root.t_dark .t_light .t_dark .t_orange_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_orange_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_orange_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_pink_DialogOverlay, :root.t_dark .t_light .t_dark .t_pink_ModalOverlay, :root.t_dark .t_light .t_dark .t_pink_SheetOverlay, :root.t_dark .t_light .t_dark .t_pink_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_pink_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_pink_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_purple_DialogOverlay, :root.t_dark .t_light .t_dark .t_purple_ModalOverlay, :root.t_dark .t_light .t_dark .t_purple_SheetOverlay, :root.t_dark .t_light .t_dark .t_purple_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_purple_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_purple_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_red_DialogOverlay, :root.t_dark .t_light .t_dark .t_red_ModalOverlay, :root.t_dark .t_light .t_dark .t_red_SheetOverlay, :root.t_dark .t_light .t_dark .t_red_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_red_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_red_active_SheetOverlay, :root.t_dark .t_light .t_dark .t_yellow_DialogOverlay, :root.t_dark .t_light .t_dark .t_yellow_ModalOverlay, :root.t_dark .t_light .t_dark .t_yellow_SheetOverlay, :root.t_dark .t_light .t_dark .t_yellow_active_DialogOverlay, :root.t_dark .t_light .t_dark .t_yellow_active_ModalOverlay, :root.t_dark .t_light .t_dark .t_yellow_active_SheetOverlay, :root.t_dark .t_orange_DialogOverlay, :root.t_dark .t_orange_ModalOverlay, :root.t_dark .t_orange_SheetOverlay, :root.t_dark .t_orange_active_DialogOverlay, :root.t_dark .t_orange_active_ModalOverlay, :root.t_dark .t_orange_active_SheetOverlay, :root.t_dark .t_pink_DialogOverlay, :root.t_dark .t_pink_ModalOverlay, :root.t_dark .t_pink_SheetOverlay, :root.t_dark .t_pink_active_DialogOverlay, :root.t_dark .t_pink_active_ModalOverlay, :root.t_dark .t_pink_active_SheetOverlay, :root.t_dark .t_purple_DialogOverlay, :root.t_dark .t_purple_ModalOverlay, :root.t_dark .t_purple_SheetOverlay, :root.t_dark .t_purple_active_DialogOverlay, :root.t_dark .t_purple_active_ModalOverlay, :root.t_dark .t_purple_active_SheetOverlay, :root.t_dark .t_red_DialogOverlay, :root.t_dark .t_red_ModalOverlay, :root.t_dark .t_red_SheetOverlay, :root.t_dark .t_red_active_DialogOverlay, :root.t_dark .t_red_active_ModalOverlay, :root.t_dark .t_red_active_SheetOverlay, :root.t_dark .t_yellow_DialogOverlay, :root.t_dark .t_yellow_ModalOverlay, :root.t_dark .t_yellow_SheetOverlay, :root.t_dark .t_yellow_active_DialogOverlay, :root.t_dark .t_yellow_active_ModalOverlay, :root.t_dark .t_yellow_active_SheetOverlay, :root.t_light .t_dark .t_DialogOverlay, :root.t_light .t_dark .t_ModalOverlay, :root.t_light .t_dark .t_SheetOverlay, :root.t_light .t_dark .t_active_DialogOverlay, :root.t_light .t_dark .t_active_ModalOverlay, :root.t_light .t_dark .t_active_SheetOverlay, :root.t_light .t_dark .t_blue_DialogOverlay, :root.t_light .t_dark .t_blue_ModalOverlay, :root.t_light .t_dark .t_blue_SheetOverlay, :root.t_light .t_dark .t_blue_active_DialogOverlay, :root.t_light .t_dark .t_blue_active_ModalOverlay, :root.t_light .t_dark .t_blue_active_SheetOverlay, :root.t_light .t_dark .t_gray_DialogOverlay, :root.t_light .t_dark .t_gray_ModalOverlay, :root.t_light .t_dark .t_gray_SheetOverlay, :root.t_light .t_dark .t_gray_active_DialogOverlay, :root.t_light .t_dark .t_gray_active_ModalOverlay, :root.t_light .t_dark .t_gray_active_SheetOverlay, :root.t_light .t_dark .t_green_DialogOverlay, :root.t_light .t_dark .t_green_ModalOverlay, :root.t_light .t_dark .t_green_SheetOverlay, :root.t_light .t_dark .t_green_active_DialogOverlay, :root.t_light .t_dark .t_green_active_ModalOverlay, :root.t_light .t_dark .t_green_active_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_active_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_active_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_active_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_blue_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_blue_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_blue_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_blue_active_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_blue_active_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_blue_active_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_gray_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_gray_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_gray_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_gray_active_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_gray_active_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_gray_active_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_green_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_green_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_green_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_green_active_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_green_active_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_green_active_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_orange_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_orange_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_orange_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_orange_active_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_orange_active_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_orange_active_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_pink_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_pink_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_pink_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_pink_active_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_pink_active_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_pink_active_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_purple_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_purple_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_purple_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_purple_active_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_purple_active_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_purple_active_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_red_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_red_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_red_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_red_active_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_red_active_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_red_active_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_yellow_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_yellow_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_yellow_SheetOverlay, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_DialogOverlay, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_ModalOverlay, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_SheetOverlay, :root.t_light .t_dark .t_orange_DialogOverlay, :root.t_light .t_dark .t_orange_ModalOverlay, :root.t_light .t_dark .t_orange_SheetOverlay, :root.t_light .t_dark .t_orange_active_DialogOverlay, :root.t_light .t_dark .t_orange_active_ModalOverlay, :root.t_light .t_dark .t_orange_active_SheetOverlay, :root.t_light .t_dark .t_pink_DialogOverlay, :root.t_light .t_dark .t_pink_ModalOverlay, :root.t_light .t_dark .t_pink_SheetOverlay, :root.t_light .t_dark .t_pink_active_DialogOverlay, :root.t_light .t_dark .t_pink_active_ModalOverlay, :root.t_light .t_dark .t_pink_active_SheetOverlay, :root.t_light .t_dark .t_purple_DialogOverlay, :root.t_light .t_dark .t_purple_ModalOverlay, :root.t_light .t_dark .t_purple_SheetOverlay, :root.t_light .t_dark .t_purple_active_DialogOverlay, :root.t_light .t_dark .t_purple_active_ModalOverlay, :root.t_light .t_dark .t_purple_active_SheetOverlay, :root.t_light .t_dark .t_red_DialogOverlay, :root.t_light .t_dark .t_red_ModalOverlay, :root.t_light .t_dark .t_red_SheetOverlay, :root.t_light .t_dark .t_red_active_DialogOverlay, :root.t_light .t_dark .t_red_active_ModalOverlay, :root.t_light .t_dark .t_red_active_SheetOverlay, :root.t_light .t_dark .t_yellow_DialogOverlay, :root.t_light .t_dark .t_yellow_ModalOverlay, :root.t_light .t_dark .t_yellow_SheetOverlay, :root.t_light .t_dark .t_yellow_active_DialogOverlay, :root.t_light .t_dark .t_yellow_active_ModalOverlay, :root.t_light .t_dark .t_yellow_active_SheetOverlay, .tm_xxt {--background:rgba(0,0,0,0.8);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);}
    .t_DialogOverlay, .t_ModalOverlay, .t_SheetOverlay, .t_active_DialogOverlay, .t_active_ModalOverlay, .t_active_SheetOverlay, .t_blue_DialogOverlay, .t_blue_ModalOverlay, .t_blue_SheetOverlay, .t_blue_active_DialogOverlay, .t_blue_active_ModalOverlay, .t_blue_active_SheetOverlay, .t_gray_DialogOverlay, .t_gray_ModalOverlay, .t_gray_SheetOverlay, .t_gray_active_DialogOverlay, .t_gray_active_ModalOverlay, .t_gray_active_SheetOverlay, .t_green_DialogOverlay, .t_green_ModalOverlay, .t_green_SheetOverlay, .t_green_active_DialogOverlay, .t_green_active_ModalOverlay, .t_green_active_SheetOverlay, .t_light .t_dark .t_DialogOverlay, .t_light .t_dark .t_ModalOverlay, .t_light .t_dark .t_SheetOverlay, .t_light .t_dark .t_active_DialogOverlay, .t_light .t_dark .t_active_ModalOverlay, .t_light .t_dark .t_active_SheetOverlay, .t_light .t_dark .t_blue_DialogOverlay, .t_light .t_dark .t_blue_ModalOverlay, .t_light .t_dark .t_blue_SheetOverlay, .t_light .t_dark .t_blue_active_DialogOverlay, .t_light .t_dark .t_blue_active_ModalOverlay, .t_light .t_dark .t_blue_active_SheetOverlay, .t_light .t_dark .t_gray_DialogOverlay, .t_light .t_dark .t_gray_ModalOverlay, .t_light .t_dark .t_gray_SheetOverlay, .t_light .t_dark .t_gray_active_DialogOverlay, .t_light .t_dark .t_gray_active_ModalOverlay, .t_light .t_dark .t_gray_active_SheetOverlay, .t_light .t_dark .t_green_DialogOverlay, .t_light .t_dark .t_green_ModalOverlay, .t_light .t_dark .t_green_SheetOverlay, .t_light .t_dark .t_green_active_DialogOverlay, .t_light .t_dark .t_green_active_ModalOverlay, .t_light .t_dark .t_green_active_SheetOverlay, .t_light .t_dark .t_orange_DialogOverlay, .t_light .t_dark .t_orange_ModalOverlay, .t_light .t_dark .t_orange_SheetOverlay, .t_light .t_dark .t_orange_active_DialogOverlay, .t_light .t_dark .t_orange_active_ModalOverlay, .t_light .t_dark .t_orange_active_SheetOverlay, .t_light .t_dark .t_pink_DialogOverlay, .t_light .t_dark .t_pink_ModalOverlay, .t_light .t_dark .t_pink_SheetOverlay, .t_light .t_dark .t_pink_active_DialogOverlay, .t_light .t_dark .t_pink_active_ModalOverlay, .t_light .t_dark .t_pink_active_SheetOverlay, .t_light .t_dark .t_purple_DialogOverlay, .t_light .t_dark .t_purple_ModalOverlay, .t_light .t_dark .t_purple_SheetOverlay, .t_light .t_dark .t_purple_active_DialogOverlay, .t_light .t_dark .t_purple_active_ModalOverlay, .t_light .t_dark .t_purple_active_SheetOverlay, .t_light .t_dark .t_red_DialogOverlay, .t_light .t_dark .t_red_ModalOverlay, .t_light .t_dark .t_red_SheetOverlay, .t_light .t_dark .t_red_active_DialogOverlay, .t_light .t_dark .t_red_active_ModalOverlay, .t_light .t_dark .t_red_active_SheetOverlay, .t_light .t_dark .t_yellow_DialogOverlay, .t_light .t_dark .t_yellow_ModalOverlay, .t_light .t_dark .t_yellow_SheetOverlay, .t_light .t_dark .t_yellow_active_DialogOverlay, .t_light .t_dark .t_yellow_active_ModalOverlay, .t_light .t_dark .t_yellow_active_SheetOverlay, .t_orange_DialogOverlay, .t_orange_ModalOverlay, .t_orange_SheetOverlay, .t_orange_active_DialogOverlay, .t_orange_active_ModalOverlay, .t_orange_active_SheetOverlay, .t_pink_DialogOverlay, .t_pink_ModalOverlay, .t_pink_SheetOverlay, .t_pink_active_DialogOverlay, .t_pink_active_ModalOverlay, .t_pink_active_SheetOverlay, .t_purple_DialogOverlay, .t_purple_ModalOverlay, .t_purple_SheetOverlay, .t_purple_active_DialogOverlay, .t_purple_active_ModalOverlay, .t_purple_active_SheetOverlay, .t_red_DialogOverlay, .t_red_ModalOverlay, .t_red_SheetOverlay, .t_red_active_DialogOverlay, .t_red_active_ModalOverlay, .t_red_active_SheetOverlay, .t_yellow_DialogOverlay, .t_yellow_ModalOverlay, .t_yellow_SheetOverlay, .t_yellow_active_DialogOverlay, .t_yellow_active_ModalOverlay, .t_yellow_active_SheetOverlay {--background:rgba(0,0,0,0.8);}
  }
:root.t_dark .t_light .t_dark .t_light .t_orange_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_orange_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_orange_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_orange_Tooltip, :root.t_dark .t_light .t_orange_ProgressIndicator, :root.t_dark .t_light .t_orange_SliderThumb, :root.t_dark .t_light .t_orange_SwitchThumb, :root.t_dark .t_light .t_orange_Tooltip, :root.t_light .t_dark .t_light .t_orange_ProgressIndicator, :root.t_light .t_dark .t_light .t_orange_SliderThumb, :root.t_light .t_dark .t_light .t_orange_SwitchThumb, :root.t_light .t_dark .t_light .t_orange_Tooltip, :root.t_light .t_orange_ProgressIndicator, :root.t_light .t_orange_SliderThumb, :root.t_light .t_orange_SwitchThumb, :root.t_light .t_orange_Tooltip, .tm_xxt {--color:var(--c-orange2Light);--colorHover:var(--c-orange1Light);--colorPress:var(--c-orange3Light);--colorFocus:var(--c-orange3Light);--background:var(--c-orange12Light);--backgroundHover:var(--c-orange11Light);--backgroundPress:var(--c-orange12Light);--backgroundFocus:var(--c-orange11Light);--borderColor:var(--c-orange10Light);--borderColorHover:var(--c-orange9Dark);--borderColorFocus:var(--c-orange8Light);--borderColorPress:var(--c-orange7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_orange_ProgressIndicator, .t_dark .t_light .t_orange_SliderThumb, .t_dark .t_light .t_orange_SwitchThumb, .t_dark .t_light .t_orange_Tooltip, .t_orange_ProgressIndicator, .t_orange_SliderThumb, .t_orange_SwitchThumb, .t_orange_Tooltip {--color:var(--c-orange2Light);--colorHover:var(--c-orange1Light);--colorPress:var(--c-orange3Light);--colorFocus:var(--c-orange3Light);--background:var(--c-orange12Light);--backgroundHover:var(--c-orange11Light);--backgroundPress:var(--c-orange12Light);--backgroundFocus:var(--c-orange11Light);--borderColor:var(--c-orange10Light);--borderColorHover:var(--c-orange9Dark);--borderColorFocus:var(--c-orange8Light);--borderColorPress:var(--c-orange7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_yellow_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_yellow_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_yellow_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_yellow_Tooltip, :root.t_dark .t_light .t_yellow_ProgressIndicator, :root.t_dark .t_light .t_yellow_SliderThumb, :root.t_dark .t_light .t_yellow_SwitchThumb, :root.t_dark .t_light .t_yellow_Tooltip, :root.t_light .t_dark .t_light .t_yellow_ProgressIndicator, :root.t_light .t_dark .t_light .t_yellow_SliderThumb, :root.t_light .t_dark .t_light .t_yellow_SwitchThumb, :root.t_light .t_dark .t_light .t_yellow_Tooltip, :root.t_light .t_yellow_ProgressIndicator, :root.t_light .t_yellow_SliderThumb, :root.t_light .t_yellow_SwitchThumb, :root.t_light .t_yellow_Tooltip, .tm_xxt {--color:var(--c-yellow2Light);--colorHover:var(--c-yellow1Light);--colorPress:var(--c-yellow3Light);--colorFocus:var(--c-yellow3Light);--background:var(--c-yellow12Light);--backgroundHover:var(--c-yellow11Light);--backgroundPress:var(--c-yellow12Light);--backgroundFocus:var(--c-yellow11Light);--borderColor:var(--c-yellow10Light);--borderColorHover:var(--c-yellow9Dark);--borderColorFocus:var(--c-yellow8Light);--borderColorPress:var(--c-yellow7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_yellow_ProgressIndicator, .t_dark .t_light .t_yellow_SliderThumb, .t_dark .t_light .t_yellow_SwitchThumb, .t_dark .t_light .t_yellow_Tooltip, .t_yellow_ProgressIndicator, .t_yellow_SliderThumb, .t_yellow_SwitchThumb, .t_yellow_Tooltip {--color:var(--c-yellow2Light);--colorHover:var(--c-yellow1Light);--colorPress:var(--c-yellow3Light);--colorFocus:var(--c-yellow3Light);--background:var(--c-yellow12Light);--backgroundHover:var(--c-yellow11Light);--backgroundPress:var(--c-yellow12Light);--backgroundFocus:var(--c-yellow11Light);--borderColor:var(--c-yellow10Light);--borderColorHover:var(--c-yellow9Dark);--borderColorFocus:var(--c-yellow8Light);--borderColorPress:var(--c-yellow7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_green_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_green_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_green_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_green_Tooltip, :root.t_dark .t_light .t_green_ProgressIndicator, :root.t_dark .t_light .t_green_SliderThumb, :root.t_dark .t_light .t_green_SwitchThumb, :root.t_dark .t_light .t_green_Tooltip, :root.t_light .t_dark .t_light .t_green_ProgressIndicator, :root.t_light .t_dark .t_light .t_green_SliderThumb, :root.t_light .t_dark .t_light .t_green_SwitchThumb, :root.t_light .t_dark .t_light .t_green_Tooltip, :root.t_light .t_green_ProgressIndicator, :root.t_light .t_green_SliderThumb, :root.t_light .t_green_SwitchThumb, :root.t_light .t_green_Tooltip, .tm_xxt {--color:var(--c-green2Light);--colorHover:var(--c-green1Light);--colorPress:var(--c-green3Light);--colorFocus:var(--c-green3Light);--background:var(--c-green12Light);--backgroundHover:var(--c-green11Light);--backgroundPress:var(--c-green12Light);--backgroundFocus:var(--c-green11Light);--borderColor:var(--c-green10Light);--borderColorHover:var(--c-green9Dark);--borderColorFocus:var(--c-green8Light);--borderColorPress:var(--c-green7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_green_ProgressIndicator, .t_dark .t_light .t_green_SliderThumb, .t_dark .t_light .t_green_SwitchThumb, .t_dark .t_light .t_green_Tooltip, .t_green_ProgressIndicator, .t_green_SliderThumb, .t_green_SwitchThumb, .t_green_Tooltip {--color:var(--c-green2Light);--colorHover:var(--c-green1Light);--colorPress:var(--c-green3Light);--colorFocus:var(--c-green3Light);--background:var(--c-green12Light);--backgroundHover:var(--c-green11Light);--backgroundPress:var(--c-green12Light);--backgroundFocus:var(--c-green11Light);--borderColor:var(--c-green10Light);--borderColorHover:var(--c-green9Dark);--borderColorFocus:var(--c-green8Light);--borderColorPress:var(--c-green7Light);}
  }
:root.t_dark .t_light .t_blue_ProgressIndicator, :root.t_dark .t_light .t_blue_SliderThumb, :root.t_dark .t_light .t_blue_SwitchThumb, :root.t_dark .t_light .t_blue_Tooltip, :root.t_dark .t_light .t_dark .t_light .t_blue_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_blue_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_blue_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_blue_Tooltip, :root.t_light .t_blue_ProgressIndicator, :root.t_light .t_blue_SliderThumb, :root.t_light .t_blue_SwitchThumb, :root.t_light .t_blue_Tooltip, :root.t_light .t_dark .t_light .t_blue_ProgressIndicator, :root.t_light .t_dark .t_light .t_blue_SliderThumb, :root.t_light .t_dark .t_light .t_blue_SwitchThumb, :root.t_light .t_dark .t_light .t_blue_Tooltip, .tm_xxt {--color:var(--c-blue2Light);--colorHover:var(--c-blue1Light);--colorPress:var(--c-blue3Light);--colorFocus:var(--c-blue3Light);--background:var(--c-blue12Light);--backgroundHover:var(--c-blue11Light);--backgroundPress:var(--c-blue12Light);--backgroundFocus:var(--c-blue11Light);--borderColor:var(--c-blue10Light);--borderColorHover:var(--c-blue9Dark);--borderColorFocus:var(--c-blue8Light);--borderColorPress:var(--c-blue7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_blue_ProgressIndicator, .t_blue_SliderThumb, .t_blue_SwitchThumb, .t_blue_Tooltip, .t_dark .t_light .t_blue_ProgressIndicator, .t_dark .t_light .t_blue_SliderThumb, .t_dark .t_light .t_blue_SwitchThumb, .t_dark .t_light .t_blue_Tooltip {--color:var(--c-blue2Light);--colorHover:var(--c-blue1Light);--colorPress:var(--c-blue3Light);--colorFocus:var(--c-blue3Light);--background:var(--c-blue12Light);--backgroundHover:var(--c-blue11Light);--backgroundPress:var(--c-blue12Light);--backgroundFocus:var(--c-blue11Light);--borderColor:var(--c-blue10Light);--borderColorHover:var(--c-blue9Dark);--borderColorFocus:var(--c-blue8Light);--borderColorPress:var(--c-blue7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_purple_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_purple_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_purple_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_purple_Tooltip, :root.t_dark .t_light .t_purple_ProgressIndicator, :root.t_dark .t_light .t_purple_SliderThumb, :root.t_dark .t_light .t_purple_SwitchThumb, :root.t_dark .t_light .t_purple_Tooltip, :root.t_light .t_dark .t_light .t_purple_ProgressIndicator, :root.t_light .t_dark .t_light .t_purple_SliderThumb, :root.t_light .t_dark .t_light .t_purple_SwitchThumb, :root.t_light .t_dark .t_light .t_purple_Tooltip, :root.t_light .t_purple_ProgressIndicator, :root.t_light .t_purple_SliderThumb, :root.t_light .t_purple_SwitchThumb, :root.t_light .t_purple_Tooltip, .tm_xxt {--color:var(--c-purple2Light);--colorHover:var(--c-purple1Light);--colorPress:var(--c-purple3Light);--colorFocus:var(--c-purple3Light);--background:var(--c-purple12Light);--backgroundHover:var(--c-purple11Light);--backgroundPress:var(--c-purple12Light);--backgroundFocus:var(--c-purple11Light);--borderColor:var(--c-purple10Light);--borderColorHover:var(--c-purple9Dark);--borderColorFocus:var(--c-purple8Light);--borderColorPress:var(--c-purple7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_purple_ProgressIndicator, .t_dark .t_light .t_purple_SliderThumb, .t_dark .t_light .t_purple_SwitchThumb, .t_dark .t_light .t_purple_Tooltip, .t_purple_ProgressIndicator, .t_purple_SliderThumb, .t_purple_SwitchThumb, .t_purple_Tooltip {--color:var(--c-purple2Light);--colorHover:var(--c-purple1Light);--colorPress:var(--c-purple3Light);--colorFocus:var(--c-purple3Light);--background:var(--c-purple12Light);--backgroundHover:var(--c-purple11Light);--backgroundPress:var(--c-purple12Light);--backgroundFocus:var(--c-purple11Light);--borderColor:var(--c-purple10Light);--borderColorHover:var(--c-purple9Dark);--borderColorFocus:var(--c-purple8Light);--borderColorPress:var(--c-purple7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_pink_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_pink_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_pink_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_pink_Tooltip, :root.t_dark .t_light .t_pink_ProgressIndicator, :root.t_dark .t_light .t_pink_SliderThumb, :root.t_dark .t_light .t_pink_SwitchThumb, :root.t_dark .t_light .t_pink_Tooltip, :root.t_light .t_dark .t_light .t_pink_ProgressIndicator, :root.t_light .t_dark .t_light .t_pink_SliderThumb, :root.t_light .t_dark .t_light .t_pink_SwitchThumb, :root.t_light .t_dark .t_light .t_pink_Tooltip, :root.t_light .t_pink_ProgressIndicator, :root.t_light .t_pink_SliderThumb, :root.t_light .t_pink_SwitchThumb, :root.t_light .t_pink_Tooltip, .tm_xxt {--color:var(--c-pink2Light);--colorHover:var(--c-pink1Light);--colorPress:var(--c-pink3Light);--colorFocus:var(--c-pink3Light);--background:var(--c-pink12Light);--backgroundHover:var(--c-pink11Light);--backgroundPress:var(--c-pink12Light);--backgroundFocus:var(--c-pink11Light);--borderColor:var(--c-pink10Light);--borderColorHover:var(--c-pink9Dark);--borderColorFocus:var(--c-pink8Light);--borderColorPress:var(--c-pink7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_pink_ProgressIndicator, .t_dark .t_light .t_pink_SliderThumb, .t_dark .t_light .t_pink_SwitchThumb, .t_dark .t_light .t_pink_Tooltip, .t_pink_ProgressIndicator, .t_pink_SliderThumb, .t_pink_SwitchThumb, .t_pink_Tooltip {--color:var(--c-pink2Light);--colorHover:var(--c-pink1Light);--colorPress:var(--c-pink3Light);--colorFocus:var(--c-pink3Light);--background:var(--c-pink12Light);--backgroundHover:var(--c-pink11Light);--backgroundPress:var(--c-pink12Light);--backgroundFocus:var(--c-pink11Light);--borderColor:var(--c-pink10Light);--borderColorHover:var(--c-pink9Dark);--borderColorFocus:var(--c-pink8Light);--borderColorPress:var(--c-pink7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_red_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_red_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_red_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_red_Tooltip, :root.t_dark .t_light .t_red_ProgressIndicator, :root.t_dark .t_light .t_red_SliderThumb, :root.t_dark .t_light .t_red_SwitchThumb, :root.t_dark .t_light .t_red_Tooltip, :root.t_light .t_dark .t_light .t_red_ProgressIndicator, :root.t_light .t_dark .t_light .t_red_SliderThumb, :root.t_light .t_dark .t_light .t_red_SwitchThumb, :root.t_light .t_dark .t_light .t_red_Tooltip, :root.t_light .t_red_ProgressIndicator, :root.t_light .t_red_SliderThumb, :root.t_light .t_red_SwitchThumb, :root.t_light .t_red_Tooltip, .tm_xxt {--color:var(--c-red2Light);--colorHover:var(--c-red1Light);--colorPress:var(--c-red3Light);--colorFocus:var(--c-red3Light);--background:var(--c-red12Light);--backgroundHover:var(--c-red11Light);--backgroundPress:var(--c-red12Light);--backgroundFocus:var(--c-red11Light);--borderColor:var(--c-red10Light);--borderColorHover:var(--c-red9Dark);--borderColorFocus:var(--c-red8Light);--borderColorPress:var(--c-red7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_red_ProgressIndicator, .t_dark .t_light .t_red_SliderThumb, .t_dark .t_light .t_red_SwitchThumb, .t_dark .t_light .t_red_Tooltip, .t_red_ProgressIndicator, .t_red_SliderThumb, .t_red_SwitchThumb, .t_red_Tooltip {--color:var(--c-red2Light);--colorHover:var(--c-red1Light);--colorPress:var(--c-red3Light);--colorFocus:var(--c-red3Light);--background:var(--c-red12Light);--backgroundHover:var(--c-red11Light);--backgroundPress:var(--c-red12Light);--backgroundFocus:var(--c-red11Light);--borderColor:var(--c-red10Light);--borderColorHover:var(--c-red9Dark);--borderColorFocus:var(--c-red8Light);--borderColorPress:var(--c-red7Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_gray_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_gray_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_gray_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_gray_Tooltip, :root.t_dark .t_light .t_gray_ProgressIndicator, :root.t_dark .t_light .t_gray_SliderThumb, :root.t_dark .t_light .t_gray_SwitchThumb, :root.t_dark .t_light .t_gray_Tooltip, :root.t_light .t_dark .t_light .t_gray_ProgressIndicator, :root.t_light .t_dark .t_light .t_gray_SliderThumb, :root.t_light .t_dark .t_light .t_gray_SwitchThumb, :root.t_light .t_dark .t_light .t_gray_Tooltip, :root.t_light .t_gray_ProgressIndicator, :root.t_light .t_gray_SliderThumb, :root.t_light .t_gray_SwitchThumb, :root.t_light .t_gray_Tooltip, .tm_xxt {--color:var(--c-gray2Light);--colorHover:var(--c-gray1Light);--colorPress:var(--c-gray3Light);--colorFocus:var(--c-gray3Light);--background:var(--c-gray12Light);--backgroundHover:var(--c-gray11Light);--backgroundPress:var(--c-gray12Light);--backgroundFocus:var(--c-gray11Light);--borderColor:var(--c-gray10Light);--borderColorHover:var(--c-gray9Light);--borderColorFocus:var(--c-gray8Light);--borderColorPress:var(--c-gray7Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_gray_ProgressIndicator, .t_dark .t_light .t_gray_SliderThumb, .t_dark .t_light .t_gray_SwitchThumb, .t_dark .t_light .t_gray_Tooltip, .t_gray_ProgressIndicator, .t_gray_SliderThumb, .t_gray_SwitchThumb, .t_gray_Tooltip {--color:var(--c-gray2Light);--colorHover:var(--c-gray1Light);--colorPress:var(--c-gray3Light);--colorFocus:var(--c-gray3Light);--background:var(--c-gray12Light);--backgroundHover:var(--c-gray11Light);--backgroundPress:var(--c-gray12Light);--backgroundFocus:var(--c-gray11Light);--borderColor:var(--c-gray10Light);--borderColorHover:var(--c-gray9Light);--borderColorFocus:var(--c-gray8Light);--borderColorPress:var(--c-gray7Light);}
  }
:root.t_dark .t_light .t_dark .t_orange_ProgressIndicator, :root.t_dark .t_light .t_dark .t_orange_SliderThumb, :root.t_dark .t_light .t_dark .t_orange_SwitchThumb, :root.t_dark .t_light .t_dark .t_orange_Tooltip, :root.t_dark .t_orange_ProgressIndicator, :root.t_dark .t_orange_SliderThumb, :root.t_dark .t_orange_SwitchThumb, :root.t_dark .t_orange_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_orange_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_orange_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_orange_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_orange_Tooltip, :root.t_light .t_dark .t_orange_ProgressIndicator, :root.t_light .t_dark .t_orange_SliderThumb, :root.t_light .t_dark .t_orange_SwitchThumb, :root.t_light .t_dark .t_orange_Tooltip, .tm_xxt {--color:var(--c-orange2Dark);--colorHover:var(--c-orange3Dark);--colorPress:var(--c-orange1Dark);--colorFocus:var(--c-orange1Dark);--background:var(--c-orange12Dark);--backgroundHover:var(--c-orange11Dark);--backgroundPress:var(--c-orange12Dark);--backgroundFocus:var(--c-orange11Dark);--borderColor:var(--c-orange10Dark);--borderColorHover:var(--c-orange9Dark);--borderColorFocus:var(--c-orange8Dark);--borderColorPress:var(--c-orange7Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_orange_ProgressIndicator, .t_light .t_dark .t_orange_SliderThumb, .t_light .t_dark .t_orange_SwitchThumb, .t_light .t_dark .t_orange_Tooltip, .t_orange_ProgressIndicator, .t_orange_SliderThumb, .t_orange_SwitchThumb, .t_orange_Tooltip {--color:var(--c-orange2Dark);--colorHover:var(--c-orange3Dark);--colorPress:var(--c-orange1Dark);--colorFocus:var(--c-orange1Dark);--background:var(--c-orange12Dark);--backgroundHover:var(--c-orange11Dark);--backgroundPress:var(--c-orange12Dark);--backgroundFocus:var(--c-orange11Dark);--borderColor:var(--c-orange10Dark);--borderColorHover:var(--c-orange9Dark);--borderColorFocus:var(--c-orange8Dark);--borderColorPress:var(--c-orange7Dark);}
  }
:root.t_dark .t_light .t_dark .t_yellow_ProgressIndicator, :root.t_dark .t_light .t_dark .t_yellow_SliderThumb, :root.t_dark .t_light .t_dark .t_yellow_SwitchThumb, :root.t_dark .t_light .t_dark .t_yellow_Tooltip, :root.t_dark .t_yellow_ProgressIndicator, :root.t_dark .t_yellow_SliderThumb, :root.t_dark .t_yellow_SwitchThumb, :root.t_dark .t_yellow_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_yellow_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_yellow_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_yellow_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_yellow_Tooltip, :root.t_light .t_dark .t_yellow_ProgressIndicator, :root.t_light .t_dark .t_yellow_SliderThumb, :root.t_light .t_dark .t_yellow_SwitchThumb, :root.t_light .t_dark .t_yellow_Tooltip, .tm_xxt {--color:var(--c-yellow2Dark);--colorHover:var(--c-yellow3Dark);--colorPress:var(--c-yellow1Dark);--colorFocus:var(--c-yellow1Dark);--background:var(--c-yellow12Dark);--backgroundHover:var(--c-yellow11Dark);--backgroundPress:var(--c-yellow12Dark);--backgroundFocus:var(--c-yellow11Dark);--borderColor:var(--c-yellow10Dark);--borderColorHover:var(--c-yellow9Dark);--borderColorFocus:var(--c-yellow8Dark);--borderColorPress:var(--c-yellow7Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_yellow_ProgressIndicator, .t_light .t_dark .t_yellow_SliderThumb, .t_light .t_dark .t_yellow_SwitchThumb, .t_light .t_dark .t_yellow_Tooltip, .t_yellow_ProgressIndicator, .t_yellow_SliderThumb, .t_yellow_SwitchThumb, .t_yellow_Tooltip {--color:var(--c-yellow2Dark);--colorHover:var(--c-yellow3Dark);--colorPress:var(--c-yellow1Dark);--colorFocus:var(--c-yellow1Dark);--background:var(--c-yellow12Dark);--backgroundHover:var(--c-yellow11Dark);--backgroundPress:var(--c-yellow12Dark);--backgroundFocus:var(--c-yellow11Dark);--borderColor:var(--c-yellow10Dark);--borderColorHover:var(--c-yellow9Dark);--borderColorFocus:var(--c-yellow8Dark);--borderColorPress:var(--c-yellow7Dark);}
  }
:root.t_dark .t_green_ProgressIndicator, :root.t_dark .t_green_SliderThumb, :root.t_dark .t_green_SwitchThumb, :root.t_dark .t_green_Tooltip, :root.t_dark .t_light .t_dark .t_green_ProgressIndicator, :root.t_dark .t_light .t_dark .t_green_SliderThumb, :root.t_dark .t_light .t_dark .t_green_SwitchThumb, :root.t_dark .t_light .t_dark .t_green_Tooltip, :root.t_light .t_dark .t_green_ProgressIndicator, :root.t_light .t_dark .t_green_SliderThumb, :root.t_light .t_dark .t_green_SwitchThumb, :root.t_light .t_dark .t_green_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_green_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_green_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_green_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_green_Tooltip, .tm_xxt {--color:var(--c-green2Dark);--colorHover:var(--c-green3Dark);--colorPress:var(--c-green1Dark);--colorFocus:var(--c-green1Dark);--background:var(--c-green12Dark);--backgroundHover:var(--c-green11Dark);--backgroundPress:var(--c-green12Dark);--backgroundFocus:var(--c-green11Dark);--borderColor:var(--c-green10Dark);--borderColorHover:var(--c-green9Dark);--borderColorFocus:var(--c-green8Dark);--borderColorPress:var(--c-green7Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_green_ProgressIndicator, .t_green_SliderThumb, .t_green_SwitchThumb, .t_green_Tooltip, .t_light .t_dark .t_green_ProgressIndicator, .t_light .t_dark .t_green_SliderThumb, .t_light .t_dark .t_green_SwitchThumb, .t_light .t_dark .t_green_Tooltip {--color:var(--c-green2Dark);--colorHover:var(--c-green3Dark);--colorPress:var(--c-green1Dark);--colorFocus:var(--c-green1Dark);--background:var(--c-green12Dark);--backgroundHover:var(--c-green11Dark);--backgroundPress:var(--c-green12Dark);--backgroundFocus:var(--c-green11Dark);--borderColor:var(--c-green10Dark);--borderColorHover:var(--c-green9Dark);--borderColorFocus:var(--c-green8Dark);--borderColorPress:var(--c-green7Dark);}
  }
:root.t_dark .t_blue_ProgressIndicator, :root.t_dark .t_blue_SliderThumb, :root.t_dark .t_blue_SwitchThumb, :root.t_dark .t_blue_Tooltip, :root.t_dark .t_light .t_dark .t_blue_ProgressIndicator, :root.t_dark .t_light .t_dark .t_blue_SliderThumb, :root.t_dark .t_light .t_dark .t_blue_SwitchThumb, :root.t_dark .t_light .t_dark .t_blue_Tooltip, :root.t_light .t_dark .t_blue_ProgressIndicator, :root.t_light .t_dark .t_blue_SliderThumb, :root.t_light .t_dark .t_blue_SwitchThumb, :root.t_light .t_dark .t_blue_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_blue_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_blue_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_blue_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_blue_Tooltip, .tm_xxt {--color:var(--c-blue2Dark);--colorHover:var(--c-blue3Dark);--colorPress:var(--c-blue1Dark);--colorFocus:var(--c-blue1Dark);--background:var(--c-blue12Dark);--backgroundHover:var(--c-blue11Dark);--backgroundPress:var(--c-blue12Dark);--backgroundFocus:var(--c-blue11Dark);--borderColor:var(--c-blue10Dark);--borderColorHover:var(--c-blue9Dark);--borderColorFocus:var(--c-blue8Dark);--borderColorPress:var(--c-blue7Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_blue_ProgressIndicator, .t_blue_SliderThumb, .t_blue_SwitchThumb, .t_blue_Tooltip, .t_light .t_dark .t_blue_ProgressIndicator, .t_light .t_dark .t_blue_SliderThumb, .t_light .t_dark .t_blue_SwitchThumb, .t_light .t_dark .t_blue_Tooltip {--color:var(--c-blue2Dark);--colorHover:var(--c-blue3Dark);--colorPress:var(--c-blue1Dark);--colorFocus:var(--c-blue1Dark);--background:var(--c-blue12Dark);--backgroundHover:var(--c-blue11Dark);--backgroundPress:var(--c-blue12Dark);--backgroundFocus:var(--c-blue11Dark);--borderColor:var(--c-blue10Dark);--borderColorHover:var(--c-blue9Dark);--borderColorFocus:var(--c-blue8Dark);--borderColorPress:var(--c-blue7Dark);}
  }
:root.t_dark .t_light .t_dark .t_purple_ProgressIndicator, :root.t_dark .t_light .t_dark .t_purple_SliderThumb, :root.t_dark .t_light .t_dark .t_purple_SwitchThumb, :root.t_dark .t_light .t_dark .t_purple_Tooltip, :root.t_dark .t_purple_ProgressIndicator, :root.t_dark .t_purple_SliderThumb, :root.t_dark .t_purple_SwitchThumb, :root.t_dark .t_purple_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_purple_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_purple_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_purple_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_purple_Tooltip, :root.t_light .t_dark .t_purple_ProgressIndicator, :root.t_light .t_dark .t_purple_SliderThumb, :root.t_light .t_dark .t_purple_SwitchThumb, :root.t_light .t_dark .t_purple_Tooltip, .tm_xxt {--color:var(--c-purple2Dark);--colorHover:var(--c-purple3Dark);--colorPress:var(--c-purple1Dark);--colorFocus:var(--c-purple1Dark);--background:var(--c-purple12Dark);--backgroundHover:var(--c-purple11Dark);--backgroundPress:var(--c-purple12Dark);--backgroundFocus:var(--c-purple11Dark);--borderColor:var(--c-purple10Dark);--borderColorHover:var(--c-purple9Dark);--borderColorFocus:var(--c-purple8Dark);--borderColorPress:var(--c-purple7Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_purple_ProgressIndicator, .t_light .t_dark .t_purple_SliderThumb, .t_light .t_dark .t_purple_SwitchThumb, .t_light .t_dark .t_purple_Tooltip, .t_purple_ProgressIndicator, .t_purple_SliderThumb, .t_purple_SwitchThumb, .t_purple_Tooltip {--color:var(--c-purple2Dark);--colorHover:var(--c-purple3Dark);--colorPress:var(--c-purple1Dark);--colorFocus:var(--c-purple1Dark);--background:var(--c-purple12Dark);--backgroundHover:var(--c-purple11Dark);--backgroundPress:var(--c-purple12Dark);--backgroundFocus:var(--c-purple11Dark);--borderColor:var(--c-purple10Dark);--borderColorHover:var(--c-purple9Dark);--borderColorFocus:var(--c-purple8Dark);--borderColorPress:var(--c-purple7Dark);}
  }
:root.t_dark .t_light .t_dark .t_pink_ProgressIndicator, :root.t_dark .t_light .t_dark .t_pink_SliderThumb, :root.t_dark .t_light .t_dark .t_pink_SwitchThumb, :root.t_dark .t_light .t_dark .t_pink_Tooltip, :root.t_dark .t_pink_ProgressIndicator, :root.t_dark .t_pink_SliderThumb, :root.t_dark .t_pink_SwitchThumb, :root.t_dark .t_pink_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_pink_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_pink_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_pink_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_pink_Tooltip, :root.t_light .t_dark .t_pink_ProgressIndicator, :root.t_light .t_dark .t_pink_SliderThumb, :root.t_light .t_dark .t_pink_SwitchThumb, :root.t_light .t_dark .t_pink_Tooltip, .tm_xxt {--color:var(--c-pink2Dark);--colorHover:var(--c-pink3Dark);--colorPress:var(--c-pink1Dark);--colorFocus:var(--c-pink1Dark);--background:var(--c-pink12Dark);--backgroundHover:var(--c-pink11Dark);--backgroundPress:var(--c-pink12Dark);--backgroundFocus:var(--c-pink11Dark);--borderColor:var(--c-pink10Dark);--borderColorHover:var(--c-pink9Dark);--borderColorFocus:var(--c-pink8Dark);--borderColorPress:var(--c-pink7Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_pink_ProgressIndicator, .t_light .t_dark .t_pink_SliderThumb, .t_light .t_dark .t_pink_SwitchThumb, .t_light .t_dark .t_pink_Tooltip, .t_pink_ProgressIndicator, .t_pink_SliderThumb, .t_pink_SwitchThumb, .t_pink_Tooltip {--color:var(--c-pink2Dark);--colorHover:var(--c-pink3Dark);--colorPress:var(--c-pink1Dark);--colorFocus:var(--c-pink1Dark);--background:var(--c-pink12Dark);--backgroundHover:var(--c-pink11Dark);--backgroundPress:var(--c-pink12Dark);--backgroundFocus:var(--c-pink11Dark);--borderColor:var(--c-pink10Dark);--borderColorHover:var(--c-pink9Dark);--borderColorFocus:var(--c-pink8Dark);--borderColorPress:var(--c-pink7Dark);}
  }
:root.t_dark .t_light .t_dark .t_red_ProgressIndicator, :root.t_dark .t_light .t_dark .t_red_SliderThumb, :root.t_dark .t_light .t_dark .t_red_SwitchThumb, :root.t_dark .t_light .t_dark .t_red_Tooltip, :root.t_dark .t_red_ProgressIndicator, :root.t_dark .t_red_SliderThumb, :root.t_dark .t_red_SwitchThumb, :root.t_dark .t_red_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_red_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_red_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_red_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_red_Tooltip, :root.t_light .t_dark .t_red_ProgressIndicator, :root.t_light .t_dark .t_red_SliderThumb, :root.t_light .t_dark .t_red_SwitchThumb, :root.t_light .t_dark .t_red_Tooltip, .tm_xxt {--color:var(--c-red2Dark);--colorHover:var(--c-red3Dark);--colorPress:var(--c-red1Dark);--colorFocus:var(--c-red1Dark);--background:var(--c-red12Dark);--backgroundHover:var(--c-red11Dark);--backgroundPress:var(--c-red12Dark);--backgroundFocus:var(--c-red11Dark);--borderColor:var(--c-red10Dark);--borderColorHover:var(--c-red9Dark);--borderColorFocus:var(--c-red8Dark);--borderColorPress:var(--c-red7Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_red_ProgressIndicator, .t_light .t_dark .t_red_SliderThumb, .t_light .t_dark .t_red_SwitchThumb, .t_light .t_dark .t_red_Tooltip, .t_red_ProgressIndicator, .t_red_SliderThumb, .t_red_SwitchThumb, .t_red_Tooltip {--color:var(--c-red2Dark);--colorHover:var(--c-red3Dark);--colorPress:var(--c-red1Dark);--colorFocus:var(--c-red1Dark);--background:var(--c-red12Dark);--backgroundHover:var(--c-red11Dark);--backgroundPress:var(--c-red12Dark);--backgroundFocus:var(--c-red11Dark);--borderColor:var(--c-red10Dark);--borderColorHover:var(--c-red9Dark);--borderColorFocus:var(--c-red8Dark);--borderColorPress:var(--c-red7Dark);}
  }
:root.t_dark .t_gray_ProgressIndicator, :root.t_dark .t_gray_SliderThumb, :root.t_dark .t_gray_SwitchThumb, :root.t_dark .t_gray_Tooltip, :root.t_dark .t_light .t_dark .t_gray_ProgressIndicator, :root.t_dark .t_light .t_dark .t_gray_SliderThumb, :root.t_dark .t_light .t_dark .t_gray_SwitchThumb, :root.t_dark .t_light .t_dark .t_gray_Tooltip, :root.t_light .t_dark .t_gray_ProgressIndicator, :root.t_light .t_dark .t_gray_SliderThumb, :root.t_light .t_dark .t_gray_SwitchThumb, :root.t_light .t_dark .t_gray_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_gray_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_gray_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_gray_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_gray_Tooltip, .tm_xxt {--color:var(--c-gray2Dark);--colorHover:var(--c-gray3Dark);--colorPress:var(--c-gray1Dark);--colorFocus:var(--c-gray1Dark);--background:var(--c-gray12Dark);--backgroundHover:var(--c-gray11Dark);--backgroundPress:var(--c-gray12Dark);--backgroundFocus:var(--c-gray11Dark);--borderColor:var(--c-gray10Dark);--borderColorHover:var(--c-gray9Dark);--borderColorFocus:var(--c-gray8Dark);--borderColorPress:var(--c-gray7Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_gray_ProgressIndicator, .t_gray_SliderThumb, .t_gray_SwitchThumb, .t_gray_Tooltip, .t_light .t_dark .t_gray_ProgressIndicator, .t_light .t_dark .t_gray_SliderThumb, .t_light .t_dark .t_gray_SwitchThumb, .t_light .t_dark .t_gray_Tooltip {--color:var(--c-gray2Dark);--colorHover:var(--c-gray3Dark);--colorPress:var(--c-gray1Dark);--colorFocus:var(--c-gray1Dark);--background:var(--c-gray12Dark);--backgroundHover:var(--c-gray11Dark);--backgroundPress:var(--c-gray12Dark);--backgroundFocus:var(--c-gray11Dark);--borderColor:var(--c-gray10Dark);--borderColorHover:var(--c-gray9Dark);--borderColorFocus:var(--c-gray8Dark);--borderColorPress:var(--c-gray7Dark);}
  }
:root.t_dark .t_light .t_active_ProgressIndicator, :root.t_dark .t_light .t_active_SliderThumb, :root.t_dark .t_light .t_active_SwitchThumb, :root.t_dark .t_light .t_active_Tooltip, :root.t_dark .t_light .t_dark .t_light .t_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_active_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_active_Tooltip, :root.t_light .t_active_ProgressIndicator, :root.t_light .t_active_SliderThumb, :root.t_light .t_active_SwitchThumb, :root.t_light .t_active_Tooltip, :root.t_light .t_dark .t_light .t_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_active_SliderThumb, :root.t_light .t_dark .t_light .t_active_SwitchThumb, :root.t_light .t_dark .t_light .t_active_Tooltip, .tm_xxt {--color:var(--c-white2);--colorHover:var(--c-black12);--colorPress:var(--c-white3);--colorFocus:var(--c-white3);--background:var(--c-white10);--backgroundHover:var(--c-gray9Light);--backgroundPress:var(--c-white10);--backgroundFocus:var(--c-gray9Light);--borderColor:var(--c-white8);--borderColorHover:var(--c-white7);--borderColorFocus:var(--c-white6);--borderColorPress:var(--c-white5);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_active_ProgressIndicator, .t_active_SliderThumb, .t_active_SwitchThumb, .t_active_Tooltip, .t_dark .t_light .t_active_ProgressIndicator, .t_dark .t_light .t_active_SliderThumb, .t_dark .t_light .t_active_SwitchThumb, .t_dark .t_light .t_active_Tooltip {--color:var(--c-white2);--colorHover:var(--c-black12);--colorPress:var(--c-white3);--colorFocus:var(--c-white3);--background:var(--c-white10);--backgroundHover:var(--c-gray9Light);--backgroundPress:var(--c-white10);--backgroundFocus:var(--c-gray9Light);--borderColor:var(--c-white8);--borderColorHover:var(--c-white7);--borderColorFocus:var(--c-white6);--borderColorPress:var(--c-white5);}
  }
:root.t_dark .t_active_ProgressIndicator, :root.t_dark .t_active_SliderThumb, :root.t_dark .t_active_SwitchThumb, :root.t_dark .t_active_Tooltip, :root.t_dark .t_light .t_dark .t_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_active_SliderThumb, :root.t_dark .t_light .t_dark .t_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_active_Tooltip, :root.t_light .t_dark .t_active_ProgressIndicator, :root.t_light .t_dark .t_active_SliderThumb, :root.t_light .t_dark .t_active_SwitchThumb, :root.t_light .t_dark .t_active_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_active_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_active_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_active_Tooltip, .tm_xxt {--color:var(--c-black2);--colorHover:var(--c-black3);--colorPress:var(--c-black1);--colorFocus:var(--c-black1);--background:var(--c-black10);--backgroundHover:var(--c-black9);--backgroundPress:var(--c-black10);--backgroundFocus:var(--c-black9);--borderColor:var(--c-black8);--borderColorHover:var(--c-black7);--borderColorFocus:var(--c-black6);--borderColorPress:var(--c-black5);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_active_ProgressIndicator, .t_active_SliderThumb, .t_active_SwitchThumb, .t_active_Tooltip, .t_light .t_dark .t_active_ProgressIndicator, .t_light .t_dark .t_active_SliderThumb, .t_light .t_dark .t_active_SwitchThumb, .t_light .t_dark .t_active_Tooltip {--color:var(--c-black2);--colorHover:var(--c-black3);--colorPress:var(--c-black1);--colorFocus:var(--c-black1);--background:var(--c-black10);--backgroundHover:var(--c-black9);--backgroundPress:var(--c-black10);--backgroundFocus:var(--c-black9);--borderColor:var(--c-black8);--borderColorHover:var(--c-black7);--borderColorFocus:var(--c-black6);--borderColorPress:var(--c-black5);}
  }
:root.t_dark .t_light .t_dark .t_light .t_orange_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_orange_active_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_orange_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_orange_active_Tooltip, :root.t_dark .t_light .t_orange_active_ProgressIndicator, :root.t_dark .t_light .t_orange_active_SliderThumb, :root.t_dark .t_light .t_orange_active_SwitchThumb, :root.t_dark .t_light .t_orange_active_Tooltip, :root.t_light .t_dark .t_light .t_orange_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_orange_active_SliderThumb, :root.t_light .t_dark .t_light .t_orange_active_SwitchThumb, :root.t_light .t_dark .t_light .t_orange_active_Tooltip, :root.t_light .t_orange_active_ProgressIndicator, :root.t_light .t_orange_active_SliderThumb, :root.t_light .t_orange_active_SwitchThumb, :root.t_light .t_orange_active_Tooltip, .tm_xxt {--color:var(--c-orange2Light);--colorHover:var(--c-orange1Light);--colorPress:var(--c-orange3Light);--colorFocus:var(--c-orange3Light);--background:var(--c-orange10Light);--backgroundHover:var(--c-orange9Dark);--backgroundPress:var(--c-orange10Light);--backgroundFocus:var(--c-orange9Dark);--borderColor:var(--c-orange8Light);--borderColorHover:var(--c-orange7Light);--borderColorFocus:var(--c-orange6Light);--borderColorPress:var(--c-orange5Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_orange_active_ProgressIndicator, .t_dark .t_light .t_orange_active_SliderThumb, .t_dark .t_light .t_orange_active_SwitchThumb, .t_dark .t_light .t_orange_active_Tooltip, .t_orange_active_ProgressIndicator, .t_orange_active_SliderThumb, .t_orange_active_SwitchThumb, .t_orange_active_Tooltip {--color:var(--c-orange2Light);--colorHover:var(--c-orange1Light);--colorPress:var(--c-orange3Light);--colorFocus:var(--c-orange3Light);--background:var(--c-orange10Light);--backgroundHover:var(--c-orange9Dark);--backgroundPress:var(--c-orange10Light);--backgroundFocus:var(--c-orange9Dark);--borderColor:var(--c-orange8Light);--borderColorHover:var(--c-orange7Light);--borderColorFocus:var(--c-orange6Light);--borderColorPress:var(--c-orange5Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_yellow_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_yellow_active_Tooltip, :root.t_dark .t_light .t_yellow_active_ProgressIndicator, :root.t_dark .t_light .t_yellow_active_SliderThumb, :root.t_dark .t_light .t_yellow_active_SwitchThumb, :root.t_dark .t_light .t_yellow_active_Tooltip, :root.t_light .t_dark .t_light .t_yellow_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_yellow_active_SliderThumb, :root.t_light .t_dark .t_light .t_yellow_active_SwitchThumb, :root.t_light .t_dark .t_light .t_yellow_active_Tooltip, :root.t_light .t_yellow_active_ProgressIndicator, :root.t_light .t_yellow_active_SliderThumb, :root.t_light .t_yellow_active_SwitchThumb, :root.t_light .t_yellow_active_Tooltip, .tm_xxt {--color:var(--c-yellow2Light);--colorHover:var(--c-yellow1Light);--colorPress:var(--c-yellow3Light);--colorFocus:var(--c-yellow3Light);--background:var(--c-yellow10Light);--backgroundHover:var(--c-yellow9Dark);--backgroundPress:var(--c-yellow10Light);--backgroundFocus:var(--c-yellow9Dark);--borderColor:var(--c-yellow8Light);--borderColorHover:var(--c-yellow7Light);--borderColorFocus:var(--c-yellow6Light);--borderColorPress:var(--c-yellow5Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_yellow_active_ProgressIndicator, .t_dark .t_light .t_yellow_active_SliderThumb, .t_dark .t_light .t_yellow_active_SwitchThumb, .t_dark .t_light .t_yellow_active_Tooltip, .t_yellow_active_ProgressIndicator, .t_yellow_active_SliderThumb, .t_yellow_active_SwitchThumb, .t_yellow_active_Tooltip {--color:var(--c-yellow2Light);--colorHover:var(--c-yellow1Light);--colorPress:var(--c-yellow3Light);--colorFocus:var(--c-yellow3Light);--background:var(--c-yellow10Light);--backgroundHover:var(--c-yellow9Dark);--backgroundPress:var(--c-yellow10Light);--backgroundFocus:var(--c-yellow9Dark);--borderColor:var(--c-yellow8Light);--borderColorHover:var(--c-yellow7Light);--borderColorFocus:var(--c-yellow6Light);--borderColorPress:var(--c-yellow5Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_green_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_green_active_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_green_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_green_active_Tooltip, :root.t_dark .t_light .t_green_active_ProgressIndicator, :root.t_dark .t_light .t_green_active_SliderThumb, :root.t_dark .t_light .t_green_active_SwitchThumb, :root.t_dark .t_light .t_green_active_Tooltip, :root.t_light .t_dark .t_light .t_green_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_green_active_SliderThumb, :root.t_light .t_dark .t_light .t_green_active_SwitchThumb, :root.t_light .t_dark .t_light .t_green_active_Tooltip, :root.t_light .t_green_active_ProgressIndicator, :root.t_light .t_green_active_SliderThumb, :root.t_light .t_green_active_SwitchThumb, :root.t_light .t_green_active_Tooltip, .tm_xxt {--color:var(--c-green2Light);--colorHover:var(--c-green1Light);--colorPress:var(--c-green3Light);--colorFocus:var(--c-green3Light);--background:var(--c-green10Light);--backgroundHover:var(--c-green9Dark);--backgroundPress:var(--c-green10Light);--backgroundFocus:var(--c-green9Dark);--borderColor:var(--c-green8Light);--borderColorHover:var(--c-green7Light);--borderColorFocus:var(--c-green6Light);--borderColorPress:var(--c-green5Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_green_active_ProgressIndicator, .t_dark .t_light .t_green_active_SliderThumb, .t_dark .t_light .t_green_active_SwitchThumb, .t_dark .t_light .t_green_active_Tooltip, .t_green_active_ProgressIndicator, .t_green_active_SliderThumb, .t_green_active_SwitchThumb, .t_green_active_Tooltip {--color:var(--c-green2Light);--colorHover:var(--c-green1Light);--colorPress:var(--c-green3Light);--colorFocus:var(--c-green3Light);--background:var(--c-green10Light);--backgroundHover:var(--c-green9Dark);--backgroundPress:var(--c-green10Light);--backgroundFocus:var(--c-green9Dark);--borderColor:var(--c-green8Light);--borderColorHover:var(--c-green7Light);--borderColorFocus:var(--c-green6Light);--borderColorPress:var(--c-green5Light);}
  }
:root.t_dark .t_light .t_blue_active_ProgressIndicator, :root.t_dark .t_light .t_blue_active_SliderThumb, :root.t_dark .t_light .t_blue_active_SwitchThumb, :root.t_dark .t_light .t_blue_active_Tooltip, :root.t_dark .t_light .t_dark .t_light .t_blue_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_blue_active_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_blue_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_blue_active_Tooltip, :root.t_light .t_blue_active_ProgressIndicator, :root.t_light .t_blue_active_SliderThumb, :root.t_light .t_blue_active_SwitchThumb, :root.t_light .t_blue_active_Tooltip, :root.t_light .t_dark .t_light .t_blue_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_blue_active_SliderThumb, :root.t_light .t_dark .t_light .t_blue_active_SwitchThumb, :root.t_light .t_dark .t_light .t_blue_active_Tooltip, .tm_xxt {--color:var(--c-blue2Light);--colorHover:var(--c-blue1Light);--colorPress:var(--c-blue3Light);--colorFocus:var(--c-blue3Light);--background:var(--c-blue10Light);--backgroundHover:var(--c-blue9Dark);--backgroundPress:var(--c-blue10Light);--backgroundFocus:var(--c-blue9Dark);--borderColor:var(--c-blue8Light);--borderColorHover:var(--c-blue7Light);--borderColorFocus:var(--c-blue6Light);--borderColorPress:var(--c-blue5Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_blue_active_ProgressIndicator, .t_blue_active_SliderThumb, .t_blue_active_SwitchThumb, .t_blue_active_Tooltip, .t_dark .t_light .t_blue_active_ProgressIndicator, .t_dark .t_light .t_blue_active_SliderThumb, .t_dark .t_light .t_blue_active_SwitchThumb, .t_dark .t_light .t_blue_active_Tooltip {--color:var(--c-blue2Light);--colorHover:var(--c-blue1Light);--colorPress:var(--c-blue3Light);--colorFocus:var(--c-blue3Light);--background:var(--c-blue10Light);--backgroundHover:var(--c-blue9Dark);--backgroundPress:var(--c-blue10Light);--backgroundFocus:var(--c-blue9Dark);--borderColor:var(--c-blue8Light);--borderColorHover:var(--c-blue7Light);--borderColorFocus:var(--c-blue6Light);--borderColorPress:var(--c-blue5Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_purple_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_purple_active_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_purple_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_purple_active_Tooltip, :root.t_dark .t_light .t_purple_active_ProgressIndicator, :root.t_dark .t_light .t_purple_active_SliderThumb, :root.t_dark .t_light .t_purple_active_SwitchThumb, :root.t_dark .t_light .t_purple_active_Tooltip, :root.t_light .t_dark .t_light .t_purple_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_purple_active_SliderThumb, :root.t_light .t_dark .t_light .t_purple_active_SwitchThumb, :root.t_light .t_dark .t_light .t_purple_active_Tooltip, :root.t_light .t_purple_active_ProgressIndicator, :root.t_light .t_purple_active_SliderThumb, :root.t_light .t_purple_active_SwitchThumb, :root.t_light .t_purple_active_Tooltip, .tm_xxt {--color:var(--c-purple2Light);--colorHover:var(--c-purple1Light);--colorPress:var(--c-purple3Light);--colorFocus:var(--c-purple3Light);--background:var(--c-purple10Light);--backgroundHover:var(--c-purple9Dark);--backgroundPress:var(--c-purple10Light);--backgroundFocus:var(--c-purple9Dark);--borderColor:var(--c-purple8Light);--borderColorHover:var(--c-purple7Light);--borderColorFocus:var(--c-purple6Light);--borderColorPress:var(--c-purple5Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_purple_active_ProgressIndicator, .t_dark .t_light .t_purple_active_SliderThumb, .t_dark .t_light .t_purple_active_SwitchThumb, .t_dark .t_light .t_purple_active_Tooltip, .t_purple_active_ProgressIndicator, .t_purple_active_SliderThumb, .t_purple_active_SwitchThumb, .t_purple_active_Tooltip {--color:var(--c-purple2Light);--colorHover:var(--c-purple1Light);--colorPress:var(--c-purple3Light);--colorFocus:var(--c-purple3Light);--background:var(--c-purple10Light);--backgroundHover:var(--c-purple9Dark);--backgroundPress:var(--c-purple10Light);--backgroundFocus:var(--c-purple9Dark);--borderColor:var(--c-purple8Light);--borderColorHover:var(--c-purple7Light);--borderColorFocus:var(--c-purple6Light);--borderColorPress:var(--c-purple5Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_pink_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_pink_active_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_pink_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_pink_active_Tooltip, :root.t_dark .t_light .t_pink_active_ProgressIndicator, :root.t_dark .t_light .t_pink_active_SliderThumb, :root.t_dark .t_light .t_pink_active_SwitchThumb, :root.t_dark .t_light .t_pink_active_Tooltip, :root.t_light .t_dark .t_light .t_pink_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_pink_active_SliderThumb, :root.t_light .t_dark .t_light .t_pink_active_SwitchThumb, :root.t_light .t_dark .t_light .t_pink_active_Tooltip, :root.t_light .t_pink_active_ProgressIndicator, :root.t_light .t_pink_active_SliderThumb, :root.t_light .t_pink_active_SwitchThumb, :root.t_light .t_pink_active_Tooltip, .tm_xxt {--color:var(--c-pink2Light);--colorHover:var(--c-pink1Light);--colorPress:var(--c-pink3Light);--colorFocus:var(--c-pink3Light);--background:var(--c-pink10Light);--backgroundHover:var(--c-pink9Dark);--backgroundPress:var(--c-pink10Light);--backgroundFocus:var(--c-pink9Dark);--borderColor:var(--c-pink8Light);--borderColorHover:var(--c-pink7Light);--borderColorFocus:var(--c-pink6Light);--borderColorPress:var(--c-pink5Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_pink_active_ProgressIndicator, .t_dark .t_light .t_pink_active_SliderThumb, .t_dark .t_light .t_pink_active_SwitchThumb, .t_dark .t_light .t_pink_active_Tooltip, .t_pink_active_ProgressIndicator, .t_pink_active_SliderThumb, .t_pink_active_SwitchThumb, .t_pink_active_Tooltip {--color:var(--c-pink2Light);--colorHover:var(--c-pink1Light);--colorPress:var(--c-pink3Light);--colorFocus:var(--c-pink3Light);--background:var(--c-pink10Light);--backgroundHover:var(--c-pink9Dark);--backgroundPress:var(--c-pink10Light);--backgroundFocus:var(--c-pink9Dark);--borderColor:var(--c-pink8Light);--borderColorHover:var(--c-pink7Light);--borderColorFocus:var(--c-pink6Light);--borderColorPress:var(--c-pink5Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_red_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_red_active_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_red_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_red_active_Tooltip, :root.t_dark .t_light .t_red_active_ProgressIndicator, :root.t_dark .t_light .t_red_active_SliderThumb, :root.t_dark .t_light .t_red_active_SwitchThumb, :root.t_dark .t_light .t_red_active_Tooltip, :root.t_light .t_dark .t_light .t_red_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_red_active_SliderThumb, :root.t_light .t_dark .t_light .t_red_active_SwitchThumb, :root.t_light .t_dark .t_light .t_red_active_Tooltip, :root.t_light .t_red_active_ProgressIndicator, :root.t_light .t_red_active_SliderThumb, :root.t_light .t_red_active_SwitchThumb, :root.t_light .t_red_active_Tooltip, .tm_xxt {--color:var(--c-red2Light);--colorHover:var(--c-red1Light);--colorPress:var(--c-red3Light);--colorFocus:var(--c-red3Light);--background:var(--c-red10Light);--backgroundHover:var(--c-red9Dark);--backgroundPress:var(--c-red10Light);--backgroundFocus:var(--c-red9Dark);--borderColor:var(--c-red8Light);--borderColorHover:var(--c-red7Light);--borderColorFocus:var(--c-red6Light);--borderColorPress:var(--c-red5Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_red_active_ProgressIndicator, .t_dark .t_light .t_red_active_SliderThumb, .t_dark .t_light .t_red_active_SwitchThumb, .t_dark .t_light .t_red_active_Tooltip, .t_red_active_ProgressIndicator, .t_red_active_SliderThumb, .t_red_active_SwitchThumb, .t_red_active_Tooltip {--color:var(--c-red2Light);--colorHover:var(--c-red1Light);--colorPress:var(--c-red3Light);--colorFocus:var(--c-red3Light);--background:var(--c-red10Light);--backgroundHover:var(--c-red9Dark);--backgroundPress:var(--c-red10Light);--backgroundFocus:var(--c-red9Dark);--borderColor:var(--c-red8Light);--borderColorHover:var(--c-red7Light);--borderColorFocus:var(--c-red6Light);--borderColorPress:var(--c-red5Light);}
  }
:root.t_dark .t_light .t_dark .t_light .t_gray_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_light .t_gray_active_SliderThumb, :root.t_dark .t_light .t_dark .t_light .t_gray_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_light .t_gray_active_Tooltip, :root.t_dark .t_light .t_gray_active_ProgressIndicator, :root.t_dark .t_light .t_gray_active_SliderThumb, :root.t_dark .t_light .t_gray_active_SwitchThumb, :root.t_dark .t_light .t_gray_active_Tooltip, :root.t_light .t_dark .t_light .t_gray_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_gray_active_SliderThumb, :root.t_light .t_dark .t_light .t_gray_active_SwitchThumb, :root.t_light .t_dark .t_light .t_gray_active_Tooltip, :root.t_light .t_gray_active_ProgressIndicator, :root.t_light .t_gray_active_SliderThumb, :root.t_light .t_gray_active_SwitchThumb, :root.t_light .t_gray_active_Tooltip, .tm_xxt {--color:var(--c-gray2Light);--colorHover:var(--c-gray1Light);--colorPress:var(--c-gray3Light);--colorFocus:var(--c-gray3Light);--background:var(--c-gray10Light);--backgroundHover:var(--c-gray9Light);--backgroundPress:var(--c-gray10Light);--backgroundFocus:var(--c-gray9Light);--borderColor:var(--c-gray8Light);--borderColorHover:var(--c-gray7Light);--borderColorFocus:var(--c-gray6Light);--borderColorPress:var(--c-gray5Light);}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_dark .t_light .t_gray_active_ProgressIndicator, .t_dark .t_light .t_gray_active_SliderThumb, .t_dark .t_light .t_gray_active_SwitchThumb, .t_dark .t_light .t_gray_active_Tooltip, .t_gray_active_ProgressIndicator, .t_gray_active_SliderThumb, .t_gray_active_SwitchThumb, .t_gray_active_Tooltip {--color:var(--c-gray2Light);--colorHover:var(--c-gray1Light);--colorPress:var(--c-gray3Light);--colorFocus:var(--c-gray3Light);--background:var(--c-gray10Light);--backgroundHover:var(--c-gray9Light);--backgroundPress:var(--c-gray10Light);--backgroundFocus:var(--c-gray9Light);--borderColor:var(--c-gray8Light);--borderColorHover:var(--c-gray7Light);--borderColorFocus:var(--c-gray6Light);--borderColorPress:var(--c-gray5Light);}
  }
:root.t_dark .t_light .t_dark .t_orange_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_orange_active_SliderThumb, :root.t_dark .t_light .t_dark .t_orange_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_orange_active_Tooltip, :root.t_dark .t_orange_active_ProgressIndicator, :root.t_dark .t_orange_active_SliderThumb, :root.t_dark .t_orange_active_SwitchThumb, :root.t_dark .t_orange_active_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_orange_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_orange_active_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_orange_active_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_orange_active_Tooltip, :root.t_light .t_dark .t_orange_active_ProgressIndicator, :root.t_light .t_dark .t_orange_active_SliderThumb, :root.t_light .t_dark .t_orange_active_SwitchThumb, :root.t_light .t_dark .t_orange_active_Tooltip, .tm_xxt {--color:var(--c-orange2Dark);--colorHover:var(--c-orange3Dark);--colorPress:var(--c-orange1Dark);--colorFocus:var(--c-orange1Dark);--background:var(--c-orange10Dark);--backgroundHover:var(--c-orange9Dark);--backgroundPress:var(--c-orange10Dark);--backgroundFocus:var(--c-orange9Dark);--borderColor:var(--c-orange8Dark);--borderColorHover:var(--c-orange7Dark);--borderColorFocus:var(--c-orange6Dark);--borderColorPress:var(--c-orange5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_orange_active_ProgressIndicator, .t_light .t_dark .t_orange_active_SliderThumb, .t_light .t_dark .t_orange_active_SwitchThumb, .t_light .t_dark .t_orange_active_Tooltip, .t_orange_active_ProgressIndicator, .t_orange_active_SliderThumb, .t_orange_active_SwitchThumb, .t_orange_active_Tooltip {--color:var(--c-orange2Dark);--colorHover:var(--c-orange3Dark);--colorPress:var(--c-orange1Dark);--colorFocus:var(--c-orange1Dark);--background:var(--c-orange10Dark);--backgroundHover:var(--c-orange9Dark);--backgroundPress:var(--c-orange10Dark);--backgroundFocus:var(--c-orange9Dark);--borderColor:var(--c-orange8Dark);--borderColorHover:var(--c-orange7Dark);--borderColorFocus:var(--c-orange6Dark);--borderColorPress:var(--c-orange5Dark);}
  }
:root.t_dark .t_light .t_dark .t_yellow_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_yellow_active_SliderThumb, :root.t_dark .t_light .t_dark .t_yellow_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_yellow_active_Tooltip, :root.t_dark .t_yellow_active_ProgressIndicator, :root.t_dark .t_yellow_active_SliderThumb, :root.t_dark .t_yellow_active_SwitchThumb, :root.t_dark .t_yellow_active_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_yellow_active_Tooltip, :root.t_light .t_dark .t_yellow_active_ProgressIndicator, :root.t_light .t_dark .t_yellow_active_SliderThumb, :root.t_light .t_dark .t_yellow_active_SwitchThumb, :root.t_light .t_dark .t_yellow_active_Tooltip, .tm_xxt {--color:var(--c-yellow2Dark);--colorHover:var(--c-yellow3Dark);--colorPress:var(--c-yellow1Dark);--colorFocus:var(--c-yellow1Dark);--background:var(--c-yellow10Dark);--backgroundHover:var(--c-yellow9Dark);--backgroundPress:var(--c-yellow10Dark);--backgroundFocus:var(--c-yellow9Dark);--borderColor:var(--c-yellow8Dark);--borderColorHover:var(--c-yellow7Dark);--borderColorFocus:var(--c-yellow6Dark);--borderColorPress:var(--c-yellow5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_yellow_active_ProgressIndicator, .t_light .t_dark .t_yellow_active_SliderThumb, .t_light .t_dark .t_yellow_active_SwitchThumb, .t_light .t_dark .t_yellow_active_Tooltip, .t_yellow_active_ProgressIndicator, .t_yellow_active_SliderThumb, .t_yellow_active_SwitchThumb, .t_yellow_active_Tooltip {--color:var(--c-yellow2Dark);--colorHover:var(--c-yellow3Dark);--colorPress:var(--c-yellow1Dark);--colorFocus:var(--c-yellow1Dark);--background:var(--c-yellow10Dark);--backgroundHover:var(--c-yellow9Dark);--backgroundPress:var(--c-yellow10Dark);--backgroundFocus:var(--c-yellow9Dark);--borderColor:var(--c-yellow8Dark);--borderColorHover:var(--c-yellow7Dark);--borderColorFocus:var(--c-yellow6Dark);--borderColorPress:var(--c-yellow5Dark);}
  }
:root.t_dark .t_green_active_ProgressIndicator, :root.t_dark .t_green_active_SliderThumb, :root.t_dark .t_green_active_SwitchThumb, :root.t_dark .t_green_active_Tooltip, :root.t_dark .t_light .t_dark .t_green_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_green_active_SliderThumb, :root.t_dark .t_light .t_dark .t_green_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_green_active_Tooltip, :root.t_light .t_dark .t_green_active_ProgressIndicator, :root.t_light .t_dark .t_green_active_SliderThumb, :root.t_light .t_dark .t_green_active_SwitchThumb, :root.t_light .t_dark .t_green_active_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_green_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_green_active_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_green_active_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_green_active_Tooltip, .tm_xxt {--color:var(--c-green2Dark);--colorHover:var(--c-green3Dark);--colorPress:var(--c-green1Dark);--colorFocus:var(--c-green1Dark);--background:var(--c-green10Dark);--backgroundHover:var(--c-green9Dark);--backgroundPress:var(--c-green10Dark);--backgroundFocus:var(--c-green9Dark);--borderColor:var(--c-green8Dark);--borderColorHover:var(--c-green7Dark);--borderColorFocus:var(--c-green6Dark);--borderColorPress:var(--c-green5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_green_active_ProgressIndicator, .t_green_active_SliderThumb, .t_green_active_SwitchThumb, .t_green_active_Tooltip, .t_light .t_dark .t_green_active_ProgressIndicator, .t_light .t_dark .t_green_active_SliderThumb, .t_light .t_dark .t_green_active_SwitchThumb, .t_light .t_dark .t_green_active_Tooltip {--color:var(--c-green2Dark);--colorHover:var(--c-green3Dark);--colorPress:var(--c-green1Dark);--colorFocus:var(--c-green1Dark);--background:var(--c-green10Dark);--backgroundHover:var(--c-green9Dark);--backgroundPress:var(--c-green10Dark);--backgroundFocus:var(--c-green9Dark);--borderColor:var(--c-green8Dark);--borderColorHover:var(--c-green7Dark);--borderColorFocus:var(--c-green6Dark);--borderColorPress:var(--c-green5Dark);}
  }
:root.t_dark .t_blue_active_ProgressIndicator, :root.t_dark .t_blue_active_SliderThumb, :root.t_dark .t_blue_active_SwitchThumb, :root.t_dark .t_blue_active_Tooltip, :root.t_dark .t_light .t_dark .t_blue_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_blue_active_SliderThumb, :root.t_dark .t_light .t_dark .t_blue_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_blue_active_Tooltip, :root.t_light .t_dark .t_blue_active_ProgressIndicator, :root.t_light .t_dark .t_blue_active_SliderThumb, :root.t_light .t_dark .t_blue_active_SwitchThumb, :root.t_light .t_dark .t_blue_active_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_blue_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_blue_active_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_blue_active_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_blue_active_Tooltip, .tm_xxt {--color:var(--c-blue2Dark);--colorHover:var(--c-blue3Dark);--colorPress:var(--c-blue1Dark);--colorFocus:var(--c-blue1Dark);--background:var(--c-blue10Dark);--backgroundHover:var(--c-blue9Dark);--backgroundPress:var(--c-blue10Dark);--backgroundFocus:var(--c-blue9Dark);--borderColor:var(--c-blue8Dark);--borderColorHover:var(--c-blue7Dark);--borderColorFocus:var(--c-blue6Dark);--borderColorPress:var(--c-blue5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_blue_active_ProgressIndicator, .t_blue_active_SliderThumb, .t_blue_active_SwitchThumb, .t_blue_active_Tooltip, .t_light .t_dark .t_blue_active_ProgressIndicator, .t_light .t_dark .t_blue_active_SliderThumb, .t_light .t_dark .t_blue_active_SwitchThumb, .t_light .t_dark .t_blue_active_Tooltip {--color:var(--c-blue2Dark);--colorHover:var(--c-blue3Dark);--colorPress:var(--c-blue1Dark);--colorFocus:var(--c-blue1Dark);--background:var(--c-blue10Dark);--backgroundHover:var(--c-blue9Dark);--backgroundPress:var(--c-blue10Dark);--backgroundFocus:var(--c-blue9Dark);--borderColor:var(--c-blue8Dark);--borderColorHover:var(--c-blue7Dark);--borderColorFocus:var(--c-blue6Dark);--borderColorPress:var(--c-blue5Dark);}
  }
:root.t_dark .t_light .t_dark .t_purple_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_purple_active_SliderThumb, :root.t_dark .t_light .t_dark .t_purple_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_purple_active_Tooltip, :root.t_dark .t_purple_active_ProgressIndicator, :root.t_dark .t_purple_active_SliderThumb, :root.t_dark .t_purple_active_SwitchThumb, :root.t_dark .t_purple_active_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_purple_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_purple_active_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_purple_active_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_purple_active_Tooltip, :root.t_light .t_dark .t_purple_active_ProgressIndicator, :root.t_light .t_dark .t_purple_active_SliderThumb, :root.t_light .t_dark .t_purple_active_SwitchThumb, :root.t_light .t_dark .t_purple_active_Tooltip, .tm_xxt {--color:var(--c-purple2Dark);--colorHover:var(--c-purple3Dark);--colorPress:var(--c-purple1Dark);--colorFocus:var(--c-purple1Dark);--background:var(--c-purple10Dark);--backgroundHover:var(--c-purple9Dark);--backgroundPress:var(--c-purple10Dark);--backgroundFocus:var(--c-purple9Dark);--borderColor:var(--c-purple8Dark);--borderColorHover:var(--c-purple7Dark);--borderColorFocus:var(--c-purple6Dark);--borderColorPress:var(--c-purple5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_purple_active_ProgressIndicator, .t_light .t_dark .t_purple_active_SliderThumb, .t_light .t_dark .t_purple_active_SwitchThumb, .t_light .t_dark .t_purple_active_Tooltip, .t_purple_active_ProgressIndicator, .t_purple_active_SliderThumb, .t_purple_active_SwitchThumb, .t_purple_active_Tooltip {--color:var(--c-purple2Dark);--colorHover:var(--c-purple3Dark);--colorPress:var(--c-purple1Dark);--colorFocus:var(--c-purple1Dark);--background:var(--c-purple10Dark);--backgroundHover:var(--c-purple9Dark);--backgroundPress:var(--c-purple10Dark);--backgroundFocus:var(--c-purple9Dark);--borderColor:var(--c-purple8Dark);--borderColorHover:var(--c-purple7Dark);--borderColorFocus:var(--c-purple6Dark);--borderColorPress:var(--c-purple5Dark);}
  }
:root.t_dark .t_light .t_dark .t_pink_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_pink_active_SliderThumb, :root.t_dark .t_light .t_dark .t_pink_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_pink_active_Tooltip, :root.t_dark .t_pink_active_ProgressIndicator, :root.t_dark .t_pink_active_SliderThumb, :root.t_dark .t_pink_active_SwitchThumb, :root.t_dark .t_pink_active_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_pink_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_pink_active_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_pink_active_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_pink_active_Tooltip, :root.t_light .t_dark .t_pink_active_ProgressIndicator, :root.t_light .t_dark .t_pink_active_SliderThumb, :root.t_light .t_dark .t_pink_active_SwitchThumb, :root.t_light .t_dark .t_pink_active_Tooltip, .tm_xxt {--color:var(--c-pink2Dark);--colorHover:var(--c-pink3Dark);--colorPress:var(--c-pink1Dark);--colorFocus:var(--c-pink1Dark);--background:var(--c-pink10Dark);--backgroundHover:var(--c-pink9Dark);--backgroundPress:var(--c-pink10Dark);--backgroundFocus:var(--c-pink9Dark);--borderColor:var(--c-pink8Dark);--borderColorHover:var(--c-pink7Dark);--borderColorFocus:var(--c-pink6Dark);--borderColorPress:var(--c-pink5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_pink_active_ProgressIndicator, .t_light .t_dark .t_pink_active_SliderThumb, .t_light .t_dark .t_pink_active_SwitchThumb, .t_light .t_dark .t_pink_active_Tooltip, .t_pink_active_ProgressIndicator, .t_pink_active_SliderThumb, .t_pink_active_SwitchThumb, .t_pink_active_Tooltip {--color:var(--c-pink2Dark);--colorHover:var(--c-pink3Dark);--colorPress:var(--c-pink1Dark);--colorFocus:var(--c-pink1Dark);--background:var(--c-pink10Dark);--backgroundHover:var(--c-pink9Dark);--backgroundPress:var(--c-pink10Dark);--backgroundFocus:var(--c-pink9Dark);--borderColor:var(--c-pink8Dark);--borderColorHover:var(--c-pink7Dark);--borderColorFocus:var(--c-pink6Dark);--borderColorPress:var(--c-pink5Dark);}
  }
:root.t_dark .t_light .t_dark .t_red_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_red_active_SliderThumb, :root.t_dark .t_light .t_dark .t_red_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_red_active_Tooltip, :root.t_dark .t_red_active_ProgressIndicator, :root.t_dark .t_red_active_SliderThumb, :root.t_dark .t_red_active_SwitchThumb, :root.t_dark .t_red_active_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_red_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_red_active_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_red_active_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_red_active_Tooltip, :root.t_light .t_dark .t_red_active_ProgressIndicator, :root.t_light .t_dark .t_red_active_SliderThumb, :root.t_light .t_dark .t_red_active_SwitchThumb, :root.t_light .t_dark .t_red_active_Tooltip, .tm_xxt {--color:var(--c-red2Dark);--colorHover:var(--c-red3Dark);--colorPress:var(--c-red1Dark);--colorFocus:var(--c-red1Dark);--background:var(--c-red10Dark);--backgroundHover:var(--c-red9Dark);--backgroundPress:var(--c-red10Dark);--backgroundFocus:var(--c-red9Dark);--borderColor:var(--c-red8Dark);--borderColorHover:var(--c-red7Dark);--borderColorFocus:var(--c-red6Dark);--borderColorPress:var(--c-red5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_light .t_dark .t_red_active_ProgressIndicator, .t_light .t_dark .t_red_active_SliderThumb, .t_light .t_dark .t_red_active_SwitchThumb, .t_light .t_dark .t_red_active_Tooltip, .t_red_active_ProgressIndicator, .t_red_active_SliderThumb, .t_red_active_SwitchThumb, .t_red_active_Tooltip {--color:var(--c-red2Dark);--colorHover:var(--c-red3Dark);--colorPress:var(--c-red1Dark);--colorFocus:var(--c-red1Dark);--background:var(--c-red10Dark);--backgroundHover:var(--c-red9Dark);--backgroundPress:var(--c-red10Dark);--backgroundFocus:var(--c-red9Dark);--borderColor:var(--c-red8Dark);--borderColorHover:var(--c-red7Dark);--borderColorFocus:var(--c-red6Dark);--borderColorPress:var(--c-red5Dark);}
  }
:root.t_dark .t_gray_active_ProgressIndicator, :root.t_dark .t_gray_active_SliderThumb, :root.t_dark .t_gray_active_SwitchThumb, :root.t_dark .t_gray_active_Tooltip, :root.t_dark .t_light .t_dark .t_gray_active_ProgressIndicator, :root.t_dark .t_light .t_dark .t_gray_active_SliderThumb, :root.t_dark .t_light .t_dark .t_gray_active_SwitchThumb, :root.t_dark .t_light .t_dark .t_gray_active_Tooltip, :root.t_light .t_dark .t_gray_active_ProgressIndicator, :root.t_light .t_dark .t_gray_active_SliderThumb, :root.t_light .t_dark .t_gray_active_SwitchThumb, :root.t_light .t_dark .t_gray_active_Tooltip, :root.t_light .t_dark .t_light .t_dark .t_gray_active_ProgressIndicator, :root.t_light .t_dark .t_light .t_dark .t_gray_active_SliderThumb, :root.t_light .t_dark .t_light .t_dark .t_gray_active_SwitchThumb, :root.t_light .t_dark .t_light .t_dark .t_gray_active_Tooltip, .tm_xxt {--color:var(--c-gray2Dark);--colorHover:var(--c-gray3Dark);--colorPress:var(--c-gray1Dark);--colorFocus:var(--c-gray1Dark);--background:var(--c-gray10Dark);--backgroundHover:var(--c-gray9Dark);--backgroundPress:var(--c-gray10Dark);--backgroundFocus:var(--c-gray9Dark);--borderColor:var(--c-gray8Dark);--borderColorHover:var(--c-gray7Dark);--borderColorFocus:var(--c-gray6Dark);--borderColorPress:var(--c-gray5Dark);}
@media(prefers-color-scheme:dark){
    body{background:var(--background);color:var(--color)}
    .t_gray_active_ProgressIndicator, .t_gray_active_SliderThumb, .t_gray_active_SwitchThumb, .t_gray_active_Tooltip, .t_light .t_dark .t_gray_active_ProgressIndicator, .t_light .t_dark .t_gray_active_SliderThumb, .t_light .t_dark .t_gray_active_SwitchThumb, .t_light .t_dark .t_gray_active_Tooltip {--color:var(--c-gray2Dark);--colorHover:var(--c-gray3Dark);--colorPress:var(--c-gray1Dark);--colorFocus:var(--c-gray1Dark);--background:var(--c-gray10Dark);--backgroundHover:var(--c-gray9Dark);--backgroundPress:var(--c-gray10Dark);--backgroundFocus:var(--c-gray9Dark);--borderColor:var(--c-gray8Dark);--borderColorHover:var(--c-gray7Dark);--borderColorFocus:var(--c-gray6Dark);--borderColorPress:var(--c-gray5Dark);}
  }
:root .t_redGoJoe, .tm_xxt {--background:#CA3D2A;--color:#ffffff;--red:#CA3D2A;}
@media(prefers-color-scheme:light){
    body{background:var(--background);color:var(--color)}
    .t_redGoJoe {--background:#CA3D2A;--color:#ffffff;--red:#CA3D2A;}
  }
  