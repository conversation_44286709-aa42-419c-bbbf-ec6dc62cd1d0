#!/usr/bin/env bash

# Exit on error
set -e

echo "🚀 Starting production update process..."

# Check if GOOGLE_SERVICES_ENV is set, if not, set it to Release for production
if [ -z "$GOOGLE_SERVICES_ENV" ]; then
  export GOOGLE_SERVICES_ENV="Release"
  echo "✅ Set GOOGLE_SERVICES_ENV to Release"
fi

# Run the prebuild script to set up environment
echo "🛠️ Running prebuild script..."
./eas-build-pre-install.sh

# Verify that the correct Google services files were copied
if [ -f "./services/google/GoogleService-Info.plist" ] && [ -f "./services/google/google-services.json" ]; then
  echo "✅ Google services files are in place"
else
  echo "❌ Google services files are missing. Prebuild script may have failed."
  exit 1
fi

# Export the app bundle
echo "📦 Exporting app bundle..."
npx expo export

# Create and publish the update to the production channel
echo "🔄 Publishing update to production channel..."
UPDATE_DATE=$(date +'%Y-%m-%d %H:%M:%S')
npx eas update --channel production --message "Production update $UPDATE_DATE"

echo "✅ Production update completed successfully!"
echo ""
echo "📱 Users will now receive a notification when they open the app"
echo "   informing them that an update is available. They will see a"
echo "   progress bar during the download and the app will restart"
echo "   automatically when the update is complete."
echo ""
echo "🔄 Update published at: $UPDATE_DATE"
