# Create Team Screen – PRD

## Feature: Create Team Screen  
**Location**: Challenge → Create Team  

---

## Requirements

### UI
- [ ] Display header with back button and challenge name.
- [ ] Display input field for "Team Name" (required).
- [ ] Display image upload with placeholder and support for setting/changing a team logo.
- [ ] Display member list with avatars and names (max N items depending on `challenge.maxTeamSize`).
- [ ] Display "Add team-mate" button until the team is full.
- [ ] Show footer note: "Want to invite someone who isn’t yet using GoJoe? You can do this once you’ve created the team."
- [ ] Display "Create Team" button (disabled unless form is valid).

---

### Logic & Validation
- [ ] Validate that a team name is present.
- [ ] Validate member list has at least one member (current user) and no more than `challenge.maxTeamSize`.
- [ ] Team name has a character limit (e.g., 30 chars).
- [ ] Logo image is optional.
- [ ] Disable “Add teammate” button if max team size is reached (`challenge.maxTeamSize === users.length`).
- [ ] If `challenge.maxTeamSize` is `null`, allow unlimited users.

---

## APIs & Hooks

### Create Team Mutation
- [ ] Create `useCreateTeamMutation` hook in `hooks/api/useCreateTeamMutation.ts`.

```ts
// Usage:
const mutation = useCreateTeamMutation();

mutation.mutate({
  challengeId,
  createChallengeTeamDto,
});
```

- **API Call**:
```ts
api.getApiClient().challengesControllerCreateTeam({
  challengeId,
  createChallengeTeamDto,
});
```

- **Params**:
```ts
{
  challengeId: string;
  createChallengeTeamDto: CreateChallengeTeamDto;
}
```

- **CreateChallengeTeamDto**:
```ts
{
  name: string;
  isChallenging?: boolean;
  users: Array<CreateTeamUserDto>;
  logo?: string;
  passcode?: string;
  challengeCode?: string;
}
```

- **CreateTeamUserDto**:
```ts
{
  userId: string;
  status?: boolean;
  isLeader?: boolean;
  isAdmin?: boolean;
  position: number;
}
```

---

### Add Teammate Flow
- [ ] Pressing "Add team-mate" opens a bottom sheet.
- [ ] Sheet contains:
  - [ ] A search bar
  - [ ] A list of users matching the search

- **User Search API**:
```ts
api
  .getApiClient()
  .userControllerSearch({
    limit: 20,
    q: search,
    challengeId: challenge.id,
  })
  .then(res => res.data.data.data);
```

- [ ] When sheet opens, request a default avatar.
- **Default Avatar API**:
```ts
api
  .getApiClient()
  .avatarControllerGenerate()
  .then(({data}) => data.data.uri);
```

---

## Form & State
- [ ] Use `react-hook-form` to manage form state for team name, image, and members.
- [ ] Use `yup` or `zod` for schema validation.
- [ ] Form is considered valid when:
  - [ ] Team name is entered
  - [ ] At least one user is in the team

---

## Button Actions
- [ ] Pressing "Create Team":
  - [ ] Triggers the `useCreateTeamMutation`
  - [ ] If successful:
    - [ ] Invalidate all queries that include `challenge` and `challengeId` in the key
    - [ ] Show an overlay with a success message (e.g., 'Team Created Successfully')
    - [ ] Redirect to the team/challenge screen
  - [ ] If error, show error message
