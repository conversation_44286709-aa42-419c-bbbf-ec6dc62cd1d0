when press the join team button, the following action with condition should happen

 - if the autoJoinBusiness business flug on the challenge object is true and the challenge object has a business property the mutation that execute the api call businessUsersControllerCreates should be executed
 this is the api call but we have a mutation hook for it
 api.getApiClient().businessUsersControllerCreate({
          createBusinessUserInputDto: {
            businessId: business.id,
            passcode: business.passcode,
          },
        });


- the mutation containgng the following api call should be executed
api.getApiClient().challengesControllerJoinTeamChallenge({
        joinTeamChallengeDto: {
          challengeCode: challenge.code,
        },
        challengeId: challenge.id,
        teamId,
      }),

- on success, a tost success message should be disaplyed