# 📄 Leave Team Action - Product Requirements Document (PRD)

## Overview
Implement a "Leave Team" feature that enables users to leave a team via a button. The process includes user confirmation, API call, cache invalidation, and success feedback.

---

## Goals
- Allow users to leave a team with a clear and user-friendly experience.
- Use a confirmation screen to avoid accidental team departures.
- Provide visual feedback on success.
- Ensure app state is updated by invalidating appropriate queries.

---

## User Flow
1. User taps the **"Leave Team"** button.
2. A **confirmation dialog** appears asking the user to confirm their decision.
3. If confirmed:
   - Show a loading state.
   - Call the API to leave the team.
   - On success:
     - Show a success toast/message.
     - Invalidate relevant queries to refresh the UI.
4. If canceled:
   - Dismiss the confirmation dialog.

---

## API Integration

- **Endpoint**:  
  `api.getApiClient().challengesControllerLeaveTeamChallenge({ teamId })`

- **HTTP Method**:  
  `POST`

- **Parameters**:  
  - `teamId`: string (ID of the team the user is leaving)

---

## Technical Requirements

### 1. Hook

- **Location**: `hooks/api/useLeaveTeam.ts`
- **Tech**: `react-query`'s `useMutation`
- **Behavior**:
  - Makes the API call.
  - Invalidates queries used in the “Join Team” feature.


---

### 2. Confirmation UI

- **Component**: Tamagui `Dialog` (preferred for customizability)
- **Location**: Inside the component containing the leave button
- **i18n**: All text must use `react-i18next`


---

### 3. Success Feedback

- **Component**: Toast (e.g. Tamagui ToastProvider or custom)
- **Text**: Internationalized


---

## Internationalization

Add keys to the translation files:

---

## Edge Cases
- Disable confirm button while the mutation is loading.
- Show error toast if the API call fails.
- Prevent duplicate submissions.
- Optionally redirect or update UI if user is no longer in any team.

---

## Optional: Analytics
- Track “Leave Team” event for metrics and usage analysis.
