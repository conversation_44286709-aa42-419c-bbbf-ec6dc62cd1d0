{"name": "health-and-fitness", "main": "expo-router/entry", "version": "3.0.7", "scripts": {"eas-build-pre-install": "./eas-build-pre-install.sh", "start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --passWithNoTests", "test:dev": "jest --watchAll", "lint": "expo lint", "generate-themes": "tamagui generate-themes ./theme-builder.ts src/themes/default.ts", "postinstall": "patch-package", "copy-debug-keystore": "cp ./files/debug.keystore android/app/debug.keystore", "prebuild:dev": "npx expo prebuild && npm run copy-debug-keystore", "prebuild:dev-clean": "npx expo prebuild --clean && npm run copy-debug-keystore", "translation-server": "cd server && npm start", "translation-server:dev": "cd server && npm run dev", "update:production": "./update-production.sh"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@config-plugins/react-native-branch": "^9.0.0", "@expo-google-fonts/inter": "0.3.0", "@expo/vector-icons": "14.0.4", "@formatjs/intl-datetimeformat": "^6.18.0", "@formatjs/intl-listformat": "^7.7.11", "@formatjs/intl-locale": "^4.2.11", "@formatjs/intl-numberformat": "^8.15.4", "@formatjs/intl-pluralrules": "^5.4.4", "@formatjs/intl-relativetimeformat": "^11.4.11", "@gojoe/typescript-sdk": "github:gojoeapp/typescript-sdk#v1.99.87", "@hookform/resolvers": "4.1.3", "@intercom/intercom-react-native": "^8.3.0", "@likashefqet/react-native-image-zoom": "4.3.0", "@mapbox/polyline": "^1.2.1", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/netinfo": "11.4.1", "@react-native-google-signin/google-signin": "20.1.6", "@react-native-picker/picker": "2.9.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/elements": "2.3.7", "@react-navigation/material-top-tabs": "^7.2.9", "@react-navigation/native": "^7.0.14", "@rnmapbox/maps": "^10.1.38", "@segment/analytics-react-native": "^2.21.0", "@segment/sovran-react-native": "^1.1.3", "@sentry/react-native": "~6.10.0", "@shopify/flash-list": "1.7.3", "@tamagui/config": "1.126.13", "@tamagui/font-inter": "1.126.13", "@tamagui/lucide-icons": "1.126.13", "@tamagui/toast": "1.126.13", "@tanstack/react-query": "5.77.2", "@turf/turf": "^7.2.0", "axios": "1.8.4", "burnt": "0.12.2", "color-hash": "2.0.2", "convert": "5.12.0", "customerio-expo-plugin": "2.0.1", "customerio-reactnative": "4.2.3", "expo": "^52.0.46", "expo-apple-authentication": "~7.1.3", "expo-application": "~6.0.2", "expo-audio": "~0.3.5", "expo-background-fetch": "~13.0.6", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.3", "expo-camera": "~16.0.18", "expo-clipboard": "~7.0.1", "expo-constants": "^17.0.4", "expo-crypto": "~14.0.2", "expo-dev-client": "~5.0.19", "expo-device": "~7.0.3", "expo-document-picker": "~13.0.3", "expo-font": "~13.0.4", "expo-gradle-ext-vars": "^0.1.2", "expo-haptics": "~14.0.1", "expo-health-connect": "^0.1.1", "expo-image": "~2.0.7", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-localization": "16.0.1", "expo-router": "~4.0.21", "expo-screen-orientation": "~8.0.4", "expo-secure-store": "14.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "2.0.1", "expo-system-ui": "~4.0.9", "expo-task-manager": "~12.0.6", "expo-tracking-transparency": "~5.1.1", "expo-updates": "~0.27.3", "expo-video": "~2.0.5", "expo-web-browser": "14.0.2", "i18next": "24.2.3", "intl": "^1.2.5", "intl-pluralrules": "2.0.1", "lottie-react-native": "7.1.0", "luxon": "3.6.1", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "7.56.4", "react-i18next": "15.4.1", "react-lottie": "1.2.10", "react-native": "0.76.9", "react-native-actions-sheet": "0.9.7", "react-native-background-fetch": "^4.2.7", "react-native-background-geolocation": "^4.18.3", "react-native-branch": "^6.7.0", "react-native-fbsdk-next": "^13.4.1", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "~1.11.0", "react-native-gifted-charts": "^1.4.60", "react-native-health-connect": "github:mikitu/react-native-health-connect", "react-native-logs": "^5.3.0", "react-native-mmkv": "3.2.0", "react-native-pager-view": "6.5.1", "react-native-reanimated": "~3.16.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-track-player": "^4.1.1", "react-native-web": "0.19.13", "react-native-webview": "13.12.5", "rn-swipe-button": "^3.0.1", "stream-chat-expo": "^6.6.3", "tamagui": "1.126.13", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "7.25.7", "@tamagui/babel-plugin": "1.126.13", "@tamagui/cli": "1.126.13", "@tamagui/metro-plugin": "1.126.13", "@types/color-hash": "2.0.0", "@types/geojson": "^7946.0.16", "@types/jest": "29.5.14", "@types/luxon": "3.6.2", "@types/mapbox__polyline": "^1.0.5", "@types/react": "18.3.12", "@types/react-lottie": "^1.2.10", "@types/react-navigation": "^3.0.8", "@types/react-test-renderer": "18.3.1", "eslint": "8.57.1", "eslint-config-expo": "8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "29.7.0", "jest-expo": "~52.0.6", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "3.4.2", "react-test-renderer": "18.2.0", "reactotron-react-native": "^5.1.12", "typescript": "5.3.3"}, "private": true, "overrides": {"@react-native-async-storage/async-storage": "1.23.1"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["tamagui", "@expo-google-fonts/inter", "@tamagui/config", "luxon", "i18next"]}}}}