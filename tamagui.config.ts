import {createFont, createTamagui} from 'tamagui';
import {fonts, tokens, themes, config} from '@tamagui/config/v3';

const lightTheme = {
  color: '#000000',
  background: '#FFFFFF',
  windowBackground: '#F2F2F4',
  grey1: '#656874',
  grey2: '#ACAFBA',
  grey3: '#DADCE7',
  primary: '#C43F2D',
  borderHighlight: '#E59F17',
  backgroundHighlight: '#FFF5D2',
  success: '#02B875',
  warning: '#F0A600',
  error: '#E76049',
  red: '#E76049',
  yellow: '#FFCE00',
  green: '#02B875',
  purple: '#311C66',
  black: '#000000',
  white: '#FFFFFF',

  //deprecated
  grey: '#ACAFBB',
  textGrey: '#646875',
  accentGrey: '#D9DCE8',
};

// const darkTheme = {
//   windowBackground: '#151515',
//   background: '#000000',
//   color: '#ffffff',
//   grey1: '#656874',
//   grey2: '#ACAFBA',
//   grey3: '#DADCE7',

//   primary: '#C43F2D',
//   borderHighlight: '#E59F17',
//   backgroundHighlight: '#FFF5D2',
//   success: '#02B875',
//   warning: '#F0A600',
//   error: '#E76049',
//   yellow: '#FFCE00',
//   red: '#E76049',
//   green: '#02B875',
//   purple: '#311C66',
//   black: '#000000',
//   white: '#FFFFFF',

//   // deprecated
//   grey: '#ACAFBB',
//   accentGrey: '#D9DCE8',
//   textGrey: '#646875',
// };

const darkTheme = {
  color: '#000000',
  background: '#FFFFFF',
  windowBackground: '#F2F2F4',
  grey1: '#656874',
  grey2: '#ACAFBA',
  grey3: '#DADCE7',
  primary: '#C43F2D',
  borderHighlight: '#E59F17',
  backgroundHighlight: '#FFF5D2',
  success: '#02B875',
  warning: '#F0A600',
  error: '#E76049',
  red: '#E76049',
  yellow: '#FFCE00',
  green: '#02B875',
  purple: '#311C66',
  black: '#000000',
  white: '#FFFFFF',

  //deprecated
  grey: '#ACAFBB',
  textGrey: '#646875',
  accentGrey: '#D9DCE8',
};

const redTheme = {
  background: '#CA3D2A',
  color: '#ffffff',
  red: '#CA3D2A',
};
const appConfig = createTamagui({
  ...config,
  themes: {
    ...themes,
    light_Button: {
      background: '#FFFFFF',
      color: '#000000',
    },
    light_Input: {
      background: '#FFFFFF',
      color: '#000000',
    },
    dark_Input: {
      background: '#000000',
      color: '#FFFFFF',
    },
    dark_Button: {
      background: '#000000',
      color: '#FFFFFF',
    },
    light: {
      ...themes.light,
      ...lightTheme,
    },
    dark: {
      ...themes.dark,
      ...darkTheme,
    },
    redGoJoe: redTheme,
  },
  tokens: {
    ...tokens,
    color: {
      ...tokens.color,
    },
  },
  fonts: {
    inter: createFont({
      family: 'Inter',
      size: {
        4: 14,
        5: 16,
        6: 18,
      },
      weight: {
        1: '100', // Thin
        2: '200', // ExtraLight
        3: '300', // Light
        4: '400', // Regular
        5: '500', // Medium
        6: '600', // SemiBold
        7: '700', // Bold
        8: '800', // ExtraBold
        9: '900', // Black
      },
    }),
    ...fonts,
  },
  settings: {
    ...config.settings,
    fastSchemeChange: false,
    disableRootThemeClass: true, // disable theme
  },
});

export type AppConfig = typeof appConfig;

declare module 'tamagui' {
  // or '@tamagui/core'
  // overrides TamaguiCustomConfig so your custom types
  // work everywhere you import `tamagui`
  interface TamaguiCustomConfig extends AppConfig {}
}

export default appConfig;
