node_modules/
.expo/
dist/
npm-debug.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/

# macOS
.DS_Store

#ide
.idea/

#MISC
.certificates/
.example/
.scratches/

# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-cli

#Continuous Native Generation (CNG)
#https://docs.expo.dev/workflow/overview/
# Ignore root native folders
/ios
/android

# But keep native/ios and native/android
!native/ios
!native/android

#Tamagui
.tamagui

#Localazy
localazy.json

#Env
.env*

#design
.design
