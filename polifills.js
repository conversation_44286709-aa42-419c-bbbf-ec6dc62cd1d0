import 'intl-pluralrules'
import 'react-native-url-polyfill/auto';

// hermes engine doesn't support Intl yet
//https://github.com/moment/luxon/issues/956
import '@formatjs/intl-locale/polyfill';
import '@formatjs/intl-datetimeformat/polyfill';
import '@formatjs/intl-datetimeformat/add-all-tz';
import '@formatjs/intl-datetimeformat/locale-data/en';
import '@formatjs/intl-datetimeformat/locale-data/en-GB';
import '@formatjs/intl-datetimeformat/locale-data/pt';
import '@formatjs/intl-datetimeformat/locale-data/fr-CA';
import '@formatjs/intl-datetimeformat/locale-data/nl';
import '@formatjs/intl-datetimeformat/locale-data/fr';
import '@formatjs/intl-datetimeformat/locale-data/de';
import '@formatjs/intl-datetimeformat/locale-data/it';
import '@formatjs/intl-datetimeformat/locale-data/ja';
import '@formatjs/intl-datetimeformat/locale-data/pt';
import '@formatjs/intl-datetimeformat/locale-data/ro';
import '@formatjs/intl-datetimeformat/locale-data/es';
import '@formatjs/intl-datetimeformat/locale-data/et';
import '@formatjs/intl-datetimeformat/locale-data/pl';
import '@formatjs/intl-datetimeformat/locale-data/bg';
import '@formatjs/intl-datetimeformat/locale-data/sv';
import '@formatjs/intl-datetimeformat/locale-data/da';
import '@formatjs/intl-datetimeformat/locale-data/fi';
import '@formatjs/intl-datetimeformat/locale-data/lv';
import '@formatjs/intl-datetimeformat/locale-data/hu';
import '@formatjs/intl-datetimeformat/locale-data/cs';

import '@formatjs/intl-relativetimeformat/polyfill';
import '@formatjs/intl-relativetimeformat/locale-data/en';
import '@formatjs/intl-relativetimeformat/locale-data/en-GB';
import '@formatjs/intl-relativetimeformat/locale-data/fr-CA';
import '@formatjs/intl-relativetimeformat/locale-data/nl';
import '@formatjs/intl-relativetimeformat/locale-data/fr';
import '@formatjs/intl-relativetimeformat/locale-data/de';
import '@formatjs/intl-relativetimeformat/locale-data/it';
import '@formatjs/intl-relativetimeformat/locale-data/ja';
import '@formatjs/intl-relativetimeformat/locale-data/pt';
import '@formatjs/intl-relativetimeformat/locale-data/ro';
import '@formatjs/intl-relativetimeformat/locale-data/es';
import '@formatjs/intl-relativetimeformat/locale-data/et';
import '@formatjs/intl-relativetimeformat/locale-data/pl';
import '@formatjs/intl-relativetimeformat/locale-data/bg';
import '@formatjs/intl-relativetimeformat/locale-data/sv';
import '@formatjs/intl-relativetimeformat/locale-data/da';
import '@formatjs/intl-relativetimeformat/locale-data/fi';
import '@formatjs/intl-relativetimeformat/locale-data/lv';
import '@formatjs/intl-relativetimeformat/locale-data/hu';
import '@formatjs/intl-relativetimeformat/locale-data/cs';

import '@formatjs/intl-pluralrules/polyfill';
import '@formatjs/intl-pluralrules/locale-data/en';
import '@formatjs/intl-pluralrules/locale-data/nl';
import '@formatjs/intl-pluralrules/locale-data/fr';
import '@formatjs/intl-pluralrules/locale-data/de';
import '@formatjs/intl-pluralrules/locale-data/it';
import '@formatjs/intl-pluralrules/locale-data/ja';
import '@formatjs/intl-pluralrules/locale-data/pt';
import '@formatjs/intl-pluralrules/locale-data/ro';
import '@formatjs/intl-pluralrules/locale-data/es';
import '@formatjs/intl-pluralrules/locale-data/et';
import '@formatjs/intl-pluralrules/locale-data/pl';
import '@formatjs/intl-pluralrules/locale-data/bg';
import '@formatjs/intl-pluralrules/locale-data/sv';
import '@formatjs/intl-pluralrules/locale-data/da';
import '@formatjs/intl-pluralrules/locale-data/fi';
import '@formatjs/intl-pluralrules/locale-data/lv';
import '@formatjs/intl-pluralrules/locale-data/hu';
import '@formatjs/intl-pluralrules/locale-data/cs';

import '@formatjs/intl-numberformat/polyfill';
import '@formatjs/intl-numberformat/locale-data/en';
import '@formatjs/intl-numberformat/locale-data/en-GB';
import '@formatjs/intl-numberformat/locale-data/nl';
import '@formatjs/intl-numberformat/locale-data/fr';
import '@formatjs/intl-numberformat/locale-data/fr-CA';
import '@formatjs/intl-numberformat/locale-data/de';
import '@formatjs/intl-numberformat/locale-data/it';
import '@formatjs/intl-numberformat/locale-data/ja';
import '@formatjs/intl-numberformat/locale-data/pt';
import '@formatjs/intl-numberformat/locale-data/ro';
import '@formatjs/intl-numberformat/locale-data/es';
import '@formatjs/intl-numberformat/locale-data/et';
import '@formatjs/intl-numberformat/locale-data/pl';
import '@formatjs/intl-numberformat/locale-data/bg';
import '@formatjs/intl-numberformat/locale-data/sv';
import '@formatjs/intl-numberformat/locale-data/da';
import '@formatjs/intl-numberformat/locale-data/fi';
import '@formatjs/intl-numberformat/locale-data/lv';
import '@formatjs/intl-numberformat/locale-data/hu';
import '@formatjs/intl-numberformat/locale-data/cs';
