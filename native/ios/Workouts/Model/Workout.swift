//
// Created by <PERSON><PERSON> on 10/11/2020.
//

import Foundation
import HealthKit

struct Workout: Encodable {
  let workoutId: String
  let activityType: String
  let activeKilocalories: Double?
  let distanceInMeters: Double?
  let swimmingStrokeCount: Double?
  let durationInSeconds: Double
  let startTime: String
  let endTime: String
  let sourceName: String
  let sourceBundleId: String
  let device: WorkoutDevice?
  let metadata: WorkoutMetadata?
  var routes: LocationData?
  var heartRate: Double?
  var synced: Bool?
  var warnings: [String]?
  var canBeSynced: Bool?
  var activityName: String?
  init(workout: HKWorkout) {
    self.workoutId = workout.uuid.uuidString
    self.activityType = workout.workoutActivityType.name
    self.activeKilocalories =
      (workout.totalEnergyBurned != nil)
      ? workout.totalEnergyBurned!.doubleValue(for: .kilocalorie()) : nil
    self.distanceInMeters =
      workout.totalDistance != nil ? workout.totalDistance!.doubleValue(for: .meter()) : nil
    self.swimmingStrokeCount =
      (workout.totalSwimmingStrokeCount != nil)
      ? workout.totalSwimmingStrokeCount!.doubleValue(for: .count()) : nil
    self.durationInSeconds = workout.duration
    self.startTime = workout.startDate.toString(dateFormat: "yyyy-MM-dd HH:mm:ss", isUTC: true)
    self.endTime = workout.endDate.toString(dateFormat: "yyyy-MM-dd HH:mm:ss", isUTC: true)
    self.sourceName = workout.sourceRevision.source.name
    self.sourceBundleId = workout.sourceRevision.source.bundleIdentifier
    self.device = workout.device != nil ? WorkoutDevice(device: workout.device!) : nil
    self.metadata = workout.metadata != nil ? WorkoutMetadata(metadata: workout.metadata!) : nil
    self.routes = nil
    self.synced = nil
    self.warnings = nil
    self.canBeSynced = nil
    self.activityName = nil
  }
  mutating func setRoutes(routes: LocationData?) {
    self.routes = routes
  }
  mutating func setHeartrate(heartrate: Double?) {
    self.heartRate = heartrate
  }
}
