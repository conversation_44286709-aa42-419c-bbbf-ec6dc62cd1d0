import Foundation
import HealthKit

struct WorkoutMetadata: Encodable {
  // METADATA
  // general keys
  let externalId: String?
  let timezone: String?
  let keyWasUserEntered: String?
  // Weather Keys
  //  A key that represents the weather condition during the sample.
  let weatherCondition: String?
  //  A key that represents the weather temperature during the sample.
  let weatherTemperatureCelsius: String?
  //  A key that represents the weather humidity during the sample.
  let weatherHumidityPercent: String?
  //  The metadata key for the barometric pressure associated with a sample. available in ios 14.0
//  let barometricPressure: String?
//  let averageMETsKcal: String?
  let elevationAscendMeter: String?
  let elevationDescendMeter: String?
//  let barometricPressure: String?

  init(metadata: [String: Any]?) {
    let wc_key = HKMetadataKeyWeatherTemperature
    let wc = metadata?[HKMetadataKeyWeatherTemperature]
    self.externalId = metadata?[HKMetadataKeyExternalUUID] as? String
    self.timezone = metadata?[HKMetadataKeyTimeZone] as? String
    self.keyWasUserEntered = metadata?[HKMetadataKeyWasUserEntered] as? String
    self.weatherCondition = metadata?[HKMetadataKeyWeatherCondition] as? String
    self.weatherTemperatureCelsius = getWorkoutWeatherTemperature(mataTemperature: metadata?[HKMetadataKeyWeatherTemperature])
//    self.averageMETsKcal = getMETs(mataValue: metadata?[HKMetadataKeyAverageMETs])
    self.weatherHumidityPercent = getWorkoutWeatherHumidity(mataHumidity: metadata?[HKMetadataKeyWeatherHumidity])
//    self.barometricPressure = metadata?[HKMetadataKeyBarometricPressure] as? String
    self.elevationAscendMeter = getWorkoutElevation(mataElevation: metadata?[HKMetadataKeyElevationAscended])
    self.elevationDescendMeter = getWorkoutElevation(mataElevation: metadata?[HKMetadataKeyElevationDescended])
  }
}

func getWorkoutWeatherTemperature(mataTemperature: Any?) -> String? {
    if let quantityTemperature = mataTemperature as? HKQuantity {
      let celsius = quantityTemperature.doubleValue(for: HKUnit.degreeCelsius())
      return String(format: "%.1f", celsius)
    }
  return nil
}

func getWorkoutWeatherHumidity(mataHumidity: Any?) -> String? {
  if let quantityHumidity = mataHumidity as? HKQuantity {
    let percent = quantityHumidity.doubleValue(for: HKUnit.percent())
    return String(percent)
  }
  return nil
}
func getWorkoutElevation(mataElevation: Any?) -> String? {
  if let quantityElevation = mataElevation as? HKQuantity {
    let m = quantityElevation.doubleValue(for: HKUnit.meter())
    return String(m)
  }
  return nil
}
func getMETs(mataValue: Any?) -> String? {
  if let quantity = mataValue as? HKQuantity {
    let val = quantity.doubleValue(for: HKUnit.kilocalorie())
    return String(val)
  }
  return nil
}
