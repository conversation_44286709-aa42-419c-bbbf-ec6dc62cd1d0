//
// Created by <PERSON><PERSON> on 18/11/2020.
//

import Foundation
import MapKit
struct Location: Encodable {
  //the coordinate of the current location.
  let coordinate: Coordinate
  //the altitude of the location. Can be positive (above sea level) or negative (below sea level).
  let altitude: Double
  //the horizontal accuracy of the location. Negative if the lateral location is invalid.
  let horizontalAccuracy: Double
  //the vertical accuracy of the location. Negative if the altitude is invalid.
  let verticalAccuracy: Double
  //the course of the location in degrees true North. Negative if course is invalid.
  let course: Double
  //the speed of the location in m/s. Negative if speed is invalid.
  let speed: Double
  //the speed accuracy of the location in m/s. -1 if invalid.
  let speedAccuracy: Double
  //the timestamp when this location was determined.
  let timestamp: Date

  init(location: CLLocation) {
    self.coordinate = Coordinate(coordinate: location.coordinate)
    self.altitude = location.altitude as Double
    self.horizontalAccuracy = location.horizontalAccuracy as Double
    self.verticalAccuracy = location.verticalAccuracy as Double
    self.course = location.course as Double
    self.speed = location.speed as Double
    self.speedAccuracy = location.speedAccuracy as Double
    self.timestamp = location.timestamp as Date
  }
}
