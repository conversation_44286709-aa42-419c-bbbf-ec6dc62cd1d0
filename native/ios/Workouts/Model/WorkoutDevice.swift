//
// Created by <PERSON><PERSON> on 10/11/2020.
//

import Foundation
import HealthKit

struct WorkoutDevice: Encodable {
  let deviceName: String
  let deviceManufacturer: String
  let deviceModel: String
  let deviceHardwareVersion: String
  let deviceSoftwareVersion: String
  init(device: HKDevice?) {
    self.deviceName = device!.name ?? ""
    self.deviceManufacturer = device!.manufacturer ?? ""
    self.deviceModel = device!.model ?? ""
    self.deviceHardwareVersion = device!.hardwareVersion ?? ""
    self.deviceSoftwareVersion = device!.softwareVersion ?? ""
  }
}