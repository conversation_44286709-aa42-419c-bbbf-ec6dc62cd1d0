//
//  GoJoeError.swift
//  GoJoe
//
//  Created by <PERSON> on 27/01/2021.
//

import Foundation
import os.log

enum GJEvent: String {
  case apiResponse = "api_error"
  case apiDecoding = "api_decoding_error"
  case apiEncoding = "api_encoding_error"
  case sendWorkouts = "send_workouts_failure"
  case backgroundDeliveryEnable = "background_delivery_enable_error"
  case backgroundDeliveryDisable = "background_delivery_disable_error"
  case requestAuthorization = "request_authorization"
  case workoutRouteQuery = "workout_route_query_error"
}

struct Err: Codable {
  var error: String?
  var data: [String: String?]?
}

struct EventHandler {

  static func handleEvent(_ event: GJEvent, error: Error?, config: Config) {
    let message = error?.localizedDescription ?? "unknown_error"
    let accessToken = config.userAccessToken() ?? ""
    let err = Err(error: error?.localizedDescription)

    guard let jsonData = try? JSONEncoder().encode(err) else {
      print("⚠️ Failed to encode error to JSON")
      return
    }

    guard let json = String(data: jsonData, encoding: .utf8) else {
      print("⚠️ Failed to convert JSON data to UTF-8 String")
      return
    }

    guard let emitter = GJRNEventEmitter.emitter else {
      print("⚠️ GJRNEventEmitter is nil")
      return
    }

    emitter.emitEvent(withName: "logMessage", body: json)
  }

  static func handleEvent(
    _ event: GJEvent, error: Error?, config: Config, response: URLResponse?, data: Data?
  ) {
    let message = error?.localizedDescription ?? "unknown_error"
    let accessToken = config.userAccessToken() ?? ""
    let dataString = data != nil ? String(data: data!, encoding: .utf8) ?? "Invalid UTF-8 data" : ""
    let statusCode = response != nil ? String((response as! HTTPURLResponse).statusCode) : ""
    let userId = config.getUserId() != nil ? String(config.getUserId()!) : ""

    let userInfo: [String: String?] = [
      NSLocalizedDescriptionKey: NSLocalizedString("The request failed.", comment: ""),
      NSLocalizedFailureReasonErrorKey: NSLocalizedString("The error is:", comment: message),
      "statusCode": statusCode,
      "data": dataString,
      "accessToken": accessToken,
      "userID": userId,
    ]

    print("⚠️ User Info: \(userInfo)")

    let err = Err(error: message, data: userInfo)
    guard let jsonData = try? JSONEncoder().encode(err) else {
      print("⚠️ Failed to encode error to JSON")
      return
    }

    guard let json = String(data: jsonData, encoding: .utf8) else {
      print("⚠️ Failed to convert JSON data to String")
      return
    }

    guard let emitter = GJRNEventEmitter.emitter else {
      print("⚠️ GJRNEventEmitter is nil")
      return
    }

    emitter.emitEvent(withName: "logMessage", body: json)
  }
  static func logText(_ text: String) {
    os_log("Workouts: %@", type: .default, text)
    guard let emitter = GJRNEventEmitter.emitter else {
      print("⚠️ GJRNEventEmitter is nil")
      return
    }
    emitter.emitEvent(withName: "logMessage", body: "Workouts: \(text)")
  }
  static func logHttpResponse(_ response: HTTPURLResponse, jsonData: Data?) {
    logText(formatResponseAsJSON(response: response, data: jsonData))
  }

  static func formatResponseAsJSON(response: URLResponse?, data: Data?) -> String {
    guard let httpresponse = response as? HTTPURLResponse else {
      print("Received response is not an HTTPURLResponse: \(String(describing: response))")
      return "Invalid HTTPURLResponse"
    }

    print("HTTPURLResponse received successfully.")

    var logDictionary: [String: Any] = [:]

    logDictionary["statusCode"] = httpresponse.statusCode
    print("Status code: \(httpresponse.statusCode)")

    // Adaugă headerele
    var headers: [String: String] = [:]
    for (key, value) in httpresponse.allHeaderFields {
      if let keyString = key as? String, let valueString = value as? String {
        headers[keyString] = valueString
        print("Header - \(keyString): \(valueString)")
      } else {
        print("Header key or value is not a string: \(key) = \(value)")
      }
    }
    logDictionary["headers"] = headers

    if let jsonData = data {
      print("Data received from response.")

      if let jsonObject = try? JSONSerialization.jsonObject(with: jsonData, options: []) {
        logDictionary["body"] = jsonObject
        print("Body serialized as JSON object: \(jsonObject)")
      } else {
        let jsonString = String(data: jsonData, encoding: .utf8) ?? "Invalid JSON"
        logDictionary["body"] = jsonString
        print("Body could not be serialized as JSON, raw string: \(jsonString)")
      }
    } else {
      print("No data received from response.")
    }

    if let logData = try? JSONSerialization.data(
      withJSONObject: logDictionary, options: .prettyPrinted),
      let logString = String(data: logData, encoding: .utf8)
    {
      print("Final JSON string: \(logString)")
      return logString
    } else {
      print("Failed to serialize log dictionary to JSON string.")
      return "Failed to serialize HTTP response to JSON"
    }
  }
}
