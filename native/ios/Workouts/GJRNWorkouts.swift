//
//  GJRNWorkouts.swift
//  Created by <PERSON><PERSON> on 11/12/2020.
//

import Foundation

@objc(GJRNWorkouts)
class GJRNWorkouts: NSObject {

  @objc
  func start() {
    WorkoutRecords.sharedInstance.startObservingNewWorkouts()
  }

  @objc
  func stop() {
    WorkoutRecords.sharedInstance.stopObservingNewWorkouts()
  }

  @objc
  func syncNow(_ resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock)
  {
    WorkoutRecords.sharedInstance.syncNow {
      EventHandler.logText("Sync Now completed")
      resolve("Sync Now completed")
    }
  }

  @objc
  func listWorkouts(
    _ date: String, resolve: @escaping RCTPromiseResolveBlock,
    reject: @escaping RCTPromiseRejectBlock
  ) {
    WorkoutRecords.sharedInstance.listWorkouts(date: date) { workouts in
      resolve(workouts)
    }
  }
}
