//
// Created by <PERSON><PERSON> on 10/11/2020.
//

import Foundation
import UIKit
import os.log

enum ApiError: Error {
    case responseProblem
    case decodingProblem
    case encodingProblem
}

struct APIRequest {
    let resourceURL: URL
    let checkActivitiesResourceURL: URL
    let cfg: Config

    init(cfg: Config) {
        guard let resourceURL = URL(string: cfg.getActivitiesPushUrl()!) else {
            fatalError()
        }
        self.resourceURL = resourceURL
        guard let checkActivitiesResourceURL = URL(string: cfg.getActivitiesCheckUrl()!) else {
            fatalError()
        }
        self.checkActivitiesResourceURL = checkActivitiesResourceURL
        self.cfg = cfg
    }

    func send(body: RequestBody, completion: @escaping (Result<ApiResponse, ApiError>) -> Void) {
        EventHandler.logText("ApiRequest.send")
        do {
            var request = URLRequest(url: resourceURL, timeoutInterval: Double.infinity)
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")

            guard let accessToken = cfg.userAccessToken() else {
                EventHandler.logText("Error: no access token")
                return
            }

            request.setValue("Bearer " + accessToken, forHTTPHeaderField: "Authorization")
            request.setValue(cfg.getUserId(), forHTTPHeaderField: "X-GJ-User-ID")
            request.setValue(cfg.getDeviceId(), forHTTPHeaderField: "X-GJ-Device-ID")
            let timezone = TimeZone.current.identifier
            request.setValue(timezone, forHTTPHeaderField: "X-GJ-Device-Timezone")
            request.httpMethod = "POST"

            let requestBodyData = try JSONEncoder().encode(body)
            request.httpBody = requestBodyData

            // Emit logMessage event with the request body
            if let requestBodyString = String(data: requestBodyData, encoding: .utf8) {
                guard let emitter = GJRNEventEmitter.emitter else {
                    print("⚠️ GJRNEventEmitter is nil")
                    return
                }
                EventHandler.logText("Request body emitted to logMessage: \(requestBodyString)")
            } else {
                EventHandler.logText("Failed to convert request body to UTF-8 string")
            }

            let dataTask = URLSession.shared.dataTask(with: request) { data, response, error in
                EventHandler.logText("dataTask Run")

                guard let httpresponse = response as? HTTPURLResponse,
                    httpresponse.statusCode == 200 || httpresponse.statusCode == 201,
                    let jsonData = data
                else {
                    if let httpResponse = response as? HTTPURLResponse {
                        guard let emitter = GJRNEventEmitter.emitter else {
                            print("⚠️ GJRNEventEmitter is nil")
                            return
                        }
                        emitter.emitEvent(
                            withName: "onPushFailure",
                            body: [
                                "Error": "Workouts: Error: http response problem",
                                "Status": String(httpResponse.statusCode),
                            ]
                        )
                    } else {
                        guard let emitter = GJRNEventEmitter.emitter else {
                            print("⚠️ GJRNEventEmitter is nil")
                            return
                        }
                        emitter.emitEvent(
                            withName: "onPushFailure",
                            body: [
                                "Error": "Workouts: Error: http response problem",
                                "Status": "Unknown",
                            ]
                        )
                    }

                    EventHandler.handleEvent(
                        .apiResponse, error: error, config: self.cfg, response: response, data: data
                    )
                    completion(.failure(.responseProblem))
                    return
                }

                do {
                    let json = String(data: jsonData, encoding: .utf8)
                    print("Response: ", json ?? "No JSON data")
                    EventHandler.logText("Workout saved")
                    let responseData = try JSONDecoder().decode(ApiResponse.self, from: jsonData)
                    EventHandler.logText("Response decoded")

                    guard let emitter = GJRNEventEmitter.emitter else {
                        print("⚠️ GJRNEventEmitter is nil")
                        return
                    }
                    emitter.emitEvent(withName: "onPushSuccess", body: ["data": json ?? ""])
                    completion(.success(responseData))
                } catch {
                    EventHandler.handleEvent(
                        .apiDecoding, error: error, config: self.cfg, response: response, data: data
                    )
                    EventHandler.logText("Error: decoding problem: \(error)")

                    guard let emitter = GJRNEventEmitter.emitter else {
                        print("⚠️ GJRNEventEmitter is nil")
                        return
                    }
                    emitter.emitEvent(
                        withName: "onPushFailure",
                        body: ["data": "Workouts: Error: decoding problem"]
                    )
                    completion(.failure(.decodingProblem))
                }
            }
            dataTask.resume()
        } catch {
            EventHandler.handleEvent(.apiEncoding, error: error, config: self.cfg)
            EventHandler.logText("Error: encoding problem")

            guard let emitter = GJRNEventEmitter.emitter else {
                print("⚠️ GJRNEventEmitter is nil")
                return
            }
            emitter.emitEvent(
                withName: "onPushFailure", body: ["data": "Workouts: Error: encoding problem"]
            )
            completion(.failure(.encodingProblem))
        }
    }

    func checkWorkouts(
        input: CheckRequestBody, completion: @escaping (Result<CheckApiResponse, ApiError>) -> Void
    ) {

        EventHandler.logText("Check Workouts Input: \(input)")
        var request = URLRequest(
            url: self.checkActivitiesResourceURL, timeoutInterval: Double.infinity)

        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        guard let accessToken = cfg.userAccessToken() else {
            let errorMsg = "Error: no access token"
            EventHandler.logText(errorMsg)
            return
        }

        request.setValue("Bearer " + accessToken, forHTTPHeaderField: "Authorization")
        request.setValue(cfg.getUserId(), forHTTPHeaderField: "X-GJ-User-ID")
        request.setValue(cfg.getDeviceId(), forHTTPHeaderField: "X-GJ-Device-ID")
        request.httpMethod = "POST"

        let requestBody = CheckRequestBody(
            workouts: input.workouts.map {
                WorkoutRequest(
                    workoutId: $0.workoutId,
                    startTime: $0.startTime,
                    endTime: $0.endTime,
                    activityType: $0.activityType
                )
            })

        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
            EventHandler.logText("Sending checkWorkouts request with body: \(requestBody)")
            let dataTask = URLSession.shared.dataTask(with: request) { data, response, error in
                guard let httpresponse = response as? HTTPURLResponse else {
                    let errorMsg = "Invalid HTTP response: \(String(describing: response))"
                    EventHandler.handleEvent(
                        .apiResponse, error: error, config: self.cfg, response: response, data: data
                    )
                    EventHandler.logText(errorMsg)
                    print(errorMsg)
                    completion(.failure(.responseProblem))
                    return
                }

                print("HTTP Response Code: \(httpresponse.statusCode)")

                guard httpresponse.statusCode == 200 || httpresponse.statusCode == 201,
                    let jsonData = data
                else {
                    let errorMsg =
                        "Error: unexpected response code: \(httpresponse.statusCode)\n\(EventHandler.formatResponseAsJSON(response: response, data: data))"
                    EventHandler.handleEvent(
                        .apiResponse, error: error, config: self.cfg, response: response, data: data
                    )
                    EventHandler.logText(errorMsg)
                    print(errorMsg)
                    completion(.failure(.responseProblem))
                    return
                }

                EventHandler.logHttpResponse(httpresponse, jsonData: jsonData)
                print("Received valid HTTP response with data.")

                do {
                    let responseArray = try JSONDecoder().decode(
                        CheckApiResponse.self, from: jsonData)
                    print("Decoding successful, response: \(responseArray)")
                    completion(.success(responseArray))
                } catch {
                    let errorMsg = "Error decoding JSON: \(error)"
                    EventHandler.handleEvent(
                        .apiDecoding, error: error, config: self.cfg, response: response, data: data
                    )
                    EventHandler.logText(errorMsg)
                    print(errorMsg)
                    completion(.failure(.decodingProblem))
                }
            }
            dataTask.resume()
        } catch {
            let errorMsg = "Error encoding JSON: \(error)"
            EventHandler.handleEvent(.apiEncoding, error: error, config: self.cfg)
            EventHandler.logText(errorMsg)
            print(errorMsg)
            completion(.failure(.encodingProblem))
        }
    }

}
