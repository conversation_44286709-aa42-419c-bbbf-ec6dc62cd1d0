import Contacts
import HealthKit
import MapKit
import UIKit
import os.log

class WorkoutRecords {
  let healthStore = HKHealthStore()
  private var config: Config
  private var arrRecords = [HKWorkout]()
  private var appStarted: Bool = false
  private var counter = 0
  static let sharedInstance: WorkoutRecords = {
    let instance = WorkoutRecords()
    EventHandler.logText("init Workouts")
    instance.config = WorkoutsConfig(cfg: [:])
    return instance
  }()
  private var anchor: HKQueryAnchor? = nil
  private var isSending: Bool = false
  private var debounceTimer: Timer?
  private init() {
    config = WorkoutsConfig(cfg: [:])
  }

  func startObservingNewWorkouts() {
    EventHandler.logText("start observing workouts")
    if appStarted {
      EventHandler.logText("background delivery already enabled")
      return
    }
    appStarted = true
    let sampleType = HKObjectType.workoutType()
    healthStore.enableBackgroundDelivery(for: sampleType, frequency: .immediate) {
      (success, error) in
      if let unwrappedError = error {
        EventHandler.logText("could not enable background delivery: \(unwrappedError)")
        EventHandler.handleEvent(.backgroundDeliveryEnable, error: error, config: self.config)
      }
      if success {
        EventHandler.logText("background delivery enabled")
      }
    }

    let query = HKObserverQuery(sampleType: sampleType, predicate: nil) {
      (query, completionHandler, error) in
      completionHandler()
      self.updateWorkouts {
      }
    }

    healthStore.execute(query)
  }

  func stopObservingNewWorkouts() {
    healthStore.disableAllBackgroundDelivery { (success, error) in
      if success {
        EventHandler.logText("background delivery disabled")
        if self.appStarted {
          self.appStarted = false
        }
      } else {
        EventHandler.handleEvent(.backgroundDeliveryDisable, error: error, config: self.config)
        EventHandler.logText("error disabling background delivery")
      }
    }
  }
  func listWorkouts(date: String, completionHandler: @escaping (String) -> Void) {
    let sampleType = HKObjectType.workoutType()

    // Create DateFormatter to parse the date string
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "yyyy-MM-dd"
    guard let dateObject = dateFormatter.date(from: date) else {
      print("Invalid date format")
      completionHandler("[]")
      return
    }

    // Calculate the start and end of the day
    let startDate = Calendar.current.startOfDay(for: dateObject)
    let endDate = Date().endOfDay

    let predicate = HKQuery.predicateForSamples(withStart: startDate, end: endDate)

    let anchoredQuery = HKAnchoredObjectQuery(
      type: sampleType, predicate: predicate, anchor: nil, limit: HKObjectQueryNoLimit
    ) { [unowned self] query, newSamples, deletedSamples, newAnchor, error in
      guard let newSamples = newSamples as? [HKWorkout] else {
        EventHandler.logText("No workouts found")
        return
      }

      EventHandler.logText("New workouts detected")
      let filteredSamples = self.filterSamples(newSamples: newSamples)

      EventHandler.logText("Filtered workouts: \(filteredSamples.count)")

      // Process the workouts to include heart rate data
      self.processWorkoutsWithHeartRate(filteredSamples) { workoutsWithHeartRate in
        self.checkWorkoutsWithBackend(workouts: workoutsWithHeartRate) { checkedWorkouts in
          let jsonData = try? JSONEncoder().encode(checkedWorkouts)
          let jsonString = String(data: jsonData!, encoding: .utf8) ?? "[]"
          completionHandler(jsonString)
        }
      }
    }

    anchoredQuery.updateHandler = { query, newSamples, deletedSamples, newAnchor, error in
      guard let newSamples = newSamples as? [HKWorkout] else {
        EventHandler.logText("No workouts found")
        return
      }

      let filteredSamples = self.filterSamples(newSamples: newSamples)

      // Process the workouts to include heart rate data
      self.processWorkoutsWithHeartRate(filteredSamples) { workoutsWithHeartRate in
        self.checkWorkoutsWithBackend(workouts: workoutsWithHeartRate) { checkedWorkouts in
          let jsonData = try? JSONEncoder().encode(checkedWorkouts)
          let jsonString = String(data: jsonData!, encoding: .utf8) ?? "[]"
          completionHandler(jsonString)
        }
      }
    }

    healthStore.execute(anchoredQuery)
  }

  /// Sync workouts immediately from last sync date to now
  func syncNow(completionHandler: @escaping () -> Void) {
    let sampleType = HKObjectType.workoutType()
    let startDate = config.getLastSyncDate()
    let endDate = Date()

    EventHandler.logText("🔄 Syncing workouts now from \(startDate) to \(endDate)")

    let predicate = HKQuery.predicateForSamples(withStart: startDate, end: endDate)

    let query = HKAnchoredObjectQuery(
      type: sampleType,
      predicate: predicate,
      anchor: nil,
      limit: HKObjectQueryNoLimit
    ) { [unowned self] query, newSamples, deletedSamples, newAnchor, error in

      guard let newSamples = newSamples else {
        EventHandler.logText("No workouts found during sync now")
        completionHandler()
        return
      }

      EventHandler.logText("🔍 Sync Now detected \(newSamples.count) workouts")
      let filteredSamples = self.filterSamples(newSamples: newSamples)

      self.handleNewWorkouts(new: filteredSamples) { workouts in
        EventHandler.logText("🔄 Processed \(workouts.count) workouts during Sync Now")

        if workouts.count > 0 && self.config.isSyncEnabled() {
          self.sendWorkouts(workouts: workouts)
        }
        completionHandler()
      }
    }

    healthStore.execute(query)
  }
  func updateWorkouts(completionHandler: @escaping () -> Void) {
    let sampleType = HKObjectType.workoutType()
    let endDate = Date().endOfDay
    let startDate = config.getLastSyncDate()

    EventHandler.logText("Fetching workouts from \(startDate) to \(endDate)")

    let predicate = HKQuery.predicateForSamples(withStart: startDate, end: endDate)

    let anchoredQuery = HKAnchoredObjectQuery(
      type: sampleType,
      predicate: predicate,
      anchor: anchor,
      limit: HKObjectQueryNoLimit
    ) { [unowned self] query, newSamples, deletedSamples, newAnchor, error in

      guard let newAnchor = newAnchor,
        let newSamples = newSamples
      else {
        EventHandler.logText("No anchor or sampleObjects returned")
        completionHandler()
        return
      }

      self.anchor = newAnchor

      if newSamples.isEmpty {
        EventHandler.logText("No new samples returned")
        completionHandler()
        return
      }

      EventHandler.logText("New workouts detected: \(newSamples.count)")
      let filteredSamples = self.filterSamples(newSamples: newSamples)

      handleNewWorkouts(new: filteredSamples) { workouts in
        EventHandler.logText(String(format: "New workouts count: %d", workouts.count))

        if workouts.count > 0 && self.config.isSyncEnabled() {
          self.sendWorkouts(workouts: workouts)
        }
      }
      completionHandler()
    }

    anchoredQuery.updateHandler = { (query, newSamples, deletedSamples, newAnchor, error) in
      guard let newAnchor = newAnchor,
        let newSamples = newSamples
      else {
        EventHandler.logText("No anchor or sampleObjects returned in update handler")
        return
      }

      self.anchor = newAnchor

      let filteredSamples = self.filterSamples(newSamples: newSamples)

      self.handleNewWorkouts(new: filteredSamples) { workouts in
        EventHandler.logText(String(format: "New workouts count: %d", workouts.count))

        if workouts.count > 0 && self.config.isSyncEnabled() {
          self.sendWorkouts(workouts: workouts)
        }
      }
    }

    healthStore.execute(anchoredQuery)
  }
  /// Filter samples to only include `HKWorkout` instances.
  func filterSamples(newSamples: [HKSample]?) -> [HKWorkout] {
    guard let samples = newSamples else { return [] }
    let lastSyncDate = config.getLastSyncDate()
    return samples.compactMap { $0 as? HKWorkout }.filter { $0.startDate >= lastSyncDate }
  }
  func handleNewWorkouts(new: [HKSample], completionHandler: @escaping ([Workout]) -> Void) {
    var workouts = [Workout]()
    for object in new {
      let workout = object as? HKWorkout
      var gjWorkout = Workout(workout: workout!)

      self.getAVGHeartRate(startDate: workout!.startDate, endDate: workout!.endDate) { beats in
        if beats != nil {
          gjWorkout.setHeartrate(heartrate: beats)
        }
        workouts.append(gjWorkout)
        if workouts.count == new.count {
          completionHandler(workouts)
        }
      }
      /*
        //     comment out this block temporary as the routes object might be too big for aws lambda payload
              let predicate = HKQuery.predicateForObjects(from: workout!)
              let routeQuery = HKAnchoredObjectQuery(type: HKSeriesType.workoutRoute(), predicate: predicate, anchor: nil, limit: HKObjectQueryNoLimit) { (query, routeSamples, deletedObjects, anchor, error) in
                guard error == nil else {
                  return
                }
                guard let routeSamples = routeSamples as? [HKWorkoutRoute] else {
                  workouts.append(gjWorkout)
                  return
                }
                if routeSamples.count == 0 {
                  workouts.append(gjWorkout)
                } else {
                  for rs in routeSamples {
                    let wkRoute = WorkoutRoute(route: rs, store: self.healthStore)
                    wkRoute.loadLocationData { error, data in
                      guard error == nil else {
                        completionHandler(workouts)
                        return
                      }
                      guard let wkRouteData = data else {
                        completionHandler(workouts)
                        return
                      }
                      gjWorkout.routes = LocationData(locations: wkRouteData)
                      workouts.append(gjWorkout)
                      if workouts.count == new.count {
                        completionHandler(workouts)
                        return
                      }
                    }
                  }
                }
                if workouts.count == new.count {
                  completionHandler(workouts)
                }
              }
      */
      //           healthStore.execute(routeQuery)
      //            workouts.append(gjWorkout)
    }
    if workouts.count == new.count {
      completionHandler(workouts)
    }
  }
  private func getAVGHeartRate(
    startDate: Date, endDate: Date, completion: @escaping (Double?) -> Void
  ) {
    var beats: Double? = 0
    let typeHeart = HKQuantityType.quantityType(forIdentifier: .heartRate)
    let predicate: NSPredicate? = HKQuery.predicateForSamples(
      withStart: startDate, end: endDate, options: HKQueryOptions.strictEndDate)

    let squery = HKStatisticsQuery(
      quantityType: typeHeart!, quantitySamplePredicate: predicate, options: .discreteAverage,
      completionHandler: {
        (
          query:
            HKStatisticsQuery, result: HKStatistics?, error: Error?
        ) -> Void in
        guard error == nil else {
          print("error")
          completion(nil)
          return

        }
        DispatchQueue.main.async(execute: { () -> Void in
          let quantity: HKQuantity? = result?.averageQuantity()
          beats = quantity?.doubleValue(for: HKUnit.count().unitDivided(by: HKUnit.minute()))
          completion(beats!)
          //              print("got: \(String(format: "%.f", beats!))")
        })
      })
    healthStore.execute(squery)
  }
  /// Process workouts to include heart rate data.
  private func processWorkoutsWithHeartRate(
    _ workouts: [HKWorkout], completion: @escaping ([Workout]) -> Void
  ) {
    var processedWorkouts = [Workout]()
    let dispatchGroup = DispatchGroup()

    for workout in workouts {
      dispatchGroup.enter()
      var gjWorkout = Workout(workout: workout)

      self.getAVGHeartRate(startDate: workout.startDate, endDate: workout.endDate) { heartRate in
        if let heartRate = heartRate {
          gjWorkout.setHeartrate(heartrate: heartRate)
        }
        processedWorkouts.append(gjWorkout)
        dispatchGroup.leave()
      }
    }

    dispatchGroup.notify(queue: .main) {
      completion(processedWorkouts)
    }
  }
  func checkWorkoutsWithBackend(
    workouts: [Workout], completionHandler: @escaping ([Workout]) -> Void
  ) {
    // Prepare request body for checkWorkouts
    let checkRequestWorkouts = workouts.map {
      WorkoutRequest(
        workoutId: $0.workoutId, startTime: $0.startTime, endTime: $0.endTime,
        activityType: $0.activityType)
    }

    EventHandler.logText("checkWorkoutsWithBackend: \(checkRequestWorkouts.count)")

    let apiRequest = APIRequest(cfg: config)
    apiRequest.checkWorkouts(input: CheckRequestBody(workouts: checkRequestWorkouts)) { result in
      switch result {
      case .success(let checkResponses):
        // Map the responses back to the workouts
        var updatedWorkouts = workouts
        for i in 0..<updatedWorkouts.count {
          if let response = checkResponses.data.first(where: {
            $0.workoutId == updatedWorkouts[i].workoutId
          }) {
            updatedWorkouts[i].synced = response.synced
            updatedWorkouts[i].activityName = response.activityName
            updatedWorkouts[i].canBeSynced = response.canBeSynced
            updatedWorkouts[i].warnings = response.warnings
          }
        }
        completionHandler(updatedWorkouts)
      case .failure(let error):
        // Handle the error
        EventHandler.logText("Error checking workouts: \(error)")
        completionHandler([])
      }
    }
  }
  private func sendWorkouts(workouts: [Workout]) {
    guard !isSending else {
      EventHandler.logText("Already sending workouts, skipping this call.")
      return
    }

    isSending = true
    counter = 0

    debounceTimer?.invalidate()

    debounceTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: false) { _ in
      EventHandler.logText("📤 Sending \(workouts.count) workouts to backend")

      let apiRequest = APIRequest(cfg: self.config)
      let requestBody = RequestBody(workouts: workouts)

      apiRequest.send(body: requestBody) { result in
        self.isSending = false

        switch result {
        case .success(let response):
          let lastSyncDate = response.data.lastSyncDate
          UserDefaults.standard.set(lastSyncDate, forKey: Constants.LAST_SYNC_DATE_KEY)
          EventHandler.logText("✅ Last Sync Date updated to \(lastSyncDate)")

        case .failure(let error):
          if self.counter < 3 {
            self.counter += 1
            EventHandler.logText("Retrying sendWorkouts. Attempt \(self.counter)")
            self.sendWorkouts(workouts: workouts)
          } else {
            EventHandler.logText("Failed to send workouts after 3 attempts: \(error)")
            self.counter = 0
          }
        }
      }
    }
  }
}

extension WorkoutRecords {
  private func formatDate(_ date: Date) -> String {
    let dateFormatter = DateFormatter()
    dateFormatter.timeZone = TimeZone(abbreviation: "UTC")
    dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
    return dateFormatter.string(from: date)
  }
}
