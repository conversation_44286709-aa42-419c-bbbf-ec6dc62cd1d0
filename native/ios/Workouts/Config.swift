//
//  Config.swift
//  workoutrecords
//
//  Created by <PERSON><PERSON> on 03/11/2020.
//

import Foundation

protocol Config {
  func get(key: String) -> Any
  func set(key: String, value: Any)
  func isSyncEnabled() -> Bool
  func isAppleHealthConnected() -> Bool
  func userAccessToken() -> String?
  func getLastSyncDate() -> Date
  func getUserId() -> String?
  func getDeviceId() -> String?
  func getApiBaseUrl() -> String?
  func getActivitiesPushUrl() -> String?
  func getActivitiesCheckUrl() -> String?
}

class WorkoutsConfig: Config {

  private var accessToken: String?
  private var lastSyncDate: String?
  private var syncEnabled: Bool = false

  /*
   @var config store configuration values
   */
  private var config: [String: Any] = [:]

  /**
  * initializer
  */
  public init(cfg: [String: Any]?) {
    if cfg != nil {
      self.config = cfg!
    }
  }

  /**
  * Get a config value for a key. returns empty string if not found
  */
  public func get(key: String) -> Any {
    return self.config[key] ?? ""
  }

  /**
  * Set a config key/value
  */
  public func set(key: String, value: Any) {
    self.config[key] = value
  }

  public func isSyncEnabled() -> Bool {
    return self.isAppleHealthConnected() && self.userAccessToken() != nil
  }

  public func isAppleHealthConnected() -> Bool {
    let isConnected = UserDefaults.standard.bool(forKey: Constants.APPLE_HEALTH_CONNECTED_KEY)
    print("isAppleHealthConnected: ", isConnected)
    return isConnected
  }

  public func userAccessToken() -> String? {
    let accToken = UserDefaults.standard.value(forKey: Constants.ACCESS_TOKEN_KEY) as? String
    print("AccessToken: ", accToken)
    return accToken
  }

  public func getUserId() -> String? {
    let userId = UserDefaults.standard.value(forKey: Constants.USER_ID_KEY) as? String
    return userId
  }

  public func getDeviceId() -> String? {
    let deviceId = UserDefaults.standard.value(forKey: Constants.DEVICE_ID_KEY) as? String
    return deviceId
  }

  public func getApiBaseUrl() -> String? {
    let baseUrl = UserDefaults.standard.value(forKey: Constants.API_BASE_URL_KEY) as? String
    return baseUrl
  }
  public func getActivitiesPushUrl() -> String? {
    return self.getApiBaseUrl()! + "/v1.0/wearable/apple-health/push/activities"
  }
  public func getActivitiesCheckUrl() -> String? {
    return self.getApiBaseUrl()! + "/v1.0/wearable/activities/check/provider/AppleHealth"
  }
  public func getLastSyncDate() -> Date {
    let storedDate = UserDefaults.standard.double(forKey: Constants.LAST_SYNC_DATE_KEY)

    // If no date is stored, use the start of today
    if storedDate == 0 {
      return Date().startOfDay
    }

    let lastSyncDate = Date(timeIntervalSince1970: TimeInterval(storedDate / 1000))
    let tenDaysAgo = Calendar.current.date(byAdding: .day, value: -10, to: Date()) ?? Date()

    if lastSyncDate < tenDaysAgo {
      EventHandler.logText("⚠️ Last sync date is older than 10 days. Adjusting to 10 days ago.")
      return tenDaysAgo
    }

    return lastSyncDate
  }
}
