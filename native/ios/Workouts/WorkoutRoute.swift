//
// Created by <PERSON><PERSON> on 18/11/2020.
//
import Foundation
import HealthKit
import MapKit

typealias WorkoutRoutesCompletionBlock = (Error?) -> Void
typealias WorkoutRouteLocationsCompletionBlock = (Error?) -> Void

enum WorkoutRoutesLoadingError: Error {
  case castingError
}

enum WorkoutLocationsLoadingError: Error {
  case castingError
}

class WorkoutRoute {
  let route : HKWorkoutRoute
  let store: HKHealthStore
  var data:[CLLocation]

  internal init(route: HKWorkoutRoute, store: HKHealthStore) {
    self.route = route
    self.store = store
    self.data = []
  }

  func loadLocationData(
      completion: @escaping  (Error?, [CLLocation]?) -> Void
  ) {
    let query = HKWorkoutRouteQuery(route: self.route) { (query, locationsOrNil, done, errorOrNil) in
      guard errorOrNil == nil else { return }
      guard let locations = locationsOrNil else { return }
      self.data.append(contentsOf: locations)

      if done {
        return completion(nil, self.data)
      }
    }
    self.store.execute(query)
  }
}

class WorkoutRouteData {
  var locations: [CLLocation]
  internal init(locations: [CLLocation]) {
    self.locations = locations
  }
}

struct LocationData: Encodable {
  var locations: [Location] = []
  init(locations: [CLLocation]) {
    for location in locations {
      self.locations.append(Location(location: location))
    }
  }
}