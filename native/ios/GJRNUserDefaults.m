#import "React/RCTBridgeModule.h"
#import <Foundation/Foundation.h>

@interface RCT_EXTERN_MODULE (GJRNUserDefaults, NSObject)
RCT_EXTERN_METHOD(set : (NSString *)value forKey : (NSString *)key)
RCT_EXTERN_METHOD(get
                  : (NSString *)key resolve
                  : (RCTPromiseResolveBlock)resolve reject
                  : (RCTPromiseRejectBlock)reject)
RCT_EXTERN_METHOD(delete
                  : (NSString *)key resolve
                  : (RCTPromiseResolveBlock)resolve reject
                  : (RCTPromiseRejectBlock)reject)
@end