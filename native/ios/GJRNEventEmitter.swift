/* GJRNEventEmitter.swift*/
import Foundation

@objc(GJRNEventEmitter)
class GJRNEventEmitter: RCTEventEmitter {
  private var supportedEventNames: Set<String> = ["onPushSuccess", "onPushFailure", "logMessage"]
  static var emitter: GJRNEventEmitter!
  private var hasAttachedListener = false

  override init() {
    super.init()
    GJRNEventEmitter.emitter = self
  }

  override public class func requiresMainQueueSetup() -> Bool {
    return false
  }

  override public func supportedEvents() -> [String] {
    return Array(supportedEventNames)
  }

  // These functions make sure that there is an attached listener so that events are
  // only sent when a listener is attached
  override public func startObserving() {
    hasAttachedListener = true
  }

  override public func stopObserving() {
    hasAttachedListener = false
  }
  // Allows sending of supported events and adds protections for when either no listeners
  // ar attached or the specified event isn't a supported event
  public func emitEvent(withName name: String, body: Any!) {
    if hasAttachedListener && supportedEventNames.contains(name) {
      var eventBody: Any = body ?? NSNull()

      // Check if body is Data and convert to a String if so
      if let data = body as? Data, let jsonString = String(data: data, encoding: .utf8) {
        eventBody = jsonString
      }

      self.sendEvent(withName: name, body: eventBody)
    }
  }
}
