import Foundation
import React

@objc(GJRNUserDefaults)
class GJRNUserDefaults: NSObject {

  @objc func set(_ value: String, forKey key: String) {
    UserDefaults.standard.set(value, forKey: key)
  }

  @objc func get(_ key: String, resolve: RCTPromiseResolveBlock, reject: RCTPromiseRejectBlock) {
    if let value = UserDefaults.standard.string(forKey: key) {
      resolve(value)
    } else {
      resolve(nil)
    }
  }

  @objc func delete(_ key: String, resolve: RCTPromiseResolveBlock, reject: RCTPromiseRejectBlock) {
    UserDefaults.standard.removeObject(forKey: key)
    resolve(true)
  }

  @objc static func requiresMainQueueSetup() -> Bool {
    return false
  }
}
