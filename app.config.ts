import {ExpoConfig, ConfigContext} from 'expo/config';
const withNativeIosFiles = require('./plugins/with-native-ios-files');
const packageJson = require('./package.json');

const wearableNames = [
  'applehealth',
  'coros',
  'fitbit',
  'garmin',
  'healthconnect',
  'oura',
  'polar',
  'suunto',
  'wahoo',
  'whoop',
  'withings',
];

function getIconPath(prefix: string = ''): string {
  const bundleId = process.env.EXPO_PUBLIC_BUNDLE_IDENTIFIER || '';
  let ret = `./assets/images/${prefix ? prefix + '-' : ''}icon.png`;
  if (bundleId.includes('dev')) {
    ret = `./assets/images/${prefix ? prefix + '-' : ''}icon-dev.png`;
  } else if (bundleId.includes('alpha')) {
    ret = `./assets/images/${prefix ? prefix + '-' : ''}icon-alpha.png`;
  } else if (bundleId.includes('beta')) {
    ret = `./assets/images/${prefix ? prefix + '-' : ''}icon-beta.png`;
  }
  return ret;
}

function getEnvironment(): string {
  const bundleId = process.env.EXPO_PUBLIC_BUNDLE_IDENTIFIER || '';
  switch (bundleId) {
    case 'com.gojoe.app':
      return 'production';
    case 'com.gojoe.app.beta':
      return 'beta';
    case 'com.gojoe.app.alpha':
      return 'alpha';
    case 'com.gojoe.app.dev':
    default:
      return 'dev';
  }
}

function getIosAssociateDomains(): string[] {
  const env = getEnvironment();
  switch (env) {
    case 'production':
      return [
        'applinks:readysetgojoe.app.link',
        'applinks:readysetgojoe-alternate.app.link',
      ];
    case 'beta':
      return [
        'applinks:readysetgojoe.test-app.link',
        'applinks:readysetgojoe-alternate.test-app.link',
      ];
    case 'alpha':
      return ['applinks:f0k9u.app.link', 'applinks:f0k9u-alternate.app.link'];
    case 'dev':
    default:
      return [
        'applinks:f0k9u.test-app.link',
        'applinks:f0k9u-alternate.test-app.link',
      ];
  }
}

export default ({config}: ConfigContext): ExpoConfig => {
  let modifiedConfig: ExpoConfig = {
    ...config,
    name: 'GoJoe',
    slug: 'health-and-fitness',
    version: packageJson.version,
    orientation: 'portrait',
    icon: getIconPath(),
    scheme: process.env.EXPO_PUBLIC_BUNDLE_IDENTIFIER,
    userInterfaceStyle: 'automatic',
    newArchEnabled: true,
    ios: {
      usesAppleSignIn: true,
      googleServicesFile: './services/google/GoogleService-Info.plist',
      supportsTablet: true,
      bundleIdentifier: process.env.EXPO_PUBLIC_BUNDLE_IDENTIFIER,
      config: {
        usesNonExemptEncryption: false,
      },
      infoPlist: {
        NSLocationAlwaysAndWhenInUseUsageDescription:
          'GoJoe requires your precise location to track your outdoor activities.',
        NSLocationWhenInUseUsageDescription:
          'GoJoe requires your precise location to track your outdoor activities.',
        NSMotionUsageDescription:
          'GoJoe uses your Motion and Fitness activity to provide real-time GPS tracking and enhance your experience by adapting its behavior based on whether you are moving.',
        UIBackgroundModes: ['location', 'fetch', 'processing', 'audio'],
        'Required Background Modes': ['Sync workouts in background'],
        BGTaskSchedulerPermittedIdentifiers: [
          'com.transistorsoft.fetch',
          'com.transistorsoft.customtask',
        ],
        NSHealthShareUsageDescription:
          'GoJoe needs access to your health data to provide insights and track your workouts effectively.',
        NSHealthUpdateUsageDescription:
          'GoJoe will share your workout data with connected services to keep your progress in sync.',
        LSApplicationQueriesSchemes: wearableNames.map(
          (name) => `${name}.${process.env.EXPO_PUBLIC_BUNDLE_IDENTIFIER}`,
        ),
        CFBundleURLTypes: wearableNames.map((name) => {
          const scheme = `${name.toLowerCase()}.${process.env.EXPO_PUBLIC_BUNDLE_IDENTIFIER}`;
          return {
            CFBundleURLSchemes: [scheme],
          };
        }),
      },
      associatedDomains: getIosAssociateDomains(),
      entitlements: {
        'aps-environment': process.env.EXPO_PUBLIC_APS_ENVIRONMEN,
        'com.apple.developer.healthkit': true,
        'com.apple.developer.healthkit.access': [],
        'com.apple.developer.healthkit.background-delivery': true,
      },
    },
    android: {
      permissions: [
        'android.permission.FOREGROUND_SERVICE_LOCATION',
        'android.permission.health.READ_STEPS',
        'android.permission.health.READ_HEART_RATE',
        'android.permission.health.READ_EXERCISE',
        'android.permission.health.READ_SLEEP',
        'android.permission.health.READ_ACTIVE_CALORIES_BURNED',
        'android.permission.health.READ_TOTAL_CALORIES_BURNED',
        'android.permission.health.READ_DISTANCE',
        'android.permission.health.READ_SPEED',
        'android.permission.health.READ_HEALTH_DATA_IN_BACKGROUND',
        'android.permission.RECEIVE_BOOT_COMPLETED',
        'android.permission.WAKE_LOCK',
        'android.permission.ACTIVITY_RECOGNITION',
        'android.permission.ACCESS_NETWORK_STATE',
      ],
      googleServicesFile: './services/google/google-services.json',
      adaptiveIcon: {
        foregroundImage: getIconPath('adaptive'),
      },
      package: process.env.EXPO_PUBLIC_BUNDLE_IDENTIFIER,
      intentFilters: wearableNames.map((scheme) => ({
        action: 'VIEW',
        data: {
          scheme: `${scheme}.${process.env.EXPO_PUBLIC_BUNDLE_IDENTIFIER}`,
        },
        category: ['BROWSABLE', 'DEFAULT'],
      })),
    },
    web: {
      bundler: 'metro',
      output: 'static',
      favicon: './assets/images/favicon.png',
    },
    plugins: [
      ['expo-health-connect'],
      [
        'expo-build-properties',
        {
          ios: {
            deploymentTarget: '15.1',
          },
          android: {
            compileSdkVersion: 35,
            targetSdkVersion: 35,
            minSdkVersion: 26,
          },
        },
      ],
      [
        'expo-splash-screen',
        {
          backgroundColor: '#CA3D2A',
          resizeMode: 'contain',
          image: './assets/logos/logo_square.png',
          imageWidth: 250,
          imageHeight: 250,
        },
      ],
      [
        'expo-font',
        {
          fonts: ['./assets/fonts/Inter.ttf'],
        },
      ],
      'expo-router',
      'expo-localization',
      [
        'expo-secure-store',
        {
          faceIDPermission:
            'Allow $(PRODUCT_NAME) to access your Face ID biometric data.',
        },
      ],
      'expo-secure-store',
      [
        'react-native-background-geolocation',
        {
          license: process.env.EXPO_PUBLIC_BACKGROUND_GEOLOCATION_KEY,
        },
      ],
      [
        'expo-gradle-ext-vars',
        {
          googlePlayServicesLocationVersion: '21.1.0',
          appCompatVersion: '1.4.2',
          logbackVersion: '3.0.0',
        },
      ],
      'react-native-background-fetch',
      [
        'expo-video',
        {
          supportsBackgroundPlayback: true,
          supportsPictureInPicture: true,
        },
      ],
      'expo-audio',
      '@react-native-google-signin/google-signin',
      [
        'react-native-fbsdk-next',
        {
          appID: process.env.EXPO_PUBLIC_FACEBOOK_APP_ID,
          clientToken: process.env.EXPO_PUBLIC_FACEBOOK_CLIENT_TOKEN,
          displayName: 'GoJoe',
          scheme: `fb${process.env.EXPO_PUBLIC_FACEBOOK_APP_ID}`,
          advertiserIDCollectionEnabled: false,
          autoLogAppEventsEnabled: false,
          isAutoInitEnabled: false,
        },
      ],
      'expo-apple-authentication',
      [
        'customerio-expo-plugin',
        {
          android: {
            googleServicesFile: './services/google/google-services.json',
          },
          ios: {
            pushNotification: {
              useRichPush: true,
              env: {
                cdpApiKey: process.env.EXPO_PUBLIC_CUSTOMER_IO_CDP_API_KEY,
                region: 'eu',
              },
            },
          },
        },
      ],
      [
        'expo-audio',
        {
          microphonePermission:
            '$(PRODUCT_NAME) would like to use your microphone for voice recording.',
        },
      ],
      [
        'expo-image-picker',
        {
          cameraPermission:
            '$(PRODUCT_NAME) would like to use your camera to share image in a message.',
          photosPermission:
            '$(PRODUCT_NAME) would like to use your device gallery to attach image in a message.',
        },
      ],
      [
        '@sentry/react-native/expo',
        {
          organization: 'gojoe',
          project: 'expo',
          url: 'https://sentry.io/',
        },
      ],
      [
        '@intercom/intercom-react-native',
        {
          appId: 'kifedith',
          iosApiKey: 'ios_sdk-8786991573c0e5f5fbbc04128523a4a8ce741d2e',
          androidApiKey: 'android_sdk-6fc1ccbe4ece1393622e329c4696d68f0cc0ec20',
          // intercomRegion: 'EU', // Europe
        },
      ],
      [
        'expo-camera',
        {
          cameraPermission: 'Allow $(PRODUCT_NAME) to access your camera',
          microphonePermission:
            'Allow $(PRODUCT_NAME) to access your microphone',
          recordAudioAndroid: true,
        },
      ],
      [
        '@rnmapbox/maps',
        {
          RNMapboxMapsDownloadToken:
            '*************************************************************************************************',
          RNMapboxMapsVersion: '11.6.0',
        },
      ],
      [
        'expo-tracking-transparency',
        {
          userTrackingPermission:
            'We use your data to understand app usage and improve performance.',
        },
      ],
      [
        '@config-plugins/react-native-branch',
        {
          apiKey: process.env.EXPO_PUBLIC_BRANCH_API_KEY,
          iosAppDomain: process.env.EXPO_PUBLIC_BRANCH_IOS_APP_DOMAIN,
        },
      ],
      'expo-screen-orientation',
    ],
    experiments: {
      typedRoutes: true,
    },
    extra: {
      eas: {
        projectId: 'b2525d71-e362-4d5c-ad62-f879c19b233c',
        build: {
          experimental: {
            ios: {
              appExtensions: [
                {
                  targetName: 'NotificationService',
                  bundleIdentifier:
                    process.env.EXPO_PUBLIC_NOTIFICATION_BUNDLE_IDENTIFIER,
                },
              ],
            },
          },
        },
      },
      customerIo: {
        cdpApiKey: process.env.EXPO_PUBLIC_CUSTOMER_IO_CDP_API_KEY,
        siteId: process.env.EXPO_PUBLIC_CUSTOMER_IO_SITE_ID,
      },
      google: {
        webClientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
      },
      betterStack: {
        token: '************************',
      },
      streamChat: {
        key: process.env.EXPO_PUBLIC_STREAM_CHAT_KEY,
      },
      sentry: {
        dns: 'https://<EMAIL>/4508919639375872',
        environment: getEnvironment(),
      },
      segment: {
        apiKey: process.env.EXPO_PUBLIC_SEGMENT_API_KEY,
      },
      mapbox: {
        accessToken:
          'pk.eyJ1IjoicmVhZHlzZXRnb2pvZSIsImEiOiJja2w3NjN0bngwdzBrMnNxcG05eG5ldG94In0.JAAgvcZWkHYxylHgawFqBA',
        // '*************************************************************************************************',
      },
      wearable: {
        scheme: wearableNames.reduce(
          (acc, name) => {
            acc[name.toLowerCase()] =
              `${name}.${process.env.EXPO_PUBLIC_BUNDLE_IDENTIFIER}`;
            return acc;
          },
          {} as Record<string, string>,
        ),
      },
      api: {
        url: process.env.EXPO_PUBLIC_API_URL,
      },
    },
    owner: 'gojoe',
    runtimeVersion: {
      policy: 'appVersion',
    },
    updates: {
      url: 'https://u.expo.dev/b2525d71-e362-4d5c-ad62-f879c19b233c',
    },
  };
  return withNativeIosFiles(modifiedConfig);
};
