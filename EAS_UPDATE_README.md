# EAS Update for Production

This document explains how to use EAS Update to update the JavaScript code of your production app without requiring a new app store submission.

## What is EAS Update?

EAS Update allows you to push updates to your app's JavaScript code over-the-air (OTA) without going through the app store review process. This is useful for:

- Fixing bugs quickly
- Updating content
- Making minor UI changes
- Adding new features that don't require native code changes

## Prerequisites

- Make sure you have the latest EAS CLI installed: `npm install -g eas-cli`
- Ensure you're logged in to your Expo account: `eas login`
- Your app must have been built with EAS Build and configured for updates

## Using the Production Update Script

We've created a script that handles the update process for the production environment. The script:

1. Runs the prebuild script to set up the correct environment
2. Ensures Google services files are properly configured for production
3. Exports the app bundle
4. Publishes the update to the production channel

### Running the Update

To update your production app, simply run:

```bash
npm run update:production
```

Or directly:

```bash
./update-production.sh
```

### What Happens During the Update

1. The script sets the `GOOGLE_SERVICES_ENV` to "Release" if not already set
2. It runs the `eas-build-pre-install.sh` script to set up the environment
3. It verifies that the Google services files are in place
4. It exports the app bundle using `npx expo export`
5. It publishes the update to the production channel with a timestamped message

### Important Notes

- This only updates the JavaScript code, not native code
- If you've made changes that require native code updates, you'll need to submit a new build to the app stores
- Updates are delivered to users when they open the app (if they have an internet connection)
- The update will only be applied to app versions that are compatible with the update

### User Experience

When a user opens the app after an update has been published:

1. They will see a notification that a new update is available
2. If they choose to update, they'll see a progress bar showing the download progress (note: this is a simulated progress as the Expo Updates API doesn't provide real-time progress information)
3. Once the download is complete, they'll be prompted to restart the app
4. After restarting, the app will be running the latest version

This provides a seamless update experience without requiring users to manually close and reopen the app to see changes.

## Troubleshooting

If you encounter issues:

1. Make sure your Expo account has access to the project
2. Verify that the app was built with EAS Build and configured for updates
3. Check that the `eas-build-pre-install.sh` script is executable
4. Ensure the Google services files exist in the correct location

For more information, refer to the [EAS Update documentation](https://docs.expo.dev/eas-update/introduction/).
