# Translation Server

This is a simple Node.js server that handles missing translation keys for the GoJoe app. When a translation key is missing in development mode, the app will send the key to this server, which will then save it to the appropriate JSON file.

## Setup

1. Install dependencies:
   ```bash
   cd server
   npm install
   ```

2. Start the server:
   ```bash
   npm start
   ```

   For development with auto-restart:
   ```bash
   npm run dev
   ```

## How it works

1. The server runs on port 3000 by default (configurable via the PORT environment variable).
2. It exposes a POST endpoint at `/api/translations/missing` that accepts the following JSON payload:
   ```json
   {
     "lng": "en",
     "ns": "main",
     "key": "Hello",
     "fallbackValue": "Hello"
   }
   ```
3. When a request is received, the server:
   - Determines the appropriate translation file based on the language and namespace
   - Checks if the file exists (creates it if it doesn't for non-default languages)
   - Reads the existing translations
   - Adds the new key if it doesn't already exist
   - Sorts the keys alphabetically
   - Writes the updated translations back to the file

## Integration with the app

The server is integrated with the app through the `missingKeyHandler` in `src/locales/index.ts`. When a translation key is missing in development mode, the app will send the key to this server.

## Notes

- This server should only be used in development mode.
- The server needs to be running for the missing key handler to work.
- If the server is not running, the app will still function normally, but missing keys will not be saved.
