const express = require('express');
const fs = require('fs');
const path = require('path');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());

/**
 * Endpoint to handle missing translation keys
 * Expects a POST request with the following body:
 * {
 *   lng: 'en',    // language code
 *   ns: 'main',   // namespace (optional)
 *   key: 'Hello', // the missing key
 *   fallbackValue: 'Hello' // the fallback value (usually same as key)
 * }
 */
app.post('/api/translations/missing', async (req, res) => {
  try {
    const {lng, ns, key, fallbackValue} = req.body;

    if (!lng || !key) {
      return res
        .status(400)
        .json({error: 'Missing required parameters: lng and key are required'});
    }

    // Determine the file path based on language
    let filePath;

    // For default English translations, we need to handle the namespace
    filePath = path.resolve(__dirname, '../src/locales/languages/en.json');

    console.log('filePath', filePath);
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      // If it's a non-default language file that doesn't exist, create it
      if (lng !== 'en') {
        fs.writeFileSync(filePath, JSON.stringify({}, null, 2));
      } else {
        return res
          .status(404)
          .json({error: `Translation file not found: ${filePath}`});
      }
    }

    // Read the existing translations
    const fileContent = fs.readFileSync(filePath, 'utf8');
    let translations = {};

    try {
      translations = JSON.parse(fileContent);
    } catch (error) {
      console.error(`Error parsing JSON from ${filePath}:`, error);
      return res.status(500).json({error: 'Error parsing translation file'});
    }

    // Check if the key already exists
    if (translations[key]) {
      return res.status(200).json({message: 'Key already exists', key});
    }

    // Add the new key with the fallback value (or the key itself if no fallback)
    translations[key] = fallbackValue || key;

    // Sort keys alphabetically for better organization
    const sortedTranslations = Object.keys(translations)
      .sort()
      .reduce((acc, currentKey) => {
        acc[currentKey] = translations[currentKey];
        return acc;
      }, {});

    // Write the updated translations back to the file
    fs.writeFileSync(filePath, JSON.stringify(sortedTranslations, null, 2));

    console.log(`Added missing translation: ${key} to ${filePath}`);
    return res
      .status(201)
      .json({message: 'Translation key added successfully', key});
  } catch (error) {
    console.error('Error handling missing translation:', error);
    return res.status(500).json({error: 'Internal server error'});
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Translation server running on port ${PORT}`);
  console.log(
    `POST endpoint available at: http://localhost:${PORT}/api/translations/missing`,
  );
});
