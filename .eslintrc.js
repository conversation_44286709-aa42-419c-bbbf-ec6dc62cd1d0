// https://docs.expo.dev/guides/using-eslint/
module.exports = {
  extends: [
    'expo',
    'plugin:prettier/recommended', // Enables eslint-config-prettier and eslint-plugin-prettier
  ],
  plugins: ['prettier'],
  rules: {
    'prettier/prettier': [
      'error',
      {
        bracketSpacing: false, // Do not enforce space inside curly braces
        singleQuote: true,
        semi: true, // Add semicolons
        trailingComma: 'all', // Add trailing commas
      },
    ],
    'object-curly-spacing': 'off', // Turn off eslint rule for object spacing
  },
};
