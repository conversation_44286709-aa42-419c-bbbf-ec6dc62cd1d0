name: Manual Build and Update

on:
  workflow_dispatch:
    inputs:
      platform:
        description: 'Platform to build (ios, android, or both)'
        required: true
        default: 'both'
        type: choice
        options:
          - ios
          - android
          - both
      profile:
        description: 'Build profile to use'
        required: true
        default: 'beta'
        type: choice
        options:
          - development
          - alpha
          - beta
          - production

jobs:
  android_device_build:
    if: ${{ inputs.platform == 'android' || inputs.platform == 'both' }}
    type: build
    params:
      platform: android
      profile: ${{ inputs.profile }}

  ios_device_build:
    if: ${{ inputs.platform == 'ios' || inputs.platform == 'both' }}
    type: build
    params:
      platform: ios
      profile: ${{ inputs.profile }}

  deploy_update:
    needs: [android_device_build, ios_device_build]
    if: ${{ always() && (needs.android_device_build.result == 'success' || needs.ios_device_build.result == 'success') }}
    platform: web
    type: custom
    steps:
      - uses: eas/checkout
      - run: ./eas-build-pre-install.sh
      - uses: eas/install_node_modules
      - run: npx expo export
      - run: npx eas update --auto

  notify_slack_success:
    needs: [deploy_update]
    if: ${{ needs.deploy_update.result == 'success' }}
    steps:
      - name: Notify Slack
        run: |
          curl -X POST -H "Content-Type: application/json" \
          -d '{
            "blocks": [
              {
                "type": "rich_text",
                "elements": [
                  {
                    "type": "rich_text_section",
                    "elements": [
                      {
                        "type": "text",
                        "text": "✅ Manual build and update completed successfully! Profile: ${{ inputs.profile }}, Platform: ${{ inputs.platform }} 🚀"
                      }
                    ]
                  }
                ]
              }
            ]
          }' *********************************************************************************

  notify_slack_fail:
    needs: [deploy_update]
    if: ${{ needs.deploy_update.result == 'failure' }}
    steps:
      - name: Notify Slack
        run: |
          curl -X POST -H "Content-Type: application/json" \
          -d '{
            "blocks": [
              {
                "type": "rich_text",
                "elements": [
                  {
                    "type": "rich_text_section",
                    "elements": [
                      {
                        "type": "text",
                        "text": "❌ Manual build and update failed! Profile: ${{ inputs.profile }}, Platform: ${{ inputs.platform }} 😞"
                      }
                    ]
                  }
                ]
              }
            ]
          }' *********************************************************************************