name: Development Build

on:
#  push:
#    branches-ignore:
#      - 'renovate/*'
#  pull_request:
#    branches-ignore:
#      - 'renovate/*'


jobs:
  # {outputs: {build_id: "", platform:ios}, status= failed|success}
  android_device_build:
    type: build
    params:
      platform: android
      profile: beta

  notify_slack_android:
    needs: [android_device_build]
    steps:
      - name: Notify Slack
        run: |
          curl -X POST -H "Content-Type: application/json" \
          -d '{
            "blocks": [
              {
                "type": "rich_text",
                "elements": [
                  {
                    "type": "rich_text_section",
                    "elements": [
                      {
                        "type": "text",
                        "text": "New Android build is ready! 🚀 "
                      },
                      {
                        "type": "link",
                        "url": "https://expo.dev/accounts/gojoe/projects/health-and-fitness/builds/${{ needs.android_device_build.outputs.build_id }}"
                      }
                    ]
                  }
                ]
              }
            ]
          }' *********************************************************************************

  notify_slack_android_fail:
    after: [android_device_build]
    if: ${{ after.android_device_build.status == 'failure' }}
    steps:
      - name: Notify Slack
        run: |
          curl -X POST -H "Content-Type: application/json" \
          -d '{
            "blocks": [
              {
                "type": "rich_text",
                "elements": [
                  {
                    "type": "rich_text_section",
                    "elements": [
                      {
                        "type": "text",
                        "text": "Beta Android build failed 😞 "
                      },
                      {
                        "type": "link",
                        "url": "https://expo.dev/accounts/gojoe/projects/health-and-fitness/builds/${{ after.android_device_build.outputs.build_id }}"
                      }
                    ]
                  }
                ]
              }
            ]
          }' *********************************************************************************

  ios_device_build:
    type: build
    params:
      platform: ios
      profile: beta

  notify_slack_ios:
    needs: [ios_device_build]
    steps:
      - name: Notify Slack
        run: |
          curl -X POST -H "Content-Type: application/json" \
          -d '{
            "blocks": [
              {
                "type": "rich_text",
                "elements": [
                  {
                    "type": "rich_text_section",
                    "elements": [
                      {
                        "type": "text",
                        "text": "New Beta iOS build is ready! 🚀 "
                      },
                      {
                        "type": "link",
                        "url": "https://expo.dev/accounts/gojoe/projects/health-and-fitness/builds/${{needs.ios_device_build.outputs.build_id}}"
                      }
                    ]
                  }
                ]
              }
            ]
          }' *********************************************************************************

  notify_slack_ios_fail:
    after: [ios_device_build]
    if: ${{ after.ios_device_build.status == 'failure' }}
    steps:
      - name: Notify Slack
        run: |
          curl -X POST -H "Content-Type: application/json" \
          -d '{
            "blocks": [
              {
                "type": "rich_text",
                "elements": [
                  {
                    "type": "rich_text_section",
                    "elements": [
                      {
                        "type": "text",
                        "text": "Beta iOS build failed 😞 "
                      },
                      {
                        "type": "link",
                        "url": "https://expo.dev/accounts/gojoe/projects/health-and-fitness/builds/${{ after.ios_device_build.outputs.build_id }}"
                      }
                    ]
                  }
                ]
              }
            ]
          }' *********************************************************************************
        

  deploy-preview:
    name: Deploy Preview
    environment: preview
    platform: web
    type: custom
    steps:
      - uses: eas/checkout
      - run: ./eas-build-pre-install.sh
      - uses: eas/install_node_modules
      - run: npx expo export -p web

