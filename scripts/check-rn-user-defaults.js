const fs = require('fs');
const path = require('path');

const IOS_PATH = path.join(__dirname, '../ios');
const PODS_PATH = path.join(IOS_PATH, 'Pods');
const MANIFEST_LOCK_PATH = path.join(PODS_PATH, 'Manifest.lock');
const APP_DELEGATE_PATH = path.join(IOS_PATH, 'GoJoe', 'AppDelegate.mm');

function checkPodPresence() {
  console.log('🔍 Checking Podfile for RNUserDefaults...');
  if (!fs.existsSync(MANIFEST_LOCK_PATH)) {
    console.warn('🚨 Manifest.lock not found. Did you run `pod install`?');
    return false;
  }

  const manifestContent = fs.readFileSync(MANIFEST_LOCK_PATH, 'utf8');
  const isPresent = manifestContent.includes('RNUserDefaults');

  if (isPresent) {
    console.log('✅ RNUserDefaults is present in Pods/Manifest.lock');
  } else {
    console.warn('🚨 RNUserDefaults not found in Pods/Manifest.lock');
  }

  return isPresent;
}

function injectRNUserDefaults() {
  console.log('🛠️ Injecting RNUserDefaults into AppDelegate.mm...');

  let appDelegateContent = fs.readFileSync(APP_DELEGATE_PATH, 'utf8');

  const importStatement = `#import "RNUserDefaults.h"`;
  const initStatement = `  [[RNUserDefaults alloc] init];`;

  // Check for import
  if (!appDelegateContent.includes(importStatement)) {
    const importInsertionPoint = appDelegateContent.indexOf('#import');
    appDelegateContent =
      appDelegateContent.slice(0, importInsertionPoint) +
      `${importStatement}\n` +
      appDelegateContent.slice(importInsertionPoint);
    console.log('✅ RNUserDefaults import statement added.');
  } else {
    console.log('ℹ️ RNUserDefaults import already present.');
  }

  // Check for initialization in didFinishLaunchingWithOptions
  if (!appDelegateContent.includes(initStatement)) {
    const didFinishLaunchingRegex =
      /\[super application:application didFinishLaunchingWithOptions:launchOptions\];/;
    if (didFinishLaunchingRegex.test(appDelegateContent)) {
      appDelegateContent = appDelegateContent.replace(
        didFinishLaunchingRegex,
        (match) => `${match}\n  ${initStatement}`,
      );
      console.log('✅ RNUserDefaults initialization added.');
    } else {
      console.warn('🚨 didFinishLaunchingWithOptions method not found.');
    }
  } else {
    console.log('ℹ️ RNUserDefaults initialization already present.');
  }

  // Write back the modified AppDelegate.mm
  fs.writeFileSync(APP_DELEGATE_PATH, appDelegateContent);
}

function runChecks() {
  console.log('🔍 Running RNUserDefaults checks...');
  const podCheck = checkPodPresence();

  if (podCheck) {
    injectRNUserDefaults();
  } else {
    console.warn('🚨 RNUserDefaults is missing from Pods. Aborting injection.');
  }
}

runChecks();
