const path = require('path');
const sharp = require('sharp');

const baseIconPath = path.resolve(__dirname, '../assets/images/icon.png');
const baseIconPathAndroid = path.resolve(
  __dirname,
  '../assets/images/adaptive-icon.png',
);
const outputDir = path.resolve(__dirname, '../assets/images');

const labelStyles = {
  dev: {background: '#007AFF', text: '#FFFFFF'}, // blue
  alpha: {background: '#34C759', text: '#FFFFFF'}, // green
  beta: {background: '#FFD700', text: '#000000'}, // yellow
};

const labels = Object.keys(labelStyles);

async function generateIcons() {
  for (const label of labels) {
    const {background, text} = labelStyles[label];

    await sharp(baseIconPath)
      .composite([
        {
          input: Buffer.from(`
            <svg width="1024" height="1024">
              <rect x="212" y="820" rx="50" ry="50" width="600" height="160" fill="${background}" />
              <text x="512" y="950" font-size="140" font-weight="bold" text-anchor="middle" fill="${text}" font-family="Arial, sans-serif">
                ${label.toUpperCase()}
              </text>
            </svg>
          `),
          top: 0,
          left: 0,
        },
      ])
      .toFile(path.join(outputDir, `icon-${label}.png`));

    console.log(`✅ Created icon-${label}.png`);
    await sharp(baseIconPathAndroid)
      .composite([
        {
          input: Buffer.from(`
            <svg width="1024" height="1024">
              <rect x="452" y="260" rx="50" ry="50" width="350" height="140" fill="${background}" />
              <text x="632" y="360" font-size="90" font-weight="bold" text-anchor="middle" fill="${text}" font-family="Arial, sans-serif">
                ${label.toUpperCase()}
              </text>
            </svg>
          `),
          top: 0,
          left: 0,
        },
      ])
      .toFile(path.join(outputDir, `adaptive-icon-${label}.png`));

    console.log(`✅ Created adaptive-icon-${label}.png`);
  }
}

generateIcons().then(() => {
  console.log('✅ All icons generated with larger text and custom colors!');
});
