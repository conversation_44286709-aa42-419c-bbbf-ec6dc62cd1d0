{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "editor.codeActionsOnSave": {
    "source.fixAll": "always"
  },
  "eslint.validate": ["javascript", "typescript", "typescriptreact", "javascriptreact"],
  "prettier.enable": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": true,
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
}
