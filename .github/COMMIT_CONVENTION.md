# ✍️ Commit Message Guidelines (Conventional Commits)

Follow this format for consistent, readable Git history and better tooling support.

## 🔑 Types

| Type       | Use for...                                                                  |
|------------|-----------------------------------------------------------------------------|
| `feat`     | New feature                                                                 |
| `fix`      | Bug fix                                                                     |
| `refactor` | Code refactoring (not fixing a bug or adding a feature)                    |
| `chore`    | Misc tasks like configs, build scripts, version bumps                      |
| `docs`     | Documentation-only changes                                                  |
| `style`    | Code style changes (formatting, etc — no logic change)                      |
| `test`     | Adding or updating tests                                                    |
| `perf`     | Performance improvements                                                    |
| `ci`       | Continuous integration changes                                              |
| `revert`   | Reverting previous commits                                                  |

---

## 🧱 Format

```bash
<type>(optional-scope): <short summary>

[optional body]

[optional footer]
